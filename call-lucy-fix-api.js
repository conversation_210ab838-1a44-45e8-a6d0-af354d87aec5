const axios = require('axios');

const callLucyFixAPI = async () => {
  try {
    console.log('🔧 Calling API to fix Lucy Mosha payment issue...\n');

    // Check if server is running
    try {
      const healthResponse = await axios.get('http://localhost:5000/api/health');
      console.log('✅ Server is running:', healthResponse.data.status);
    } catch (error) {
      console.log('❌ Server is not responding. Please make sure the server is running.');
      return;
    }

    // Wait a moment for server to fully initialize
    console.log('⏳ Waiting for server to fully initialize...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Call the fix endpoint
    console.log('\n🔄 Calling Lucy fix endpoint...');
    
    const response = await axios.post('http://localhost:5000/api/users/fix-lucy-payment', {});
    
    if (response.data.success) {
      console.log('🎉 SUCCESS! Lucy Mosha payment issue has been fixed!');
      
      console.log('\n📋 Updated Account Details:');
      console.log('- User ID:', response.data.data.userId);
      console.log('- Name:', response.data.data.name);
      console.log('- Email:', response.data.data.email);
      console.log('- Payment Required:', response.data.data.paymentRequired);
      console.log('- Subscription Status:', response.data.data.subscriptionStatus);
      console.log('- Subscription Plan:', response.data.data.subscriptionPlan);
      console.log('- Start Date:', response.data.data.subscriptionStartDate);
      console.log('- End Date:', response.data.data.subscriptionEndDate);
      
      console.log('\n✅ Lucy Mosha can now:');
      console.log('✅ Access all platform features');
      console.log('✅ Take quizzes without payment restrictions');
      console.log('✅ View study materials');
      console.log('✅ Use AI features');
      console.log('✅ No more payment loading screens');
      
      console.log('\n📱 Instructions for Lucy:');
      console.log('1. 🔄 Refresh the browser page (F5 or Ctrl+R)');
      console.log('2. 🚪 Or log out and log back in');
      console.log('3. ✨ The payment loading screen should disappear');
      console.log('4. 🎯 She should now have full access to BrainWave platform');
      console.log('5. 📚 She can start taking quizzes and accessing study materials');
      
      console.log('\n🎊 PROBLEM SOLVED! Lucy\'s payment has been processed successfully!');
      
    } else {
      console.log('❌ Failed to fix payment issue:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Error calling fix API:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
      
      if (error.response.status === 404) {
        console.log('\n💡 The endpoint might not be available yet. Server may still be starting...');
        console.log('Please wait a moment and try again.');
      }
    }
  }
};

callLucyFixAPI();
