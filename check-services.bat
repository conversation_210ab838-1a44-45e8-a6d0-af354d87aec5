@echo off
echo ========================================
echo    🔍 BrainWave Services Status Check
echo ========================================
echo.

echo 📊 Checking port usage...
echo.

echo 🚀 Node.js Server (Port 5000):
netstat -an | findstr ":5000" | findstr "LISTENING" > nul
if %errorlevel% == 0 (
    echo ✅ Server is running on port 5000
) else (
    echo ❌ Server is NOT running on port 5000
)

echo.
echo ⚛️  React Client (Port 3000):
netstat -an | findstr ":3000" | findstr "LISTENING" > nul
if %errorlevel% == 0 (
    echo ✅ React client is running on port 3000
) else (
    echo ❌ React client is NOT running on port 3000
)

echo.
echo 🗄️  MongoDB (Port 27017):
netstat -an | findstr ":27017" | findstr "LISTENING" > nul
if %errorlevel% == 0 (
    echo ✅ MongoDB is running on port 27017
) else (
    echo ⚠️  MongoDB is not running locally (may be using cloud)
)

echo.
echo ========================================
echo    🌐 Quick Access Links
echo ========================================
echo.
echo 🏠 Home Page: http://localhost:3000
echo 🔐 Login: http://localhost:3000/login
echo 📋 Subscription: http://localhost:3000/subscription
echo 🚀 Server: http://localhost:5000
echo 📋 API Plans: http://localhost:5000/api/plans
echo.

echo 🧪 Testing connections...
echo.

echo Testing server...
curl -s http://localhost:5000 > nul
if %errorlevel% == 0 (
    echo ✅ Server responds to requests
) else (
    echo ❌ Server not responding
)

echo Testing React client...
curl -s http://localhost:3000 > nul
if %errorlevel% == 0 (
    echo ✅ React client responds to requests
) else (
    echo ❌ React client not responding
)

echo.
echo ========================================
echo    📝 Instructions
echo ========================================
echo.
echo 🔄 To start services: run start-all-services.bat
echo 🛑 To stop services: close the terminal windows
echo 🌐 To open app: go to http://localhost:3000
echo.
pause
