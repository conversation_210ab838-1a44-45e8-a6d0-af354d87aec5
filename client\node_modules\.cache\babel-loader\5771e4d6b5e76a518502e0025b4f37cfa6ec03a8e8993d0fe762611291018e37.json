{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{useSelector}from'react-redux';import{motion}from'framer-motion';import{message}from'antd';import'./Hub.css';import{FaHome,FaQuestionCircle,FaBook,FaChartLine,FaUser,FaComments,FaCreditCard,FaInfoCircle,FaGraduationCap,FaTrophy,FaStar,FaRocket,FaRobot,FaSignOutAlt}from'react-icons/fa';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const Hub=()=>{const navigate=useNavigate();const{user}=useSelector(state=>state.user);const[currentQuote,setCurrentQuote]=useState(0);// Logout function\nconst handleLogout=()=>{// Clear authentication data\nlocalStorage.removeItem('token');localStorage.removeItem('user');// Show success message\nmessage.success('Logged out successfully!');// Navigate to home page\nnavigate('/');};const inspiringQuotes=[\"Education is the most powerful weapon which you can use to change the world. - Nelson Mandela\",\"The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt\",\"Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill\",\"Your limitation—it's only your imagination.\",\"Great things never come from comfort zones.\",\"Dream it. Wish it. Do it.\"];useEffect(()=>{const interval=setInterval(()=>{setCurrentQuote(prev=>(prev+1)%inspiringQuotes.length);},4000);return()=>clearInterval(interval);},[]);const navigationItems=[{title:'Take Quiz',description:'Test your knowledge',icon:FaQuestionCircle,path:'/user/quiz',color:'from-blue-500 to-blue-600',hoverColor:'from-blue-600 to-blue-700'},{title:'Study Materials',description:'Books, videos & notes',icon:FaBook,path:'/user/study-material',color:'from-purple-500 to-purple-600',hoverColor:'from-purple-600 to-purple-700'},{title:'Reports',description:'Track your progress',icon:FaChartLine,path:'/user/reports',color:'from-green-500 to-green-600',hoverColor:'from-green-600 to-green-700'},{title:'Ranking',description:'See your position',icon:FaTrophy,path:'/user/ranking',color:'from-yellow-500 to-yellow-600',hoverColor:'from-yellow-600 to-yellow-700'},{title:'Profile',description:'Manage your account',icon:FaUser,path:'/profile',color:'from-indigo-500 to-indigo-600',hoverColor:'from-indigo-600 to-indigo-700'},{title:'Forum',description:'Connect with peers',icon:FaComments,path:'/forum',color:'from-pink-500 to-pink-600',hoverColor:'from-pink-600 to-pink-700'},{title:'Plans',description:'Upgrade your learning',icon:FaCreditCard,path:'/user/plans',color:'from-emerald-500 to-emerald-600',hoverColor:'from-emerald-600 to-emerald-700'},{title:'About Us',description:'Learn about our mission',icon:FaInfoCircle,path:'/user/about-us',color:'from-cyan-500 to-cyan-600',hoverColor:'from-cyan-600 to-cyan-700'}];return/*#__PURE__*/_jsx(\"div\",{className:\"hub-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"hub-content\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:0.6},className:\"hub-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"hub-welcome relative overflow-hidden min-h-[200px]\",children:[/*#__PURE__*/_jsx(motion.div,{className:\"absolute inset-0 rounded-3xl\",style:{background:'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',filter:'blur(30px)'},animate:{scale:[1,1.1,1],opacity:[0.3,0.6,0.3],rotate:[0,2,-2,0]},transition:{duration:8,repeat:Infinity,ease:\"easeInOut\"}}),/*#__PURE__*/_jsxs(motion.div,{className:\"relative z-10 text-center\",initial:{opacity:0},animate:{opacity:1},transition:{duration:1.5},children:[/*#__PURE__*/_jsx(motion.div,{className:\"relative inline-block mr-6\",initial:{x:-200,opacity:0,rotateY:-90},animate:{x:0,opacity:1,rotateY:0},transition:{duration:1.8,delay:0.5,ease:\"easeOut\"},children:/*#__PURE__*/_jsx(motion.span,{className:\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\",style:{background:'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',WebkitBackgroundClip:'text',WebkitTextFillColor:'transparent',backgroundClip:'text',backgroundSize:'200% 200%',fontFamily:\"'Inter', 'SF Pro Display', system-ui, sans-serif\",letterSpacing:'-0.06em',textShadow:'0 0 50px rgba(59, 130, 246, 0.4)'},animate:{backgroundPosition:['0% 50%','100% 50%','0% 50%'],y:[0,-5,0],textShadow:['0 0 50px rgba(59, 130, 246, 0.4)','0 0 80px rgba(59, 130, 246, 0.7)','0 0 50px rgba(59, 130, 246, 0.4)']},transition:{backgroundPosition:{duration:6,repeat:Infinity,ease:\"easeInOut\"},y:{duration:3,repeat:Infinity,ease:\"easeInOut\"},textShadow:{duration:4,repeat:Infinity,ease:\"easeInOut\"}},whileHover:{scale:1.05,rotateZ:[0,2,-2,0],transition:{duration:0.6}},children:\"Study\"})}),/*#__PURE__*/_jsx(motion.div,{className:\"relative inline-block\",initial:{x:200,opacity:0,rotateY:90},animate:{x:0,opacity:1,rotateY:0},transition:{duration:1.8,delay:1,ease:\"easeOut\"},children:/*#__PURE__*/_jsx(motion.span,{className:\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\",style:{background:'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',WebkitBackgroundClip:'text',WebkitTextFillColor:'transparent',backgroundClip:'text',backgroundSize:'200% 200%',fontFamily:\"'Inter', 'SF Pro Display', system-ui, sans-serif\",letterSpacing:'-0.06em',textShadow:'0 0 50px rgba(16, 185, 129, 0.4)'},animate:{backgroundPosition:['0% 50%','100% 50%','0% 50%'],y:[0,5,0],textShadow:['0 0 50px rgba(16, 185, 129, 0.4)','0 0 80px rgba(16, 185, 129, 0.7)','0 0 50px rgba(16, 185, 129, 0.4)']},transition:{backgroundPosition:{duration:5,repeat:Infinity,ease:\"easeInOut\",delay:1},y:{duration:3.5,repeat:Infinity,ease:\"easeInOut\",delay:0.5},textShadow:{duration:4.5,repeat:Infinity,ease:\"easeInOut\",delay:1}},whileHover:{scale:1.05,rotateZ:[0,-2,2,0],transition:{duration:0.6}},children:\"Smarter\"})}),/*#__PURE__*/_jsx(motion.div,{className:\"mt-8 relative\",initial:{opacity:0,y:50,scale:0.8},animate:{opacity:1,y:0,scale:1},transition:{duration:1.5,delay:2},children:/*#__PURE__*/_jsxs(motion.span,{className:\"text-3xl sm:text-4xl font-bold block\",style:{background:'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',WebkitBackgroundClip:'text',WebkitTextFillColor:'transparent',backgroundClip:'text',backgroundSize:'200% 200%',textShadow:'0 0 30px rgba(245, 158, 11, 0.4)'},animate:{backgroundPosition:['0% 50%','100% 50%','0% 50%'],scale:[1,1.02,1],textShadow:['0 0 30px rgba(245, 158, 11, 0.4)','0 0 50px rgba(245, 158, 11, 0.7)','0 0 30px rgba(245, 158, 11, 0.4)']},transition:{backgroundPosition:{duration:4,repeat:Infinity,ease:\"easeInOut\"},scale:{duration:2,repeat:Infinity,ease:\"easeInOut\"},textShadow:{duration:3,repeat:Infinity,ease:\"easeInOut\"}},whileHover:{scale:1.1,rotate:[0,3,-3,0],transition:{duration:0.4}},children:[user===null||user===void 0?void 0:user.name,\"!\"]})}),/*#__PURE__*/_jsx(motion.div,{className:\"mt-6 relative\",initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:2.5},children:/*#__PURE__*/_jsx(motion.div,{className:\"h-2 mx-auto rounded-full relative overflow-hidden\",style:{width:'90%',background:'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',boxShadow:'0 0 30px rgba(59, 130, 246, 0.5)'},animate:{boxShadow:['0 0 30px rgba(59, 130, 246, 0.5)','0 0 50px rgba(16, 185, 129, 0.7)','0 0 40px rgba(245, 158, 11, 0.6)','0 0 30px rgba(59, 130, 246, 0.5)']},transition:{duration:6,repeat:Infinity,ease:\"easeInOut\"},children:/*#__PURE__*/_jsx(motion.div,{className:\"absolute inset-0 rounded-full\",style:{background:'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',width:'40%'},animate:{x:['-100%','250%']},transition:{duration:4,repeat:Infinity,ease:\"easeInOut\",delay:3}})})})]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"hub-subtitle\",children:\"Ready to shine today? \\u2728 Choose your learning path below.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"hub-quote\",children:[/*#__PURE__*/_jsx(FaStar,{style:{color:'#f59e0b',marginRight:'0.5rem'}}),\"\\\"\",inspiringQuotes[currentQuote],\"\\\"\",/*#__PURE__*/_jsx(FaStar,{style:{color:'#f59e0b',marginLeft:'0.5rem'}}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'0.875rem',color:'#6b7280',marginTop:'0.5rem'},children:\"- BrainWave Team\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hub-grid-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"hub-grid\",children:navigationItems.map((item,index)=>{const IconComponent=item.icon;return/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:0.6,delay:index*0.1},className:\"hub-card hover:\".concat(item.hoverColor,\" \").concat(item.color),onClick:()=>navigate(item.path),tabIndex:0,role:\"button\",onKeyDown:e=>{if(e.key==='Enter'||e.key===' '){navigate(item.path);}},style:{cursor:'pointer',touchAction:'manipulation'// Improves touch responsiveness\n},children:[/*#__PURE__*/_jsx(\"div\",{className:\"hub-card-icon\",children:/*#__PURE__*/_jsx(IconComponent,{})}),/*#__PURE__*/_jsx(\"h3\",{className:\"hub-card-title\",children:item.title}),/*#__PURE__*/_jsx(\"p\",{className:\"hub-card-description\",children:item.description})]},item.title);})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:0.8,delay:0.5},className:\"hub-bottom-decoration\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"decoration-content\",children:[/*#__PURE__*/_jsx(FaGraduationCap,{className:\"decoration-icon animate-bounce-gentle\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Your learning journey starts here!\"}),/*#__PURE__*/_jsx(FaRocket,{className:\"decoration-icon animate-bounce-gentle\"})]})})]})]})});};export default Hub;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "navigate", "user", "state", "currentQuote", "setCurrentQuote", "handleLogout", "localStorage", "removeItem", "success", "inspiringQuotes", "interval", "setInterval", "prev", "length", "clearInterval", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "style", "background", "filter", "scale", "rotate", "repeat", "Infinity", "ease", "x", "rotateY", "delay", "span", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "backgroundSize", "fontFamily", "letterSpacing", "textShadow", "backgroundPosition", "whileHover", "rotateZ", "name", "width", "boxShadow", "marginRight", "marginLeft", "fontSize", "marginTop", "map", "item", "index", "IconComponent", "concat", "onClick", "tabIndex", "role", "onKeyDown", "e", "key", "cursor", "touchAction"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n  const inspiringQuotes = [\n    \"Education is the most powerful weapon which you can use to change the world. - <PERSON>\",\n    \"The future belongs to those who believe in the beauty of their dreams. - <PERSON>\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON> Churchill\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const navigationItems = [\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: 'Reports',\n      description: 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage your account',\n      icon: FaUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      hoverColor: 'from-indigo-600 to-indigo-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade your learning',\n      icon: FaCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600',\n      hoverColor: 'from-emerald-600 to-emerald-700'\n    },\n    {\n      title: 'About Us',\n      description: 'Learn about our mission',\n      icon: FaInfoCircle,\n      path: '/user/about-us',\n      color: 'from-cyan-500 to-cyan-600',\n      hoverColor: 'from-cyan-600 to-cyan-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      <div className=\"hub-content\">\n\n\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"hub-header\"\n        >\n\n\n          {/* Dynamic Inspiring Study Smarter Animation */}\n          <div className=\"hub-welcome relative overflow-hidden min-h-[200px]\">\n\n\n            {/* Dynamic Background Waves */}\n            <motion.div\n              className=\"absolute inset-0 rounded-3xl\"\n              style={{\n                background: 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',\n                filter: 'blur(30px)'\n              }}\n              animate={{\n                scale: [1, 1.1, 1],\n                opacity: [0.3, 0.6, 0.3],\n                rotate: [0, 2, -2, 0]\n              }}\n              transition={{\n                duration: 8,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            />\n\n            {/* Main Content */}\n            <motion.div\n              className=\"relative z-10 text-center\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 1.5 }}\n            >\n              {/* Study Text with Motion */}\n              <motion.div\n                className=\"relative inline-block mr-6\"\n                initial={{ x: -200, opacity: 0, rotateY: -90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 0.5,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(59, 130, 246, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, -5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(59, 130, 246, 0.4)',\n                      '0 0 80px rgba(59, 130, 246, 0.7)',\n                      '0 0 50px rgba(59, 130, 246, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 6, repeat: Infinity, ease: \"easeInOut\" },\n                    y: { duration: 3, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, 2, -2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Study\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* Smarter Text with Motion */}\n              <motion.div\n                className=\"relative inline-block\"\n                initial={{ x: 200, opacity: 0, rotateY: 90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 1,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(16, 185, 129, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, 5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(16, 185, 129, 0.4)',\n                      '0 0 80px rgba(16, 185, 129, 0.7)',\n                      '0 0 50px rgba(16, 185, 129, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 5, repeat: Infinity, ease: \"easeInOut\", delay: 1 },\n                    y: { duration: 3.5, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 },\n                    textShadow: { duration: 4.5, repeat: Infinity, ease: \"easeInOut\", delay: 1 }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, -2, 2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Smarter\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* User Name with Inspiring Animation */}\n              <motion.div\n                className=\"mt-8 relative\"\n                initial={{ opacity: 0, y: 50, scale: 0.8 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                transition={{ duration: 1.5, delay: 2 }}\n              >\n                <motion.span\n                  className=\"text-3xl sm:text-4xl font-bold block\"\n                  style={{\n                    background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    textShadow: '0 0 30px rgba(245, 158, 11, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    scale: [1, 1.02, 1],\n                    textShadow: [\n                      '0 0 30px rgba(245, 158, 11, 0.4)',\n                      '0 0 50px rgba(245, 158, 11, 0.7)',\n                      '0 0 30px rgba(245, 158, 11, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                    scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 3, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.1,\n                    rotate: [0, 3, -3, 0],\n                    transition: { duration: 0.4 }\n                  }}\n                >\n                  {user?.name}!\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* Dynamic Inspiring Underline */}\n              <motion.div\n                className=\"mt-6 relative\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 1, delay: 2.5 }}\n              >\n                <motion.div\n                  className=\"h-2 mx-auto rounded-full relative overflow-hidden\"\n                  style={{\n                    width: '90%',\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',\n                    boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)'\n                  }}\n                  animate={{\n                    boxShadow: [\n                      '0 0 30px rgba(59, 130, 246, 0.5)',\n                      '0 0 50px rgba(16, 185, 129, 0.7)',\n                      '0 0 40px rgba(245, 158, 11, 0.6)',\n                      '0 0 30px rgba(59, 130, 246, 0.5)'\n                    ]\n                  }}\n                  transition={{\n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                >\n                  {/* Moving Light Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 rounded-full\"\n                    style={{\n                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',\n                      width: '40%'\n                    }}\n                    animate={{\n                      x: ['-100%', '250%']\n                    }}\n                    transition={{\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 3\n                    }}\n                  />\n                </motion.div>\n              </motion.div>\n            </motion.div>\n          </div>\n\n          <p className=\"hub-subtitle\">\n            Ready to shine today? ✨ Choose your learning path below.\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color}`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.description}\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,OAAO,KAAQ,MAAM,CAC9B,MAAO,WAAW,CAClB,OACEC,MAAM,CACNC,gBAAgB,CAChBC,MAAM,CACNC,WAAW,CACXC,MAAM,CACNC,UAAU,CACVC,YAAY,CACZC,YAAY,CACZC,eAAe,CACfC,QAAQ,CACRC,MAAM,CACNC,QAAQ,CACRC,OAAO,CACPC,YAAY,KACP,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAExB,KAAM,CAAAC,GAAG,CAAGA,CAAA,GAAM,CAChB,KAAM,CAAAC,QAAQ,CAAGvB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEwB,IAAK,CAAC,CAAGvB,WAAW,CAAEwB,KAAK,EAAKA,KAAK,CAACD,IAAI,CAAC,CACnD,KAAM,CAACE,YAAY,CAAEC,eAAe,CAAC,CAAG7B,QAAQ,CAAC,CAAC,CAAC,CAEnD;AACA,KAAM,CAAA8B,YAAY,CAAGA,CAAA,GAAM,CACzB;AACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC,CAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC,CAE/B;AACA3B,OAAO,CAAC4B,OAAO,CAAC,0BAA0B,CAAC,CAE3C;AACAR,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED,KAAM,CAAAS,eAAe,CAAG,CACtB,+FAA+F,CAC/F,4FAA4F,CAC5F,4GAA4G,CAC5G,6CAA6C,CAC7C,6CAA6C,CAC7C,2BAA2B,CAC5B,CAEDjC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkC,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCP,eAAe,CAAEQ,IAAI,EAAK,CAACA,IAAI,CAAG,CAAC,EAAIH,eAAe,CAACI,MAAM,CAAC,CAChE,CAAC,CAAE,IAAI,CAAC,CACR,MAAO,IAAMC,aAAa,CAACJ,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAK,eAAe,CAAG,CACtB,CACEC,KAAK,CAAE,WAAW,CAClBC,WAAW,CAAE,qBAAqB,CAClCC,IAAI,CAAEpC,gBAAgB,CACtBqC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,2BAA2B,CAClCC,UAAU,CAAE,2BACd,CAAC,CACD,CACEL,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,uBAAuB,CACpCC,IAAI,CAAEnC,MAAM,CACZoC,IAAI,CAAE,sBAAsB,CAC5BC,KAAK,CAAE,+BAA+B,CACtCC,UAAU,CAAE,+BACd,CAAC,CACD,CACEL,KAAK,CAAE,SAAS,CAChBC,WAAW,CAAE,qBAAqB,CAClCC,IAAI,CAAElC,WAAW,CACjBmC,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,6BAA6B,CACpCC,UAAU,CAAE,6BACd,CAAC,CACD,CACEL,KAAK,CAAE,SAAS,CAChBC,WAAW,CAAE,mBAAmB,CAChCC,IAAI,CAAE5B,QAAQ,CACd6B,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,+BAA+B,CACtCC,UAAU,CAAE,+BACd,CAAC,CACD,CACEL,KAAK,CAAE,SAAS,CAChBC,WAAW,CAAE,qBAAqB,CAClCC,IAAI,CAAEjC,MAAM,CACZkC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,+BAA+B,CACtCC,UAAU,CAAE,+BACd,CAAC,CACD,CACEL,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,oBAAoB,CACjCC,IAAI,CAAEhC,UAAU,CAChBiC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,2BAA2B,CAClCC,UAAU,CAAE,2BACd,CAAC,CACD,CACEL,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,uBAAuB,CACpCC,IAAI,CAAE/B,YAAY,CAClBgC,IAAI,CAAE,aAAa,CACnBC,KAAK,CAAE,iCAAiC,CACxCC,UAAU,CAAE,iCACd,CAAC,CACD,CACEL,KAAK,CAAE,UAAU,CACjBC,WAAW,CAAE,yBAAyB,CACtCC,IAAI,CAAE9B,YAAY,CAClB+B,IAAI,CAAE,gBAAgB,CACtBC,KAAK,CAAE,2BAA2B,CAClCC,UAAU,CAAE,2BACd,CAAC,CACF,CAED,mBACEzB,IAAA,QAAK0B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BzB,KAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,eAG1BzB,KAAA,CAACnB,MAAM,CAAC6C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BR,SAAS,CAAC,YAAY,CAAAC,QAAA,eAKtBzB,KAAA,QAAKwB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eAIjE3B,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,8BAA8B,CACxCS,KAAK,CAAE,CACLC,UAAU,CAAE,+GAA+G,CAC3HC,MAAM,CAAE,YACV,CAAE,CACFL,OAAO,CAAE,CACPM,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAClBR,OAAO,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACxBS,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CACtB,CAAE,CACFN,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXM,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAE,CACH,CAAC,cAGFxC,KAAA,CAACnB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,2BAA2B,CACrCG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAP,QAAA,eAG9B3B,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,4BAA4B,CACtCG,OAAO,CAAE,CAAEc,CAAC,CAAE,CAAC,GAAG,CAAEb,OAAO,CAAE,CAAC,CAAEc,OAAO,CAAE,CAAC,EAAG,CAAE,CAC/CZ,OAAO,CAAE,CACPW,CAAC,CAAE,CAAC,CACJb,OAAO,CAAE,CAAC,CACVc,OAAO,CAAE,CACX,CAAE,CACFX,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbW,KAAK,CAAE,GAAG,CACVH,IAAI,CAAE,SACR,CAAE,CAAAf,QAAA,cAEF3B,IAAA,CAACjB,MAAM,CAAC+D,IAAI,EACVpB,SAAS,CAAC,kEAAkE,CAC5ES,KAAK,CAAE,CACLC,UAAU,CAAE,6EAA6E,CACzFW,oBAAoB,CAAE,MAAM,CAC5BC,mBAAmB,CAAE,aAAa,CAClCC,cAAc,CAAE,MAAM,CACtBC,cAAc,CAAE,WAAW,CAC3BC,UAAU,CAAE,kDAAkD,CAC9DC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,kCACd,CAAE,CACFrB,OAAO,CAAE,CACPsB,kBAAkB,CAAE,CAAC,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CACpDvB,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CACbsB,UAAU,CAAE,CACV,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAEtC,CAAE,CACFpB,UAAU,CAAE,CACVqB,kBAAkB,CAAE,CAAEpB,QAAQ,CAAE,CAAC,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAY,CAAC,CACxEX,CAAC,CAAE,CAAEG,QAAQ,CAAE,CAAC,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAY,CAAC,CACvDW,UAAU,CAAE,CAAEnB,QAAQ,CAAE,CAAC,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAY,CACjE,CAAE,CACFa,UAAU,CAAE,CACVjB,KAAK,CAAE,IAAI,CACXkB,OAAO,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CACtBvB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CAAE,CAAAP,QAAA,CACH,OAED,CAAa,CAAC,CAGJ,CAAC,cAGb3B,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,uBAAuB,CACjCG,OAAO,CAAE,CAAEc,CAAC,CAAE,GAAG,CAAEb,OAAO,CAAE,CAAC,CAAEc,OAAO,CAAE,EAAG,CAAE,CAC7CZ,OAAO,CAAE,CACPW,CAAC,CAAE,CAAC,CACJb,OAAO,CAAE,CAAC,CACVc,OAAO,CAAE,CACX,CAAE,CACFX,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbW,KAAK,CAAE,CAAC,CACRH,IAAI,CAAE,SACR,CAAE,CAAAf,QAAA,cAEF3B,IAAA,CAACjB,MAAM,CAAC+D,IAAI,EACVpB,SAAS,CAAC,kEAAkE,CAC5ES,KAAK,CAAE,CACLC,UAAU,CAAE,6EAA6E,CACzFW,oBAAoB,CAAE,MAAM,CAC5BC,mBAAmB,CAAE,aAAa,CAClCC,cAAc,CAAE,MAAM,CACtBC,cAAc,CAAE,WAAW,CAC3BC,UAAU,CAAE,kDAAkD,CAC9DC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,kCACd,CAAE,CACFrB,OAAO,CAAE,CACPsB,kBAAkB,CAAE,CAAC,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CACpDvB,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACZsB,UAAU,CAAE,CACV,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAEtC,CAAE,CACFpB,UAAU,CAAE,CACVqB,kBAAkB,CAAE,CAAEpB,QAAQ,CAAE,CAAC,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAW,CAAEG,KAAK,CAAE,CAAE,CAAC,CAClFd,CAAC,CAAE,CAAEG,QAAQ,CAAE,GAAG,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAW,CAAEG,KAAK,CAAE,GAAI,CAAC,CACrEQ,UAAU,CAAE,CAAEnB,QAAQ,CAAE,GAAG,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAW,CAAEG,KAAK,CAAE,CAAE,CAC7E,CAAE,CACFU,UAAU,CAAE,CACVjB,KAAK,CAAE,IAAI,CACXkB,OAAO,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtBvB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CAAE,CAAAP,QAAA,CACH,SAED,CAAa,CAAC,CAGJ,CAAC,cAGb3B,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,eAAe,CACzBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAE,CAAEO,KAAK,CAAE,GAAI,CAAE,CAC3CN,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEO,KAAK,CAAE,CAAE,CAAE,CACxCL,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEW,KAAK,CAAE,CAAE,CAAE,CAAAlB,QAAA,cAExCzB,KAAA,CAACnB,MAAM,CAAC+D,IAAI,EACVpB,SAAS,CAAC,sCAAsC,CAChDS,KAAK,CAAE,CACLC,UAAU,CAAE,4DAA4D,CACxEW,oBAAoB,CAAE,MAAM,CAC5BC,mBAAmB,CAAE,aAAa,CAClCC,cAAc,CAAE,MAAM,CACtBC,cAAc,CAAE,WAAW,CAC3BG,UAAU,CAAE,kCACd,CAAE,CACFrB,OAAO,CAAE,CACPsB,kBAAkB,CAAE,CAAC,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CACpDhB,KAAK,CAAE,CAAC,CAAC,CAAE,IAAI,CAAE,CAAC,CAAC,CACnBe,UAAU,CAAE,CACV,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAEtC,CAAE,CACFpB,UAAU,CAAE,CACVqB,kBAAkB,CAAE,CAAEpB,QAAQ,CAAE,CAAC,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAY,CAAC,CACxEJ,KAAK,CAAE,CAAEJ,QAAQ,CAAE,CAAC,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAY,CAAC,CAC3DW,UAAU,CAAE,CAAEnB,QAAQ,CAAE,CAAC,CAAEM,MAAM,CAAEC,QAAQ,CAAEC,IAAI,CAAE,WAAY,CACjE,CAAE,CACFa,UAAU,CAAE,CACVjB,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBN,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CAAE,CAAAP,QAAA,EAEDtB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoD,IAAI,CAAC,GACd,EAAa,CAAC,CAGJ,CAAC,cAGbzD,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,eAAe,CACzBG,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,UAAU,CAAE,CAAEC,QAAQ,CAAE,CAAC,CAAEW,KAAK,CAAE,GAAI,CAAE,CAAAlB,QAAA,cAExC3B,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,mDAAmD,CAC7DS,KAAK,CAAE,CACLuB,KAAK,CAAE,KAAK,CACZtB,UAAU,CAAE,qEAAqE,CACjFuB,SAAS,CAAE,kCACb,CAAE,CACF3B,OAAO,CAAE,CACP2B,SAAS,CAAE,CACT,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAEtC,CAAE,CACF1B,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXM,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAE,CAAAf,QAAA,cAGF3B,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTF,SAAS,CAAC,+BAA+B,CACzCS,KAAK,CAAE,CACLC,UAAU,CAAE,yEAAyE,CACrFsB,KAAK,CAAE,KACT,CAAE,CACF1B,OAAO,CAAE,CACPW,CAAC,CAAE,CAAC,OAAO,CAAE,MAAM,CACrB,CAAE,CACFV,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXM,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WAAW,CACjBG,KAAK,CAAE,CACT,CAAE,CACH,CAAC,CACQ,CAAC,CACH,CAAC,EACH,CAAC,EACV,CAAC,cAEN7C,IAAA,MAAG0B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,+DAE5B,CAAG,CAAC,cAEJzB,KAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3B,IAAA,CAACL,MAAM,EAACwC,KAAK,CAAE,CAAEX,KAAK,CAAE,SAAS,CAAEoC,WAAW,CAAE,QAAS,CAAE,CAAE,CAAC,KAC7D,CAAC/C,eAAe,CAACN,YAAY,CAAC,CAAC,IAChC,cAAAP,IAAA,CAACL,MAAM,EAACwC,KAAK,CAAE,CAAEX,KAAK,CAAE,SAAS,CAAEqC,UAAU,CAAE,QAAS,CAAE,CAAE,CAAC,cAC7D7D,IAAA,QAAKmC,KAAK,CAAE,CAAE2B,QAAQ,CAAE,UAAU,CAAEtC,KAAK,CAAE,SAAS,CAAEuC,SAAS,CAAE,QAAS,CAAE,CAAApC,QAAA,CAAC,kBAE7E,CAAK,CAAC,EACH,CAAC,EACI,CAAC,cAEbzB,KAAA,QAAKwB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC3B,IAAA,QAAK0B,SAAS,CAAC,UAAU,CAAAC,QAAA,CACtBR,eAAe,CAAC6C,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACpC,KAAM,CAAAC,aAAa,CAAGF,IAAI,CAAC3C,IAAI,CAC/B,mBACEpB,KAAA,CAACnB,MAAM,CAAC6C,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEW,KAAK,CAAEqB,KAAK,CAAG,GAAI,CAAE,CAClDxC,SAAS,mBAAA0C,MAAA,CAAoBH,IAAI,CAACxC,UAAU,MAAA2C,MAAA,CAAIH,IAAI,CAACzC,KAAK,CAAG,CAC7D6C,OAAO,CAAEA,CAAA,GAAMjE,QAAQ,CAAC6D,IAAI,CAAC1C,IAAI,CAAE,CACnC+C,QAAQ,CAAE,CAAE,CACZC,IAAI,CAAC,QAAQ,CACbC,SAAS,CAAGC,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,EAAID,CAAC,CAACC,GAAG,GAAK,GAAG,CAAE,CACtCtE,QAAQ,CAAC6D,IAAI,CAAC1C,IAAI,CAAC,CACrB,CACF,CAAE,CACFY,KAAK,CAAE,CACLwC,MAAM,CAAE,SAAS,CACjBC,WAAW,CAAE,cAAgB;AAC/B,CAAE,CAAAjD,QAAA,eAEF3B,IAAA,QAAK0B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B3B,IAAA,CAACmE,aAAa,GAAE,CAAC,CACd,CAAC,cAENnE,IAAA,OAAI0B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC3BsC,IAAI,CAAC7C,KAAK,CACT,CAAC,cAELpB,IAAA,MAAG0B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAChCsC,IAAI,CAAC5C,WAAW,CAChB,CAAC,GA5BC4C,IAAI,CAAC7C,KA6BA,CAAC,CAEjB,CAAC,CAAC,CACC,CAAC,cAENpB,IAAA,CAACjB,MAAM,CAAC6C,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CACxBE,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAE,CAAE,CACxBG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEW,KAAK,CAAE,GAAI,CAAE,CAC1CnB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cAEjCzB,KAAA,QAAKwB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACjC3B,IAAA,CAACP,eAAe,EAACiC,SAAS,CAAC,uCAAuC,CAAE,CAAC,cACrE1B,IAAA,SAAA2B,QAAA,CAAM,oCAAkC,CAAM,CAAC,cAC/C3B,IAAA,CAACJ,QAAQ,EAAC8B,SAAS,CAAC,uCAAuC,CAAE,CAAC,EAC3D,CAAC,CACI,CAAC,EACV,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}