{"ast": null, "code": "import React,{Suspense,lazy}from\"react\";import\"./stylesheets/theme.css\";import\"./stylesheets/alignments.css\";import\"./stylesheets/textelements.css\";import\"./stylesheets/form-elements.css\";import\"./stylesheets/custom-components.css\";import\"./stylesheets/layout.css\";import\"./styles/modern.css\";import\"./styles/animations.css\";import{BrowserRouter,Routes,Route}from\"react-router-dom\";import ProtectedRoute from\"./components/ProtectedRoute\";import Loader from\"./components/Loader\";import{useSelector}from\"react-redux\";import{ThemeProvider}from\"./contexts/ThemeContext\";import{ErrorBoundary}from\"./components/modern\";import AdminProtectedRoute from\"./components/AdminProtectedRoute\";// Immediate load components (critical for initial render)\nimport Login from\"./pages/common/Login\";import Register from\"./pages/common/Register\";import Home from\"./pages/common/Home\";// Lazy load components for better performance\nimport{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const Quiz=/*#__PURE__*/lazy(()=>import(\"./pages/user/Quiz\"));const QuizPlay=/*#__PURE__*/lazy(()=>import(\"./pages/user/Quiz/QuizPlay\"));const QuizResult=/*#__PURE__*/lazy(()=>import(\"./pages/user/Quiz/QuizResult\"));const Exams=/*#__PURE__*/lazy(()=>import(\"./pages/admin/Exams\"));const AddEditExam=/*#__PURE__*/lazy(()=>import(\"./pages/admin/Exams/AddEditExam\"));const Users=/*#__PURE__*/lazy(()=>import(\"./pages/admin/Users\"));const AdminDashboard=/*#__PURE__*/lazy(()=>import(\"./pages/admin/Dashboard\"));const TrialPage=/*#__PURE__*/lazy(()=>import(\"./pages/trial/TrialPage\"));const WriteExam=/*#__PURE__*/lazy(()=>import(\"./pages/user/WriteExam\"));const UserReports=/*#__PURE__*/lazy(()=>import(\"./pages/user/UserReports\"));const AdminReports=/*#__PURE__*/lazy(()=>import(\"./pages/admin/AdminReports\"));const StudyMaterial=/*#__PURE__*/lazy(()=>import(\"./pages/user/StudyMaterial\"));const Ranking=/*#__PURE__*/lazy(()=>import(\"./pages/user/Ranking\"));const RankingErrorBoundary=/*#__PURE__*/lazy(()=>import(\"./components/RankingErrorBoundary\"));const Profile=/*#__PURE__*/lazy(()=>import(\"./pages/common/Profile\"));const AboutUs=/*#__PURE__*/lazy(()=>import(\"./pages/user/AboutUs\"));const Forum=/*#__PURE__*/lazy(()=>import(\"./pages/common/Forum\"));const Test=/*#__PURE__*/lazy(()=>import(\"./pages/user/Test\"));const Plans=/*#__PURE__*/lazy(()=>import(\"./pages/user/Plans/Plans\"));const Hub=/*#__PURE__*/lazy(()=>import(\"./pages/user/Hub\"));const AdminStudyMaterials=/*#__PURE__*/lazy(()=>import(\"./pages/admin/StudyMaterials\"));const AdminNotifications=/*#__PURE__*/lazy(()=>import(\"./pages/admin/Notifications/AdminNotifications\"));const AdminForum=/*#__PURE__*/lazy(()=>import(\"./pages/admin/Forum\"));const DebugAuth=/*#__PURE__*/lazy(()=>import(\"./components/DebugAuth\"));const RankingDemo=/*#__PURE__*/lazy(()=>import(\"./components/modern/RankingDemo\"));// Global error handler for CSS style errors\nwindow.addEventListener('error',event=>{if(event.message&&event.message.includes('Indexed property setter is not supported')){console.warn('CSS Style Error caught and handled:',event.message);event.preventDefault();return false;}});// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection',event=>{if(event.reason&&event.reason.message&&event.reason.message.includes('Indexed property setter is not supported')){console.warn('CSS Style Promise Rejection caught and handled:',event.reason.message);event.preventDefault();}});// Fast loading component for lazy routes\nconst FastLoader=()=>/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 font-medium\",children:\"Loading page...\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm mt-2\",children:\"Please wait a moment\"})]})});function App(){const{loading}=useSelector(state=>state.loader);return/*#__PURE__*/_jsx(ErrorBoundary,{children:/*#__PURE__*/_jsxs(ThemeProvider,{children:[loading&&/*#__PURE__*/_jsx(Loader,{}),/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(Register,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Home,{})}),/*#__PURE__*/_jsx(Route,{path:\"/ranking-demo\",element:/*#__PURE__*/_jsx(RankingDemo,{})}),/*#__PURE__*/_jsx(Route,{path:\"/trial\",element:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(TrialPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/test\",element:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(Test,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/forum\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(Forum,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(Profile,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(Profile,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/plans\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Plans,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/hub\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(Hub,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/quiz\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(Quiz,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/write-exam/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(WriteExam,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/quiz/:id/result\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(QuizResult,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/quiz/:id/play\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(QuizPlay,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/reports\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(UserReports,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/study-material\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(StudyMaterial,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/ranking\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(FastLoader,{}),children:/*#__PURE__*/_jsx(RankingErrorBoundary,{children:/*#__PURE__*/_jsx(Ranking,{})})})})}),/*#__PURE__*/_jsx(Route,{path:\"/user/about-us\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AboutUs,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AdminDashboard,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/users\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(Users,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/exams\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(Exams,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/exams/add\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AddEditExam,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/exams/edit/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AddEditExam,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/study-materials\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AdminStudyMaterials,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/reports\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AdminReports,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/notifications\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AdminNotifications,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/forum\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(AdminForum,{})})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/debug\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(AdminProtectedRoute,{children:/*#__PURE__*/_jsx(DebugAuth,{})})})})]})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "ProtectedRoute", "Loader", "useSelector", "ThemeProvider", "Error<PERSON>ou<PERSON><PERSON>", "AdminProtectedRoute", "<PERSON><PERSON>", "Register", "Home", "jsx", "_jsx", "jsxs", "_jsxs", "Quiz", "QuizPlay", "QuizResult", "<PERSON><PERSON>", "AddEditExam", "Users", "AdminDashboard", "TrialPage", "WriteExam", "UserReports", "AdminReports", "StudyMaterial", "Ranking", "RankingError<PERSON><PERSON><PERSON>ry", "Profile", "AboutUs", "Forum", "Test", "Plans", "<PERSON><PERSON>", "AdminStudyMaterials", "AdminNotifications", "AdminForum", "DebugAuth", "RankingDemo", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "FastLoader", "className", "children", "App", "loading", "state", "loader", "path", "element", "fallback"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense, lazy } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\r\n\r\n// Immediate load components (critical for initial render)\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport Home from \"./pages/common/Home\";\r\n\r\n// Lazy load components for better performance\r\nconst Quiz = lazy(() => import(\"./pages/user/Quiz\"));\r\nconst QuizPlay = lazy(() => import(\"./pages/user/Quiz/QuizPlay\"));\r\nconst QuizResult = lazy(() => import(\"./pages/user/Quiz/QuizResult\"));\r\nconst Exams = lazy(() => import(\"./pages/admin/Exams\"));\r\nconst AddEditExam = lazy(() => import(\"./pages/admin/Exams/AddEditExam\"));\r\nconst Users = lazy(() => import(\"./pages/admin/Users\"));\r\nconst AdminDashboard = lazy(() => import(\"./pages/admin/Dashboard\"));\r\nconst TrialPage = lazy(() => import(\"./pages/trial/TrialPage\"));\r\nconst WriteExam = lazy(() => import(\"./pages/user/WriteExam\"));\r\nconst UserReports = lazy(() => import(\"./pages/user/UserReports\"));\r\nconst AdminReports = lazy(() => import(\"./pages/admin/AdminReports\"));\r\nconst StudyMaterial = lazy(() => import(\"./pages/user/StudyMaterial\"));\r\nconst Ranking = lazy(() => import(\"./pages/user/Ranking\"));\r\nconst RankingErrorBoundary = lazy(() => import(\"./components/RankingErrorBoundary\"));\r\nconst Profile = lazy(() => import(\"./pages/common/Profile\"));\r\nconst AboutUs = lazy(() => import(\"./pages/user/AboutUs\"));\r\nconst Forum = lazy(() => import(\"./pages/common/Forum\"));\r\nconst Test = lazy(() => import(\"./pages/user/Test\"));\r\nconst Plans = lazy(() => import(\"./pages/user/Plans/Plans\"));\r\nconst Hub = lazy(() => import(\"./pages/user/Hub\"));\r\nconst AdminStudyMaterials = lazy(() => import(\"./pages/admin/StudyMaterials\"));\r\nconst AdminNotifications = lazy(() => import(\"./pages/admin/Notifications/AdminNotifications\"));\r\nconst AdminForum = lazy(() => import(\"./pages/admin/Forum\"));\r\nconst DebugAuth = lazy(() => import(\"./components/DebugAuth\"));\r\nconst RankingDemo = lazy(() => import(\"./components/modern/RankingDemo\"));\r\n\r\n// Global error handler for CSS style errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// Fast loading component for lazy routes\r\nconst FastLoader = () => (\r\n  <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n    <div className=\"text-center\">\r\n      <div className=\"relative\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n        <div className=\"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"></div>\r\n      </div>\r\n      <p className=\"text-gray-600 font-medium\">Loading page...</p>\r\n      <p className=\"text-gray-400 text-sm mt-2\">Please wait a moment</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        {loading && <Loader />}\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n\r\n          {/* Trial Route (No authentication required) */}\r\n          <Route path=\"/trial\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <TrialPage />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Forum />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n\r\n          <Route\r\n            path=\"/user/plans\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Plans />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Hub />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Quiz />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <WriteExam />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <QuizResult />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <UserReports />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <StudyMaterial />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <RankingErrorBoundary>\r\n                    <Ranking />\r\n                  </RankingErrorBoundary>\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/about-us\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AboutUs />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminDashboard />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Users />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Exams />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminStudyMaterials />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminReports />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/notifications\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminNotifications />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminForum />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/debug\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <DebugAuth />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,IAAI,KAAQ,OAAO,CAC7C,MAAO,yBAAyB,CAChC,MAAO,8BAA8B,CACrC,MAAO,gCAAgC,CACvC,MAAO,iCAAiC,CACxC,MAAO,qCAAqC,CAC5C,MAAO,0BAA0B,CACjC,MAAO,qBAAqB,CAC5B,MAAO,yBAAyB,CAChC,OAASC,aAAa,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CAC/D,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,aAAa,KAAQ,yBAAyB,CACvD,OAASC,aAAa,KAAQ,qBAAqB,CACnD,MAAO,CAAAC,mBAAmB,KAAM,kCAAkC,CAElE;AACA,MAAO,CAAAC,KAAK,KAAM,sBAAsB,CACxC,MAAO,CAAAC,QAAQ,KAAM,yBAAyB,CAC9C,MAAO,CAAAC,IAAI,KAAM,qBAAqB,CAEtC;AAAA,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,IAAI,cAAGjB,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CACpD,KAAM,CAAAkB,QAAQ,cAAGlB,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CACjE,KAAM,CAAAmB,UAAU,cAAGnB,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CACrE,KAAM,CAAAoB,KAAK,cAAGpB,IAAI,CAAC,IAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC,CACvD,KAAM,CAAAqB,WAAW,cAAGrB,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CACzE,KAAM,CAAAsB,KAAK,cAAGtB,IAAI,CAAC,IAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC,CACvD,KAAM,CAAAuB,cAAc,cAAGvB,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CACpE,KAAM,CAAAwB,SAAS,cAAGxB,IAAI,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC,CAC/D,KAAM,CAAAyB,SAAS,cAAGzB,IAAI,CAAC,IAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAC9D,KAAM,CAAA0B,WAAW,cAAG1B,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAClE,KAAM,CAAA2B,YAAY,cAAG3B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CACrE,KAAM,CAAA4B,aAAa,cAAG5B,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CACtE,KAAM,CAAA6B,OAAO,cAAG7B,IAAI,CAAC,IAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAC1D,KAAM,CAAA8B,oBAAoB,cAAG9B,IAAI,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC,CACpF,KAAM,CAAA+B,OAAO,cAAG/B,IAAI,CAAC,IAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAC5D,KAAM,CAAAgC,OAAO,cAAGhC,IAAI,CAAC,IAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAC1D,KAAM,CAAAiC,KAAK,cAAGjC,IAAI,CAAC,IAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC,CACxD,KAAM,CAAAkC,IAAI,cAAGlC,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CACpD,KAAM,CAAAmC,KAAK,cAAGnC,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAC5D,KAAM,CAAAoC,GAAG,cAAGpC,IAAI,CAAC,IAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAClD,KAAM,CAAAqC,mBAAmB,cAAGrC,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAC9E,KAAM,CAAAsC,kBAAkB,cAAGtC,IAAI,CAAC,IAAM,MAAM,CAAC,gDAAgD,CAAC,CAAC,CAC/F,KAAM,CAAAuC,UAAU,cAAGvC,IAAI,CAAC,IAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAC5D,KAAM,CAAAwC,SAAS,cAAGxC,IAAI,CAAC,IAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAC9D,KAAM,CAAAyC,WAAW,cAAGzC,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAEzE;AACA0C,MAAM,CAACC,gBAAgB,CAAC,OAAO,CAAGC,KAAK,EAAK,CAC1C,GAAIA,KAAK,CAACC,OAAO,EAAID,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,CAAE,CACvFC,OAAO,CAACC,IAAI,CAAC,qCAAqC,CAAEJ,KAAK,CAACC,OAAO,CAAC,CAClED,KAAK,CAACK,cAAc,CAAC,CAAC,CACtB,MAAO,MAAK,CACd,CACF,CAAC,CAAC,CAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,CAAGC,KAAK,EAAK,CACvD,GAAIA,KAAK,CAACM,MAAM,EAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,EAAID,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,CAAE,CACrHC,OAAO,CAACC,IAAI,CAAC,iDAAiD,CAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC,CACrFD,KAAK,CAACK,cAAc,CAAC,CAAC,CACxB,CACF,CAAC,CAAC,CACF;AACA,KAAM,CAAAE,UAAU,CAAGA,CAAA,gBACjBrC,IAAA,QAAKsC,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACzGrC,KAAA,QAAKoC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BrC,KAAA,QAAKoC,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBvC,IAAA,QAAKsC,SAAS,CAAC,6EAA6E,CAAM,CAAC,cACnGtC,IAAA,QAAKsC,SAAS,CAAC,0FAA0F,CAAM,CAAC,EAC7G,CAAC,cACNtC,IAAA,MAAGsC,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,cAC5DvC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,sBAAoB,CAAG,CAAC,EAC/D,CAAC,CACH,CACN,CAED,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,OAAQ,CAAC,CAAGjD,WAAW,CAAEkD,KAAK,EAAKA,KAAK,CAACC,MAAM,CAAC,CACxD,mBACE3C,IAAA,CAACN,aAAa,EAAA6C,QAAA,cACZrC,KAAA,CAACT,aAAa,EAAA8C,QAAA,EACXE,OAAO,eAAIzC,IAAA,CAACT,MAAM,GAAE,CAAC,cACtBS,IAAA,CAACb,aAAa,EAAAoD,QAAA,cACdrC,KAAA,CAACd,MAAM,EAAAmD,QAAA,eAELvC,IAAA,CAACX,KAAK,EAACuD,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE7C,IAAA,CAACJ,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CI,IAAA,CAACX,KAAK,EAACuD,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE7C,IAAA,CAACH,QAAQ,GAAE,CAAE,CAAE,CAAC,cACjDG,IAAA,CAACX,KAAK,EAACuD,IAAI,CAAC,GAAG,CAACC,OAAO,cAAE7C,IAAA,CAACF,IAAI,GAAE,CAAE,CAAE,CAAC,cACrCE,IAAA,CAACX,KAAK,EAACuD,IAAI,CAAC,eAAe,CAACC,OAAO,cAAE7C,IAAA,CAAC2B,WAAW,GAAE,CAAE,CAAE,CAAC,cAGxD3B,IAAA,CAACX,KAAK,EAACuD,IAAI,CAAC,QAAQ,CAACC,OAAO,cAC1B7C,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACU,SAAS,GAAE,CAAC,CACL,CACX,CAAE,CAAC,cAEJV,IAAA,CAACX,KAAK,EAACuD,IAAI,CAAC,OAAO,CAACC,OAAO,cACzB7C,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACoB,IAAI,GAAE,CAAC,CACA,CACX,CAAE,CAAC,cACJpB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,QAAQ,CACbC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACmB,KAAK,GAAE,CAAC,CACD,CAAC,CACG,CACjB,CACF,CAAC,cAGFnB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,UAAU,CACfC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACiB,OAAO,GAAE,CAAC,CACH,CAAC,CACG,CACjB,CACF,CAAC,cACFjB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,eAAe,CACpBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACiB,OAAO,GAAE,CAAC,CACH,CAAC,CACG,CACjB,CACF,CAAC,cAGFjB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,aAAa,CAClBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACqB,KAAK,GAAE,CAAC,CACK,CACjB,CACF,CAAC,cACFrB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,WAAW,CAChBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACsB,GAAG,GAAE,CAAC,CACC,CAAC,CACG,CACjB,CACF,CAAC,cACFtB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,YAAY,CACjBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACG,IAAI,GAAE,CAAC,CACA,CAAC,CACG,CACjB,CACF,CAAC,cAEFH,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,sBAAsB,CAC3BC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACW,SAAS,GAAE,CAAC,CACL,CAAC,CACG,CACjB,CACF,CAAC,cACFX,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACK,UAAU,GAAE,CAAC,CACN,CAAC,CACG,CACjB,CACF,CAAC,cAIFL,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,gBAAgB,CACrBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACI,QAAQ,GAAE,CAAC,CACE,CACjB,CACF,CAAC,cAEFJ,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,eAAe,CACpBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACY,WAAW,GAAE,CAAC,CACP,CAAC,CACG,CACjB,CACF,CAAC,cACFZ,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,sBAAsB,CAC3BC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACc,aAAa,GAAE,CAAC,CACT,CAAC,CACG,CACjB,CACF,CAAC,cACFd,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,eAAe,CACpBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACf,QAAQ,EAAC6D,QAAQ,cAAE9C,IAAA,CAACqC,UAAU,GAAE,CAAE,CAAAE,QAAA,cACjCvC,IAAA,CAACgB,oBAAoB,EAAAuB,QAAA,cACnBvC,IAAA,CAACe,OAAO,GAAE,CAAC,CACS,CAAC,CACf,CAAC,CACG,CACjB,CACF,CAAC,cACFf,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,gBAAgB,CACrBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACkB,OAAO,GAAE,CAAC,CACG,CACjB,CACF,CAAC,cAGFlB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACS,cAAc,GAAE,CAAC,CACC,CAAC,CACR,CACjB,CACF,CAAC,cACFT,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,cAAc,CACnBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACQ,KAAK,GAAE,CAAC,CACU,CAAC,CACR,CACjB,CACF,CAAC,cACFR,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,cAAc,CACnBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACM,KAAK,GAAE,CAAC,CACU,CAAC,CACR,CACjB,CACF,CAAC,cACFN,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACO,WAAW,GAAE,CAAC,CACI,CAAC,CACR,CACjB,CACF,CAAC,cAEFP,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,uBAAuB,CAC5BC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACO,WAAW,GAAE,CAAC,CACI,CAAC,CACR,CACjB,CACF,CAAC,cAEFP,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,wBAAwB,CAC7BC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACuB,mBAAmB,GAAE,CAAC,CACJ,CAAC,CACR,CACjB,CACF,CAAC,cAEFvB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,gBAAgB,CACrBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACa,YAAY,GAAE,CAAC,CACG,CAAC,CACR,CACjB,CACF,CAAC,cAEFb,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,sBAAsB,CAC3BC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACwB,kBAAkB,GAAE,CAAC,CACH,CAAC,CACR,CACjB,CACF,CAAC,cAEFxB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,cAAc,CACnBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAACyB,UAAU,GAAE,CAAC,CACK,CAAC,CACR,CACjB,CACF,CAAC,cAEFzB,IAAA,CAACX,KAAK,EACJuD,IAAI,CAAC,cAAc,CACnBC,OAAO,cACL7C,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAClBvC,IAAA,CAAC0B,SAAS,GAAE,CAAC,CACM,CAAC,CACR,CACjB,CACF,CAAC,EACI,CAAC,CACI,CAAC,EACH,CAAC,CACD,CAAC,CAEpB,CAEA,cAAe,CAAAc,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}