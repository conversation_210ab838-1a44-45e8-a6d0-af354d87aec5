{"ast": null, "code": "import React from\"react\";import ReactDOM from\"react-dom/client\";import\"./index.css\";import App from\"./App\";import reportWebVitals from\"./reportWebVitals\";import store from\"./redux/store\";import{Provider}from\"react-redux\";import{jsx as _jsx}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById(\"root\"));root.render(/*#__PURE__*/_jsx(Provider,{store:store,children:/*#__PURE__*/_jsx(App,{})}));reportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "reportWebVitals", "store", "Provider", "jsx", "_jsx", "root", "createRoot", "document", "getElementById", "render", "children"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/index.js"], "sourcesContent": ["import React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport reportWebVitals from \"./reportWebVitals\";\nimport store from \"./redux/store\";\nimport { Provider } from \"react-redux\";\n\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render(\n  <Provider store={store}>\n    <App />\n  </Provider>\n);\n\nreportWebVitals();"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,KAAK,KAAM,eAAe,CACjC,OAASC,QAAQ,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEvC,KAAM,CAAAC,IAAI,CAAGP,QAAQ,CAACQ,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC,CACjEH,IAAI,CAACI,MAAM,cACTL,IAAA,CAACF,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAS,QAAA,cACrBN,IAAA,CAACL,GAAG,GAAE,CAAC,CACC,CACZ,CAAC,CAEDC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}