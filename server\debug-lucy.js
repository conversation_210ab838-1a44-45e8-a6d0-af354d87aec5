
        const User = require('./models/userModel');
        const Subscription = require('./models/subscriptionModel');
        
        const findLucy = async () => {
          try {
            const users = await User.find({
              $or: [
                { username: { $regex: 'lucy', $options: 'i' } },
                { name: { $regex: 'lucy', $options: 'i' } },
                { email: { $regex: 'lucy', $options: 'i' } }
              ]
            }).limit(5);
            
            console.log('Found users:', users.map(u => ({
              id: u._id,
              name: u.name,
              username: u.username,
              email: u.email,
              paymentRequired: u.paymentRequired,
              subscriptionStatus: u.subscriptionStatus
            })));
            
            for (const user of users) {
              const subscriptions = await Subscription.find({ user: user._id }).populate('activePlan');
              console.log(`Subscriptions for ${user.name}:`, subscriptions);
            }
            
          } catch (error) {
            console.error('Error:', error.message);
          }
        };
        
        findLucy();
      