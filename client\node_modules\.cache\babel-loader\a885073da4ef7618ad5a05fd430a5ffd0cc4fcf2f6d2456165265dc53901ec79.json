{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Register\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input, Select } from \"antd\";\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\nimport Logo from \"../../../assets/logo.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction Register() {\n  _s();\n  const [verification, setVerification] = useState(false);\n  const [data, setData] = useState(\"\");\n  const [otp, setOTP] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\");\n  const [formValues, setFormValues] = useState({});\n  const navigate = useNavigate();\n\n  // Phone number validation helper\n  const validatePhoneNumber = phone => {\n    const phoneRegex = /^0[67]\\d{8}$/;\n    return phoneRegex.test(phone);\n  };\n  const onFinish = async values => {\n    try {\n      const response = await registerUser(values);\n      if (response.success) {\n        message.success({\n          content: response.message,\n          duration: 6,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        // Add a small delay to let user see the success message\n        setTimeout(() => {\n          navigate(\"/login\");\n        }, 1500);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Registration failed\");\n        setVerification(false);\n      }\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\n      setVerification(false);\n    }\n  };\n  const verifyUser = async values => {\n    var _values$otp;\n    if (!((_values$otp = values.otp) !== null && _values$otp !== void 0 && _values$otp.trim())) {\n      message.error(\"🔢 Please enter the verification code\");\n      return;\n    }\n    if (values.otp.length !== 6) {\n      message.error(\"🔢 Verification code must be 6 digits\");\n      return;\n    }\n    if (values.otp === otp) {\n      message.loading(\"✅ Verifying your code...\", 1);\n      setTimeout(() => {\n        onFinish(data);\n      }, 1000);\n    } else {\n      message.error({\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\n        duration: 5,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n\n  // Enhanced error handling function\n  const showUserFriendlyError = (error, defaultMessage) => {\n    var _error$response, _error$response$data, _error$response2, _error$response2$data;\n    const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || defaultMessage;\n    const errorType = (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.errorType;\n\n    // Show different message styles based on error type\n    if (errorType === \"EMAIL_EXISTS\" || errorType === \"PHONE_EXISTS\") {\n      message.warning({\n        content: errorMessage,\n        duration: 6,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else if (errorType === \"INVALID_EMAIL\" || errorType === \"INVALID_PHONE\") {\n      message.error({\n        content: errorMessage,\n        duration: 5,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else {\n      message.error({\n        content: errorMessage,\n        duration: 4,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n  const generateOTP = async formData => {\n    var _formData$name, _formData$school, _formData$level, _formData$email, _formData$phoneNumber, _formData$password, _formData$confirmPass;\n    console.log(\"🚀 Form submitted with data:\", formData);\n\n    // Enhanced validation with user-friendly messages\n    if (!((_formData$name = formData.name) !== null && _formData$name !== void 0 && _formData$name.trim())) {\n      message.error(\"👤 Please enter your full name\");\n      return;\n    }\n    if (!((_formData$school = formData.school) !== null && _formData$school !== void 0 && _formData$school.trim())) {\n      message.error(\"🏫 Please enter your school name\");\n      return;\n    }\n    if (!((_formData$level = formData.level) !== null && _formData$level !== void 0 && _formData$level.trim())) {\n      message.error(\"🎓 Please select your education level\");\n      return;\n    }\n    if (!formData.class) {\n      message.error(\"📝 Please select your class/form\");\n      return;\n    }\n    if (!((_formData$email = formData.email) !== null && _formData$email !== void 0 && _formData$email.trim())) {\n      message.error(\"📧 Please enter your email address\");\n      return;\n    }\n    if (!((_formData$phoneNumber = formData.phoneNumber) !== null && _formData$phoneNumber !== void 0 && _formData$phoneNumber.trim())) {\n      message.error(\"📱 Please enter your phone number\");\n      return;\n    }\n    if (!((_formData$password = formData.password) !== null && _formData$password !== void 0 && _formData$password.trim())) {\n      message.error(\"🔒 Please create a password\");\n      return;\n    }\n    if (!((_formData$confirmPass = formData.confirmPassword) !== null && _formData$confirmPass !== void 0 && _formData$confirmPass.trim())) {\n      message.error(\"🔒 Please confirm your password\");\n      return;\n    }\n    if (formData.password !== formData.confirmPassword) {\n      message.error(\"🔒 Passwords do not match. Please check and try again.\");\n      return;\n    }\n    console.log(\"✅ All validations passed, proceeding with OTP generation...\");\n    setLoading(true);\n    try {\n      const response = await sendOTP(formData);\n      if (response.success) {\n        message.success({\n          content: response.message,\n          duration: 5,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        setData(formData);\n        setOTP(response.data);\n        setVerification(true);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Failed to send verification code\");\n      }\n    } catch (error) {\n      console.error(\"OTP generation error:\", error);\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"register-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register-card\",\n      children: verification ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Verify Your Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"We've sent a verification code to your email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"otp-instructions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"otp-info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"otp-info-title\",\n              children: \"\\uD83D\\uDCE7 Check Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"otp-info-text\",\n              children: [\"We've sent a \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"6-digit verification code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 32\n              }, this), \" to your email address.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-steps\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Open your email app or website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Look for an email from BrainWave\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Copy the 6-digit code from the email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Enter the code in the box below\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-help\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"help-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCA1 Can't find the email?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), \" Check your spam/junk folder or wait a few minutes for delivery.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: verifyUser,\n          className: \"register-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"otp\",\n            label: \"Verification Code\",\n            rules: [{\n              required: true,\n              message: \"Please enter the OTP!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"number\",\n              className: \"form-input otp-input\",\n              placeholder: \"Enter 6-digit code (e.g., 123456)\",\n              maxLength: 6\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter the verification code from your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"register-btn\",\n            disabled: loading,\n            children: loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resend-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"resend-text\",\n              children: \"Didn't receive the code?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"resend-btn\",\n              onClick: () => generateOTP(data),\n              disabled: loading,\n              children: \"\\uD83D\\uDCE7 Resend Verification Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"Join thousands of students learning with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: generateOTP,\n          className: \"register-form\",\n          validateTrigger: \"onChange\",\n          onValuesChange: (changedValues, allValues) => {\n            console.log(\"📝 Form values changed:\", allValues);\n            setFormValues(allValues);\n          },\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Full Name\",\n            hasFeedback: true,\n            rules: [{\n              required: true,\n              message: \"👤 Please enter your full name\"\n            }, {\n              min: 2,\n              message: \"👤 Name must be at least 2 characters long\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your full name (e.g., John Doe)\",\n              autoComplete: \"name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter your first and last name as you'd like it to appear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"school\",\n            label: \"School Name\",\n            hasFeedback: true,\n            rules: [{\n              required: true,\n              message: \"🏫 Please enter your school name\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your school name (e.g., Dar es Salaam Secondary School)\",\n              autoComplete: \"organization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter the full name of your current school\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Education Level\",\n            hasFeedback: true,\n            rules: [{\n              required: true,\n              message: \"🎓 Please select your education level\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              onChange: value => setSchoolType(value),\n              className: \"form-input\",\n              placeholder: \"Choose your current education level\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"Primary\",\n                children: \"\\uD83C\\uDF92 Primary Education (Classes 1-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Secondary\",\n                children: \"\\uD83D\\uDCDA Secondary Education (Forms 1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Advance\",\n                children: \"\\uD83C\\uDF93 Advanced Level (Forms 5-6)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Select the education level you are currently studying\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class\",\n            label: \"Class/Form\",\n            hasFeedback: true,\n            rules: [{\n              required: true,\n              message: \"📝 Please select your class or form\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              className: \"form-input\",\n              placeholder: schoolType ? \"Select your class/form\" : \"Please select education level first\",\n              disabled: !schoolType,\n              children: [schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: i,\n                children: `📚 Class ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 21\n              }, this)), schoolType === \"Secondary\" && [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `📖 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 21\n              }, this)), schoolType === \"Advance\" && [5, 6].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `🎓 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: [!schoolType && \"Please select your education level first\", schoolType === \"Primary\" && \"Select your current class (1-7)\", schoolType === \"Secondary\" && \"Select your current form (1-4)\", schoolType === \"Advance\" && \"Select your current form (5-6)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email Address\",\n            hasFeedback: true,\n            validateTrigger: ['onChange', 'onBlur'],\n            rules: [{\n              required: true,\n              message: \"📧 Please enter your email!\"\n            }, {\n              type: \"email\",\n              message: \"📧 Please enter a valid email address!\"\n            }, {\n              pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\n              message: \"📧 Please enter a properly formatted email address!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              className: \"form-input\",\n              placeholder: \"Enter your email address (e.g., <EMAIL>)\",\n              autoComplete: \"email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"We'll send important updates to this email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"phoneNumber\",\n            label: \"Phone Number\",\n            hasFeedback: true,\n            validateTrigger: ['onChange', 'onBlur'],\n            rules: [{\n              required: true,\n              message: \"📱 Please enter your phone number\"\n            }, {\n              validator: (_, value) => {\n                if (!value) return Promise.resolve();\n                if (validatePhoneNumber(value)) {\n                  return Promise.resolve();\n                }\n                return Promise.reject(new Error(\"📱 Phone number must start with 06 or 07 and be 10 digits (e.g., 0712345678)\"));\n              }\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"tel\",\n              className: \"form-input\",\n              placeholder: \"Enter mobile number (e.g., 0712345678 or 0612345678)\",\n              autoComplete: \"tel\",\n              maxLength: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-help-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"\\uD83D\\uDCB3 Enter your mobile number for payment processing (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: [\"\\uD83D\\uDCDE Must start with \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"06\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 68\n                }, this), \" or \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"07\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 91\n                }, this), \" and be exactly 10 digits\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"\\uD83D\\uDCE7 Note: Email verification code will be sent to your email, not phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            hasFeedback: true,\n            validateTrigger: ['onChange', 'onBlur'],\n            rules: [{\n              required: true,\n              message: \"🔒 Please enter your password!\"\n            }, {\n              min: 8,\n              message: \"🔒 Password must be at least 8 characters long!\"\n            }, {\n              pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n              message: \"🔒 Password must contain uppercase, lowercase, number and special character!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Create a strong password (min 8 characters)\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Must include: uppercase, lowercase, number, and special character\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"confirmPassword\",\n            label: \"Retype Password\",\n            hasFeedback: true,\n            validateTrigger: ['onChange', 'onBlur'],\n            dependencies: ['password'],\n            rules: [{\n              required: true,\n              message: \"🔒 Please retype your password!\"\n            }, ({\n              getFieldValue\n            }) => ({\n              validator(_, value) {\n                if (!value || getFieldValue('password') === value) {\n                  return Promise.resolve();\n                }\n                return Promise.reject(new Error('🔒 The two passwords do not match!'));\n              }\n            })],\n            children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Retype your password to confirm\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Must match the password above\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"register-btn\",\n              disabled: loading,\n              onClick: () => console.log(\"🔘 Create Account button clicked\"),\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this), \"Creating Account...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: \"\\uD83D\\uDE80 Create Account\"\n              }, void 0, false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account? \", \" \", /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"register-link\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"H+m0g5I8/zutYGktBX3nS86QIpo=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["Form", "message", "Input", "Select", "React", "useState", "Link", "useNavigate", "registerUser", "sendOTP", "Logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "Register", "_s", "verification", "setVerification", "data", "setData", "otp", "setOTP", "loading", "setLoading", "schoolType", "setSchoolType", "formValues", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "validatePhoneNumber", "phone", "phoneRegex", "test", "onFinish", "values", "response", "success", "content", "duration", "style", "marginTop", "setTimeout", "showUserFriendlyError", "error", "console", "verifyUser", "_values$otp", "trim", "length", "defaultMessage", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errorMessage", "errorType", "warning", "generateOTP", "formData", "_formData$name", "_formData$school", "_formData$level", "_formData$email", "_formData$phoneNumber", "_formData$password", "_formData$confirmPass", "log", "name", "school", "level", "class", "email", "phoneNumber", "password", "confirmPassword", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "label", "rules", "required", "type", "placeholder", "max<PERSON><PERSON><PERSON>", "disabled", "onClick", "validate<PERSON><PERSON>ger", "onValuesChange", "changedValues", "allValues", "hasFeedback", "min", "autoComplete", "onChange", "value", "map", "i", "pattern", "validator", "_", "Promise", "resolve", "reject", "Error", "Password", "dependencies", "getFieldValue", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Register/index.js"], "sourcesContent": ["import { Form, message, Input, Select } from \"antd\";\r\nimport React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\r\nimport Logo from \"../../../assets/logo.png\";\r\n\r\nconst { Option } = Select;\r\n\r\nfunction Register() {\r\n  const [verification, setVerification] = useState(false);\r\n  const [data, setData] = useState(\"\");\r\n  const [otp, setOTP] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [schoolType, setSchoolType] = useState(\"\");\r\n  const [formValues, setFormValues] = useState({});\r\n  const navigate = useNavigate();\r\n\r\n  // Phone number validation helper\r\n  const validatePhoneNumber = (phone) => {\r\n    const phoneRegex = /^0[67]\\d{8}$/;\r\n    return phoneRegex.test(phone);\r\n  };\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      const response = await registerUser(values);\r\n      if (response.success) {\r\n        message.success({\r\n          content: response.message,\r\n          duration: 6,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        // Add a small delay to let user see the success message\r\n        setTimeout(() => {\r\n          navigate(\"/login\");\r\n        }, 1500);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Registration failed\");\r\n        setVerification(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Registration error:\", error);\r\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\r\n      setVerification(false);\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (!values.otp?.trim()) {\r\n      message.error(\"🔢 Please enter the verification code\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp.length !== 6) {\r\n      message.error(\"🔢 Verification code must be 6 digits\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp === otp) {\r\n      message.loading(\"✅ Verifying your code...\", 1);\r\n      setTimeout(() => {\r\n        onFinish(data);\r\n      }, 1000);\r\n    } else {\r\n      message.error({\r\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\r\n        duration: 5,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  // Enhanced error handling function\r\n  const showUserFriendlyError = (error, defaultMessage) => {\r\n    const errorMessage = error.response?.data?.message || error.message || defaultMessage;\r\n    const errorType = error.response?.data?.errorType;\r\n\r\n    // Show different message styles based on error type\r\n    if (errorType === \"EMAIL_EXISTS\" || errorType === \"PHONE_EXISTS\") {\r\n      message.warning({\r\n        content: errorMessage,\r\n        duration: 6,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else if (errorType === \"INVALID_EMAIL\" || errorType === \"INVALID_PHONE\") {\r\n      message.error({\r\n        content: errorMessage,\r\n        duration: 5,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else {\r\n      message.error({\r\n        content: errorMessage,\r\n        duration: 4,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  const generateOTP = async (formData) => {\r\n    console.log(\"🚀 Form submitted with data:\", formData);\r\n\r\n    // Enhanced validation with user-friendly messages\r\n    if (!formData.name?.trim()) {\r\n      message.error(\"👤 Please enter your full name\");\r\n      return;\r\n    }\r\n    if (!formData.school?.trim()) {\r\n      message.error(\"🏫 Please enter your school name\");\r\n      return;\r\n    }\r\n    if (!formData.level?.trim()) {\r\n      message.error(\"🎓 Please select your education level\");\r\n      return;\r\n    }\r\n    if (!formData.class) {\r\n      message.error(\"📝 Please select your class/form\");\r\n      return;\r\n    }\r\n    if (!formData.email?.trim()) {\r\n      message.error(\"📧 Please enter your email address\");\r\n      return;\r\n    }\r\n    if (!formData.phoneNumber?.trim()) {\r\n      message.error(\"📱 Please enter your phone number\");\r\n      return;\r\n    }\r\n    if (!formData.password?.trim()) {\r\n      message.error(\"🔒 Please create a password\");\r\n      return;\r\n    }\r\n    if (!formData.confirmPassword?.trim()) {\r\n      message.error(\"🔒 Please confirm your password\");\r\n      return;\r\n    }\r\n    if (formData.password !== formData.confirmPassword) {\r\n      message.error(\"🔒 Passwords do not match. Please check and try again.\");\r\n      return;\r\n    }\r\n\r\n    console.log(\"✅ All validations passed, proceeding with OTP generation...\");\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await sendOTP(formData);\r\n      if (response.success) {\r\n        message.success({\r\n          content: response.message,\r\n          duration: 5,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        setData(formData);\r\n        setOTP(response.data);\r\n        setVerification(true);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Failed to send verification code\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"OTP generation error:\", error);\r\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"register-container\">\r\n      <div className=\"register-card\">\r\n        {verification ? (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Verify Your Email</h1>\r\n              <p className=\"register-subtitle\">We've sent a verification code to your email</p>\r\n            </div>\r\n\r\n            {/* OTP Instructions */}\r\n            <div className=\"otp-instructions\">\r\n              <div className=\"otp-info-card\">\r\n                <h3 className=\"otp-info-title\">📧 Check Your Email</h3>\r\n                <p className=\"otp-info-text\">\r\n                  We've sent a <strong>6-digit verification code</strong> to your email address.\r\n                </p>\r\n                <div className=\"otp-steps\">\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">1</span>\r\n                    <span className=\"step-text\">Open your email app or website</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">2</span>\r\n                    <span className=\"step-text\">Look for an email from BrainWave</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">3</span>\r\n                    <span className=\"step-text\">Copy the 6-digit code from the email</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">4</span>\r\n                    <span className=\"step-text\">Enter the code in the box below</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"otp-help\">\r\n                  <p className=\"help-text\">\r\n                    <strong>💡 Can't find the email?</strong> Check your spam/junk folder or wait a few minutes for delivery.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Form layout=\"vertical\" onFinish={verifyUser} className=\"register-form\">\r\n              <Form.Item name=\"otp\" label=\"Verification Code\" rules={[{ required: true, message: \"Please enter the OTP!\" }]}>\r\n                <Input\r\n                  type=\"number\"\r\n                  className=\"form-input otp-input\"\r\n                  placeholder=\"Enter 6-digit code (e.g., 123456)\"\r\n                  maxLength={6}\r\n                />\r\n                <p className=\"form-help-text\">Enter the verification code from your email</p>\r\n              </Form.Item>\r\n\r\n              <button type=\"submit\" className=\"register-btn\" disabled={loading}>\r\n                {loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"}\r\n              </button>\r\n\r\n              <div className=\"resend-section\">\r\n                <p className=\"resend-text\">Didn't receive the code?</p>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"resend-btn\"\r\n                  onClick={() => generateOTP(data)}\r\n                  disabled={loading}\r\n                >\r\n                  📧 Resend Verification Code\r\n                </button>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Create Account</h1>\r\n              <p className=\"register-subtitle\">Join thousands of students learning with BrainWave</p>\r\n            </div>\r\n\r\n\r\n\r\n            <Form\r\n              layout=\"vertical\"\r\n              onFinish={generateOTP}\r\n              className=\"register-form\"\r\n              validateTrigger=\"onChange\"\r\n              onValuesChange={(changedValues, allValues) => {\r\n                console.log(\"📝 Form values changed:\", allValues);\r\n                setFormValues(allValues);\r\n              }}\r\n            >\r\n\r\n\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"Full Name\"\r\n                hasFeedback\r\n                rules={[\r\n                  { required: true, message: \"👤 Please enter your full name\" },\r\n                  { min: 2, message: \"👤 Name must be at least 2 characters long\" }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your full name (e.g., John Doe)\"\r\n                  autoComplete=\"name\"\r\n                />\r\n                <p className=\"form-help-text\">Enter your first and last name as you'd like it to appear</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"school\"\r\n                label=\"School Name\"\r\n                hasFeedback\r\n                rules={[\r\n                  { required: true, message: \"🏫 Please enter your school name\" }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your school name (e.g., Dar es Salaam Secondary School)\"\r\n                  autoComplete=\"organization\"\r\n                />\r\n                <p className=\"form-help-text\">Enter the full name of your current school</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"level\"\r\n                label=\"Education Level\"\r\n                hasFeedback\r\n\r\n                rules={[{ required: true, message: \"🎓 Please select your education level\" }]}\r\n              >\r\n                <Select\r\n                  onChange={(value) => setSchoolType(value)}\r\n                  className=\"form-input\"\r\n                  placeholder=\"Choose your current education level\"\r\n                >\r\n                  <Option value=\"Primary\">🎒 Primary Education (Classes 1-7)</Option>\r\n                  <Option value=\"Secondary\">📚 Secondary Education (Forms 1-4)</Option>\r\n                  <Option value=\"Advance\">🎓 Advanced Level (Forms 5-6)</Option>\r\n                </Select>\r\n                <p className=\"form-help-text\">Select the education level you are currently studying</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"class\"\r\n                label=\"Class/Form\"\r\n                hasFeedback\r\n\r\n                rules={[{ required: true, message: \"📝 Please select your class or form\" }]}\r\n              >\r\n                <Select\r\n                  className=\"form-input\"\r\n                  placeholder={schoolType ? \"Select your class/form\" : \"Please select education level first\"}\r\n                  disabled={!schoolType}\r\n                >\r\n                  {schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map((i) => (\r\n                    <Option key={i} value={i}>{`📚 Class ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Secondary\" && [1, 2, 3, 4].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`📖 Form ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Advance\" && [5, 6].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`🎓 Form ${i}`}</Option>\r\n                  ))}\r\n                </Select>\r\n                <p className=\"form-help-text\">\r\n                  {!schoolType && \"Please select your education level first\"}\r\n                  {schoolType === \"Primary\" && \"Select your current class (1-7)\"}\r\n                  {schoolType === \"Secondary\" && \"Select your current form (1-4)\"}\r\n                  {schoolType === \"Advance\" && \"Select your current form (5-6)\"}\r\n                </p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"Email Address\"\r\n                hasFeedback\r\n                validateTrigger={['onChange', 'onBlur']}\r\n                rules={[\r\n                  { required: true, message: \"📧 Please enter your email!\" },\r\n                  { type: \"email\", message: \"📧 Please enter a valid email address!\" },\r\n                  {\r\n                    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\r\n                    message: \"📧 Please enter a properly formatted email address!\"\r\n                  }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"email\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your email address (e.g., <EMAIL>)\"\r\n                  autoComplete=\"email\"\r\n                />\r\n                <p className=\"form-help-text\">We'll send important updates to this email</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phoneNumber\"\r\n                label=\"Phone Number\"\r\n                hasFeedback\r\n                validateTrigger={['onChange', 'onBlur']}\r\n                rules={[\r\n                  { required: true, message: \"📱 Please enter your phone number\" },\r\n                  {\r\n                    validator: (_, value) => {\r\n                      if (!value) return Promise.resolve();\r\n                      if (validatePhoneNumber(value)) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error(\"📱 Phone number must start with 06 or 07 and be 10 digits (e.g., 0712345678)\"));\r\n                    }\r\n                  },\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"tel\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter mobile number (e.g., 0712345678 or 0612345678)\"\r\n                  autoComplete=\"tel\"\r\n                  maxLength={10}\r\n                />\r\n                <div className=\"phone-help-section\">\r\n                  <p className=\"form-help-text\">💳 Enter your mobile number for payment processing (M-Pesa, Tigo Pesa, Airtel Money)</p>\r\n                  <p className=\"form-help-text\">📞 Must start with <strong>06</strong> or <strong>07</strong> and be exactly 10 digits</p>\r\n                  <p className=\"form-help-text\">📧 Note: Email verification code will be sent to your email, not phone</p>\r\n                </div>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                label=\"Password\"\r\n                hasFeedback\r\n                validateTrigger={['onChange', 'onBlur']}\r\n                rules={[\r\n                  { required: true, message: \"🔒 Please enter your password!\" },\r\n                  { min: 8, message: \"🔒 Password must be at least 8 characters long!\" },\r\n                  {\r\n                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\r\n                    message: \"🔒 Password must contain uppercase, lowercase, number and special character!\"\r\n                  }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Create a strong password (min 8 characters)\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must include: uppercase, lowercase, number, and special character</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                label=\"Retype Password\"\r\n                hasFeedback\r\n                validateTrigger={['onChange', 'onBlur']}\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: \"🔒 Please retype your password!\" },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('🔒 The two passwords do not match!'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Retype your password to confirm\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must match the password above</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"register-btn\"\r\n                  disabled={loading}\r\n                  onClick={() => console.log(\"🔘 Create Account button clicked\")}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <span className=\"loading-spinner\"></span>\r\n                      Creating Account...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      🚀 Create Account\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </Form.Item>\r\n            </Form>\r\n\r\n            <div className=\"register-footer\">\r\n              <p>\r\n                Already have an account? {\" \"}\r\n                <Link to=\"/login\" className=\"register-link\">\r\n                  Sign In\r\n                </Link>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACnD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAC/D,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAM;EAAEC;AAAO,CAAC,GAAGZ,MAAM;AAEzB,SAASa,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiB,GAAG,EAAEC,MAAM,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAMyB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwB,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,cAAc;IACjC,OAAOA,UAAU,CAACC,IAAI,CAACF,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMG,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7B,YAAY,CAAC4B,MAAM,CAAC;MAC3C,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpBrC,OAAO,CAACqC,OAAO,CAAC;UACdC,OAAO,EAAEF,QAAQ,CAACpC,OAAO;UACzBuC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACF;QACAC,UAAU,CAAC,MAAM;UACfb,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLc,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,qBAAqB,CAAC;QAC1DlB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CD,qBAAqB,CAACC,KAAK,EAAE,wCAAwC,CAAC;MACtE1B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAM4B,UAAU,GAAG,MAAOX,MAAM,IAAK;IAAA,IAAAY,WAAA;IACnC,IAAI,GAAAA,WAAA,GAACZ,MAAM,CAACd,GAAG,cAAA0B,WAAA,eAAVA,WAAA,CAAYC,IAAI,CAAC,CAAC,GAAE;MACvBhD,OAAO,CAAC4C,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIT,MAAM,CAACd,GAAG,CAAC4B,MAAM,KAAK,CAAC,EAAE;MAC3BjD,OAAO,CAAC4C,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIT,MAAM,CAACd,GAAG,KAAKA,GAAG,EAAE;MACtBrB,OAAO,CAACuB,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;MAC9CmB,UAAU,CAAC,MAAM;QACfR,QAAQ,CAACf,IAAI,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLnB,OAAO,CAAC4C,KAAK,CAAC;QACZN,OAAO,EAAE,8EAA8E;QACvFC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAACC,KAAK,EAAEM,cAAc,KAAK;IAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACvD,MAAMC,YAAY,GAAG,EAAAJ,eAAA,GAAAP,KAAK,CAACR,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhC,IAAI,cAAAiC,oBAAA,uBAApBA,oBAAA,CAAsBpD,OAAO,KAAI4C,KAAK,CAAC5C,OAAO,IAAIkD,cAAc;IACrF,MAAMM,SAAS,IAAAH,gBAAA,GAAGT,KAAK,CAACR,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlC,IAAI,cAAAmC,qBAAA,uBAApBA,qBAAA,CAAsBE,SAAS;;IAEjD;IACA,IAAIA,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,cAAc,EAAE;MAChExD,OAAO,CAACyD,OAAO,CAAC;QACdnB,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIe,SAAS,KAAK,eAAe,IAAIA,SAAS,KAAK,eAAe,EAAE;MACzExD,OAAO,CAAC4C,KAAK,CAAC;QACZN,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzC,OAAO,CAAC4C,KAAK,CAAC;QACZN,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMiB,WAAW,GAAG,MAAOC,QAAQ,IAAK;IAAA,IAAAC,cAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA;IACtCrB,OAAO,CAACsB,GAAG,CAAC,8BAA8B,EAAER,QAAQ,CAAC;;IAErD;IACA,IAAI,GAAAC,cAAA,GAACD,QAAQ,CAACS,IAAI,cAAAR,cAAA,eAAbA,cAAA,CAAeZ,IAAI,CAAC,CAAC,GAAE;MAC1BhD,OAAO,CAAC4C,KAAK,CAAC,gCAAgC,CAAC;MAC/C;IACF;IACA,IAAI,GAAAiB,gBAAA,GAACF,QAAQ,CAACU,MAAM,cAAAR,gBAAA,eAAfA,gBAAA,CAAiBb,IAAI,CAAC,CAAC,GAAE;MAC5BhD,OAAO,CAAC4C,KAAK,CAAC,kCAAkC,CAAC;MACjD;IACF;IACA,IAAI,GAAAkB,eAAA,GAACH,QAAQ,CAACW,KAAK,cAAAR,eAAA,eAAdA,eAAA,CAAgBd,IAAI,CAAC,CAAC,GAAE;MAC3BhD,OAAO,CAAC4C,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IACA,IAAI,CAACe,QAAQ,CAACY,KAAK,EAAE;MACnBvE,OAAO,CAAC4C,KAAK,CAAC,kCAAkC,CAAC;MACjD;IACF;IACA,IAAI,GAAAmB,eAAA,GAACJ,QAAQ,CAACa,KAAK,cAAAT,eAAA,eAAdA,eAAA,CAAgBf,IAAI,CAAC,CAAC,GAAE;MAC3BhD,OAAO,CAAC4C,KAAK,CAAC,oCAAoC,CAAC;MACnD;IACF;IACA,IAAI,GAAAoB,qBAAA,GAACL,QAAQ,CAACc,WAAW,cAAAT,qBAAA,eAApBA,qBAAA,CAAsBhB,IAAI,CAAC,CAAC,GAAE;MACjChD,OAAO,CAAC4C,KAAK,CAAC,mCAAmC,CAAC;MAClD;IACF;IACA,IAAI,GAAAqB,kBAAA,GAACN,QAAQ,CAACe,QAAQ,cAAAT,kBAAA,eAAjBA,kBAAA,CAAmBjB,IAAI,CAAC,CAAC,GAAE;MAC9BhD,OAAO,CAAC4C,KAAK,CAAC,6BAA6B,CAAC;MAC5C;IACF;IACA,IAAI,GAAAsB,qBAAA,GAACP,QAAQ,CAACgB,eAAe,cAAAT,qBAAA,eAAxBA,qBAAA,CAA0BlB,IAAI,CAAC,CAAC,GAAE;MACrChD,OAAO,CAAC4C,KAAK,CAAC,iCAAiC,CAAC;MAChD;IACF;IACA,IAAIe,QAAQ,CAACe,QAAQ,KAAKf,QAAQ,CAACgB,eAAe,EAAE;MAClD3E,OAAO,CAAC4C,KAAK,CAAC,wDAAwD,CAAC;MACvE;IACF;IAEAC,OAAO,CAACsB,GAAG,CAAC,6DAA6D,CAAC;IAE1E3C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM5B,OAAO,CAACmD,QAAQ,CAAC;MACxC,IAAIvB,QAAQ,CAACC,OAAO,EAAE;QACpBrC,OAAO,CAACqC,OAAO,CAAC;UACdC,OAAO,EAAEF,QAAQ,CAACpC,OAAO;UACzBuC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACFrB,OAAO,CAACuC,QAAQ,CAAC;QACjBrC,MAAM,CAACc,QAAQ,CAACjB,IAAI,CAAC;QACrBD,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACLyB,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,kCAAkC,CAAC;MACzE;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,qBAAqB,CAACC,KAAK,EAAE,oEAAoE,CAAC;IACpG;IACApB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAID,oBACEb,OAAA;IAAKiE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjClE,OAAA;MAAKiE,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3B5D,YAAY,gBACXN,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAKiE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BlE,OAAA;YAAKmE,GAAG,EAAErE,IAAK;YAACsE,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjExE,OAAA;YAAIiE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDxE,OAAA;YAAGiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAA4C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAGNxE,OAAA;UAAKiE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BlE,OAAA;YAAKiE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlE,OAAA;cAAIiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDxE,OAAA;cAAGiE,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,eACd,eAAAlE,OAAA;gBAAAkE,QAAA,EAAQ;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,2BACzD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJxE,OAAA;cAAKiE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlE,OAAA;gBAAKiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBlE,OAAA;kBAAMiE,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCxE,OAAA;kBAAMiE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNxE,OAAA;gBAAKiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBlE,OAAA;kBAAMiE,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCxE,OAAA;kBAAMiE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAgC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNxE,OAAA;gBAAKiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBlE,OAAA;kBAAMiE,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCxE,OAAA;kBAAMiE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAoC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNxE,OAAA;gBAAKiE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBlE,OAAA;kBAAMiE,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCxE,OAAA;kBAAMiE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxE,OAAA;cAAKiE,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBlE,OAAA;gBAAGiE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtBlE,OAAA;kBAAAkE,QAAA,EAAQ;gBAAwB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oEAC3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA,CAACZ,IAAI;UAACqF,MAAM,EAAC,UAAU;UAAClD,QAAQ,EAAEY,UAAW;UAAC8B,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACrElE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YAACjB,IAAI,EAAC,KAAK;YAACkB,KAAK,EAAC,mBAAmB;YAACC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAA6E,QAAA,gBAC5GlE,OAAA,CAACV,KAAK;cACJwF,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,sBAAsB;cAChCc,WAAW,EAAC,mCAAmC;cAC/CC,SAAS,EAAE;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACFxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA2C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEZxE,OAAA;YAAQ8E,IAAI,EAAC,QAAQ;YAACb,SAAS,EAAC,cAAc;YAACgB,QAAQ,EAAErE,OAAQ;YAAAsD,QAAA,EAC9DtD,OAAO,GAAG,gBAAgB,GAAG;UAAkC;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAETxE,OAAA;YAAKiE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BlE,OAAA;cAAGiE,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvDxE,OAAA;cACE8E,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,YAAY;cACtBiB,OAAO,EAAEA,CAAA,KAAMnC,WAAW,CAACvC,IAAI,CAAE;cACjCyE,QAAQ,EAAErE,OAAQ;cAAAsD,QAAA,EACnB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,gBAENxE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAKiE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BlE,OAAA;YAAKmE,GAAG,EAAErE,IAAK;YAACsE,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjExE,OAAA;YAAIiE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDxE,OAAA;YAAGiE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAINxE,OAAA,CAACZ,IAAI;UACHqF,MAAM,EAAC,UAAU;UACjBlD,QAAQ,EAAEwB,WAAY;UACtBkB,SAAS,EAAC,eAAe;UACzBkB,eAAe,EAAC,UAAU;UAC1BC,cAAc,EAAEA,CAACC,aAAa,EAAEC,SAAS,KAAK;YAC5CpD,OAAO,CAACsB,GAAG,CAAC,yBAAyB,EAAE8B,SAAS,CAAC;YACjDrE,aAAa,CAACqE,SAAS,CAAC;UAC1B,CAAE;UAAApB,QAAA,gBAIFlE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,MAAM;YACXkB,KAAK,EAAC,WAAW;YACjBY,WAAW;YACXX,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAiC,CAAC,EAC7D;cAAEmG,GAAG,EAAE,CAAC;cAAEnG,OAAO,EAAE;YAA6C,CAAC,CACjE;YAAA6E,QAAA,gBAEFlE,OAAA,CAACV,KAAK;cACJwF,IAAI,EAAC,MAAM;cACXb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,uCAAuC;cACnDU,YAAY,EAAC;YAAM;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACFxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAyD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,QAAQ;YACbkB,KAAK,EAAC,aAAa;YACnBY,WAAW;YACXX,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAmC,CAAC,CAC/D;YAAA6E,QAAA,gBAEFlE,OAAA,CAACV,KAAK;cACJwF,IAAI,EAAC,MAAM;cACXb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,+DAA+D;cAC3EU,YAAY,EAAC;YAAc;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA0C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,OAAO;YACZkB,KAAK,EAAC,iBAAiB;YACvBY,WAAW;YAEXX,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAwC,CAAC,CAAE;YAAA6E,QAAA,gBAE9ElE,OAAA,CAACT,MAAM;cACLmG,QAAQ,EAAGC,KAAK,IAAK5E,aAAa,CAAC4E,KAAK,CAAE;cAC1C1B,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,qCAAqC;cAAAb,QAAA,gBAEjDlE,OAAA,CAACG,MAAM;gBAACwF,KAAK,EAAC,SAAS;gBAAAzB,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnExE,OAAA,CAACG,MAAM;gBAACwF,KAAK,EAAC,WAAW;gBAAAzB,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrExE,OAAA,CAACG,MAAM;gBAACwF,KAAK,EAAC,SAAS;gBAAAzB,QAAA,EAAC;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACTxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAqD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,OAAO;YACZkB,KAAK,EAAC,YAAY;YAClBY,WAAW;YAEXX,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAsC,CAAC,CAAE;YAAA6E,QAAA,gBAE5ElE,OAAA,CAACT,MAAM;cACL0E,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAEjE,UAAU,GAAG,wBAAwB,GAAG,qCAAsC;cAC3FmE,QAAQ,EAAE,CAACnE,UAAW;cAAAoD,QAAA,GAErBpD,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC8E,GAAG,CAAEC,CAAC,iBACvD7F,OAAA,CAACG,MAAM;gBAASwF,KAAK,EAAEE,CAAE;gBAAA3B,QAAA,EAAG,YAAW2B,CAAE;cAAC,GAA7BA,CAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CACpD,CAAC,EACD1D,UAAU,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC8E,GAAG,CAAEC,CAAC,iBAChD7F,OAAA,CAACG,MAAM;gBAASwF,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAA3B,QAAA,EAAG,WAAU2B,CAAE;cAAC,GAAtCA,CAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC,EACD1D,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC8E,GAAG,CAAEC,CAAC,iBACxC7F,OAAA,CAACG,MAAM;gBAASwF,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAA3B,QAAA,EAAG,WAAU2B,CAAE;cAAC,GAAtCA,CAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAC1B,CAACpD,UAAU,IAAI,0CAA0C,EACzDA,UAAU,KAAK,SAAS,IAAI,iCAAiC,EAC7DA,UAAU,KAAK,WAAW,IAAI,gCAAgC,EAC9DA,UAAU,KAAK,SAAS,IAAI,gCAAgC;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,OAAO;YACZkB,KAAK,EAAC,eAAe;YACrBY,WAAW;YACXJ,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAE;YACxCP,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAA8B,CAAC,EAC1D;cAAEyF,IAAI,EAAE,OAAO;cAAEzF,OAAO,EAAE;YAAyC,CAAC,EACpE;cACEyG,OAAO,EAAE,kDAAkD;cAC3DzG,OAAO,EAAE;YACX,CAAC,CACD;YAAA6E,QAAA,gBAEFlE,OAAA,CAACV,KAAK;cACJwF,IAAI,EAAC,OAAO;cACZb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,sDAAsD;cAClEU,YAAY,EAAC;YAAO;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA0C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,aAAa;YAClBkB,KAAK,EAAC,cAAc;YACpBY,WAAW;YACXJ,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAE;YACxCP,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAoC,CAAC,EAChE;cACE0G,SAAS,EAAEA,CAACC,CAAC,EAAEL,KAAK,KAAK;gBACvB,IAAI,CAACA,KAAK,EAAE,OAAOM,OAAO,CAACC,OAAO,CAAC,CAAC;gBACpC,IAAI/E,mBAAmB,CAACwE,KAAK,CAAC,EAAE;kBAC9B,OAAOM,OAAO,CAACC,OAAO,CAAC,CAAC;gBAC1B;gBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,8EAA8E,CAAC,CAAC;cAClH;YACF,CAAC,CACD;YAAAlC,QAAA,gBAEFlE,OAAA,CAACV,KAAK;cACJwF,IAAI,EAAC,KAAK;cACVb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,sDAAsD;cAClEU,YAAY,EAAC,KAAK;cAClBT,SAAS,EAAE;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFxE,OAAA;cAAKiE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjClE,OAAA;gBAAGiE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAoF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtHxE,OAAA;gBAAGiE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,+BAAmB,eAAAlE,OAAA;kBAAAkE,QAAA,EAAQ;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,QAAI,eAAAxE,OAAA;kBAAAkE,QAAA,EAAQ;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6BAAyB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxHxE,OAAA;gBAAGiE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAsE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,UAAU;YACfkB,KAAK,EAAC,UAAU;YAChBY,WAAW;YACXJ,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAE;YACxCP,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAiC,CAAC,EAC7D;cAAEmG,GAAG,EAAE,CAAC;cAAEnG,OAAO,EAAE;YAAkD,CAAC,EACtE;cACEyG,OAAO,EAAE,iEAAiE;cAC1EzG,OAAO,EAAE;YACX,CAAC,CACD;YAAA6E,QAAA,gBAEFlE,OAAA,CAACV,KAAK,CAAC+G,QAAQ;cACbpC,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,6CAA6C;cACzDU,YAAY,EAAC;YAAc;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAiE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YACRjB,IAAI,EAAC,iBAAiB;YACtBkB,KAAK,EAAC,iBAAiB;YACvBY,WAAW;YACXJ,eAAe,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAE;YACxCmB,YAAY,EAAE,CAAC,UAAU,CAAE;YAC3B1B,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAExF,OAAO,EAAE;YAAkC,CAAC,EAC9D,CAAC;cAAEkH;YAAc,CAAC,MAAM;cACtBR,SAASA,CAACC,CAAC,EAAEL,KAAK,EAAE;gBAClB,IAAI,CAACA,KAAK,IAAIY,aAAa,CAAC,UAAU,CAAC,KAAKZ,KAAK,EAAE;kBACjD,OAAOM,OAAO,CAACC,OAAO,CAAC,CAAC;gBAC1B;gBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,oCAAoC,CAAC,CAAC;cACxE;YACF,CAAC,CAAC,CACF;YAAAlC,QAAA,gBAEFlE,OAAA,CAACV,KAAK,CAAC+G,QAAQ;cACbpC,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,iCAAiC;cAC7CU,YAAY,EAAC;YAAc;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFxE,OAAA;cAAGiE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEZxE,OAAA,CAACZ,IAAI,CAACsF,IAAI;YAAAR,QAAA,eACRlE,OAAA;cACE8E,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,cAAc;cACxBgB,QAAQ,EAAErE,OAAQ;cAClBsE,OAAO,EAAEA,CAAA,KAAMhD,OAAO,CAACsB,GAAG,CAAC,kCAAkC,CAAE;cAAAU,QAAA,EAE9DtD,OAAO,gBACNZ,OAAA,CAAAE,SAAA;gBAAAgE,QAAA,gBACElE,OAAA;kBAAMiE,SAAS,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,uBAE3C;cAAA,eAAE,CAAC,gBAEHxE,OAAA,CAAAE,SAAA;gBAAAgE,QAAA,EAAE;cAEF,gBAAE;YACH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEPxE,OAAA;UAAKiE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BlE,OAAA;YAAAkE,QAAA,GAAG,2BACwB,EAAC,GAAG,eAC7BlE,OAAA,CAACN,IAAI;cAAC8G,EAAE,EAAC,QAAQ;cAACvC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE5C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnE,EAAA,CAzdQD,QAAQ;EAAA,QAOET,WAAW;AAAA;AAAA8G,EAAA,GAPrBrG,QAAQ;AA2djB,eAAeA,QAAQ;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}