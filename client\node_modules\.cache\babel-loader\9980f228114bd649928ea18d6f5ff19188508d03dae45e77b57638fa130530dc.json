{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Plans\\\\components\\\\WaitingModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport Modal from \"react-modal\";\nimport \"./WaitingModal.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\n\nconst WaitingModal = ({\n  isOpen,\n  onClose\n}) => {\n  _s();\n  const [dots, setDots] = useState('');\n\n  // Animated dots for loading effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setDots(prev => prev.length >= 3 ? '' : prev + '.');\n    }, 500);\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: isOpen,\n    onRequestClose: onClose,\n    className: \"waiting-modal-content\",\n    overlayClassName: \"waiting-modal-overlay\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"waiting-modal-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-icon-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-processing-icon\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"80px\",\n            height: \"80px\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"payment-icon\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              stroke: \"#007BFF\",\n              strokeWidth: \"2\",\n              fill: \"none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 12l2 2 4-4\",\n              stroke: \"#007BFF\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"Processing Payment\", dots]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"payment-subtitle\",\n        children: \"Please wait while we process your payment securely\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"waiting-modal-timer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"progress-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-fill\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"progress-text\",\n          children: \"Connecting to secure payment gateway...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-instructions\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"instruction-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-number\",\n          children: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-text\",\n          children: \"Check your phone for SMS confirmation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"instruction-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-number\",\n          children: \"2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-text\",\n          children: \"Follow the instructions in the message\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"instruction-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-number\",\n          children: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-text\",\n          children: \"Complete the payment process\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"security-notice\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z\",\n          stroke: \"#10B981\",\n          strokeWidth: \"2\",\n          fill: \"none\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M9 12L11 14L15 10\",\n          stroke: \"#10B981\",\n          strokeWidth: \"2\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Your payment is secured with bank-level encryption\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 9\n  }, this);\n};\n_s(WaitingModal, \"x5A7Sg53ZjAlz59b/ycs4VK0Y+g=\");\n_c = WaitingModal;\nexport default WaitingModal;\nvar _c;\n$RefreshReg$(_c, \"WaitingModal\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Modal", "jsxDEV", "_jsxDEV", "setAppElement", "WaitingModal", "isOpen", "onClose", "_s", "dots", "setDots", "interval", "setInterval", "prev", "length", "clearInterval", "onRequestClose", "className", "overlayClassName", "children", "width", "height", "viewBox", "fill", "xmlns", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "strokeLinecap", "strokeLinejoin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Plans/components/WaitingModal.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport Modal from \"react-modal\";\r\nimport \"./WaitingModal.css\";\r\n\r\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\r\n\r\nconst WaitingModal = ({ isOpen, onClose }) => {\r\n    const [dots, setDots] = useState('');\r\n\r\n    // Animated dots for loading effect\r\n    useEffect(() => {\r\n        const interval = setInterval(() => {\r\n            setDots(prev => prev.length >= 3 ? '' : prev + '.');\r\n        }, 500);\r\n        return () => clearInterval(interval);\r\n    }, []);\r\n\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            className=\"waiting-modal-content\"\r\n            overlayClassName=\"waiting-modal-overlay\"\r\n        >\r\n            <div className=\"waiting-modal-header\">\r\n                <div className=\"payment-icon-container\">\r\n                    <div className=\"payment-processing-icon\">\r\n                        <svg\r\n                            width=\"80px\"\r\n                            height=\"80px\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            fill=\"none\"\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            className=\"payment-icon\"\r\n                        >\r\n                            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"#007BFF\" strokeWidth=\"2\" fill=\"none\"/>\r\n                            <path d=\"M8 12l2 2 4-4\" stroke=\"#007BFF\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        </svg>\r\n                        <div className=\"loading-spinner\"></div>\r\n                    </div>\r\n                </div>\r\n                <h2>Processing Payment{dots}</h2>\r\n                <p className=\"payment-subtitle\">Please wait while we process your payment securely</p>\r\n            </div>\r\n\r\n            <div className=\"waiting-modal-timer\">\r\n                <div className=\"progress-container\">\r\n                    <div className=\"progress-bar\">\r\n                        <div className=\"progress-fill\"></div>\r\n                    </div>\r\n                    <p className=\"progress-text\">Connecting to secure payment gateway...</p>\r\n                </div>\r\n            </div>\r\n\r\n\r\n            <div className=\"payment-instructions\">\r\n                <div className=\"instruction-item\">\r\n                    <span className=\"step-number\">1</span>\r\n                    <span className=\"step-text\">Check your phone for SMS confirmation</span>\r\n                </div>\r\n                <div className=\"instruction-item\">\r\n                    <span className=\"step-number\">2</span>\r\n                    <span className=\"step-text\">Follow the instructions in the message</span>\r\n                </div>\r\n                <div className=\"instruction-item\">\r\n                    <span className=\"step-number\">3</span>\r\n                    <span className=\"step-text\">Complete the payment process</span>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"security-notice\">\r\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z\" stroke=\"#10B981\" strokeWidth=\"2\" fill=\"none\"/>\r\n                    <path d=\"M9 12L11 14L15 10\" stroke=\"#10B981\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                </svg>\r\n                <span>Your payment is secured with bank-level encryption</span>\r\n            </div>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default WaitingModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5BF,KAAK,CAACG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;;AAE9B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;;EAEpC;EACAD,SAAS,CAAC,MAAM;IACZ,MAAMY,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BF,OAAO,CAACG,IAAI,IAAIA,IAAI,CAACC,MAAM,IAAI,CAAC,GAAG,EAAE,GAAGD,IAAI,GAAG,GAAG,CAAC;IACvD,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAME,aAAa,CAACJ,QAAQ,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIR,OAAA,CAACF,KAAK;IACFK,MAAM,EAAEA,MAAO;IACfU,cAAc,EAAET,OAAQ;IACxBU,SAAS,EAAC,uBAAuB;IACjCC,gBAAgB,EAAC,uBAAuB;IAAAC,QAAA,gBAExChB,OAAA;MAAKc,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACjChB,OAAA;QAAKc,SAAS,EAAC,wBAAwB;QAAAE,QAAA,eACnChB,OAAA;UAAKc,SAAS,EAAC,yBAAyB;UAAAE,QAAA,gBACpChB,OAAA;YACIiB,KAAK,EAAC,MAAM;YACZC,MAAM,EAAC,MAAM;YACbC,OAAO,EAAC,WAAW;YACnBC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,4BAA4B;YAClCP,SAAS,EAAC,cAAc;YAAAE,QAAA,gBAExBhB,OAAA;cAAQsB,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAACC,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC,GAAG;cAACN,IAAI,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC7E9B,OAAA;cAAM+B,CAAC,EAAC,eAAe;cAACN,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC,GAAG;cAACM,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eACN9B,OAAA;YAAKc,SAAS,EAAC;UAAiB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN9B,OAAA;QAAAgB,QAAA,GAAI,oBAAkB,EAACV,IAAI;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjC9B,OAAA;QAAGc,SAAS,EAAC,kBAAkB;QAAAE,QAAA,EAAC;MAAkD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CAAC,eAEN9B,OAAA;MAAKc,SAAS,EAAC,qBAAqB;MAAAE,QAAA,eAChChB,OAAA;QAAKc,SAAS,EAAC,oBAAoB;QAAAE,QAAA,gBAC/BhB,OAAA;UAAKc,SAAS,EAAC,cAAc;UAAAE,QAAA,eACzBhB,OAAA;YAAKc,SAAS,EAAC;UAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACN9B,OAAA;UAAGc,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAC;QAAuC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN9B,OAAA;MAAKc,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACjChB,OAAA;QAAKc,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC7BhB,OAAA;UAAMc,SAAS,EAAC,aAAa;UAAAE,QAAA,EAAC;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtC9B,OAAA;UAAMc,SAAS,EAAC,WAAW;UAAAE,QAAA,EAAC;QAAqC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACN9B,OAAA;QAAKc,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC7BhB,OAAA;UAAMc,SAAS,EAAC,aAAa;UAAAE,QAAA,EAAC;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtC9B,OAAA;UAAMc,SAAS,EAAC,WAAW;UAAAE,QAAA,EAAC;QAAsC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACN9B,OAAA;QAAKc,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC7BhB,OAAA;UAAMc,SAAS,EAAC,aAAa;UAAAE,QAAA,EAAC;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtC9B,OAAA;UAAMc,SAAS,EAAC,WAAW;UAAAE,QAAA,EAAC;QAA4B;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN9B,OAAA;MAAKc,SAAS,EAAC,iBAAiB;MAAAE,QAAA,gBAC5BhB,OAAA;QAAKiB,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,OAAO,EAAC,WAAW;QAACC,IAAI,EAAC,MAAM;QAACC,KAAK,EAAC,4BAA4B;QAAAL,QAAA,gBAC1FhB,OAAA;UAAM+B,CAAC,EAAC,0EAA0E;UAACN,MAAM,EAAC,SAAS;UAACC,WAAW,EAAC,GAAG;UAACN,IAAI,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACjI9B,OAAA;UAAM+B,CAAC,EAAC,mBAAmB;UAACN,MAAM,EAAC,SAAS;UAACC,WAAW,EAAC,GAAG;UAACM,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G,CAAC,eACN9B,OAAA;QAAAgB,QAAA,EAAM;MAAkD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB,CAAC;AAACzB,EAAA,CAzEIH,YAAY;AAAAgC,EAAA,GAAZhC,YAAY;AA2ElB,eAAeA,YAAY;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}