const axios = require('axios');

// Test profile update with debugging
async function testProfileUpdate() {
  try {
    // First, let's test with a simple login to get a token
    console.log('🔄 Testing profile update...');
    
    // You'll need to replace these with actual credentials
    const loginResponse = await axios.post('http://localhost:5000/api/users/login', {
      email: '<EMAIL>', // Replace with actual test user email
      password: 'password123'     // Replace with actual test user password
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.data.token;
    const userId = loginResponse.data.data.user._id;
    
    console.log('✅ Login successful, testing profile update...');
    
    // Test profile update
    const updateData = {
      userId: userId,
      name: 'Test User Updated',
      email: '<EMAIL>',
      school: 'Test School',
      class_: '5',
      level: 'Primary',
      phoneNumber: '1234567890'
    };
    
    console.log('📤 Sending update data:', JSON.stringify(updateData, null, 2));
    
    const updateResponse = await axios.post(
      'http://localhost:5000/api/users/update-user-info',
      updateData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Update successful:', updateResponse.data);
    
  } catch (error) {
    console.log('❌ Error occurred:');
    console.log('Status:', error.response?.status);
    console.log('Status Text:', error.response?.statusText);
    console.log('Error Data:', JSON.stringify(error.response?.data, null, 2));
    console.log('Request Data:', JSON.stringify(error.config?.data, null, 2));
    
    if (error.response?.status === 400) {
      console.log('\n🔍 This is a 400 Bad Request error. Common causes:');
      console.log('1. Missing required fields');
      console.log('2. Invalid data format');
      console.log('3. Validation errors');
      console.log('4. Invalid level value');
    }
  }
}

// Test with different scenarios
async function testDifferentScenarios() {
  console.log('🧪 Testing different update scenarios...\n');
  
  // Test cases
  const testCases = [
    {
      name: 'Valid Primary Level',
      data: { level: 'Primary', class_: '5' }
    },
    {
      name: 'Valid Secondary Level',
      data: { level: 'Secondary', class_: '2' }
    },
    {
      name: 'Valid Advance Level',
      data: { level: 'Advance', class_: '5' }
    },
    {
      name: 'Invalid Level',
      data: { level: 'Invalid', class_: '5' }
    },
    {
      name: 'Missing Class',
      data: { level: 'Primary', class_: '' }
    },
    {
      name: 'Missing Name',
      data: { name: '', level: 'Primary', class_: '5' }
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🔄 Testing: ${testCase.name}`);
    
    try {
      // Mock request data
      const requestData = {
        userId: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        school: 'Test School',
        phoneNumber: '1234567890',
        ...testCase.data
      };
      
      console.log('📤 Request data:', JSON.stringify(requestData, null, 2));
      
      // This would normally make the request, but we're just logging for now
      console.log('✅ Would send this data to server');
      
    } catch (error) {
      console.log('❌ Test case failed:', error.message);
    }
  }
}

// Run the tests
if (require.main === module) {
  console.log('🚀 Starting profile update debugging...\n');
  
  // First run the mock tests
  testDifferentScenarios().then(() => {
    console.log('\n' + '='.repeat(50));
    console.log('📝 To test with real server, update the credentials in testProfileUpdate()');
    console.log('   and uncomment the line below:');
    console.log('// testProfileUpdate();');
  });
  
  // Uncomment this line and add real credentials to test with actual server
  // testProfileUpdate();
}

module.exports = { testProfileUpdate, testDifferentScenarios };
