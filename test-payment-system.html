<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment System Test - BrainWave</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007BFF;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .phone-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💳 Payment System Test - BrainWave</h1>
        
        <div class="phone-info">
            <h4>📱 Tanzania Phone Number Format:</h4>
            <ul>
                <li><strong>Valid formats:</strong> 06xxxxxxxx or 07xxxxxxxx (10 digits total)</li>
                <li><strong>Examples:</strong> 0712345678, 0654321098</li>
                <li><strong>ZenoPay SMS:</strong> Payment confirmation will be sent to this number</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication Test</h3>
            <div id="auth-status" class="status loading">Not tested</div>
            <div class="form-group">
                <label>Email/Username:</label>
                <input type="text" id="email" placeholder="Enter your email or username">
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="password" placeholder="Enter your password">
            </div>
            <button onclick="testLogin()">Test Login</button>
        </div>

        <div class="test-section">
            <h3>📱 Phone Number Validation</h3>
            <div id="phone-status" class="status loading">Login first</div>
            <div class="form-group">
                <label>Test Phone Number:</label>
                <input type="tel" id="testPhone" placeholder="Enter phone number (e.g., 0712345678)" maxlength="10">
            </div>
            <button onclick="validatePhoneNumber()" id="phoneBtn" disabled>Validate Phone</button>
        </div>

        <div class="test-section">
            <h3>💳 Payment Test</h3>
            <div id="payment-status" class="status loading">Login and validate phone first</div>
            
            <div class="form-group">
                <label>Test Plan:</label>
                <select id="testPlan">
                    <option value="">Select a plan</option>
                    <option value='{"title":"Basic Plan","discountedPrice":5000}'>Basic Plan - 5,000 TZS</option>
                    <option value='{"title":"Premium Plan","discountedPrice":10000}'>Premium Plan - 10,000 TZS</option>
                    <option value='{"title":"Glimp Plan","discountedPrice":13000}'>Glimp Plan - 13,000 TZS</option>
                </select>
            </div>
            
            <button onclick="testPayment()" id="paymentBtn" disabled>Test Payment</button>
        </div>

        <div class="test-section">
            <h3>📋 Debug Log</h3>
            <div id="debug-log" class="log">
                Ready to test payment system...<br>
            </div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        let authToken = null;
        let userId = null;
        let userPhone = null;

        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = 'Log cleared...<br>';
        }

        function validateTanzaniaPhone(phone) {
            const phoneRegex = /^0[67]\d{8}$/;
            return phoneRegex.test(phone);
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const statusDiv = document.getElementById('auth-status');
            
            if (!email || !password) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Please enter email/username and password';
                return;
            }

            statusDiv.className = 'status loading';
            statusDiv.textContent = '🔄 Testing login...';
            log(`Attempting login with: ${email}`);

            try {
                const response = await fetch('http://localhost:5000/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                log(`Login response: ${JSON.stringify(data, null, 2)}`);

                if (data.success) {
                    authToken = data.data.token;
                    userId = data.data.user._id;
                    userPhone = data.data.user.phoneNumber;
                    
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Login successful';
                    
                    document.getElementById('phoneBtn').disabled = false;
                    document.getElementById('testPhone').value = userPhone || '';
                    
                    log(`✅ Login successful. User ID: ${userId}`);
                    log(`📱 User phone: ${userPhone || 'Not set'}`);
                    
                    // Auto-validate phone if available
                    if (userPhone) {
                        setTimeout(validatePhoneNumber, 1000);
                    }
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Login failed: ${data.message}`;
                    log(`❌ Login failed: ${data.message}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Login error: ${error.message}`;
                log(`❌ Login error: ${error.message}`);
            }
        }

        function validatePhoneNumber() {
            const phone = document.getElementById('testPhone').value;
            const statusDiv = document.getElementById('phone-status');
            
            if (!phone) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Please enter a phone number';
                return;
            }

            log(`📱 Validating phone number: ${phone}`);

            if (validateTanzaniaPhone(phone)) {
                statusDiv.className = 'status success';
                statusDiv.textContent = `✅ Valid Tanzania phone number: ${phone}`;
                document.getElementById('paymentBtn').disabled = false;
                log(`✅ Phone number validation passed`);
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Invalid phone format. Must be 06xxxxxxxx or 07xxxxxxxx';
                document.getElementById('paymentBtn').disabled = true;
                log(`❌ Phone number validation failed`);
            }
        }

        async function testPayment() {
            if (!authToken || !userId) {
                log('❌ No auth token or user ID. Please login first.');
                return;
            }

            const planSelect = document.getElementById('testPlan');
            if (!planSelect.value) {
                log('❌ Please select a test plan.');
                return;
            }

            const plan = JSON.parse(planSelect.value);
            const statusDiv = document.getElementById('payment-status');
            
            statusDiv.className = 'status loading';
            statusDiv.textContent = '🔄 Testing payment...';

            log(`💳 Initiating payment for: ${plan.title} - ${plan.discountedPrice} TZS`);

            try {
                const response = await fetch('http://localhost:5000/api/payment/create-invoice', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ plan, userId })
                });

                const data = await response.json();
                log(`📥 Payment response (${response.status}): ${JSON.stringify(data, null, 2)}`);

                if (response.ok && data.success) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Payment request sent successfully! Check your phone for SMS.';
                    log('✅ Payment initiation successful');
                    log(`📱 SMS should be sent to: ${userPhone}`);
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Payment failed: ${data.message || 'Unknown error'}`;
                    log(`❌ Payment failed (${response.status}): ${data.message || 'Unknown error'}`);
                    
                    if (data.errorType) {
                        log(`Error type: ${data.errorType}`);
                    }
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Payment error: ${error.message}`;
                log(`❌ Payment error: ${error.message}`);
            }
        }

        // Auto-populate phone number format example
        document.getElementById('testPhone').addEventListener('input', function() {
            const value = this.value;
            if (value.length === 10) {
                validatePhoneNumber();
            }
        });
    </script>
</body>
</html>
