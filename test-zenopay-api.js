const axios = require('axios');
const qs = require('qs');

async function testZenoPayAPI() {
  console.log('🧪 Testing ZenoPay API Configuration...\n');

  // Current configuration from .env
  const config = {
    account_id: 'zp38236',
    secret_key: 'your_secret_key_here', // ❌ This is the issue!
    api_key: '-YIkdkUWpqEyy9DOaKPTDeaEZ5O97_DkSxmZdBLwYrE',
    webhook_url: 'https://d6df-39-52-5-75.ngrok-free.app/api/payment/webhook'
  };

  console.log('📋 Current ZenoPay Configuration:');
  console.log('Account ID:', config.account_id);
  console.log('Secret Key:', config.secret_key === 'your_secret_key_here' ? '❌ PLACEHOLDER VALUE!' : '✅ Set');
  console.log('API Key:', config.api_key ? '✅ Set (' + config.api_key.substring(0, 10) + '...)' : '❌ Missing');
  console.log('Webhook URL:', config.webhook_url);
  console.log('');

  // Test data
  const testData = {
    buyer_name: 'Test User',
    buyer_phone: '**********',
    buyer_email: '<EMAIL>',
    amount: 5000,
    account_id: config.account_id,
    secret_key: config.secret_key,
    api_key: config.api_key,
    webhook_url: config.webhook_url
  };

  console.log('📤 Test Payment Data:');
  console.log(JSON.stringify(testData, null, 2));
  console.log('');

  try {
    console.log('🔄 Sending request to ZenoPay API...');
    
    const formattedData = qs.stringify(testData);
    console.log('📝 Formatted data:', formattedData.substring(0, 100) + '...');
    
    const response = await axios.post('https://api.zeno.africa', formattedData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 30000
    });

    console.log('✅ ZenoPay API Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

    if (response.data.status === 'success') {
      console.log('\n🎉 SUCCESS! ZenoPay API is working correctly.');
      console.log('📱 SMS should be sent to:', testData.buyer_phone);
    } else {
      console.log('\n⚠️ ZenoPay returned an error:', response.data.message || 'Unknown error');
    }

  } catch (error) {
    console.log('\n❌ ZenoPay API Error:');
    console.log('Error message:', error.message);
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Status text:', error.response.statusText);
      console.log('Response data:', JSON.stringify(error.response.data, null, 2));
      
      // Common error analysis
      if (error.response.status === 401) {
        console.log('\n🔍 Analysis: Authentication failed');
        console.log('   - Check if ZENOPAY_SECRET_KEY is correct');
        console.log('   - Verify ZENOPAY_API_KEY is valid');
        console.log('   - Ensure ZENOPAY_ACCOUNT_ID is correct');
      } else if (error.response.status === 400) {
        console.log('\n🔍 Analysis: Bad request');
        console.log('   - Check if all required fields are provided');
        console.log('   - Verify phone number format');
        console.log('   - Check amount format');
      } else if (error.response.status === 403) {
        console.log('\n🔍 Analysis: Forbidden');
        console.log('   - Account may be suspended or inactive');
        console.log('   - Check account permissions');
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n🔍 Analysis: Connection refused');
      console.log('   - Check internet connection');
      console.log('   - ZenoPay API may be down');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('\n🔍 Analysis: Request timeout');
      console.log('   - Network connectivity issues');
      console.log('   - ZenoPay API may be slow');
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('🔧 RECOMMENDATIONS:');
  console.log('');
  
  if (config.secret_key === 'your_secret_key_here') {
    console.log('❌ CRITICAL: Update ZENOPAY_SECRET_KEY in .env file');
    console.log('   Contact ZenoPay support to get your actual secret key');
    console.log('');
  }
  
  console.log('✅ Steps to fix:');
  console.log('1. Get the correct ZENOPAY_SECRET_KEY from ZenoPay dashboard');
  console.log('2. Update the .env file with the real secret key');
  console.log('3. Restart the server');
  console.log('4. Test payment flow again');
  console.log('');
  console.log('📞 ZenoPay Support: Contact them for API credentials');
  console.log('🌐 ZenoPay Dashboard: Check your account settings');
}

// Run the test
if (require.main === module) {
  testZenoPayAPI().catch(console.error);
}

module.exports = { testZenoPayAPI };
