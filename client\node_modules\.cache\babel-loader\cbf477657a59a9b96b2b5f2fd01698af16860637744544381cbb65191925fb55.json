{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{Col,Form,message,Row,Select,Table}from\"antd\";import React,{useEffect,useState}from\"react\";import{addExam,deleteQuestionById,editExamById,getExamById}from\"../../../apicalls/exams\";import PageTitle from\"../../../components/PageTitle\";import{useNavigate,useParams}from\"react-router-dom\";import{useDispatch}from\"react-redux\";import{HideLoading,ShowLoading}from\"../../../redux/loaderSlice\";import{Tabs}from\"antd\";import AddEditQuestion from\"./AddEditQuestion\";import{primarySubjects,secondarySubjects,advanceSubjects}from\"../../../data/Subjects\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{jsx as _jsx}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";const{TabPane}=Tabs;function AddEditExam(){var _examData$questions;const dispatch=useDispatch();const navigate=useNavigate();const[examData,setExamData]=useState(null);const[level,setLevel]=useState('');const[showAddEditQuestionModal,setShowAddEditQuestionModal]=useState(false);const[selectedQuestion,setSelectedQuestion]=useState(null);const[classValue,setClassValue]=useState('');const params=useParams();const onFinish=async values=>{try{dispatch(ShowLoading());let response;if(params.id){response=await editExamById(_objectSpread(_objectSpread({},values),{},{examId:params.id}));}else{response=await addExam(values);}if(response.success){message.success(response.message);// Dispatch event to notify other components about new exam creation\nif(!params.id){var _response$data,_response$data2;// Only for new exams, not edits\nwindow.dispatchEvent(new CustomEvent('newExamCreated',{detail:{examName:values.name,level:values.level,timestamp:Date.now()}}));// For new exams, navigate to edit mode so user can add questions\nconst newExamId=((_response$data=response.data)===null||_response$data===void 0?void 0:_response$data._id)||((_response$data2=response.data)===null||_response$data2===void 0?void 0:_response$data2.id);if(newExamId){dispatch(HideLoading());// Hide loading before navigation\nnavigate(\"/admin/exams/edit/\".concat(newExamId));return;// Don't continue with the rest of the function\n}}// For edits, stay on the same page and refresh data\nif(params.id){getExamData();// Refresh the exam data\n}}else{message.error(response.message);}dispatch(HideLoading());}catch(error){dispatch(HideLoading());message.error(error.message);}};const getExamData=async()=>{try{var _response$data3,_response$data4;dispatch(ShowLoading());// Get user data from localStorage for the API call\nconst user=JSON.parse(localStorage.getItem(\"user\"));const response=await getExamById({examId:params.id,userId:user===null||user===void 0?void 0:user._id// Add userId for backend validation\n});setClassValue(response===null||response===void 0?void 0:(_response$data3=response.data)===null||_response$data3===void 0?void 0:_response$data3.class);setLevel(response===null||response===void 0?void 0:(_response$data4=response.data)===null||_response$data4===void 0?void 0:_response$data4.level);dispatch(HideLoading());if(response.success){setExamData(response.data);}else{message.error(response.message);}}catch(error){dispatch(HideLoading());message.error(error.message);}};useEffect(()=>{if(params.id){getExamData();}},[]);const deleteQuestion=async questionId=>{try{dispatch(ShowLoading());const response=await deleteQuestionById({questionId,examId:params.id});dispatch(HideLoading());if(response.success){message.success(response.message);getExamData();}else{message.error(response.message);}}catch(error){dispatch(HideLoading());message.error(error.message);}};const questionsColumns=[{title:\"Question\",dataIndex:\"name\"},{title:\"Options\",dataIndex:\"options\",render:(text,record)=>{if(record!==null&&record!==void 0&&record.options&&typeof record.options==='object'&&Object.keys(record.options).length>0){return Object.keys(record.options).map(key=>/*#__PURE__*/_jsxs(\"div\",{children:[key,\": \",record.options[key]]},key));}else{return/*#__PURE__*/_jsx(\"div\",{children:\"No options available for this question.\"});}}},{title:\"Correct Answer\",dataIndex:\"correctAnswer\",render:(text,record)=>{// Handle both old (correctOption) and new (correctAnswer) formats\nconst correctAnswer=record.correctAnswer||record.correctOption;if(record.answerType===\"Free Text\"||record.type===\"fill\"||record.type===\"text\"){return/*#__PURE__*/_jsx(\"div\",{children:correctAnswer});}else{return/*#__PURE__*/_jsxs(\"div\",{children:[correctAnswer,\": \",record.options&&record.options[correctAnswer]?record.options[correctAnswer]:correctAnswer]});}}},{title:\"Source\",dataIndex:\"source\",render:(text,record)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1\",children:[record!==null&&record!==void 0&&record.isAIGenerated?/*#__PURE__*/_jsx(\"span\",{className:\"flex items-center gap-1 text-blue-600 text-sm\",children:\"\\uD83E\\uDD16 AI\"}):/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600 text-sm\",children:\"Manual\"}),((record===null||record===void 0?void 0:record.image)||(record===null||record===void 0?void 0:record.imageUrl))&&/*#__PURE__*/_jsx(\"span\",{title:\"Has Image\",children:\"\\uD83D\\uDDBC\\uFE0F\"})]})},{title:\"Action\",dataIndex:\"action\",render:(text,record)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 items-center\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800\",title:\"Edit Question\",onClick:()=>{setSelectedQuestion(record);setShowAddEditQuestionModal(true);}}),(record===null||record===void 0?void 0:record.isAIGenerated)&&!(record!==null&&record!==void 0&&record.image)&&!(record!==null&&record!==void 0&&record.imageUrl)&&/*#__PURE__*/_jsx(\"i\",{className:\"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800\",title:\"Add Image to AI Question\",onClick:()=>{setSelectedQuestion(record);setShowAddEditQuestionModal(true);}}),(record===null||record===void 0?void 0:record.isAIGenerated)&&/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-500 text-sm\",title:\"AI Generated Question\",children:\"\\uD83E\\uDD16\"}),((record===null||record===void 0?void 0:record.image)||(record===null||record===void 0?void 0:record.imageUrl))&&/*#__PURE__*/_jsx(\"span\",{className:\"text-green-500 text-sm\",title:\"Has Image\",children:\"\\uD83D\\uDDBC\\uFE0F\"}),/*#__PURE__*/_jsx(\"i\",{className:\"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800\",title:\"Delete Question\",onClick:()=>{deleteQuestion(record._id);}})]})}];const handleLevelChange=e=>{setLevel(e.target.value);setClassValue(\"\");// Reset class\n};console.log(classValue,\"classValue\");return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(PageTitle,{title:params.id?\"Edit Exam\":\"Add Exam\"}),/*#__PURE__*/_jsx(\"div\",{className:\"divider\"}),(examData||!params.id)&&/*#__PURE__*/_jsx(Form,{layout:\"vertical\",onFinish:onFinish,initialValues:examData,children:/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"1\",children:[/*#__PURE__*/_jsxs(TabPane,{tab:\"Exam Details\",children:[/*#__PURE__*/_jsxs(Row,{gutter:[10,10],children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{label:\"Exam Name\",name:\"name\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\"})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{label:\"Topic\",name:\"topic\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Enter quiz topic (e.g., Algebra, Cell Biology)\"})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{label:\"Exam Duration (Seconds)\",name:\"duration\",children:/*#__PURE__*/_jsx(\"input\",{type:\"number\"})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"level\",label:\"Level\",initialValue:\"\",children:/*#__PURE__*/_jsxs(\"select\",{value:level,onChange:handleLevelChange,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",disabled:true,children:\"Select Level\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Primary\",children:\"Primary\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Secondary\",children:\"Secondary\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Advance\",children:\"Advance\"})]})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{label:\"Category\",name:\"category\",children:/*#__PURE__*/_jsxs(\"select\",{name:\"\",id:\"\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Category\"}),level.toLowerCase()===\"primary\"&&/*#__PURE__*/_jsx(_Fragment,{children:primarySubjects.map((subject,index)=>/*#__PURE__*/_jsx(\"option\",{value:subject,children:subject},index))}),level.toLowerCase()===\"secondary\"&&/*#__PURE__*/_jsx(_Fragment,{children:secondarySubjects.map((subject,index)=>/*#__PURE__*/_jsx(\"option\",{value:subject,children:subject},index))}),level.toLowerCase()===\"advance\"&&/*#__PURE__*/_jsx(_Fragment,{children:advanceSubjects.map((subject,index)=>/*#__PURE__*/_jsx(\"option\",{value:subject,children:subject},index))})]})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{name:\"class\",label:\"Class\",initialValue:\"\",required:true,children:/*#__PURE__*/_jsxs(\"select\",{value:classValue,onChange:e=>setClassValue(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Class\"}),level.toLowerCase()===\"primary\"&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"option\",{value:\"1\",children:\"1\"}),/*#__PURE__*/_jsx(\"option\",{value:\"2\",children:\"2\"}),/*#__PURE__*/_jsx(\"option\",{value:\"3\",children:\"3\"}),/*#__PURE__*/_jsx(\"option\",{value:\"4\",children:\"4\"}),/*#__PURE__*/_jsx(\"option\",{value:\"5\",children:\"5\"}),/*#__PURE__*/_jsx(\"option\",{value:\"6\",children:\"6\"}),/*#__PURE__*/_jsx(\"option\",{value:\"7\",children:\"7\"})]}),level.toLowerCase()===\"secondary\"&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"option\",{value:\"Form-1\",children:\"Form-1\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Form-2\",children:\"Form-2\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Form-3\",children:\"Form-3\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Form-4\",children:\"Form-4\"})]}),level.toLowerCase()===\"advance\"&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"option\",{value:\"Form-5\",children:\"Form-5\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Form-6\",children:\"Form-6\"})]})]})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{label:\"Total Marks\",name:\"totalMarks\",children:/*#__PURE__*/_jsx(\"input\",{type:\"number\"})})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(Form.Item,{label:\"Passing Marks\",name:\"passingMarks\",children:/*#__PURE__*/_jsx(\"input\",{type:\"number\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end gap-2\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"primary-outlined-btn\",type:\"button\",onClick:()=>navigate(\"/admin/exams\"),children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{className:\"primary-contained-btn\",type:\"submit\",children:\"Save\"})]})]},\"1\"),params.id&&/*#__PURE__*/_jsxs(TabPane,{tab:\"Questions\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold\",children:\"Exam Questions\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Add and manage questions for this exam\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"primary-contained-btn\",type:\"button\",onClick:()=>setShowAddEditQuestionModal(true),children:\"Add Question\"})]}),/*#__PURE__*/_jsx(Table,{columns:questionsColumns,dataSource:(examData===null||examData===void 0?void 0:examData.questions)||[],pagination:{pageSize:10,showSizeChanger:true,showQuickJumper:true},locale:{emptyText:(examData===null||examData===void 0?void 0:(_examData$questions=examData.questions)===null||_examData$questions===void 0?void 0:_examData$questions.length)===0?'No questions added yet. Click \"Add Question\" to add questions.':'Loading questions...'}})]},\"2\")]})}),showAddEditQuestionModal&&/*#__PURE__*/_jsx(AddEditQuestion,{setShowAddEditQuestionModal:setShowAddEditQuestionModal,showAddEditQuestionModal:showAddEditQuestionModal,examId:params.id,refreshData:getExamData,selectedQuestion:selectedQuestion,setSelectedQuestion:setSelectedQuestion})]});}export default AddEditExam;", "map": {"version": 3, "names": ["Col", "Form", "message", "Row", "Select", "Table", "React", "useEffect", "useState", "addExam", "deleteQuestionById", "editExamById", "getExamById", "Page<PERSON><PERSON>le", "useNavigate", "useParams", "useDispatch", "HideLoading", "ShowLoading", "Tabs", "AddEditQuestion", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "TabPane", "AddEditExam", "_examData$questions", "dispatch", "navigate", "examData", "setExamData", "level", "setLevel", "showAddEditQuestionModal", "setShowAddEditQuestionModal", "selectedQuestion", "setSelectedQuestion", "classValue", "setClassValue", "params", "onFinish", "values", "response", "id", "_objectSpread", "examId", "success", "_response$data", "_response$data2", "window", "dispatchEvent", "CustomEvent", "detail", "examName", "name", "timestamp", "Date", "now", "newExamId", "data", "_id", "concat", "getExamData", "error", "_response$data3", "_response$data4", "user", "JSON", "parse", "localStorage", "getItem", "userId", "class", "deleteQuestion", "questionId", "questionsColumns", "title", "dataIndex", "render", "text", "record", "options", "Object", "keys", "length", "map", "key", "children", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "answerType", "type", "className", "isAIGenerated", "image", "imageUrl", "onClick", "handleLevelChange", "e", "target", "value", "console", "log", "layout", "initialValues", "defaultActiveKey", "tab", "gutter", "span", "<PERSON><PERSON>", "label", "placeholder", "initialValue", "onChange", "disabled", "toLowerCase", "subject", "index", "required", "columns", "dataSource", "questions", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "locale", "emptyText", "refreshData"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Exams/AddEditExam.js"], "sourcesContent": ["import { Col, Form, message, Row, Select, Table } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  addExam,\r\n  deleteQuestionById,\r\n  editExamById,\r\n  getExamById,\r\n} from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Tabs } from \"antd\";\r\nimport AddEditQuestion from \"./AddEditQuestion\";\r\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\r\nconst { TabPane } = Tabs;\r\n\r\nfunction AddEditExam() {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [examData, setExamData] = useState(null);\r\n  const [level, setLevel] = useState('');\r\n  const [showAddEditQuestionModal, setShowAddEditQuestionModal] = useState(false);\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [classValue, setClassValue] = useState('');\r\n  const params = useParams();\r\n\r\n\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      let response;\r\n\r\n      if (params.id) {\r\n        response = await editExamById({\r\n          ...values,\r\n          examId: params.id,\r\n        });\r\n      } else {\r\n        response = await addExam(values);\r\n      }\r\n      if (response.success) {\r\n        message.success(response.message);\r\n\r\n        // Dispatch event to notify other components about new exam creation\r\n        if (!params.id) { // Only for new exams, not edits\r\n          window.dispatchEvent(new CustomEvent('newExamCreated', {\r\n            detail: {\r\n              examName: values.name,\r\n              level: values.level,\r\n              timestamp: Date.now()\r\n            }\r\n          }));\r\n\r\n          // For new exams, navigate to edit mode so user can add questions\r\n          const newExamId = response.data?._id || response.data?.id;\r\n          if (newExamId) {\r\n            dispatch(HideLoading()); // Hide loading before navigation\r\n            navigate(`/admin/exams/edit/${newExamId}`);\r\n            return; // Don't continue with the rest of the function\r\n          }\r\n        }\r\n\r\n        // For edits, stay on the same page and refresh data\r\n        if (params.id) {\r\n          getExamData(); // Refresh the exam data\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getExamData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user data from localStorage for the API call\r\n      const user = JSON.parse(localStorage.getItem(\"user\"));\r\n\r\n      const response = await getExamById({\r\n        examId: params.id,\r\n        userId: user?._id, // Add userId for backend validation\r\n      });\r\n\r\n      setClassValue(response?.data?.class);\r\n      setLevel(response?.data?.level);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExamData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, []);\r\n\r\n  const deleteQuestion = async (questionId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteQuestionById({\r\n        questionId,\r\n        examId: params.id\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const questionsColumns = [\r\n    {\r\n      title: \"Question\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Options\",\r\n      dataIndex: \"options\",\r\n      render: (text, record) => {\r\n        if (record?.options && typeof record.options === 'object' && Object.keys(record.options).length > 0) {\r\n          return Object.keys(record.options).map((key) => (\r\n            <div key={key}>\r\n              {key}: {record.options[key]}\r\n            </div>\r\n          ));\r\n        } else {\r\n          return <div>No options available for this question.</div>;\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Correct Answer\",\r\n      dataIndex: \"correctAnswer\",\r\n      render: (text, record) => {\r\n        // Handle both old (correctOption) and new (correctAnswer) formats\r\n        const correctAnswer = record.correctAnswer || record.correctOption;\r\n\r\n        if (record.answerType === \"Free Text\" || record.type === \"fill\" || record.type === \"text\") {\r\n          return <div>{correctAnswer}</div>;\r\n        } else {\r\n          return (\r\n            <div>\r\n              {correctAnswer}: {record.options && record.options[correctAnswer] ? record.options[correctAnswer] : correctAnswer}\r\n            </div>\r\n          );\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center gap-1\">\r\n          {record?.isAIGenerated ? (\r\n            <span className=\"flex items-center gap-1 text-blue-600 text-sm\">\r\n              🤖 AI\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-gray-600 text-sm\">Manual</span>\r\n          )}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span title=\"Has Image\">🖼️</span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2 items-center\">\r\n          {/* Edit Button */}\r\n          <i\r\n            className=\"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800\"\r\n            title=\"Edit Question\"\r\n            onClick={() => {\r\n              setSelectedQuestion(record);\r\n              setShowAddEditQuestionModal(true);\r\n            }}\r\n          ></i>\r\n\r\n          {/* Add Image Button for AI-generated questions without images */}\r\n          {record?.isAIGenerated && !record?.image && !record?.imageUrl && (\r\n            <i\r\n              className=\"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800\"\r\n              title=\"Add Image to AI Question\"\r\n              onClick={() => {\r\n                setSelectedQuestion(record);\r\n                setShowAddEditQuestionModal(true);\r\n              }}\r\n            ></i>\r\n          )}\r\n\r\n          {/* AI Generated Indicator */}\r\n          {record?.isAIGenerated && (\r\n            <span\r\n              className=\"text-blue-500 text-sm\"\r\n              title=\"AI Generated Question\"\r\n            >\r\n              🤖\r\n            </span>\r\n          )}\r\n\r\n          {/* Image Indicator */}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span\r\n              className=\"text-green-500 text-sm\"\r\n              title=\"Has Image\"\r\n            >\r\n              🖼️\r\n            </span>\r\n          )}\r\n\r\n          {/* Delete Button */}\r\n          <i\r\n            className=\"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800\"\r\n            title=\"Delete Question\"\r\n            onClick={() => {\r\n              deleteQuestion(record._id);\r\n            }}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const handleLevelChange = (e) => {\r\n    setLevel(e.target.value);\r\n    setClassValue(\"\"); // Reset class\r\n  };\r\n\r\n  console.log(classValue, \"classValue\")\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title={params.id ? \"Edit Exam\" : \"Add Exam\"} />\r\n      <div className=\"divider\"></div>\r\n\r\n      {(examData || !params.id) && (\r\n        <Form layout=\"vertical\" onFinish={onFinish} initialValues={examData}>\r\n          <Tabs defaultActiveKey=\"1\">\r\n            <TabPane tab=\"Exam Details\" key=\"1\">\r\n              <Row gutter={[10, 10]}>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Name\" name=\"name\">\r\n                    <input type=\"text\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Topic\" name=\"topic\">\r\n                    <input type=\"text\" placeholder=\"Enter quiz topic (e.g., Algebra, Cell Biology)\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Duration (Seconds)\" name=\"duration\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n\r\n\r\n                <Col span={8}>\r\n                  <Form.Item name=\"level\" label=\"Level\" initialValue=\"\">\r\n                    <select value={level} onChange={handleLevelChange}   >\r\n                      <option value=\"\" disabled >\r\n                        Select Level\r\n                      </option>\r\n                      <option value=\"Primary\">Primary</option>\r\n                      <option value=\"Secondary\">Secondary</option>\r\n                      <option value=\"Advance\">Advance</option>\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Category\" name=\"category\">\r\n                    <select name=\"\" id=\"\">\r\n                      <option value=\"\">Select Category</option>\r\n                      {level.toLowerCase() === \"primary\" && (\r\n                        <>\r\n                          {primarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"secondary\" && (\r\n                        <>\r\n                          {secondarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"advance\" && (\r\n                        <>\r\n                          {advanceSubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n\r\n                  <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" required>\r\n                    <select value={classValue} onChange={(e) => setClassValue(e.target.value)}>\r\n                      <option value=\"\"  >\r\n                        Select Class\r\n                      </option>\r\n                      {level.toLowerCase() === \"primary\" && (\r\n                        <>\r\n                          <option value=\"1\">1</option>\r\n                          <option value=\"2\">2</option>\r\n                          <option value=\"3\">3</option>\r\n                          <option value=\"4\">4</option>\r\n                          <option value=\"5\">5</option>\r\n                          <option value=\"6\">6</option>\r\n                          <option value=\"7\">7</option>\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"secondary\" && (\r\n                        <>\r\n                          <option value=\"Form-1\">Form-1</option>\r\n                          <option value=\"Form-2\">Form-2</option>\r\n                          <option value=\"Form-3\">Form-3</option>\r\n                          <option value=\"Form-4\">Form-4</option>\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"advance\" && (\r\n                        <>\r\n                          <option value=\"Form-5\">Form-5</option>\r\n                          <option value=\"Form-6\">Form-6</option>\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Total Marks\" name=\"totalMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Passing Marks\" name=\"passingMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n              <div className=\"flex justify-end gap-2\">\r\n                <button\r\n                  className=\"primary-outlined-btn\"\r\n                  type=\"button\"\r\n                  onClick={() => navigate(\"/admin/exams\")}\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button className=\"primary-contained-btn\" type=\"submit\">\r\n                  Save\r\n                </button>\r\n              </div>\r\n            </TabPane>\r\n            {params.id && (\r\n              <TabPane tab=\"Questions\" key=\"2\">\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold\">Exam Questions</h3>\r\n                    <p className=\"text-gray-600\">Add and manage questions for this exam</p>\r\n                  </div>\r\n                  <button\r\n                    className=\"primary-contained-btn\"\r\n                    type=\"button\"\r\n                    onClick={() => setShowAddEditQuestionModal(true)}\r\n                  >\r\n                    Add Question\r\n                  </button>\r\n                </div>\r\n\r\n                <Table\r\n                  columns={questionsColumns}\r\n                  dataSource={examData?.questions || []}\r\n                  pagination={{\r\n                    pageSize: 10,\r\n                    showSizeChanger: true,\r\n                    showQuickJumper: true,\r\n                  }}\r\n                  locale={{\r\n                    emptyText: examData?.questions?.length === 0 ?\r\n                      'No questions added yet. Click \"Add Question\" to add questions.' :\r\n                      'Loading questions...'\r\n                  }}\r\n                />\r\n              </TabPane>\r\n            )}\r\n          </Tabs>\r\n        </Form>\r\n      )}\r\n\r\n      {showAddEditQuestionModal && (\r\n        <AddEditQuestion\r\n          setShowAddEditQuestionModal={setShowAddEditQuestionModal}\r\n          showAddEditQuestionModal={showAddEditQuestionModal}\r\n          examId={params.id}\r\n          refreshData={getExamData}\r\n          selectedQuestion={selectedQuestion}\r\n          setSelectedQuestion={setSelectedQuestion}\r\n        />\r\n      )}\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AddEditExam;"], "mappings": "+HAAA,OAASA,GAAG,CAAEC,IAAI,CAAEC,OAAO,CAAEC,GAAG,CAAEC,MAAM,CAAEC,KAAK,KAAQ,MAAM,CAC7D,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OACEC,OAAO,CACPC,kBAAkB,CAClBC,YAAY,CACZC,WAAW,KACN,yBAAyB,CAChC,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,OAASC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CAEzD,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,WAAW,CAAEC,WAAW,KAAQ,4BAA4B,CACrE,OAASC,IAAI,KAAQ,MAAM,CAC3B,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,OAASC,eAAe,CAAEC,iBAAiB,CAAEC,eAAe,KAAQ,wBAAwB,CAAC,OAAAC,IAAA,IAAAC,KAAA,gCAAAC,GAAA,IAAAC,IAAA,gCAAAC,QAAA,IAAAC,SAAA,yBAC7F,KAAM,CAAEC,OAAQ,CAAC,CAAGX,IAAI,CAExB,QAAS,CAAAY,WAAWA,CAAA,CAAG,KAAAC,mBAAA,CACrB,KAAM,CAAAC,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkB,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACqB,QAAQ,CAAEC,WAAW,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAAC6B,KAAK,CAAEC,QAAQ,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC+B,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAC/E,KAAM,CAACiC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlC,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAACmC,UAAU,CAAEC,aAAa,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAAqC,MAAM,CAAG9B,SAAS,CAAC,CAAC,CAI1B,KAAM,CAAA+B,QAAQ,CAAG,KAAO,CAAAC,MAAM,EAAK,CACjC,GAAI,CACFd,QAAQ,CAACf,WAAW,CAAC,CAAC,CAAC,CACvB,GAAI,CAAA8B,QAAQ,CAEZ,GAAIH,MAAM,CAACI,EAAE,CAAE,CACbD,QAAQ,CAAG,KAAM,CAAArC,YAAY,CAAAuC,aAAA,CAAAA,aAAA,IACxBH,MAAM,MACTI,MAAM,CAAEN,MAAM,CAACI,EAAE,EAClB,CAAC,CACJ,CAAC,IAAM,CACLD,QAAQ,CAAG,KAAM,CAAAvC,OAAO,CAACsC,MAAM,CAAC,CAClC,CACA,GAAIC,QAAQ,CAACI,OAAO,CAAE,CACpBlD,OAAO,CAACkD,OAAO,CAACJ,QAAQ,CAAC9C,OAAO,CAAC,CAEjC;AACA,GAAI,CAAC2C,MAAM,CAACI,EAAE,CAAE,KAAAI,cAAA,CAAAC,eAAA,CAAE;AAChBC,MAAM,CAACC,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,gBAAgB,CAAE,CACrDC,MAAM,CAAE,CACNC,QAAQ,CAAEZ,MAAM,CAACa,IAAI,CACrBvB,KAAK,CAAEU,MAAM,CAACV,KAAK,CACnBwB,SAAS,CAAEC,IAAI,CAACC,GAAG,CAAC,CACtB,CACF,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAC,SAAS,CAAG,EAAAX,cAAA,CAAAL,QAAQ,CAACiB,IAAI,UAAAZ,cAAA,iBAAbA,cAAA,CAAea,GAAG,KAAAZ,eAAA,CAAIN,QAAQ,CAACiB,IAAI,UAAAX,eAAA,iBAAbA,eAAA,CAAeL,EAAE,EACzD,GAAIe,SAAS,CAAE,CACb/B,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC,CAAE;AACzBiB,QAAQ,sBAAAiC,MAAA,CAAsBH,SAAS,CAAE,CAAC,CAC1C,OAAQ;AACV,CACF,CAEA;AACA,GAAInB,MAAM,CAACI,EAAE,CAAE,CACbmB,WAAW,CAAC,CAAC,CAAE;AACjB,CACF,CAAC,IAAM,CACLlE,OAAO,CAACmE,KAAK,CAACrB,QAAQ,CAAC9C,OAAO,CAAC,CACjC,CACA+B,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC,CACzB,CAAE,MAAOoD,KAAK,CAAE,CACdpC,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC,CACvBf,OAAO,CAACmE,KAAK,CAACA,KAAK,CAACnE,OAAO,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAkE,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,KAAAE,eAAA,CAAAC,eAAA,CACFtC,QAAQ,CAACf,WAAW,CAAC,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAsD,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAErD,KAAM,CAAA5B,QAAQ,CAAG,KAAM,CAAApC,WAAW,CAAC,CACjCuC,MAAM,CAAEN,MAAM,CAACI,EAAE,CACjB4B,MAAM,CAAEL,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEN,GAAK;AACrB,CAAC,CAAC,CAEFtB,aAAa,CAACI,QAAQ,SAARA,QAAQ,kBAAAsB,eAAA,CAARtB,QAAQ,CAAEiB,IAAI,UAAAK,eAAA,iBAAdA,eAAA,CAAgBQ,KAAK,CAAC,CACpCxC,QAAQ,CAACU,QAAQ,SAARA,QAAQ,kBAAAuB,eAAA,CAARvB,QAAQ,CAAEiB,IAAI,UAAAM,eAAA,iBAAdA,eAAA,CAAgBlC,KAAK,CAAC,CAC/BJ,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC,CACvB,GAAI+B,QAAQ,CAACI,OAAO,CAAE,CACpBhB,WAAW,CAACY,QAAQ,CAACiB,IAAI,CAAC,CAC5B,CAAC,IAAM,CACL/D,OAAO,CAACmE,KAAK,CAACrB,QAAQ,CAAC9C,OAAO,CAAC,CACjC,CACF,CAAE,MAAOmE,KAAK,CAAE,CACdpC,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC,CACvBf,OAAO,CAACmE,KAAK,CAACA,KAAK,CAACnE,OAAO,CAAC,CAC9B,CACF,CAAC,CAEDK,SAAS,CAAC,IAAM,CACd,GAAIsC,MAAM,CAACI,EAAE,CAAE,CACbmB,WAAW,CAAC,CAAC,CACf,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAW,cAAc,CAAG,KAAO,CAAAC,UAAU,EAAK,CAC3C,GAAI,CACF/C,QAAQ,CAACf,WAAW,CAAC,CAAC,CAAC,CACvB,KAAM,CAAA8B,QAAQ,CAAG,KAAM,CAAAtC,kBAAkB,CAAC,CACxCsE,UAAU,CACV7B,MAAM,CAAEN,MAAM,CAACI,EACjB,CAAC,CAAC,CACFhB,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC,CACvB,GAAI+B,QAAQ,CAACI,OAAO,CAAE,CACpBlD,OAAO,CAACkD,OAAO,CAACJ,QAAQ,CAAC9C,OAAO,CAAC,CACjCkE,WAAW,CAAC,CAAC,CACf,CAAC,IAAM,CACLlE,OAAO,CAACmE,KAAK,CAACrB,QAAQ,CAAC9C,OAAO,CAAC,CACjC,CACF,CAAE,MAAOmE,KAAK,CAAE,CACdpC,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC,CACvBf,OAAO,CAACmE,KAAK,CAACA,KAAK,CAACnE,OAAO,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAA+E,gBAAgB,CAAG,CACvB,CACEC,KAAK,CAAE,UAAU,CACjBC,SAAS,CAAE,MACb,CAAC,CACD,CACED,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,SAAS,CACpBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,GAAK,CACxB,GAAIA,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEC,OAAO,EAAI,MAAO,CAAAD,MAAM,CAACC,OAAO,GAAK,QAAQ,EAAIC,MAAM,CAACC,IAAI,CAACH,MAAM,CAACC,OAAO,CAAC,CAACG,MAAM,CAAG,CAAC,CAAE,CACnG,MAAO,CAAAF,MAAM,CAACC,IAAI,CAACH,MAAM,CAACC,OAAO,CAAC,CAACI,GAAG,CAAEC,GAAG,eACzCnE,KAAA,QAAAoE,QAAA,EACGD,GAAG,CAAC,IAAE,CAACN,MAAM,CAACC,OAAO,CAACK,GAAG,CAAC,GADnBA,GAEL,CACN,CAAC,CACJ,CAAC,IAAM,CACL,mBAAOjE,IAAA,QAAAkE,QAAA,CAAK,yCAAuC,CAAK,CAAC,CAC3D,CACF,CACF,CAAC,CACD,CACEX,KAAK,CAAE,gBAAgB,CACvBC,SAAS,CAAE,eAAe,CAC1BC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,GAAK,CACxB;AACA,KAAM,CAAAQ,aAAa,CAAGR,MAAM,CAACQ,aAAa,EAAIR,MAAM,CAACS,aAAa,CAElE,GAAIT,MAAM,CAACU,UAAU,GAAK,WAAW,EAAIV,MAAM,CAACW,IAAI,GAAK,MAAM,EAAIX,MAAM,CAACW,IAAI,GAAK,MAAM,CAAE,CACzF,mBAAOtE,IAAA,QAAAkE,QAAA,CAAMC,aAAa,CAAM,CAAC,CACnC,CAAC,IAAM,CACL,mBACErE,KAAA,QAAAoE,QAAA,EACGC,aAAa,CAAC,IAAE,CAACR,MAAM,CAACC,OAAO,EAAID,MAAM,CAACC,OAAO,CAACO,aAAa,CAAC,CAAGR,MAAM,CAACC,OAAO,CAACO,aAAa,CAAC,CAAGA,aAAa,EAC9G,CAAC,CAEV,CACF,CACF,CAAC,CACD,CACEZ,KAAK,CAAE,QAAQ,CACfC,SAAS,CAAE,QAAQ,CACnBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,gBACnB7D,KAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAL,QAAA,EACrCP,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEa,aAAa,cACpBxE,IAAA,SAAMuE,SAAS,CAAC,+CAA+C,CAAAL,QAAA,CAAC,iBAEhE,CAAM,CAAC,cAEPlE,IAAA,SAAMuE,SAAS,CAAC,uBAAuB,CAAAL,QAAA,CAAC,QAAM,CAAM,CACrD,CACA,CAAC,CAAAP,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEc,KAAK,IAAId,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEe,QAAQ,iBACjC1E,IAAA,SAAMuD,KAAK,CAAC,WAAW,CAAAW,QAAA,CAAC,oBAAG,CAAM,CAClC,EACE,CAET,CAAC,CACD,CACEX,KAAK,CAAE,QAAQ,CACfC,SAAS,CAAE,QAAQ,CACnBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,gBACnB7D,KAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAL,QAAA,eAEtClE,IAAA,MACEuE,SAAS,CAAC,iEAAiE,CAC3EhB,KAAK,CAAC,eAAe,CACrBoB,OAAO,CAAEA,CAAA,GAAM,CACb5D,mBAAmB,CAAC4C,MAAM,CAAC,CAC3B9C,2BAA2B,CAAC,IAAI,CAAC,CACnC,CAAE,CACA,CAAC,CAGJ,CAAA8C,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEa,aAAa,GAAI,EAACb,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEc,KAAK,GAAI,EAACd,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEe,QAAQ,gBAC3D1E,IAAA,MACEuE,SAAS,CAAC,sEAAsE,CAChFhB,KAAK,CAAC,0BAA0B,CAChCoB,OAAO,CAAEA,CAAA,GAAM,CACb5D,mBAAmB,CAAC4C,MAAM,CAAC,CAC3B9C,2BAA2B,CAAC,IAAI,CAAC,CACnC,CAAE,CACA,CACL,CAGA,CAAA8C,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEa,aAAa,gBACpBxE,IAAA,SACEuE,SAAS,CAAC,uBAAuB,CACjChB,KAAK,CAAC,uBAAuB,CAAAW,QAAA,CAC9B,cAED,CAAM,CACP,CAGA,CAAC,CAAAP,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEc,KAAK,IAAId,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEe,QAAQ,iBACjC1E,IAAA,SACEuE,SAAS,CAAC,wBAAwB,CAClChB,KAAK,CAAC,WAAW,CAAAW,QAAA,CAClB,oBAED,CAAM,CACP,cAGDlE,IAAA,MACEuE,SAAS,CAAC,mEAAmE,CAC7EhB,KAAK,CAAC,iBAAiB,CACvBoB,OAAO,CAAEA,CAAA,GAAM,CACbvB,cAAc,CAACO,MAAM,CAACpB,GAAG,CAAC,CAC5B,CAAE,CACA,CAAC,EACF,CAET,CAAC,CACF,CAED,KAAM,CAAAqC,iBAAiB,CAAIC,CAAC,EAAK,CAC/BlE,QAAQ,CAACkE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CACxB9D,aAAa,CAAC,EAAE,CAAC,CAAE;AACrB,CAAC,CAED+D,OAAO,CAACC,GAAG,CAACjE,UAAU,CAAE,YAAY,CAAC,CAIrC,mBACElB,KAAA,QAAAoE,QAAA,eACElE,IAAA,CAACd,SAAS,EAACqE,KAAK,CAAErC,MAAM,CAACI,EAAE,CAAG,WAAW,CAAG,UAAW,CAAE,CAAC,cAC1DtB,IAAA,QAAKuE,SAAS,CAAC,SAAS,CAAM,CAAC,CAE9B,CAAC/D,QAAQ,EAAI,CAACU,MAAM,CAACI,EAAE,gBACtBtB,IAAA,CAAC1B,IAAI,EAAC4G,MAAM,CAAC,UAAU,CAAC/D,QAAQ,CAAEA,QAAS,CAACgE,aAAa,CAAE3E,QAAS,CAAA0D,QAAA,cAClEpE,KAAA,CAACN,IAAI,EAAC4F,gBAAgB,CAAC,GAAG,CAAAlB,QAAA,eACxBpE,KAAA,CAACK,OAAO,EAACkF,GAAG,CAAC,cAAc,CAAAnB,QAAA,eACzBpE,KAAA,CAACtB,GAAG,EAAC8G,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAApB,QAAA,eACpBlE,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cACXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACC,KAAK,CAAC,WAAW,CAACxD,IAAI,CAAC,MAAM,CAAAiC,QAAA,cACtClE,IAAA,UAAOsE,IAAI,CAAC,MAAM,CAAE,CAAC,CACZ,CAAC,CACT,CAAC,cACNtE,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cACXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACC,KAAK,CAAC,OAAO,CAACxD,IAAI,CAAC,OAAO,CAAAiC,QAAA,cACnClE,IAAA,UAAOsE,IAAI,CAAC,MAAM,CAACoB,WAAW,CAAC,gDAAgD,CAAE,CAAC,CACzE,CAAC,CACT,CAAC,cACN1F,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cACXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACC,KAAK,CAAC,yBAAyB,CAACxD,IAAI,CAAC,UAAU,CAAAiC,QAAA,cACxDlE,IAAA,UAAOsE,IAAI,CAAC,QAAQ,CAAE,CAAC,CACd,CAAC,CACT,CAAC,cAINtE,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cACXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACvD,IAAI,CAAC,OAAO,CAACwD,KAAK,CAAC,OAAO,CAACE,YAAY,CAAC,EAAE,CAAAzB,QAAA,cACnDpE,KAAA,WAAQiF,KAAK,CAAErE,KAAM,CAACkF,QAAQ,CAAEhB,iBAAkB,CAAAV,QAAA,eAChDlE,IAAA,WAAQ+E,KAAK,CAAC,EAAE,CAACc,QAAQ,MAAA3B,QAAA,CAAE,cAE3B,CAAQ,CAAC,cACTlE,IAAA,WAAQ+E,KAAK,CAAC,SAAS,CAAAb,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxClE,IAAA,WAAQ+E,KAAK,CAAC,WAAW,CAAAb,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5ClE,IAAA,WAAQ+E,KAAK,CAAC,SAAS,CAAAb,QAAA,CAAC,SAAO,CAAQ,CAAC,EAClC,CAAC,CACA,CAAC,CACT,CAAC,cAENlE,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cACXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACC,KAAK,CAAC,UAAU,CAACxD,IAAI,CAAC,UAAU,CAAAiC,QAAA,cACzCpE,KAAA,WAAQmC,IAAI,CAAC,EAAE,CAACX,EAAE,CAAC,EAAE,CAAA4C,QAAA,eACnBlE,IAAA,WAAQ+E,KAAK,CAAC,EAAE,CAAAb,QAAA,CAAC,iBAAe,CAAQ,CAAC,CACxCxD,KAAK,CAACoF,WAAW,CAAC,CAAC,GAAK,SAAS,eAChC9F,IAAA,CAAAE,SAAA,EAAAgE,QAAA,CACGxE,eAAe,CAACsE,GAAG,CAAC,CAAC+B,OAAO,CAAEC,KAAK,gBAClChG,IAAA,WAAoB+E,KAAK,CAAEgB,OAAQ,CAAA7B,QAAA,CAChC6B,OAAO,EADGC,KAEL,CACT,CAAC,CACF,CACH,CACAtF,KAAK,CAACoF,WAAW,CAAC,CAAC,GAAK,WAAW,eAClC9F,IAAA,CAAAE,SAAA,EAAAgE,QAAA,CACGvE,iBAAiB,CAACqE,GAAG,CAAC,CAAC+B,OAAO,CAAEC,KAAK,gBACpChG,IAAA,WAAoB+E,KAAK,CAAEgB,OAAQ,CAAA7B,QAAA,CAChC6B,OAAO,EADGC,KAEL,CACT,CAAC,CACF,CACH,CACAtF,KAAK,CAACoF,WAAW,CAAC,CAAC,GAAK,SAAS,eAChC9F,IAAA,CAAAE,SAAA,EAAAgE,QAAA,CACGtE,eAAe,CAACoE,GAAG,CAAC,CAAC+B,OAAO,CAAEC,KAAK,gBAClChG,IAAA,WAAoB+E,KAAK,CAAEgB,OAAQ,CAAA7B,QAAA,CAChC6B,OAAO,EADGC,KAEL,CACT,CAAC,CACF,CACH,EACK,CAAC,CACA,CAAC,CACT,CAAC,cAENhG,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cAEXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACvD,IAAI,CAAC,OAAO,CAACwD,KAAK,CAAC,OAAO,CAACE,YAAY,CAAC,EAAE,CAACM,QAAQ,MAAA/B,QAAA,cAC5DpE,KAAA,WAAQiF,KAAK,CAAE/D,UAAW,CAAC4E,QAAQ,CAAGf,CAAC,EAAK5D,aAAa,CAAC4D,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAAAb,QAAA,eACxElE,IAAA,WAAQ+E,KAAK,CAAC,EAAE,CAAAb,QAAA,CAAG,cAEnB,CAAQ,CAAC,CACRxD,KAAK,CAACoF,WAAW,CAAC,CAAC,GAAK,SAAS,eAChChG,KAAA,CAAAI,SAAA,EAAAgE,QAAA,eACElE,IAAA,WAAQ+E,KAAK,CAAC,GAAG,CAAAb,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlE,IAAA,WAAQ+E,KAAK,CAAC,GAAG,CAAAb,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlE,IAAA,WAAQ+E,KAAK,CAAC,GAAG,CAAAb,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlE,IAAA,WAAQ+E,KAAK,CAAC,GAAG,CAAAb,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlE,IAAA,WAAQ+E,KAAK,CAAC,GAAG,CAAAb,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlE,IAAA,WAAQ+E,KAAK,CAAC,GAAG,CAAAb,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlE,IAAA,WAAQ+E,KAAK,CAAC,GAAG,CAAAb,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5B,CACH,CACAxD,KAAK,CAACoF,WAAW,CAAC,CAAC,GAAK,WAAW,eAClChG,KAAA,CAAAI,SAAA,EAAAgE,QAAA,eACElE,IAAA,WAAQ+E,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtClE,IAAA,WAAQ+E,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtClE,IAAA,WAAQ+E,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtClE,IAAA,WAAQ+E,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,EACtC,CACH,CACAxD,KAAK,CAACoF,WAAW,CAAC,CAAC,GAAK,SAAS,eAChChG,KAAA,CAAAI,SAAA,EAAAgE,QAAA,eACElE,IAAA,WAAQ+E,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtClE,IAAA,WAAQ+E,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,EACtC,CACH,EACK,CAAC,CACA,CAAC,CACT,CAAC,cACNlE,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cACXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACC,KAAK,CAAC,aAAa,CAACxD,IAAI,CAAC,YAAY,CAAAiC,QAAA,cAC9ClE,IAAA,UAAOsE,IAAI,CAAC,QAAQ,CAAE,CAAC,CACd,CAAC,CACT,CAAC,cACNtE,IAAA,CAAC3B,GAAG,EAACkH,IAAI,CAAE,CAAE,CAAArB,QAAA,cACXlE,IAAA,CAAC1B,IAAI,CAACkH,IAAI,EAACC,KAAK,CAAC,eAAe,CAACxD,IAAI,CAAC,cAAc,CAAAiC,QAAA,cAClDlE,IAAA,UAAOsE,IAAI,CAAC,QAAQ,CAAE,CAAC,CACd,CAAC,CACT,CAAC,EACH,CAAC,cACNxE,KAAA,QAAKyE,SAAS,CAAC,wBAAwB,CAAAL,QAAA,eACrClE,IAAA,WACEuE,SAAS,CAAC,sBAAsB,CAChCD,IAAI,CAAC,QAAQ,CACbK,OAAO,CAAEA,CAAA,GAAMpE,QAAQ,CAAC,cAAc,CAAE,CAAA2D,QAAA,CACzC,QAED,CAAQ,CAAC,cACTlE,IAAA,WAAQuE,SAAS,CAAC,uBAAuB,CAACD,IAAI,CAAC,QAAQ,CAAAJ,QAAA,CAAC,MAExD,CAAQ,CAAC,EACN,CAAC,GA7HwB,GA8HvB,CAAC,CACThD,MAAM,CAACI,EAAE,eACRxB,KAAA,CAACK,OAAO,EAACkF,GAAG,CAAC,WAAW,CAAAnB,QAAA,eACtBpE,KAAA,QAAKyE,SAAS,CAAC,wCAAwC,CAAAL,QAAA,eACrDpE,KAAA,QAAAoE,QAAA,eACElE,IAAA,OAAIuE,SAAS,CAAC,uBAAuB,CAAAL,QAAA,CAAC,gBAAc,CAAI,CAAC,cACzDlE,IAAA,MAAGuE,SAAS,CAAC,eAAe,CAAAL,QAAA,CAAC,wCAAsC,CAAG,CAAC,EACpE,CAAC,cACNlE,IAAA,WACEuE,SAAS,CAAC,uBAAuB,CACjCD,IAAI,CAAC,QAAQ,CACbK,OAAO,CAAEA,CAAA,GAAM9D,2BAA2B,CAAC,IAAI,CAAE,CAAAqD,QAAA,CAClD,cAED,CAAQ,CAAC,EACN,CAAC,cAENlE,IAAA,CAACtB,KAAK,EACJwH,OAAO,CAAE5C,gBAAiB,CAC1B6C,UAAU,CAAE,CAAA3F,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE4F,SAAS,GAAI,EAAG,CACtCC,UAAU,CAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IACnB,CAAE,CACFC,MAAM,CAAE,CACNC,SAAS,CAAE,CAAAlG,QAAQ,SAARA,QAAQ,kBAAAH,mBAAA,CAARG,QAAQ,CAAE4F,SAAS,UAAA/F,mBAAA,iBAAnBA,mBAAA,CAAqB0D,MAAM,IAAK,CAAC,CAC1C,gEAAgE,CAChE,sBACJ,CAAE,CACH,CAAC,GA5ByB,GA6BpB,CACV,EACG,CAAC,CACH,CACP,CAEAnD,wBAAwB,eACvBZ,IAAA,CAACP,eAAe,EACdoB,2BAA2B,CAAEA,2BAA4B,CACzDD,wBAAwB,CAAEA,wBAAyB,CACnDY,MAAM,CAAEN,MAAM,CAACI,EAAG,CAClBqF,WAAW,CAAElE,WAAY,CACzB3B,gBAAgB,CAAEA,gBAAiB,CACnCC,mBAAmB,CAAEA,mBAAoB,CAC1C,CACF,EAGE,CAAC,CAEV,CAEA,cAAe,CAAAX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}