const axios = require('axios');

async function testPaymentFlow() {
  console.log('🧪 Testing actual payment flow...\n');
  
  // Test with a real user scenario
  const testPayload = {
    plan: {
      title: 'Basic Plan',
      discountedPrice: 5000,
      _id: 'test-plan-id'
    },
    userId: 'test-user-id'
  };
  
  console.log('📤 Sending payment request to server...');
  console.log('Payload:', JSON.stringify(testPayload, null, 2));
  
  try {
    const response = await axios.post('http://localhost:5000/api/payment/create-invoice', testPayload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      timeout: 10000
    });
    
    console.log('✅ Payment response:', response.data);
    
    if (response.data.success) {
      console.log('🎉 Payment initiated successfully!');
      console.log('📱 SMS should be sent to user phone number');
    }
    
  } catch (error) {
    console.log('❌ Payment error:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.response?.data?.message || error.message);
    console.log('Full error:', JSON.stringify(error.response?.data, null, 2));
    
    if (error.response?.status === 401) {
      console.log('\n🔍 This is expected - we used a test token');
      console.log('The authentication middleware is working correctly');
    }
  }
}

testPaymentFlow().catch(console.error);
