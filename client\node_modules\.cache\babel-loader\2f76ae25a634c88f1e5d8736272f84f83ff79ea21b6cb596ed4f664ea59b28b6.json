{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionModal\\\\SubscriptionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionModal = ({\n  isOpen,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  var _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = phone => {\n    const isValid = phone && /^(06|07)\\d{8}$/.test(phone);\n    console.log('🔍 Phone validation:', {\n      phone,\n      isValid\n    });\n    return isValid;\n  };\n\n  // Update user's phone number in profile - PERMANENT UPDATE\n  const updateUserPhoneNumber = async newPhone => {\n    try {\n      console.log('🔥 PERMANENTLY UPDATING USER PHONE NUMBER:', newPhone);\n      console.log('👤 Current user data:', user);\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Create complete update payload with ALL user fields\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone // THIS IS THE KEY CHANGE\n      };\n\n      console.log('📤 PERMANENT UPDATE PAYLOAD:', updatePayload);\n\n      // Use the working updateUserInfo API\n      const response = await updateUserInfo(updatePayload);\n      console.log('📥 PERMANENT UPDATE RESPONSE:', response);\n      if (response.success) {\n        console.log('🎉 PHONE NUMBER PERMANENTLY UPDATED!');\n\n        // Create updated user object\n        const updatedUser = {\n          ...user,\n          phoneNumber: newPhone // Ensure phone number is updated\n        };\n\n        console.log('👤 Updated user object:', updatedUser);\n\n        // Update Redux store\n        dispatch(SetUser(updatedUser));\n\n        // Update localStorage - THIS ENSURES PERSISTENCE AFTER REFRESH\n        localStorage.setItem('user', JSON.stringify(updatedUser));\n\n        // Update payment phone state\n        setPaymentPhone(newPhone);\n        console.log('✅ ALL UPDATES COMPLETE - PHONE NUMBER SHOULD PERSIST AFTER REFRESH');\n        console.log('📱 New phone number:', newPhone);\n\n        // Verify the save worked\n        const savedUser = JSON.parse(localStorage.getItem('user'));\n        console.log('💾 Saved to localStorage:', savedUser.phoneNumber);\n        if (savedUser.phoneNumber === newPhone) {\n          console.log('🎉 VERIFICATION SUCCESSFUL - PHONE NUMBER SAVED CORRECTLY');\n        } else {\n          console.log('⚠️ VERIFICATION FAILED - PHONE NUMBER NOT SAVED CORRECTLY');\n        }\n        return true;\n      } else {\n        console.error('❌ PERMANENT UPDATE FAILED:', response);\n        return false;\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('❌ PERMANENT UPDATE ERROR:', error);\n      console.error('❌ Error details:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      return false;\n    }\n  };\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    }\n  }, [isOpen, user === null || user === void 0 ? void 0 : user.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.phoneNumber && !isEditingPhone) {\n      console.log('🔄 User phone changed, updating payment phone:', user.phoneNumber);\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user === null || user === void 0 ? void 0 : user.phoneNumber, isEditingPhone]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = plan => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone,\n        // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n          return true;\n        }\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: [step === 'plans' && '🚀 Choose Your Learning Plan', step === 'payment' && '💳 Complete Your Payment', step === 'success' && '⏳ Processing Payment...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [step === 'plans' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading plans...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 17\n          }, this) : plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$features, _plan$features2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-card\",\n              onClick: () => handlePlanSelect(plan),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-amount\",\n                  children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 23\n                }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-original\",\n                  children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-period\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: [(_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 25\n                }, this)), ((_plan$features2 = plan.features) === null || _plan$features2 === void 0 ? void 0 : _plan$features2.length) > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: [plan.features.length - 4, \" more features\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                children: [\"Choose \", plan.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), step === 'payment' && selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-plan-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Selected Plan: \", selectedPlan.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"plan-price-summary\",\n              children: [(_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS for \", selectedPlan.duration, \" month\", selectedPlan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-label\",\n                  children: \"Phone Number for Payment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this), !isEditingPhone ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `info-value ${phoneUpdated ? 'updated' : ''}`,\n                    children: [paymentPhone || 'Not provided', phoneUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"updated-indicator\",\n                      children: \"\\u2705 Updated\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 44\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 25\n                  }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                      style: {\n                        color: '#666',\n                        fontSize: '10px',\n                        display: 'block'\n                      },\n                      children: [\"Debug: Payment=\", paymentPhone, \" | User=\", user === null || user === void 0 ? void 0 : user.phoneNumber, \" | Updated=\", phoneUpdated ? 'Yes' : 'No']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        console.log('🔄 Testing page refresh...');\n                        window.location.reload();\n                      },\n                      style: {\n                        fontSize: '10px',\n                        padding: '2px 6px',\n                        marginTop: '4px',\n                        background: '#f0f0f0',\n                        border: '1px solid #ccc',\n                        borderRadius: '4px',\n                        cursor: 'pointer'\n                      },\n                      children: \"\\uD83D\\uDD04 Test Refresh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"edit-phone-btn\",\n                    onClick: () => setIsEditingPhone(true),\n                    type: \"button\",\n                    children: \"\\u270F\\uFE0F Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-edit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    value: paymentPhone,\n                    onChange: e => setPaymentPhone(e.target.value),\n                    placeholder: \"Enter phone number (e.g., 0744963858)\",\n                    className: `phone-input ${paymentPhone ? isValidPhone(paymentPhone) ? 'valid' : 'invalid' : ''}`,\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 25\n                  }, this), paymentPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`,\n                    children: isValidPhone(paymentPhone) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message valid\",\n                      children: \"\\u2705 Valid phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message invalid\",\n                      children: \"\\u274C Must start with 06 or 07 and be 10 digits\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"phone-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"save-phone-btn\",\n                      onClick: async e => {\n                        e.preventDefault();\n                        console.log('🔥 SAVE BUTTON CLICKED!');\n                        console.log('📱 Payment phone:', paymentPhone);\n                        console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n                        if (!isValidPhone(paymentPhone)) {\n                          console.log('❌ Invalid phone number');\n                          message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                          return;\n                        }\n                        try {\n                          console.log('🔥 STARTING PERMANENT PHONE NUMBER SAVE...');\n                          console.log('📱 Phone to save:', paymentPhone);\n\n                          // Show loading state\n                          const btn = e.target;\n                          const originalText = btn.textContent;\n                          btn.textContent = '⏳ Saving...';\n                          btn.disabled = true;\n\n                          // ACTUALLY UPDATE THE USER PROFILE - NOT BACKGROUND!\n                          console.log('🔄 PERMANENTLY UPDATING USER PROFILE...');\n                          const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n                          if (updateSuccess) {\n                            console.log('🎉 PHONE NUMBER PERMANENTLY SAVED!');\n\n                            // Close editing mode\n                            setIsEditingPhone(false);\n                            setPhoneUpdated(true);\n\n                            // Show success message\n                            message.success({\n                              content: '🎉 Phone number updated permanently!',\n                              duration: 4,\n                              style: {\n                                marginTop: '20vh',\n                                fontSize: '15px',\n                                fontWeight: '600'\n                              }\n                            });\n\n                            // Additional confirmation\n                            setTimeout(() => {\n                              message.info({\n                                content: '✅ Your phone number will persist after page refresh!',\n                                duration: 4,\n                                style: {\n                                  marginTop: '20vh',\n                                  fontSize: '14px'\n                                }\n                              });\n                            }, 1500);\n                          } else {\n                            console.log('❌ PHONE NUMBER UPDATE FAILED');\n                            message.error({\n                              content: '❌ Failed to save phone number permanently. Please try again.',\n                              duration: 4\n                            });\n                          }\n\n                          // Restore button\n                          btn.textContent = originalText;\n                          btn.disabled = !isValidPhone(paymentPhone);\n\n                          // Reset the updated indicator after 8 seconds\n                          setTimeout(() => {\n                            setPhoneUpdated(false);\n                          }, 8000);\n                        } catch (error) {\n                          console.error('❌ Error saving phone number:', error);\n                          message.error('Failed to save phone number. Please try again.');\n                        }\n                      },\n                      disabled: !isValidPhone(paymentPhone),\n                      type: \"button\",\n                      children: \"\\u2705 Save\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 399,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"cancel-phone-btn\",\n                      onClick: () => {\n                        setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n                        setIsEditingPhone(false);\n                      },\n                      type: \"button\",\n                      children: \"\\u274C Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-note\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"\\uD83D\\uDCA1 This number will receive the payment SMS. You can use a different number than your profile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Payment Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: \"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"back-btn\",\n              onClick: () => setStep('plans'),\n              children: \"\\u2190 Back to Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pay-btn\",\n              onClick: e => {\n                e.preventDefault();\n                console.log('💳 PAY BUTTON CLICKED!');\n                console.log('📱 Payment phone:', paymentPhone);\n                console.log('✏️ Is editing phone:', isEditingPhone);\n                console.log('⏳ Payment loading:', paymentLoading);\n                if (isEditingPhone) {\n                  message.warning('Please save your phone number first');\n                  return;\n                }\n                if (!paymentPhone) {\n                  message.error('Please enter a phone number');\n                  return;\n                }\n                if (!isValidPhone(paymentPhone)) {\n                  message.error('Please enter a valid phone number');\n                  return;\n                }\n                handlePayment();\n              },\n              disabled: paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone),\n              children: paymentLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 23\n                }, this), \"Processing...\"]\n              }, void 0, true) : isEditingPhone ? 'Save phone number first' : !paymentPhone ? 'Enter phone number' : !isValidPhone(paymentPhone) ? 'Invalid phone number' : `Pay ${(_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString()} TZS`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this), step === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-animation\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Request Sent!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation and complete the payment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Check your phone for SMS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Follow the payment instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Your subscription will activate automatically\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"check-status-btn\",\n              onClick: async () => {\n                console.log('🔍 Manual payment check triggered');\n                try {\n                  const response = await checkPaymentStatus();\n                  console.log('📥 Manual check response:', response);\n                  if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                    console.log('✅ Payment confirmed manually!');\n                    dispatch(SetSubscription(response));\n                    message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                    onSuccess && onSuccess();\n                    setTimeout(() => onClose(), 1000);\n                  } else {\n                    message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                  }\n                } catch (error) {\n                  console.error('❌ Manual check error:', error);\n                  message.error('Error checking payment status');\n                }\n              },\n              children: \"\\u2705 Check Payment Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"done-btn\",\n              onClick: handleClose,\n              children: \"I'll complete the payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n};\n_s(SubscriptionModal, \"aZxc1xREfgbzjCdSZ68Mez4hUuk=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = SubscriptionModal;\nexport default SubscriptionModal;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "message", "getPlans", "addPayment", "checkPaymentStatus", "updateUserInfo", "axiosInstance", "SetSubscription", "SetUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionModal", "isOpen", "onClose", "onSuccess", "_s", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "paymentPhone", "setPaymentPhone", "isEditingPhone", "setIsEditingPhone", "phoneUpdated", "setPhoneUpdated", "isValidPhone", "phone", "<PERSON><PERSON><PERSON><PERSON>", "test", "console", "log", "updateUserPhoneNumber", "newPhone", "user", "_id", "error", "updatePayload", "userId", "name", "email", "school", "class_", "class", "className", "level", "phoneNumber", "response", "success", "updatedUser", "dispatch", "localStorage", "setItem", "JSON", "stringify", "savedUser", "parse", "getItem", "_error$response", "data", "state", "fetchPlans", "Array", "isArray", "handlePlanSelect", "plan", "handlePayment", "length", "_user$name", "paymentData", "userPhone", "userEmail", "replace", "toLowerCase", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "checkStatus", "paymentStatus", "status", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "warning", "handleClose", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "title", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "process", "env", "NODE_ENV", "color", "display", "window", "location", "reload", "padding", "background", "border", "borderRadius", "cursor", "type", "value", "onChange", "e", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "preventDefault", "btn", "originalText", "textContent", "disabled", "updateSuccess", "info", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionModal/SubscriptionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = (phone) => {\n    const isValid = phone && /^(06|07)\\d{8}$/.test(phone);\n    console.log('🔍 Phone validation:', { phone, isValid });\n    return isValid;\n  };\n\n  // Update user's phone number in profile - PERMANENT UPDATE\n  const updateUserPhoneNumber = async (newPhone) => {\n    try {\n      console.log('🔥 PERMANENTLY UPDATING USER PHONE NUMBER:', newPhone);\n      console.log('👤 Current user data:', user);\n\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Create complete update payload with ALL user fields\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone  // THIS IS THE KEY CHANGE\n      };\n\n      console.log('📤 PERMANENT UPDATE PAYLOAD:', updatePayload);\n\n      // Use the working updateUserInfo API\n      const response = await updateUserInfo(updatePayload);\n\n      console.log('📥 PERMANENT UPDATE RESPONSE:', response);\n\n      if (response.success) {\n        console.log('🎉 PHONE NUMBER PERMANENTLY UPDATED!');\n\n        // Create updated user object\n        const updatedUser = {\n          ...user,\n          phoneNumber: newPhone  // Ensure phone number is updated\n        };\n\n        console.log('👤 Updated user object:', updatedUser);\n\n        // Update Redux store\n        dispatch(SetUser(updatedUser));\n\n        // Update localStorage - THIS ENSURES PERSISTENCE AFTER REFRESH\n        localStorage.setItem('user', JSON.stringify(updatedUser));\n\n        // Update payment phone state\n        setPaymentPhone(newPhone);\n\n        console.log('✅ ALL UPDATES COMPLETE - PHONE NUMBER SHOULD PERSIST AFTER REFRESH');\n        console.log('📱 New phone number:', newPhone);\n\n        // Verify the save worked\n        const savedUser = JSON.parse(localStorage.getItem('user'));\n        console.log('💾 Saved to localStorage:', savedUser.phoneNumber);\n\n        if (savedUser.phoneNumber === newPhone) {\n          console.log('🎉 VERIFICATION SUCCESSFUL - PHONE NUMBER SAVED CORRECTLY');\n        } else {\n          console.log('⚠️ VERIFICATION FAILED - PHONE NUMBER NOT SAVED CORRECTLY');\n        }\n\n        return true;\n      } else {\n        console.error('❌ PERMANENT UPDATE FAILED:', response);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ PERMANENT UPDATE ERROR:', error);\n      console.error('❌ Error details:', error.response?.data);\n      return false;\n    }\n  };\n  \n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone(user?.phoneNumber || '');\n    }\n  }, [isOpen, user?.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user?.phoneNumber && !isEditingPhone) {\n      console.log('🔄 User phone changed, updating payment phone:', user.phoneNumber);\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user?.phoneNumber, isEditingPhone]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone, // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n        \n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone(user?.phoneNumber || '');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n            {step === 'success' && '⏳ Processing Payment...'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('glimp') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"phone-section\">\n                  <div className=\"info-item\">\n                    <span className=\"info-label\">Phone Number for Payment:</span>\n                    {!isEditingPhone ? (\n                      <div className=\"phone-display\">\n                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>\n                          {paymentPhone || 'Not provided'}\n                          {phoneUpdated && <span className=\"updated-indicator\">✅ Updated</span>}\n                        </span>\n                        {process.env.NODE_ENV === 'development' && (\n                          <div style={{marginTop: '8px'}}>\n                            <small style={{color: '#666', fontSize: '10px', display: 'block'}}>\n                              Debug: Payment={paymentPhone} | User={user?.phoneNumber} | Updated={phoneUpdated ? 'Yes' : 'No'}\n                            </small>\n                            <button\n                              onClick={() => {\n                                console.log('🔄 Testing page refresh...');\n                                window.location.reload();\n                              }}\n                              style={{\n                                fontSize: '10px',\n                                padding: '2px 6px',\n                                marginTop: '4px',\n                                background: '#f0f0f0',\n                                border: '1px solid #ccc',\n                                borderRadius: '4px',\n                                cursor: 'pointer'\n                              }}\n                            >\n                              🔄 Test Refresh\n                            </button>\n                          </div>\n                        )}\n                        <button\n                          className=\"edit-phone-btn\"\n                          onClick={() => setIsEditingPhone(true)}\n                          type=\"button\"\n                        >\n                          ✏️ Change\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"phone-edit\">\n                        <input\n                          type=\"tel\"\n                          value={paymentPhone}\n                          onChange={(e) => setPaymentPhone(e.target.value)}\n                          placeholder=\"Enter phone number (e.g., 0744963858)\"\n                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}\n                          maxLength=\"10\"\n                        />\n                        {paymentPhone && (\n                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>\n                            {isValidPhone(paymentPhone) ? (\n                              <span className=\"validation-message valid\">✅ Valid phone number</span>\n                            ) : (\n                              <span className=\"validation-message invalid\">❌ Must start with 06 or 07 and be 10 digits</span>\n                            )}\n                          </div>\n                        )}\n                        <div className=\"phone-actions\">\n                          <button\n                            className=\"save-phone-btn\"\n                            onClick={async (e) => {\n                              e.preventDefault();\n                              console.log('🔥 SAVE BUTTON CLICKED!');\n                              console.log('📱 Payment phone:', paymentPhone);\n                              console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n\n                              if (!isValidPhone(paymentPhone)) {\n                                console.log('❌ Invalid phone number');\n                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                                return;\n                              }\n\n                              try {\n                                console.log('🔥 STARTING PERMANENT PHONE NUMBER SAVE...');\n                                console.log('📱 Phone to save:', paymentPhone);\n\n                                // Show loading state\n                                const btn = e.target;\n                                const originalText = btn.textContent;\n                                btn.textContent = '⏳ Saving...';\n                                btn.disabled = true;\n\n                                // ACTUALLY UPDATE THE USER PROFILE - NOT BACKGROUND!\n                                console.log('🔄 PERMANENTLY UPDATING USER PROFILE...');\n                                const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n\n                                if (updateSuccess) {\n                                  console.log('🎉 PHONE NUMBER PERMANENTLY SAVED!');\n\n                                  // Close editing mode\n                                  setIsEditingPhone(false);\n                                  setPhoneUpdated(true);\n\n                                  // Show success message\n                                  message.success({\n                                    content: '🎉 Phone number updated permanently!',\n                                    duration: 4,\n                                    style: {\n                                      marginTop: '20vh',\n                                      fontSize: '15px',\n                                      fontWeight: '600'\n                                    }\n                                  });\n\n                                  // Additional confirmation\n                                  setTimeout(() => {\n                                    message.info({\n                                      content: '✅ Your phone number will persist after page refresh!',\n                                      duration: 4,\n                                      style: {\n                                        marginTop: '20vh',\n                                        fontSize: '14px'\n                                      }\n                                    });\n                                  }, 1500);\n\n                                } else {\n                                  console.log('❌ PHONE NUMBER UPDATE FAILED');\n                                  message.error({\n                                    content: '❌ Failed to save phone number permanently. Please try again.',\n                                    duration: 4\n                                  });\n                                }\n\n                                // Restore button\n                                btn.textContent = originalText;\n                                btn.disabled = !isValidPhone(paymentPhone);\n\n                                // Reset the updated indicator after 8 seconds\n                                setTimeout(() => {\n                                  setPhoneUpdated(false);\n                                }, 8000);\n\n                              } catch (error) {\n                                console.error('❌ Error saving phone number:', error);\n                                message.error('Failed to save phone number. Please try again.');\n                              }\n                            }}\n                            disabled={!isValidPhone(paymentPhone)}\n                            type=\"button\"\n                          >\n                            ✅ Save\n                          </button>\n                          <button\n                            className=\"cancel-phone-btn\"\n                            onClick={() => {\n                              setPaymentPhone(user?.phoneNumber || '');\n                              setIsEditingPhone(false);\n                            }}\n                            type=\"button\"\n                          >\n                            ❌ Cancel\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"phone-note\">\n                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    console.log('💳 PAY BUTTON CLICKED!');\n                    console.log('📱 Payment phone:', paymentPhone);\n                    console.log('✏️ Is editing phone:', isEditingPhone);\n                    console.log('⏳ Payment loading:', paymentLoading);\n\n                    if (isEditingPhone) {\n                      message.warning('Please save your phone number first');\n                      return;\n                    }\n\n                    if (!paymentPhone) {\n                      message.error('Please enter a phone number');\n                      return;\n                    }\n\n                    if (!isValidPhone(paymentPhone)) {\n                      message.error('Please enter a valid phone number');\n                      return;\n                    }\n\n                    handlePayment();\n                  }}\n                  disabled={paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone)}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : isEditingPhone ? (\n                    'Save phone number first'\n                  ) : !paymentPhone ? (\n                    'Enter phone number'\n                  ) : !isValidPhone(paymentPhone) ? (\n                    'Invalid phone number'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {step === 'success' && (\n            <div className=\"success-step\">\n              <div className=\"success-animation\">\n                <div className=\"pulse-circle\">\n                  <div className=\"phone-icon\">📱</div>\n                </div>\n              </div>\n              \n              <h3>Payment Request Sent!</h3>\n              <p>Please check your phone for SMS confirmation and complete the payment.</p>\n              \n              <div className=\"payment-steps\">\n                <div className=\"step\">\n                  <span className=\"step-number\">1</span>\n                  <span className=\"step-text\">Check your phone for SMS</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">2</span>\n                  <span className=\"step-text\">Follow the payment instructions</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">3</span>\n                  <span className=\"step-text\">Your subscription will activate automatically</span>\n                </div>\n              </div>\n\n              <div className=\"success-actions\">\n                <button\n                  className=\"check-status-btn\"\n                  onClick={async () => {\n                    console.log('🔍 Manual payment check triggered');\n                    try {\n                      const response = await checkPaymentStatus();\n                      console.log('📥 Manual check response:', response);\n\n                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                        console.log('✅ Payment confirmed manually!');\n                        dispatch(SetSubscription(response));\n                        message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                        onSuccess && onSuccess();\n                        setTimeout(() => onClose(), 1000);\n                      } else {\n                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                      }\n                    } catch (error) {\n                      console.error('❌ Manual check error:', error);\n                      message.error('Error checking payment status');\n                    }\n                  }}\n                >\n                  ✅ Check Payment Status\n                </button>\n\n                <button className=\"done-btn\" onClick={handleClose}>\n                  I'll complete the payment\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,wBAAwB;AACvE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAClE,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMyC,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,OAAO,GAAGD,KAAK,IAAI,gBAAgB,CAACE,IAAI,CAACF,KAAK,CAAC;IACrDG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAAEJ,KAAK;MAAEC;IAAQ,CAAC,CAAC;IACvD,OAAOA,OAAO;EAChB,CAAC;;EAED;EACA,MAAMI,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACFH,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEE,QAAQ,CAAC;MACnEH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,IAAI,CAAC;MAE1C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE;QACbL,OAAO,CAACM,KAAK,CAAC,sBAAsB,CAAC;QACrC,OAAO,KAAK;MACd;;MAEA;MACA,MAAMC,aAAa,GAAG;QACpBC,MAAM,EAAEJ,IAAI,CAACC,GAAG;QAChBI,IAAI,EAAEL,IAAI,CAACK,IAAI;QACfC,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,EAAE;QACvBC,MAAM,EAAEP,IAAI,CAACO,MAAM,IAAI,EAAE;QACzBC,MAAM,EAAER,IAAI,CAACS,KAAK,IAAIT,IAAI,CAACU,SAAS,IAAI,EAAE;QAC1CC,KAAK,EAAEX,IAAI,CAACW,KAAK,IAAI,SAAS;QAC9BC,WAAW,EAAEb,QAAQ,CAAE;MACzB,CAAC;;MAEDH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEM,aAAa,CAAC;;MAE1D;MACA,MAAMU,QAAQ,GAAG,MAAMtD,cAAc,CAAC4C,aAAa,CAAC;MAEpDP,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgB,QAAQ,CAAC;MAEtD,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBlB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;QAEnD;QACA,MAAMkB,WAAW,GAAG;UAClB,GAAGf,IAAI;UACPY,WAAW,EAAEb,QAAQ,CAAE;QACzB,CAAC;;QAEDH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkB,WAAW,CAAC;;QAEnD;QACAC,QAAQ,CAACtD,OAAO,CAACqD,WAAW,CAAC,CAAC;;QAE9B;QACAE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,WAAW,CAAC,CAAC;;QAEzD;QACA5B,eAAe,CAACY,QAAQ,CAAC;QAEzBH,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;QACjFD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAAC;;QAE7C;QACA,MAAMsB,SAAS,GAAGF,IAAI,CAACG,KAAK,CAACL,YAAY,CAACM,OAAO,CAAC,MAAM,CAAC,CAAC;QAC1D3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEwB,SAAS,CAACT,WAAW,CAAC;QAE/D,IAAIS,SAAS,CAACT,WAAW,KAAKb,QAAQ,EAAE;UACtCH,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QAC1E,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QAC1E;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACLD,OAAO,CAACM,KAAK,CAAC,4BAA4B,EAAEW,QAAQ,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAsB,eAAA;MACd5B,OAAO,CAACM,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDN,OAAO,CAACM,KAAK,CAAC,kBAAkB,GAAAsB,eAAA,GAAEtB,KAAK,CAACW,QAAQ,cAAAW,eAAA,uBAAdA,eAAA,CAAgBC,IAAI,CAAC;MACvD,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM;IAAEzB;EAAK,CAAC,GAAG/C,WAAW,CAAEyE,KAAK,IAAKA,KAAK,CAAC1B,IAAI,CAAC;EACnD,MAAMgB,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,EAAE;MACVyD,UAAU,CAAC,CAAC;MACZ;MACAxC,eAAe,CAAC,CAAAa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;IAC1C;EACF,CAAC,EAAE,CAAC1C,MAAM,EAAE8B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,CAAC,CAAC;;EAE/B;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIgD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEY,WAAW,IAAI,CAACxB,cAAc,EAAE;MACxCQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEG,IAAI,CAACY,WAAW,CAAC;MAC/EzB,eAAe,CAACa,IAAI,CAACY,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,EAAExB,cAAc,CAAC,CAAC;EAEvC,MAAMuC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF9C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgC,QAAQ,GAAG,MAAMzD,QAAQ,CAAC,CAAC;MACjCqB,QAAQ,CAACmD,KAAK,CAACC,OAAO,CAAChB,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C/C,OAAO,CAAC+C,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiD,gBAAgB,GAAIC,IAAI,IAAK;IACjCpD,eAAe,CAACoD,IAAI,CAAC;IACrB9C,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAM+C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACtD,YAAY,EAAE;MACjBvB,OAAO,CAAC+C,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF;IAEA,IAAI,CAAChB,YAAY,IAAIA,YAAY,CAAC+C,MAAM,GAAG,EAAE,EAAE;MAC7C9E,OAAO,CAAC+C,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,IAAI,CAAC,gBAAgB,CAACP,IAAI,CAACT,YAAY,CAAC,EAAE;MACxC/B,OAAO,CAAC+C,KAAK,CAAC,wEAAwE,CAAC;MACvF;IACF;IAEA,IAAI;MAAA,IAAAgC,UAAA;MACFnD,iBAAiB,CAAC,IAAI,CAAC;MACvBiC,QAAQ,CAACpD,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMuE,WAAW,GAAG;QAClBJ,IAAI,EAAErD,YAAY;QAClB0B,MAAM,EAAEJ,IAAI,CAACC,GAAG;QAChBmC,SAAS,EAAElD,YAAY;QAAE;QACzBmD,SAAS,EAAErC,IAAI,CAACM,KAAK,IAAK,IAAA4B,UAAA,GAAElC,IAAI,CAACK,IAAI,cAAA6B,UAAA,uBAATA,UAAA,CAAWI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAM1B,QAAQ,GAAG,MAAMxD,UAAU,CAAC8E,WAAW,CAAC;MAE9C,IAAItB,QAAQ,CAACC,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAAC,kEAAkE,CAAC;QACnF7B,OAAO,CAAC,SAAS,CAAC;;QAElB;QACAuD,wBAAwB,CAAC3B,QAAQ,CAAC4B,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC7B,QAAQ,CAAC1D,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC/C,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,IAAI,mCAAmC,CAAC;IACrE,CAAC,SAAS;MACR4B,iBAAiB,CAAC,KAAK,CAAC;MACxBiC,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM6E,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFF,QAAQ,EAAE;QACVhD,OAAO,CAACC,GAAG,CAAE,yCAAwC+C,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAMhC,QAAQ,GAAG,MAAMvD,kBAAkB,CAAC,CAAC;QAC3CsC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgB,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACX,KAAK,IAAIW,QAAQ,CAACkC,aAAa,KAAK,MAAM,IAAIlC,QAAQ,CAACmC,MAAM,KAAK,QAAQ,EAAE;UACpGpD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACAmB,QAAQ,CAACvD,eAAe,CAACoD,QAAQ,CAAC,CAAC;;UAEnC;UACA1D,OAAO,CAAC2D,OAAO,CAAC;YACdmC,OAAO,EAAE,2CAA2C;YACpDC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;;UAEF;UACAlF,SAAS,IAAIA,SAAS,CAAC,CAAC;;UAExB;UACAmF,UAAU,CAAC,MAAM;YACfpF,OAAO,CAAC,CAAC;UACX,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,IAAI;QACb;QAEA,IAAIyE,QAAQ,IAAIC,WAAW,EAAE;UAC3BjD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C1C,OAAO,CAACqG,OAAO,CAAC;YACdP,OAAO,EAAE,sGAAsG;YAC/GC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,OAAO,KAAK;QACd;;QAEA;QACAK,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI0C,QAAQ,IAAIC,WAAW,EAAE;UAC3B1F,OAAO,CAAC+C,KAAK,CAAC,4EAA4E,CAAC;QAC7F,CAAC,MAAM;UACLqD,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACAA,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBxE,OAAO,CAAC,OAAO,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,KAAK,CAAC;IACxBM,iBAAiB,CAAC,KAAK,CAAC;IACxBE,eAAe,CAAC,KAAK,CAAC;IACtBJ,eAAe,CAAC,CAAAa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;IACxCzC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAK4C,SAAS,EAAC,4BAA4B;IAAAgD,QAAA,eACzC5F,OAAA;MAAK4C,SAAS,EAAC,oBAAoB;MAAAgD,QAAA,gBACjC5F,OAAA;QAAK4C,SAAS,EAAC,cAAc;QAAAgD,QAAA,gBAC3B5F,OAAA;UAAI4C,SAAS,EAAC,aAAa;UAAAgD,QAAA,GACxB1E,IAAI,KAAK,OAAO,IAAI,8BAA8B,EAClDA,IAAI,KAAK,SAAS,IAAI,0BAA0B,EAChDA,IAAI,KAAK,SAAS,IAAI,yBAAyB;QAAA;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACLhG,OAAA;UAAQ4C,SAAS,EAAC,cAAc;UAACqD,OAAO,EAAEN,WAAY;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAENhG,OAAA;QAAK4C,SAAS,EAAC,eAAe;QAAAgD,QAAA,GAC3B1E,IAAI,KAAK,OAAO,iBACflB,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAgD,QAAA,EACxB9E,OAAO,gBACNd,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAgD,QAAA,gBAC5B5F,OAAA;cAAK4C,SAAS,EAAC;YAAS;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/BhG,OAAA;cAAA4F,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAENtF,KAAK,CAACwF,GAAG,CAAEjC,IAAI;YAAA,IAAAkC,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA;YAAA,oBACbtG,OAAA;cAAoB4C,SAAS,EAAC,WAAW;cAACqD,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACC,IAAI,CAAE;cAAA2B,QAAA,gBAC9E5F,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,gBAC1B5F,OAAA;kBAAI4C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,EAAE3B,IAAI,CAACsC;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAG,WAAA,GAAAlC,IAAI,CAACsC,KAAK,cAAAJ,WAAA,uBAAVA,WAAA,CAAY1B,WAAW,CAAC,CAAC,CAAC+B,QAAQ,CAAC,OAAO,CAAC,kBAC1CxG,OAAA;kBAAM4C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENhG,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,gBACzB5F,OAAA;kBAAM4C,SAAS,EAAC,cAAc;kBAAAgD,QAAA,IAAAQ,qBAAA,GAAEnC,IAAI,CAACwC,eAAe,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACjF/B,IAAI,CAAC0C,WAAW,IAAI1C,IAAI,CAAC0C,WAAW,KAAK1C,IAAI,CAACwC,eAAe,iBAC5DzG,OAAA;kBAAM4C,SAAS,EAAC,gBAAgB;kBAAAgD,QAAA,GAAE3B,IAAI,CAAC0C,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/E,eACDhG,OAAA;kBAAM4C,SAAS,EAAC,cAAc;kBAAAgD,QAAA,GAAE3B,IAAI,CAACmB,QAAQ,EAAC,QAAM,EAACnB,IAAI,CAACmB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eAENhG,OAAA;gBAAK4C,SAAS,EAAC,eAAe;gBAAAgD,QAAA,IAAAS,cAAA,GAC3BpC,IAAI,CAAC2C,QAAQ,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,KAAK,kBAC7C/G,OAAA;kBAAiB4C,SAAS,EAAC,SAAS;kBAAAgD,QAAA,gBAClC5F,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvChG,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,EAAEkB;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFvCe,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC,EACD,EAAAM,eAAA,GAAArC,IAAI,CAAC2C,QAAQ,cAAAN,eAAA,uBAAbA,eAAA,CAAenC,MAAM,IAAG,CAAC,iBACxBnE,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAAAgD,QAAA,gBACtB5F,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvChG,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,GAAE3B,IAAI,CAAC2C,QAAQ,CAACzC,MAAM,GAAG,CAAC,EAAC,gBAAc;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENhG,OAAA;gBAAQ4C,SAAS,EAAC,iBAAiB;gBAAAgD,QAAA,GAAC,SAC3B,EAAC3B,IAAI,CAACsC,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjCD/B,IAAI,CAAC9B,GAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCb,CAAC;UAAA,CACP;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEA9E,IAAI,KAAK,SAAS,IAAIN,YAAY,iBACjCZ,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAgD,QAAA,gBAC3B5F,OAAA;YAAK4C,SAAS,EAAC,uBAAuB;YAAAgD,QAAA,gBACpC5F,OAAA;cAAA4F,QAAA,GAAI,iBAAe,EAAChF,YAAY,CAAC2F,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5ChG,OAAA;cAAG4C,SAAS,EAAC,oBAAoB;cAAAgD,QAAA,IAAApF,qBAAA,GAC9BI,YAAY,CAAC6F,eAAe,cAAAjG,qBAAA,uBAA5BA,qBAAA,CAA8BkG,cAAc,CAAC,CAAC,EAAC,WAAS,EAAC9F,YAAY,CAACwE,QAAQ,EAAC,QAAM,EAACxE,YAAY,CAACwE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENhG,OAAA;YAAK4C,SAAS,EAAC,cAAc;YAAAgD,QAAA,gBAC3B5F,OAAA;cAAK4C,SAAS,EAAC,eAAe;cAAAgD,QAAA,gBAC5B5F,OAAA;gBAAK4C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,gBACxB5F,OAAA;kBAAM4C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC5D,CAAC1E,cAAc,gBACdtB,OAAA;kBAAK4C,SAAS,EAAC,eAAe;kBAAAgD,QAAA,gBAC5B5F,OAAA;oBAAM4C,SAAS,EAAG,cAAapB,YAAY,GAAG,SAAS,GAAG,EAAG,EAAE;oBAAAoE,QAAA,GAC5DxE,YAAY,IAAI,cAAc,EAC9BI,YAAY,iBAAIxB,OAAA;sBAAM4C,SAAS,EAAC,mBAAmB;sBAAAgD,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,EACNgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrClH,OAAA;oBAAKqF,KAAK,EAAE;sBAACC,SAAS,EAAE;oBAAK,CAAE;oBAAAM,QAAA,gBAC7B5F,OAAA;sBAAOqF,KAAK,EAAE;wBAAC8B,KAAK,EAAE,MAAM;wBAAE5B,QAAQ,EAAE,MAAM;wBAAE6B,OAAO,EAAE;sBAAO,CAAE;sBAAAxB,QAAA,GAAC,iBAClD,EAACxE,YAAY,EAAC,UAAQ,EAACc,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,EAAC,aAAW,EAACtB,YAAY,GAAG,KAAK,GAAG,IAAI;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,eACRhG,OAAA;sBACEiG,OAAO,EAAEA,CAAA,KAAM;wBACbnE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;wBACzCsF,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;sBAC1B,CAAE;sBACFlC,KAAK,EAAE;wBACLE,QAAQ,EAAE,MAAM;wBAChBiC,OAAO,EAAE,SAAS;wBAClBlC,SAAS,EAAE,KAAK;wBAChBmC,UAAU,EAAE,SAAS;wBACrBC,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBC,MAAM,EAAE;sBACV,CAAE;sBAAAhC,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,eACDhG,OAAA;oBACE4C,SAAS,EAAC,gBAAgB;oBAC1BqD,OAAO,EAAEA,CAAA,KAAM1E,iBAAiB,CAAC,IAAI,CAAE;oBACvCsG,IAAI,EAAC,QAAQ;oBAAAjC,QAAA,EACd;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAENhG,OAAA;kBAAK4C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,gBACzB5F,OAAA;oBACE6H,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAE1G,YAAa;oBACpB2G,QAAQ,EAAGC,CAAC,IAAK3G,eAAe,CAAC2G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,WAAW,EAAC,uCAAuC;oBACnDtF,SAAS,EAAG,eAAcxB,YAAY,GAAIM,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAS,GAAI,EAAG,EAAE;oBACnG+G,SAAS,EAAC;kBAAI;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACD5E,YAAY,iBACXpB,OAAA;oBAAK4C,SAAS,EAAG,oBAAmBlB,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAU,EAAE;oBAAAwE,QAAA,EACpFlE,YAAY,CAACN,YAAY,CAAC,gBACzBpB,OAAA;sBAAM4C,SAAS,EAAC,0BAA0B;sBAAAgD,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEtEhG,OAAA;sBAAM4C,SAAS,EAAC,4BAA4B;sBAAAgD,QAAA,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC/F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eACDhG,OAAA;oBAAK4C,SAAS,EAAC,eAAe;oBAAAgD,QAAA,gBAC5B5F,OAAA;sBACE4C,SAAS,EAAC,gBAAgB;sBAC1BqD,OAAO,EAAE,MAAO+B,CAAC,IAAK;wBACpBA,CAAC,CAACI,cAAc,CAAC,CAAC;wBAClBtG,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;wBACtCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEX,YAAY,CAAC;wBAC9CU,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEL,YAAY,CAACN,YAAY,CAAC,CAAC;wBAE5D,IAAI,CAACM,YAAY,CAACN,YAAY,CAAC,EAAE;0BAC/BU,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;0BACrC1C,OAAO,CAAC+C,KAAK,CAAC,wEAAwE,CAAC;0BACvF;wBACF;wBAEA,IAAI;0BACFN,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;0BACzDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEX,YAAY,CAAC;;0BAE9C;0BACA,MAAMiH,GAAG,GAAGL,CAAC,CAACC,MAAM;0BACpB,MAAMK,YAAY,GAAGD,GAAG,CAACE,WAAW;0BACpCF,GAAG,CAACE,WAAW,GAAG,aAAa;0BAC/BF,GAAG,CAACG,QAAQ,GAAG,IAAI;;0BAEnB;0BACA1G,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;0BACtD,MAAM0G,aAAa,GAAG,MAAMzG,qBAAqB,CAACZ,YAAY,CAAC;0BAE/D,IAAIqH,aAAa,EAAE;4BACjB3G,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;4BAEjD;4BACAR,iBAAiB,CAAC,KAAK,CAAC;4BACxBE,eAAe,CAAC,IAAI,CAAC;;4BAErB;4BACApC,OAAO,CAAC2D,OAAO,CAAC;8BACdmC,OAAO,EAAE,sCAAsC;8BAC/CC,QAAQ,EAAE,CAAC;8BACXC,KAAK,EAAE;gCACLC,SAAS,EAAE,MAAM;gCACjBC,QAAQ,EAAE,MAAM;gCAChBC,UAAU,EAAE;8BACd;4BACF,CAAC,CAAC;;4BAEF;4BACAC,UAAU,CAAC,MAAM;8BACfpG,OAAO,CAACqJ,IAAI,CAAC;gCACXvD,OAAO,EAAE,sDAAsD;gCAC/DC,QAAQ,EAAE,CAAC;gCACXC,KAAK,EAAE;kCACLC,SAAS,EAAE,MAAM;kCACjBC,QAAQ,EAAE;gCACZ;8BACF,CAAC,CAAC;4BACJ,CAAC,EAAE,IAAI,CAAC;0BAEV,CAAC,MAAM;4BACLzD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;4BAC3C1C,OAAO,CAAC+C,KAAK,CAAC;8BACZ+C,OAAO,EAAE,8DAA8D;8BACvEC,QAAQ,EAAE;4BACZ,CAAC,CAAC;0BACJ;;0BAEA;0BACAiD,GAAG,CAACE,WAAW,GAAGD,YAAY;0BAC9BD,GAAG,CAACG,QAAQ,GAAG,CAAC9G,YAAY,CAACN,YAAY,CAAC;;0BAE1C;0BACAqE,UAAU,CAAC,MAAM;4BACfhE,eAAe,CAAC,KAAK,CAAC;0BACxB,CAAC,EAAE,IAAI,CAAC;wBAEV,CAAC,CAAC,OAAOW,KAAK,EAAE;0BACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;0BACpD/C,OAAO,CAAC+C,KAAK,CAAC,gDAAgD,CAAC;wBACjE;sBACF,CAAE;sBACFoG,QAAQ,EAAE,CAAC9G,YAAY,CAACN,YAAY,CAAE;sBACtCyG,IAAI,EAAC,QAAQ;sBAAAjC,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACThG,OAAA;sBACE4C,SAAS,EAAC,kBAAkB;sBAC5BqD,OAAO,EAAEA,CAAA,KAAM;wBACb5E,eAAe,CAAC,CAAAa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;wBACxCvB,iBAAiB,CAAC,KAAK,CAAC;sBAC1B,CAAE;sBACFsG,IAAI,EAAC,QAAQ;sBAAAjC,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNhG,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,eACzB5F,OAAA;kBAAA4F,QAAA,EAAO;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENhG,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAgD,QAAA,gBACxB5F,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDhG,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK4C,SAAS,EAAC,iBAAiB;YAAAgD,QAAA,gBAC9B5F,OAAA;cAAQ4C,SAAS,EAAC,UAAU;cAACqD,OAAO,EAAEA,CAAA,KAAM9E,OAAO,CAAC,OAAO,CAAE;cAAAyE,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThG,OAAA;cACE4C,SAAS,EAAC,SAAS;cACnBqD,OAAO,EAAG+B,CAAC,IAAK;gBACdA,CAAC,CAACI,cAAc,CAAC,CAAC;gBAClBtG,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;gBACrCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEX,YAAY,CAAC;gBAC9CU,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAET,cAAc,CAAC;gBACnDQ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEf,cAAc,CAAC;gBAEjD,IAAIM,cAAc,EAAE;kBAClBjC,OAAO,CAACqG,OAAO,CAAC,qCAAqC,CAAC;kBACtD;gBACF;gBAEA,IAAI,CAACtE,YAAY,EAAE;kBACjB/B,OAAO,CAAC+C,KAAK,CAAC,6BAA6B,CAAC;kBAC5C;gBACF;gBAEA,IAAI,CAACV,YAAY,CAACN,YAAY,CAAC,EAAE;kBAC/B/B,OAAO,CAAC+C,KAAK,CAAC,mCAAmC,CAAC;kBAClD;gBACF;gBAEA8B,aAAa,CAAC,CAAC;cACjB,CAAE;cACFsE,QAAQ,EAAExH,cAAc,IAAI,CAACI,YAAY,IAAIE,cAAc,IAAI,CAACI,YAAY,CAACN,YAAY,CAAE;cAAAwE,QAAA,EAE1F5E,cAAc,gBACbhB,OAAA,CAAAE,SAAA;gBAAA0F,QAAA,gBACE5F,OAAA;kBAAM4C,SAAS,EAAC;gBAAa;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEvC;cAAA,eAAE,CAAC,GACD1E,cAAc,GAChB,yBAAyB,GACvB,CAACF,YAAY,GACf,oBAAoB,GAClB,CAACM,YAAY,CAACN,YAAY,CAAC,GAC7B,sBAAsB,GAErB,OAAI,CAAAX,sBAAA,GAAEG,YAAY,CAAC6F,eAAe,cAAAhG,sBAAA,uBAA5BA,sBAAA,CAA8BiG,cAAc,CAAC,CAAE;YACvD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA9E,IAAI,KAAK,SAAS,iBACjBlB,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAgD,QAAA,gBAC3B5F,OAAA;YAAK4C,SAAS,EAAC,mBAAmB;YAAAgD,QAAA,eAChC5F,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAgD,QAAA,eAC3B5F,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAA4F,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BhG,OAAA;YAAA4F,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE7EhG,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAgD,QAAA,gBAC5B5F,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAgD,QAAA,gBACnB5F,OAAA;gBAAM4C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtChG,OAAA;gBAAM4C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNhG,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAgD,QAAA,gBACnB5F,OAAA;gBAAM4C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtChG,OAAA;gBAAM4C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNhG,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAgD,QAAA,gBACnB5F,OAAA;gBAAM4C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtChG,OAAA;gBAAM4C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhG,OAAA;YAAK4C,SAAS,EAAC,iBAAiB;YAAAgD,QAAA,gBAC9B5F,OAAA;cACE4C,SAAS,EAAC,kBAAkB;cAC5BqD,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnBnE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;gBAChD,IAAI;kBACF,MAAMgB,QAAQ,GAAG,MAAMvD,kBAAkB,CAAC,CAAC;kBAC3CsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgB,QAAQ,CAAC;kBAElD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACX,KAAK,IAAIW,QAAQ,CAACkC,aAAa,KAAK,MAAM,IAAIlC,QAAQ,CAACmC,MAAM,KAAK,QAAQ,EAAE;oBACpGpD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;oBAC5CmB,QAAQ,CAACvD,eAAe,CAACoD,QAAQ,CAAC,CAAC;oBACnC1D,OAAO,CAAC2D,OAAO,CAAC,2CAA2C,CAAC;oBAC5D1C,SAAS,IAAIA,SAAS,CAAC,CAAC;oBACxBmF,UAAU,CAAC,MAAMpF,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;kBACnC,CAAC,MAAM;oBACLhB,OAAO,CAACqJ,IAAI,CAAC,0EAA0E,CAAC;kBAC1F;gBACF,CAAC,CAAC,OAAOtG,KAAK,EAAE;kBACdN,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;kBAC7C/C,OAAO,CAAC+C,KAAK,CAAC,+BAA+B,CAAC;gBAChD;cACF,CAAE;cAAAwD,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAEThG,OAAA;cAAQ4C,SAAS,EAAC,UAAU;cAACqD,OAAO,EAAEN,WAAY;cAAAC,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CAlmBIJ,iBAAiB;EAAA,QA2FJhB,WAAW,EACXC,WAAW;AAAA;AAAAuJ,EAAA,GA5FxBxI,iBAAiB;AAomBvB,eAAeA,iBAAiB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}