<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainWave System Status</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .service-card {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s;
        }
        .service-card.online {
            border-color: #10b981;
            background: #ecfdf5;
        }
        .service-card.offline {
            border-color: #ef4444;
            background: #fef2f2;
        }
        .service-card.unknown {
            border-color: #f59e0b;
            background: #fffbeb;
        }
        .service-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-indicator.online {
            background: #10b981;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
        }
        .status-indicator.offline {
            background: #ef4444;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }
        .status-indicator.unknown {
            background: #f59e0b;
            box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
        }
        .service-details {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 15px;
        }
        .service-url {
            font-family: monospace;
            background: #f1f5f9;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.85rem;
            margin-bottom: 10px;
        }
        .test-btn {
            background: #4f46e5;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        .test-btn:hover {
            background: #4338ca;
        }
        .quick-links {
            background: #f1f5f9;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .link-btn {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: transform 0.2s;
        }
        .link-btn:hover {
            transform: translateY(-2px);
        }
        .refresh-btn {
            background: #059669;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .last-updated {
            text-align: center;
            color: #6b7280;
            font-size: 0.9rem;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 BrainWave System Status</h1>
            <p>Real-time status of all system components</p>
            <button class="refresh-btn" onclick="checkAllServices()">🔄 Refresh Status</button>
        </div>

        <div class="status-grid">
            <!-- React Client -->
            <div class="service-card unknown" id="react-card">
                <div class="service-title">
                    <span class="status-indicator unknown" id="react-indicator"></span>
                    📱 React Client
                </div>
                <div class="service-details">Frontend application (Port 3000)</div>
                <div class="service-url">http://localhost:3000</div>
                <button class="test-btn" onclick="testService('react')">Test</button>
                <button class="test-btn" onclick="openService('http://localhost:3000')">Open</button>
            </div>

            <!-- Node.js Server -->
            <div class="service-card unknown" id="server-card">
                <div class="service-title">
                    <span class="status-indicator unknown" id="server-indicator"></span>
                    🚀 Node.js Server
                </div>
                <div class="service-details">Backend API server (Port 5000)</div>
                <div class="service-url">http://localhost:5000</div>
                <button class="test-btn" onclick="testService('server')">Test</button>
                <button class="test-btn" onclick="openService('http://localhost:5000/api/plans')">API Test</button>
            </div>

            <!-- MongoDB Database -->
            <div class="service-card offline" id="mongodb-card">
                <div class="service-title">
                    <span class="status-indicator offline" id="mongodb-indicator"></span>
                    🗄️ MongoDB Database
                </div>
                <div class="service-details">Database server (Port 27017)</div>
                <div class="service-url">mongodb://localhost:27017</div>
                <button class="test-btn" onclick="testService('mongodb')">Test</button>
                <div style="margin-top: 10px; font-size: 0.8rem; color: #ef4444;">
                    ⚠️ MongoDB not available - using demo mode
                </div>
            </div>
        </div>

        <div class="quick-links">
            <h3>🔗 Quick Links</h3>
            <div class="links-grid">
                <a href="http://localhost:3000" target="_blank" class="link-btn">🏠 Home Page</a>
                <a href="http://localhost:3000/login" target="_blank" class="link-btn">🔐 Login</a>
                <a href="http://localhost:3000/register" target="_blank" class="link-btn">📝 Register</a>
                <a href="http://localhost:5000/api/plans" target="_blank" class="link-btn">📋 API Plans</a>
                <a href="file:///c:/Users/<USER>/Desktop/20/New folder/profile-demo.html" target="_blank" class="link-btn">👤 Profile Demo</a>
                <a href="file:///c:/Users/<USER>/Desktop/20/New folder/test-demo-modes.html" target="_blank" class="link-btn">🧪 Demo Tester</a>
            </div>
        </div>

        <div class="last-updated" id="last-updated">
            Last updated: Never
        </div>
    </div>

    <script>
        async function testService(service) {
            const card = document.getElementById(`${service}-card`);
            const indicator = document.getElementById(`${service}-indicator`);
            
            // Set to testing state
            card.className = 'service-card unknown';
            indicator.className = 'status-indicator unknown';
            
            try {
                let url, isOnline = false;
                
                switch(service) {
                    case 'react':
                        url = 'http://localhost:3000';
                        break;
                    case 'server':
                        url = 'http://localhost:5000/api/plans';
                        break;
                    case 'mongodb':
                        // MongoDB test would require a specific endpoint
                        // For now, we'll assume it's offline since we're using demo mode
                        isOnline = false;
                        break;
                }
                
                if (service !== 'mongodb') {
                    const response = await fetch(url, { 
                        method: 'GET',
                        mode: 'no-cors' // Allow cross-origin for testing
                    });
                    isOnline = true; // If no error thrown, service is online
                }
                
                // Update status
                card.className = `service-card ${isOnline ? 'online' : 'offline'}`;
                indicator.className = `status-indicator ${isOnline ? 'online' : 'offline'}`;
                
            } catch (error) {
                // Service is offline
                card.className = 'service-card offline';
                indicator.className = 'status-indicator offline';
            }
        }

        async function checkAllServices() {
            document.getElementById('last-updated').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
            
            // Test all services
            await testService('react');
            await testService('server');
            await testService('mongodb');
        }

        function openService(url) {
            window.open(url, '_blank');
        }

        // Auto-check services on page load
        window.addEventListener('load', () => {
            setTimeout(checkAllServices, 1000);
        });

        // Auto-refresh every 30 seconds
        setInterval(checkAllServices, 30000);
    </script>
</body>
</html>
