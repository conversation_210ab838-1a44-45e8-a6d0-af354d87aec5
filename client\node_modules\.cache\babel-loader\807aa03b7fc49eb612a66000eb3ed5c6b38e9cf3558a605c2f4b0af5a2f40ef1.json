{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionModal\\\\SubscriptionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionModal = ({\n  isOpen,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  var _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n\n  // Check if user has valid phone number\n  const hasValidPhone = () => {\n    const phone = user === null || user === void 0 ? void 0 : user.phoneNumber;\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile - PERMANENT UPDATE\n  const updateUserPhoneNumber = async newPhone => {\n    try {\n      console.log('🔥 PERMANENTLY UPDATING USER PHONE NUMBER:', newPhone);\n      console.log('👤 Current user data:', user);\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Create complete update payload with ALL user fields\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone // THIS IS THE KEY CHANGE\n      };\n\n      console.log('📤 PERMANENT UPDATE PAYLOAD:', updatePayload);\n\n      // Use the working updateUserInfo API\n      const response = await updateUserInfo(updatePayload);\n      console.log('📥 PERMANENT UPDATE RESPONSE:', response);\n      if (response.success) {\n        console.log('🎉 PHONE NUMBER PERMANENTLY UPDATED!');\n\n        // Create updated user object\n        const updatedUser = {\n          ...user,\n          phoneNumber: newPhone // Ensure phone number is updated\n        };\n\n        console.log('👤 Updated user object:', updatedUser);\n\n        // Update Redux store\n        dispatch(SetUser(updatedUser));\n\n        // Update localStorage - THIS ENSURES PERSISTENCE AFTER REFRESH\n        localStorage.setItem('user', JSON.stringify(updatedUser));\n\n        // Update payment phone state\n        setPaymentPhone(newPhone);\n        console.log('✅ ALL UPDATES COMPLETE - PHONE NUMBER SHOULD PERSIST AFTER REFRESH');\n        console.log('📱 New phone number:', newPhone);\n\n        // Verify the save worked\n        const savedUser = JSON.parse(localStorage.getItem('user'));\n        console.log('💾 Saved to localStorage:', savedUser.phoneNumber);\n        if (savedUser.phoneNumber === newPhone) {\n          console.log('🎉 VERIFICATION SUCCESSFUL - PHONE NUMBER SAVED CORRECTLY');\n        } else {\n          console.log('⚠️ VERIFICATION FAILED - PHONE NUMBER NOT SAVED CORRECTLY');\n        }\n        return true;\n      } else {\n        console.error('❌ PERMANENT UPDATE FAILED:', response);\n        return false;\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('❌ PERMANENT UPDATE ERROR:', error);\n      console.error('❌ Error details:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      return false;\n    }\n  };\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    }\n  }, [isOpen, user === null || user === void 0 ? void 0 : user.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.phoneNumber && !isEditingPhone) {\n      console.log('🔄 User phone changed, updating payment phone:', user.phoneNumber);\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user === null || user === void 0 ? void 0 : user.phoneNumber, isEditingPhone]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = plan => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone,\n        // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n          return true;\n        }\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: [step === 'plans' && '🚀 Choose Your Learning Plan', step === 'payment' && '💳 Complete Your Payment', step === 'success' && '⏳ Processing Payment...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [step === 'plans' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading plans...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this) : plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$features, _plan$features2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-card\",\n              onClick: () => handlePlanSelect(plan),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-amount\",\n                  children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-original\",\n                  children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-period\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: [(_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 25\n                }, this)), ((_plan$features2 = plan.features) === null || _plan$features2 === void 0 ? void 0 : _plan$features2.length) > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: [plan.features.length - 4, \" more features\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                children: [\"Choose \", plan.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this), step === 'payment' && selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-plan-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Selected Plan: \", selectedPlan.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"plan-price-summary\",\n              children: [(_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS for \", selectedPlan.duration, \" month\", selectedPlan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-label\",\n                  children: \"Phone Number for Payment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), !isEditingPhone ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `info-value ${phoneUpdated ? 'updated' : ''}`,\n                    children: [paymentPhone || 'Not provided', phoneUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"updated-indicator\",\n                      children: \"\\u2705 Updated\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 44\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 25\n                  }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                      style: {\n                        color: '#666',\n                        fontSize: '10px',\n                        display: 'block'\n                      },\n                      children: [\"Debug: Payment=\", paymentPhone, \" | User=\", user === null || user === void 0 ? void 0 : user.phoneNumber, \" | Updated=\", phoneUpdated ? 'Yes' : 'No']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        console.log('🔄 Testing page refresh...');\n                        window.location.reload();\n                      },\n                      style: {\n                        fontSize: '10px',\n                        padding: '2px 6px',\n                        marginTop: '4px',\n                        background: '#f0f0f0',\n                        border: '1px solid #ccc',\n                        borderRadius: '4px',\n                        cursor: 'pointer'\n                      },\n                      children: \"\\uD83D\\uDD04 Test Refresh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"edit-phone-btn\",\n                    onClick: () => setIsEditingPhone(true),\n                    type: \"button\",\n                    children: \"\\u270F\\uFE0F Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-edit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    value: paymentPhone,\n                    onChange: e => setPaymentPhone(e.target.value),\n                    placeholder: \"Enter phone number (e.g., 0744963858)\",\n                    className: `phone-input ${paymentPhone ? isValidPhone(paymentPhone) ? 'valid' : 'invalid' : ''}`,\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this), paymentPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`,\n                    children: isValidPhone(paymentPhone) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message valid\",\n                      children: \"\\u2705 Valid phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message invalid\",\n                      children: \"\\u274C Must start with 06 or 07 and be 10 digits\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"phone-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"save-phone-btn\",\n                      onClick: async e => {\n                        e.preventDefault();\n                        console.log('🔥 SAVE BUTTON CLICKED!');\n                        console.log('📱 Payment phone:', paymentPhone);\n                        console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n                        if (!isValidPhone(paymentPhone)) {\n                          console.log('❌ Invalid phone number');\n                          message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                          return;\n                        }\n                        try {\n                          console.log('🔥 STARTING PERMANENT PHONE NUMBER SAVE...');\n                          console.log('📱 Phone to save:', paymentPhone);\n\n                          // Show loading state\n                          const btn = e.target;\n                          const originalText = btn.textContent;\n                          btn.textContent = '⏳ Saving...';\n                          btn.disabled = true;\n\n                          // ACTUALLY UPDATE THE USER PROFILE - NOT BACKGROUND!\n                          console.log('🔄 PERMANENTLY UPDATING USER PROFILE...');\n                          const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n                          if (updateSuccess) {\n                            console.log('🎉 PHONE NUMBER PERMANENTLY SAVED!');\n\n                            // Close editing mode\n                            setIsEditingPhone(false);\n                            setPhoneUpdated(true);\n\n                            // Show success message\n                            message.success({\n                              content: '🎉 Phone number updated permanently!',\n                              duration: 4,\n                              style: {\n                                marginTop: '20vh',\n                                fontSize: '15px',\n                                fontWeight: '600'\n                              }\n                            });\n\n                            // Additional confirmation\n                            setTimeout(() => {\n                              message.info({\n                                content: '✅ Your phone number will persist after page refresh!',\n                                duration: 4,\n                                style: {\n                                  marginTop: '20vh',\n                                  fontSize: '14px'\n                                }\n                              });\n                            }, 1500);\n                          } else {\n                            console.log('❌ PHONE NUMBER UPDATE FAILED');\n                            message.error({\n                              content: '❌ Failed to save phone number permanently. Please try again.',\n                              duration: 4\n                            });\n                          }\n\n                          // Restore button\n                          btn.textContent = originalText;\n                          btn.disabled = !isValidPhone(paymentPhone);\n\n                          // Reset the updated indicator after 8 seconds\n                          setTimeout(() => {\n                            setPhoneUpdated(false);\n                          }, 8000);\n                        } catch (error) {\n                          console.error('❌ Error saving phone number:', error);\n                          message.error('Failed to save phone number. Please try again.');\n                        }\n                      },\n                      disabled: !isValidPhone(paymentPhone),\n                      type: \"button\",\n                      children: \"\\u2705 Save\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"cancel-phone-btn\",\n                      onClick: () => {\n                        setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n                        setIsEditingPhone(false);\n                      },\n                      type: \"button\",\n                      children: \"\\u274C Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-note\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"\\uD83D\\uDCA1 This number will receive the payment SMS. You can use a different number than your profile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Payment Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: \"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"back-btn\",\n              onClick: () => setStep('plans'),\n              children: \"\\u2190 Back to Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pay-btn\",\n              onClick: e => {\n                e.preventDefault();\n                console.log('💳 PAY BUTTON CLICKED!');\n                console.log('📱 Payment phone:', paymentPhone);\n                console.log('✏️ Is editing phone:', isEditingPhone);\n                console.log('⏳ Payment loading:', paymentLoading);\n                if (isEditingPhone) {\n                  message.warning('Please save your phone number first');\n                  return;\n                }\n                if (!paymentPhone) {\n                  message.error('Please enter a phone number');\n                  return;\n                }\n                if (!isValidPhone(paymentPhone)) {\n                  message.error('Please enter a valid phone number');\n                  return;\n                }\n                handlePayment();\n              },\n              disabled: paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone),\n              children: paymentLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 23\n                }, this), \"Processing...\"]\n              }, void 0, true) : isEditingPhone ? 'Save phone number first' : !paymentPhone ? 'Enter phone number' : !isValidPhone(paymentPhone) ? 'Invalid phone number' : `Pay ${(_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString()} TZS`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), step === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-animation\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Request Sent!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation and complete the payment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Check your phone for SMS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Follow the payment instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Your subscription will activate automatically\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"check-status-btn\",\n              onClick: async () => {\n                console.log('🔍 Manual payment check triggered');\n                try {\n                  const response = await checkPaymentStatus();\n                  console.log('📥 Manual check response:', response);\n                  if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                    console.log('✅ Payment confirmed manually!');\n                    dispatch(SetSubscription(response));\n                    message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                    onSuccess && onSuccess();\n                    setTimeout(() => onClose(), 1000);\n                  } else {\n                    message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                  }\n                } catch (error) {\n                  console.error('❌ Manual check error:', error);\n                  message.error('Error checking payment status');\n                }\n              },\n              children: \"\\u2705 Check Payment Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"done-btn\",\n              onClick: handleClose,\n              children: \"I'll complete the payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this);\n};\n_s(SubscriptionModal, \"rA7RWYaF7B+qnqmxmK/BO3PDrYo=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = SubscriptionModal;\nexport default SubscriptionModal;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "message", "getPlans", "addPayment", "checkPaymentStatus", "updateUserInfo", "axiosInstance", "SetSubscription", "SetUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionModal", "isOpen", "onClose", "onSuccess", "_s", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "hasValidPhone", "phone", "user", "phoneNumber", "test", "updateUserPhoneNumber", "newPhone", "console", "log", "_id", "error", "updatePayload", "userId", "name", "email", "school", "class_", "class", "className", "level", "response", "success", "updatedUser", "dispatch", "localStorage", "setItem", "JSON", "stringify", "setPaymentPhone", "savedUser", "parse", "getItem", "_error$response", "data", "state", "fetchPlans", "isEditingPhone", "Array", "isArray", "handlePlanSelect", "plan", "handlePayment", "paymentPhone", "length", "_user$name", "paymentData", "userPhone", "userEmail", "replace", "toLowerCase", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "checkStatus", "paymentStatus", "status", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "warning", "handleClose", "setIsEditingPhone", "setPhoneUpdated", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "title", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "phoneUpdated", "process", "env", "NODE_ENV", "color", "display", "window", "location", "reload", "padding", "background", "border", "borderRadius", "cursor", "type", "value", "onChange", "e", "target", "placeholder", "isValidPhone", "max<PERSON><PERSON><PERSON>", "preventDefault", "btn", "originalText", "textContent", "disabled", "updateSuccess", "info", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionModal/SubscriptionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n\n  // Check if user has valid phone number\n  const hasValidPhone = () => {\n    const phone = user?.phoneNumber;\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile - PERMANENT UPDATE\n  const updateUserPhoneNumber = async (newPhone) => {\n    try {\n      console.log('🔥 PERMANENTLY UPDATING USER PHONE NUMBER:', newPhone);\n      console.log('👤 Current user data:', user);\n\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Create complete update payload with ALL user fields\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone  // THIS IS THE KEY CHANGE\n      };\n\n      console.log('📤 PERMANENT UPDATE PAYLOAD:', updatePayload);\n\n      // Use the working updateUserInfo API\n      const response = await updateUserInfo(updatePayload);\n\n      console.log('📥 PERMANENT UPDATE RESPONSE:', response);\n\n      if (response.success) {\n        console.log('🎉 PHONE NUMBER PERMANENTLY UPDATED!');\n\n        // Create updated user object\n        const updatedUser = {\n          ...user,\n          phoneNumber: newPhone  // Ensure phone number is updated\n        };\n\n        console.log('👤 Updated user object:', updatedUser);\n\n        // Update Redux store\n        dispatch(SetUser(updatedUser));\n\n        // Update localStorage - THIS ENSURES PERSISTENCE AFTER REFRESH\n        localStorage.setItem('user', JSON.stringify(updatedUser));\n\n        // Update payment phone state\n        setPaymentPhone(newPhone);\n\n        console.log('✅ ALL UPDATES COMPLETE - PHONE NUMBER SHOULD PERSIST AFTER REFRESH');\n        console.log('📱 New phone number:', newPhone);\n\n        // Verify the save worked\n        const savedUser = JSON.parse(localStorage.getItem('user'));\n        console.log('💾 Saved to localStorage:', savedUser.phoneNumber);\n\n        if (savedUser.phoneNumber === newPhone) {\n          console.log('🎉 VERIFICATION SUCCESSFUL - PHONE NUMBER SAVED CORRECTLY');\n        } else {\n          console.log('⚠️ VERIFICATION FAILED - PHONE NUMBER NOT SAVED CORRECTLY');\n        }\n\n        return true;\n      } else {\n        console.error('❌ PERMANENT UPDATE FAILED:', response);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ PERMANENT UPDATE ERROR:', error);\n      console.error('❌ Error details:', error.response?.data);\n      return false;\n    }\n  };\n  \n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone(user?.phoneNumber || '');\n    }\n  }, [isOpen, user?.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user?.phoneNumber && !isEditingPhone) {\n      console.log('🔄 User phone changed, updating payment phone:', user.phoneNumber);\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user?.phoneNumber, isEditingPhone]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone, // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n        \n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone(user?.phoneNumber || '');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n            {step === 'success' && '⏳ Processing Payment...'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('glimp') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"phone-section\">\n                  <div className=\"info-item\">\n                    <span className=\"info-label\">Phone Number for Payment:</span>\n                    {!isEditingPhone ? (\n                      <div className=\"phone-display\">\n                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>\n                          {paymentPhone || 'Not provided'}\n                          {phoneUpdated && <span className=\"updated-indicator\">✅ Updated</span>}\n                        </span>\n                        {process.env.NODE_ENV === 'development' && (\n                          <div style={{marginTop: '8px'}}>\n                            <small style={{color: '#666', fontSize: '10px', display: 'block'}}>\n                              Debug: Payment={paymentPhone} | User={user?.phoneNumber} | Updated={phoneUpdated ? 'Yes' : 'No'}\n                            </small>\n                            <button\n                              onClick={() => {\n                                console.log('🔄 Testing page refresh...');\n                                window.location.reload();\n                              }}\n                              style={{\n                                fontSize: '10px',\n                                padding: '2px 6px',\n                                marginTop: '4px',\n                                background: '#f0f0f0',\n                                border: '1px solid #ccc',\n                                borderRadius: '4px',\n                                cursor: 'pointer'\n                              }}\n                            >\n                              🔄 Test Refresh\n                            </button>\n                          </div>\n                        )}\n                        <button\n                          className=\"edit-phone-btn\"\n                          onClick={() => setIsEditingPhone(true)}\n                          type=\"button\"\n                        >\n                          ✏️ Change\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"phone-edit\">\n                        <input\n                          type=\"tel\"\n                          value={paymentPhone}\n                          onChange={(e) => setPaymentPhone(e.target.value)}\n                          placeholder=\"Enter phone number (e.g., 0744963858)\"\n                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}\n                          maxLength=\"10\"\n                        />\n                        {paymentPhone && (\n                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>\n                            {isValidPhone(paymentPhone) ? (\n                              <span className=\"validation-message valid\">✅ Valid phone number</span>\n                            ) : (\n                              <span className=\"validation-message invalid\">❌ Must start with 06 or 07 and be 10 digits</span>\n                            )}\n                          </div>\n                        )}\n                        <div className=\"phone-actions\">\n                          <button\n                            className=\"save-phone-btn\"\n                            onClick={async (e) => {\n                              e.preventDefault();\n                              console.log('🔥 SAVE BUTTON CLICKED!');\n                              console.log('📱 Payment phone:', paymentPhone);\n                              console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n\n                              if (!isValidPhone(paymentPhone)) {\n                                console.log('❌ Invalid phone number');\n                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                                return;\n                              }\n\n                              try {\n                                console.log('🔥 STARTING PERMANENT PHONE NUMBER SAVE...');\n                                console.log('📱 Phone to save:', paymentPhone);\n\n                                // Show loading state\n                                const btn = e.target;\n                                const originalText = btn.textContent;\n                                btn.textContent = '⏳ Saving...';\n                                btn.disabled = true;\n\n                                // ACTUALLY UPDATE THE USER PROFILE - NOT BACKGROUND!\n                                console.log('🔄 PERMANENTLY UPDATING USER PROFILE...');\n                                const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n\n                                if (updateSuccess) {\n                                  console.log('🎉 PHONE NUMBER PERMANENTLY SAVED!');\n\n                                  // Close editing mode\n                                  setIsEditingPhone(false);\n                                  setPhoneUpdated(true);\n\n                                  // Show success message\n                                  message.success({\n                                    content: '🎉 Phone number updated permanently!',\n                                    duration: 4,\n                                    style: {\n                                      marginTop: '20vh',\n                                      fontSize: '15px',\n                                      fontWeight: '600'\n                                    }\n                                  });\n\n                                  // Additional confirmation\n                                  setTimeout(() => {\n                                    message.info({\n                                      content: '✅ Your phone number will persist after page refresh!',\n                                      duration: 4,\n                                      style: {\n                                        marginTop: '20vh',\n                                        fontSize: '14px'\n                                      }\n                                    });\n                                  }, 1500);\n\n                                } else {\n                                  console.log('❌ PHONE NUMBER UPDATE FAILED');\n                                  message.error({\n                                    content: '❌ Failed to save phone number permanently. Please try again.',\n                                    duration: 4\n                                  });\n                                }\n\n                                // Restore button\n                                btn.textContent = originalText;\n                                btn.disabled = !isValidPhone(paymentPhone);\n\n                                // Reset the updated indicator after 8 seconds\n                                setTimeout(() => {\n                                  setPhoneUpdated(false);\n                                }, 8000);\n\n                              } catch (error) {\n                                console.error('❌ Error saving phone number:', error);\n                                message.error('Failed to save phone number. Please try again.');\n                              }\n                            }}\n                            disabled={!isValidPhone(paymentPhone)}\n                            type=\"button\"\n                          >\n                            ✅ Save\n                          </button>\n                          <button\n                            className=\"cancel-phone-btn\"\n                            onClick={() => {\n                              setPaymentPhone(user?.phoneNumber || '');\n                              setIsEditingPhone(false);\n                            }}\n                            type=\"button\"\n                          >\n                            ❌ Cancel\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"phone-note\">\n                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    console.log('💳 PAY BUTTON CLICKED!');\n                    console.log('📱 Payment phone:', paymentPhone);\n                    console.log('✏️ Is editing phone:', isEditingPhone);\n                    console.log('⏳ Payment loading:', paymentLoading);\n\n                    if (isEditingPhone) {\n                      message.warning('Please save your phone number first');\n                      return;\n                    }\n\n                    if (!paymentPhone) {\n                      message.error('Please enter a phone number');\n                      return;\n                    }\n\n                    if (!isValidPhone(paymentPhone)) {\n                      message.error('Please enter a valid phone number');\n                      return;\n                    }\n\n                    handlePayment();\n                  }}\n                  disabled={paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone)}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : isEditingPhone ? (\n                    'Save phone number first'\n                  ) : !paymentPhone ? (\n                    'Enter phone number'\n                  ) : !isValidPhone(paymentPhone) ? (\n                    'Invalid phone number'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {step === 'success' && (\n            <div className=\"success-step\">\n              <div className=\"success-animation\">\n                <div className=\"pulse-circle\">\n                  <div className=\"phone-icon\">📱</div>\n                </div>\n              </div>\n              \n              <h3>Payment Request Sent!</h3>\n              <p>Please check your phone for SMS confirmation and complete the payment.</p>\n              \n              <div className=\"payment-steps\">\n                <div className=\"step\">\n                  <span className=\"step-number\">1</span>\n                  <span className=\"step-text\">Check your phone for SMS</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">2</span>\n                  <span className=\"step-text\">Follow the payment instructions</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">3</span>\n                  <span className=\"step-text\">Your subscription will activate automatically</span>\n                </div>\n              </div>\n\n              <div className=\"success-actions\">\n                <button\n                  className=\"check-status-btn\"\n                  onClick={async () => {\n                    console.log('🔍 Manual payment check triggered');\n                    try {\n                      const response = await checkPaymentStatus();\n                      console.log('📥 Manual check response:', response);\n\n                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                        console.log('✅ Payment confirmed manually!');\n                        dispatch(SetSubscription(response));\n                        message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                        onSuccess && onSuccess();\n                        setTimeout(() => onClose(), 1000);\n                      } else {\n                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                      }\n                    } catch (error) {\n                      console.error('❌ Manual check error:', error);\n                      message.error('Error checking payment status');\n                    }\n                  }}\n                >\n                  ✅ Check Payment Status\n                </button>\n\n                <button className=\"done-btn\" onClick={handleClose}>\n                  I'll complete the payment\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,wBAAwB;AACvE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAClE,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAMmC,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,KAAK,GAAGC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW;IAC/B,OAAOF,KAAK,IAAI,gBAAgB,CAACG,IAAI,CAACH,KAAK,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMI,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEF,QAAQ,CAAC;MACnEC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEN,IAAI,CAAC;MAE1C,IAAI,CAACA,IAAI,CAACO,GAAG,EAAE;QACbF,OAAO,CAACG,KAAK,CAAC,sBAAsB,CAAC;QACrC,OAAO,KAAK;MACd;;MAEA;MACA,MAAMC,aAAa,GAAG;QACpBC,MAAM,EAAEV,IAAI,CAACO,GAAG;QAChBI,IAAI,EAAEX,IAAI,CAACW,IAAI;QACfC,KAAK,EAAEZ,IAAI,CAACY,KAAK,IAAI,EAAE;QACvBC,MAAM,EAAEb,IAAI,CAACa,MAAM,IAAI,EAAE;QACzBC,MAAM,EAAEd,IAAI,CAACe,KAAK,IAAIf,IAAI,CAACgB,SAAS,IAAI,EAAE;QAC1CC,KAAK,EAAEjB,IAAI,CAACiB,KAAK,IAAI,SAAS;QAC9BhB,WAAW,EAAEG,QAAQ,CAAE;MACzB,CAAC;;MAEDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEG,aAAa,CAAC;;MAE1D;MACA,MAAMS,QAAQ,GAAG,MAAM/C,cAAc,CAACsC,aAAa,CAAC;MAEpDJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEY,QAAQ,CAAC;MAEtD,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBd,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;QAEnD;QACA,MAAMc,WAAW,GAAG;UAClB,GAAGpB,IAAI;UACPC,WAAW,EAAEG,QAAQ,CAAE;QACzB,CAAC;;QAEDC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEc,WAAW,CAAC;;QAEnD;QACAC,QAAQ,CAAC/C,OAAO,CAAC8C,WAAW,CAAC,CAAC;;QAE9B;QACAE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,WAAW,CAAC,CAAC;;QAEzD;QACAM,eAAe,CAACtB,QAAQ,CAAC;QAEzBC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;QACjFD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,QAAQ,CAAC;;QAE7C;QACA,MAAMuB,SAAS,GAAGH,IAAI,CAACI,KAAK,CAACN,YAAY,CAACO,OAAO,CAAC,MAAM,CAAC,CAAC;QAC1DxB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEqB,SAAS,CAAC1B,WAAW,CAAC;QAE/D,IAAI0B,SAAS,CAAC1B,WAAW,KAAKG,QAAQ,EAAE;UACtCC,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QAC1E,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QAC1E;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACLD,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEU,QAAQ,CAAC;QACrD,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAsB,eAAA;MACdzB,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDH,OAAO,CAACG,KAAK,CAAC,kBAAkB,GAAAsB,eAAA,GAAEtB,KAAK,CAACU,QAAQ,cAAAY,eAAA,uBAAdA,eAAA,CAAgBC,IAAI,CAAC;MACvD,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM;IAAE/B;EAAK,CAAC,GAAGnC,WAAW,CAAEmE,KAAK,IAAKA,KAAK,CAAChC,IAAI,CAAC;EACnD,MAAMqB,QAAQ,GAAGvD,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,EAAE;MACVmD,UAAU,CAAC,CAAC;MACZ;MACAP,eAAe,CAAC,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,KAAI,EAAE,CAAC;IAC1C;EACF,CAAC,EAAE,CAACnB,MAAM,EAAEkB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,CAAC,CAAC;;EAE/B;EACArC,SAAS,CAAC,MAAM;IACd,IAAIoC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,WAAW,IAAI,CAACiC,cAAc,EAAE;MACxC7B,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEN,IAAI,CAACC,WAAW,CAAC;MAC/EyB,eAAe,CAAC1B,IAAI,CAACC,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,EAAEiC,cAAc,CAAC,CAAC;EAEvC,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,QAAQ,GAAG,MAAMlD,QAAQ,CAAC,CAAC;MACjCqB,QAAQ,CAAC8C,KAAK,CAACC,OAAO,CAAClB,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CzC,OAAO,CAACyC,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,gBAAgB,GAAIC,IAAI,IAAK;IACjC/C,eAAe,CAAC+C,IAAI,CAAC;IACrBzC,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAM0C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACjD,YAAY,EAAE;MACjBvB,OAAO,CAACyC,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF;IAEA,IAAI,CAACgC,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,EAAE,EAAE;MAC7C1E,OAAO,CAACyC,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,IAAI,CAAC,gBAAgB,CAACN,IAAI,CAACsC,YAAY,CAAC,EAAE;MACxCzE,OAAO,CAACyC,KAAK,CAAC,wEAAwE,CAAC;MACvF;IACF;IAEA,IAAI;MAAA,IAAAkC,UAAA;MACF/C,iBAAiB,CAAC,IAAI,CAAC;MACvB0B,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMmE,WAAW,GAAG;QAClBL,IAAI,EAAEhD,YAAY;QAClBoB,MAAM,EAAEV,IAAI,CAACO,GAAG;QAChBqC,SAAS,EAAEJ,YAAY;QAAE;QACzBK,SAAS,EAAE7C,IAAI,CAACY,KAAK,IAAK,IAAA8B,UAAA,GAAE1C,IAAI,CAACW,IAAI,cAAA+B,UAAA,uBAATA,UAAA,CAAWI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAM7B,QAAQ,GAAG,MAAMjD,UAAU,CAAC0E,WAAW,CAAC;MAE9C,IAAIzB,QAAQ,CAACC,OAAO,EAAE;QACpBpD,OAAO,CAACoD,OAAO,CAAC,kEAAkE,CAAC;QACnFtB,OAAO,CAAC,SAAS,CAAC;;QAElB;QACAmD,wBAAwB,CAAC9B,QAAQ,CAAC+B,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAChC,QAAQ,CAACnD,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOyC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCzC,OAAO,CAACyC,KAAK,CAACA,KAAK,CAACzC,OAAO,IAAI,mCAAmC,CAAC;IACrE,CAAC,SAAS;MACR4B,iBAAiB,CAAC,KAAK,CAAC;MACxB0B,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMyE,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFF,QAAQ,EAAE;QACV/C,OAAO,CAACC,GAAG,CAAE,yCAAwC8C,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAMnC,QAAQ,GAAG,MAAMhD,kBAAkB,CAAC,CAAC;QAC3CmC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEY,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACV,KAAK,IAAIU,QAAQ,CAACqC,aAAa,KAAK,MAAM,IAAIrC,QAAQ,CAACsC,MAAM,KAAK,QAAQ,EAAE;UACpGnD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACAe,QAAQ,CAAChD,eAAe,CAAC6C,QAAQ,CAAC,CAAC;;UAEnC;UACAnD,OAAO,CAACoD,OAAO,CAAC;YACdsC,OAAO,EAAE,2CAA2C;YACpDC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;;UAEF;UACA9E,SAAS,IAAIA,SAAS,CAAC,CAAC;;UAExB;UACA+E,UAAU,CAAC,MAAM;YACfhF,OAAO,CAAC,CAAC;UACX,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,IAAI;QACb;QAEA,IAAIqE,QAAQ,IAAIC,WAAW,EAAE;UAC3BhD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CvC,OAAO,CAACiG,OAAO,CAAC;YACdP,OAAO,EAAE,sGAAsG;YAC/GC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,OAAO,KAAK;QACd;;QAEA;QACAK,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAO9C,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI4C,QAAQ,IAAIC,WAAW,EAAE;UAC3BtF,OAAO,CAACyC,KAAK,CAAC,4EAA4E,CAAC;QAC7F,CAAC,MAAM;UACLuD,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACAA,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBpE,OAAO,CAAC,OAAO,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,KAAK,CAAC;IACxBuE,iBAAiB,CAAC,KAAK,CAAC;IACxBC,eAAe,CAAC,KAAK,CAAC;IACtBzC,eAAe,CAAC,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,KAAI,EAAE,CAAC;IACxClB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKsC,SAAS,EAAC,4BAA4B;IAAAoD,QAAA,eACzC1F,OAAA;MAAKsC,SAAS,EAAC,oBAAoB;MAAAoD,QAAA,gBACjC1F,OAAA;QAAKsC,SAAS,EAAC,cAAc;QAAAoD,QAAA,gBAC3B1F,OAAA;UAAIsC,SAAS,EAAC,aAAa;UAAAoD,QAAA,GACxBxE,IAAI,KAAK,OAAO,IAAI,8BAA8B,EAClDA,IAAI,KAAK,SAAS,IAAI,0BAA0B,EAChDA,IAAI,KAAK,SAAS,IAAI,yBAAyB;QAAA;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACL9F,OAAA;UAAQsC,SAAS,EAAC,cAAc;UAACyD,OAAO,EAAER,WAAY;UAAAG,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEN9F,OAAA;QAAKsC,SAAS,EAAC,eAAe;QAAAoD,QAAA,GAC3BxE,IAAI,KAAK,OAAO,iBACflB,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAAAoD,QAAA,EACxB5E,OAAO,gBACNd,OAAA;YAAKsC,SAAS,EAAC,eAAe;YAAAoD,QAAA,gBAC5B1F,OAAA;cAAKsC,SAAS,EAAC;YAAS;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/B9F,OAAA;cAAA0F,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAENpF,KAAK,CAACsF,GAAG,CAAEpC,IAAI;YAAA,IAAAqC,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA;YAAA,oBACbpG,OAAA;cAAoBsC,SAAS,EAAC,WAAW;cAACyD,OAAO,EAAEA,CAAA,KAAMpC,gBAAgB,CAACC,IAAI,CAAE;cAAA8B,QAAA,gBAC9E1F,OAAA;gBAAKsC,SAAS,EAAC,aAAa;gBAAAoD,QAAA,gBAC1B1F,OAAA;kBAAIsC,SAAS,EAAC,YAAY;kBAAAoD,QAAA,EAAE9B,IAAI,CAACyC;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAG,WAAA,GAAArC,IAAI,CAACyC,KAAK,cAAAJ,WAAA,uBAAVA,WAAA,CAAY5B,WAAW,CAAC,CAAC,CAACiC,QAAQ,CAAC,OAAO,CAAC,kBAC1CtG,OAAA;kBAAMsC,SAAS,EAAC,YAAY;kBAAAoD,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9F,OAAA;gBAAKsC,SAAS,EAAC,YAAY;gBAAAoD,QAAA,gBACzB1F,OAAA;kBAAMsC,SAAS,EAAC,cAAc;kBAAAoD,QAAA,IAAAQ,qBAAA,GAAEtC,IAAI,CAAC2C,eAAe,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACjFlC,IAAI,CAAC6C,WAAW,IAAI7C,IAAI,CAAC6C,WAAW,KAAK7C,IAAI,CAAC2C,eAAe,iBAC5DvG,OAAA;kBAAMsC,SAAS,EAAC,gBAAgB;kBAAAoD,QAAA,GAAE9B,IAAI,CAAC6C,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/E,eACD9F,OAAA;kBAAMsC,SAAS,EAAC,cAAc;kBAAAoD,QAAA,GAAE9B,IAAI,CAACoB,QAAQ,EAAC,QAAM,EAACpB,IAAI,CAACoB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eAEN9F,OAAA;gBAAKsC,SAAS,EAAC,eAAe;gBAAAoD,QAAA,IAAAS,cAAA,GAC3BvC,IAAI,CAAC8C,QAAQ,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,KAAK,kBAC7C7G,OAAA;kBAAiBsC,SAAS,EAAC,SAAS;kBAAAoD,QAAA,gBAClC1F,OAAA;oBAAMsC,SAAS,EAAC,cAAc;oBAAAoD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC9F,OAAA;oBAAMsC,SAAS,EAAC,cAAc;oBAAAoD,QAAA,EAAEkB;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFvCe,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC,EACD,EAAAM,eAAA,GAAAxC,IAAI,CAAC8C,QAAQ,cAAAN,eAAA,uBAAbA,eAAA,CAAerC,MAAM,IAAG,CAAC,iBACxB/D,OAAA;kBAAKsC,SAAS,EAAC,SAAS;kBAAAoD,QAAA,gBACtB1F,OAAA;oBAAMsC,SAAS,EAAC,cAAc;oBAAAoD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC9F,OAAA;oBAAMsC,SAAS,EAAC,cAAc;oBAAAoD,QAAA,GAAE9B,IAAI,CAAC8C,QAAQ,CAAC3C,MAAM,GAAG,CAAC,EAAC,gBAAc;kBAAA;oBAAA4B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9F,OAAA;gBAAQsC,SAAS,EAAC,iBAAiB;gBAAAoD,QAAA,GAAC,SAC3B,EAAC9B,IAAI,CAACyC,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjCDlC,IAAI,CAAC/B,GAAG;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCb,CAAC;UAAA,CACP;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEA5E,IAAI,KAAK,SAAS,IAAIN,YAAY,iBACjCZ,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAoD,QAAA,gBAC3B1F,OAAA;YAAKsC,SAAS,EAAC,uBAAuB;YAAAoD,QAAA,gBACpC1F,OAAA;cAAA0F,QAAA,GAAI,iBAAe,EAAC9E,YAAY,CAACyF,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C9F,OAAA;cAAGsC,SAAS,EAAC,oBAAoB;cAAAoD,QAAA,IAAAlF,qBAAA,GAC9BI,YAAY,CAAC2F,eAAe,cAAA/F,qBAAA,uBAA5BA,qBAAA,CAA8BgG,cAAc,CAAC,CAAC,EAAC,WAAS,EAAC5F,YAAY,CAACoE,QAAQ,EAAC,QAAM,EAACpE,YAAY,CAACoE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN9F,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAoD,QAAA,gBAC3B1F,OAAA;cAAKsC,SAAS,EAAC,eAAe;cAAAoD,QAAA,gBAC5B1F,OAAA;gBAAKsC,SAAS,EAAC,WAAW;gBAAAoD,QAAA,gBACxB1F,OAAA;kBAAMsC,SAAS,EAAC,YAAY;kBAAAoD,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC5D,CAACtC,cAAc,gBACdxD,OAAA;kBAAKsC,SAAS,EAAC,eAAe;kBAAAoD,QAAA,gBAC5B1F,OAAA;oBAAMsC,SAAS,EAAG,cAAawE,YAAY,GAAG,SAAS,GAAG,EAAG,EAAE;oBAAApB,QAAA,GAC5D5B,YAAY,IAAI,cAAc,EAC9BgD,YAAY,iBAAI9G,OAAA;sBAAMsC,SAAS,EAAC,mBAAmB;sBAAAoD,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,EACNiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCjH,OAAA;oBAAKiF,KAAK,EAAE;sBAACC,SAAS,EAAE;oBAAK,CAAE;oBAAAQ,QAAA,gBAC7B1F,OAAA;sBAAOiF,KAAK,EAAE;wBAACiC,KAAK,EAAE,MAAM;wBAAE/B,QAAQ,EAAE,MAAM;wBAAEgC,OAAO,EAAE;sBAAO,CAAE;sBAAAzB,QAAA,GAAC,iBAClD,EAAC5B,YAAY,EAAC,UAAQ,EAACxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,EAAC,aAAW,EAACuF,YAAY,GAAG,KAAK,GAAG,IAAI;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,eACR9F,OAAA;sBACE+F,OAAO,EAAEA,CAAA,KAAM;wBACbpE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;wBACzCwF,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;sBAC1B,CAAE;sBACFrC,KAAK,EAAE;wBACLE,QAAQ,EAAE,MAAM;wBAChBoC,OAAO,EAAE,SAAS;wBAClBrC,SAAS,EAAE,KAAK;wBAChBsC,UAAU,EAAE,SAAS;wBACrBC,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBC,MAAM,EAAE;sBACV,CAAE;sBAAAjC,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,eACD9F,OAAA;oBACEsC,SAAS,EAAC,gBAAgB;oBAC1ByD,OAAO,EAAEA,CAAA,KAAMP,iBAAiB,CAAC,IAAI,CAAE;oBACvCoC,IAAI,EAAC,QAAQ;oBAAAlC,QAAA,EACd;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAEN9F,OAAA;kBAAKsC,SAAS,EAAC,YAAY;kBAAAoD,QAAA,gBACzB1F,OAAA;oBACE4H,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAE/D,YAAa;oBACpBgE,QAAQ,EAAGC,CAAC,IAAK/E,eAAe,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,WAAW,EAAC,uCAAuC;oBACnD3F,SAAS,EAAG,eAAcwB,YAAY,GAAIoE,YAAY,CAACpE,YAAY,CAAC,GAAG,OAAO,GAAG,SAAS,GAAI,EAAG,EAAE;oBACnGqE,SAAS,EAAC;kBAAI;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACDhC,YAAY,iBACX9D,OAAA;oBAAKsC,SAAS,EAAG,oBAAmB4F,YAAY,CAACpE,YAAY,CAAC,GAAG,OAAO,GAAG,SAAU,EAAE;oBAAA4B,QAAA,EACpFwC,YAAY,CAACpE,YAAY,CAAC,gBACzB9D,OAAA;sBAAMsC,SAAS,EAAC,0BAA0B;sBAAAoD,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEtE9F,OAAA;sBAAMsC,SAAS,EAAC,4BAA4B;sBAAAoD,QAAA,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC/F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eACD9F,OAAA;oBAAKsC,SAAS,EAAC,eAAe;oBAAAoD,QAAA,gBAC5B1F,OAAA;sBACEsC,SAAS,EAAC,gBAAgB;sBAC1ByD,OAAO,EAAE,MAAOgC,CAAC,IAAK;wBACpBA,CAAC,CAACK,cAAc,CAAC,CAAC;wBAClBzG,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;wBACtCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkC,YAAY,CAAC;wBAC9CnC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsG,YAAY,CAACpE,YAAY,CAAC,CAAC;wBAE5D,IAAI,CAACoE,YAAY,CAACpE,YAAY,CAAC,EAAE;0BAC/BnC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;0BACrCvC,OAAO,CAACyC,KAAK,CAAC,wEAAwE,CAAC;0BACvF;wBACF;wBAEA,IAAI;0BACFH,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;0BACzDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkC,YAAY,CAAC;;0BAE9C;0BACA,MAAMuE,GAAG,GAAGN,CAAC,CAACC,MAAM;0BACpB,MAAMM,YAAY,GAAGD,GAAG,CAACE,WAAW;0BACpCF,GAAG,CAACE,WAAW,GAAG,aAAa;0BAC/BF,GAAG,CAACG,QAAQ,GAAG,IAAI;;0BAEnB;0BACA7G,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;0BACtD,MAAM6G,aAAa,GAAG,MAAMhH,qBAAqB,CAACqC,YAAY,CAAC;0BAE/D,IAAI2E,aAAa,EAAE;4BACjB9G,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;4BAEjD;4BACA4D,iBAAiB,CAAC,KAAK,CAAC;4BACxBC,eAAe,CAAC,IAAI,CAAC;;4BAErB;4BACApG,OAAO,CAACoD,OAAO,CAAC;8BACdsC,OAAO,EAAE,sCAAsC;8BAC/CC,QAAQ,EAAE,CAAC;8BACXC,KAAK,EAAE;gCACLC,SAAS,EAAE,MAAM;gCACjBC,QAAQ,EAAE,MAAM;gCAChBC,UAAU,EAAE;8BACd;4BACF,CAAC,CAAC;;4BAEF;4BACAC,UAAU,CAAC,MAAM;8BACfhG,OAAO,CAACqJ,IAAI,CAAC;gCACX3D,OAAO,EAAE,sDAAsD;gCAC/DC,QAAQ,EAAE,CAAC;gCACXC,KAAK,EAAE;kCACLC,SAAS,EAAE,MAAM;kCACjBC,QAAQ,EAAE;gCACZ;8BACF,CAAC,CAAC;4BACJ,CAAC,EAAE,IAAI,CAAC;0BAEV,CAAC,MAAM;4BACLxD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;4BAC3CvC,OAAO,CAACyC,KAAK,CAAC;8BACZiD,OAAO,EAAE,8DAA8D;8BACvEC,QAAQ,EAAE;4BACZ,CAAC,CAAC;0BACJ;;0BAEA;0BACAqD,GAAG,CAACE,WAAW,GAAGD,YAAY;0BAC9BD,GAAG,CAACG,QAAQ,GAAG,CAACN,YAAY,CAACpE,YAAY,CAAC;;0BAE1C;0BACAuB,UAAU,CAAC,MAAM;4BACfI,eAAe,CAAC,KAAK,CAAC;0BACxB,CAAC,EAAE,IAAI,CAAC;wBAEV,CAAC,CAAC,OAAO3D,KAAK,EAAE;0BACdH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;0BACpDzC,OAAO,CAACyC,KAAK,CAAC,gDAAgD,CAAC;wBACjE;sBACF,CAAE;sBACF0G,QAAQ,EAAE,CAACN,YAAY,CAACpE,YAAY,CAAE;sBACtC8D,IAAI,EAAC,QAAQ;sBAAAlC,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT9F,OAAA;sBACEsC,SAAS,EAAC,kBAAkB;sBAC5ByD,OAAO,EAAEA,CAAA,KAAM;wBACb/C,eAAe,CAAC,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,KAAI,EAAE,CAAC;wBACxCiE,iBAAiB,CAAC,KAAK,CAAC;sBAC1B,CAAE;sBACFoC,IAAI,EAAC,QAAQ;sBAAAlC,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN9F,OAAA;gBAAKsC,SAAS,EAAC,YAAY;gBAAAoD,QAAA,eACzB1F,OAAA;kBAAA0F,QAAA,EAAO;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9F,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAoD,QAAA,gBACxB1F,OAAA;gBAAMsC,SAAS,EAAC,YAAY;gBAAAoD,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnD9F,OAAA;gBAAMsC,SAAS,EAAC,YAAY;gBAAAoD,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAoD,QAAA,gBAC9B1F,OAAA;cAAQsC,SAAS,EAAC,UAAU;cAACyD,OAAO,EAAEA,CAAA,KAAM5E,OAAO,CAAC,OAAO,CAAE;cAAAuE,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9F,OAAA;cACEsC,SAAS,EAAC,SAAS;cACnByD,OAAO,EAAGgC,CAAC,IAAK;gBACdA,CAAC,CAACK,cAAc,CAAC,CAAC;gBAClBzG,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;gBACrCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkC,YAAY,CAAC;gBAC9CnC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4B,cAAc,CAAC;gBACnD7B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEZ,cAAc,CAAC;gBAEjD,IAAIwC,cAAc,EAAE;kBAClBnE,OAAO,CAACiG,OAAO,CAAC,qCAAqC,CAAC;kBACtD;gBACF;gBAEA,IAAI,CAACxB,YAAY,EAAE;kBACjBzE,OAAO,CAACyC,KAAK,CAAC,6BAA6B,CAAC;kBAC5C;gBACF;gBAEA,IAAI,CAACoG,YAAY,CAACpE,YAAY,CAAC,EAAE;kBAC/BzE,OAAO,CAACyC,KAAK,CAAC,mCAAmC,CAAC;kBAClD;gBACF;gBAEA+B,aAAa,CAAC,CAAC;cACjB,CAAE;cACF2E,QAAQ,EAAExH,cAAc,IAAI,CAAC8C,YAAY,IAAIN,cAAc,IAAI,CAAC0E,YAAY,CAACpE,YAAY,CAAE;cAAA4B,QAAA,EAE1F1E,cAAc,gBACbhB,OAAA,CAAAE,SAAA;gBAAAwF,QAAA,gBACE1F,OAAA;kBAAMsC,SAAS,EAAC;gBAAa;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEvC;cAAA,eAAE,CAAC,GACDtC,cAAc,GAChB,yBAAyB,GACvB,CAACM,YAAY,GACf,oBAAoB,GAClB,CAACoE,YAAY,CAACpE,YAAY,CAAC,GAC7B,sBAAsB,GAErB,OAAI,CAAArD,sBAAA,GAAEG,YAAY,CAAC2F,eAAe,cAAA9F,sBAAA,uBAA5BA,sBAAA,CAA8B+F,cAAc,CAAC,CAAE;YACvD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA5E,IAAI,KAAK,SAAS,iBACjBlB,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAoD,QAAA,gBAC3B1F,OAAA;YAAKsC,SAAS,EAAC,mBAAmB;YAAAoD,QAAA,eAChC1F,OAAA;cAAKsC,SAAS,EAAC,cAAc;cAAAoD,QAAA,eAC3B1F,OAAA;gBAAKsC,SAAS,EAAC,YAAY;gBAAAoD,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA;YAAA0F,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B9F,OAAA;YAAA0F,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE7E9F,OAAA;YAAKsC,SAAS,EAAC,eAAe;YAAAoD,QAAA,gBAC5B1F,OAAA;cAAKsC,SAAS,EAAC,MAAM;cAAAoD,QAAA,gBACnB1F,OAAA;gBAAMsC,SAAS,EAAC,aAAa;gBAAAoD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC9F,OAAA;gBAAMsC,SAAS,EAAC,WAAW;gBAAAoD,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN9F,OAAA;cAAKsC,SAAS,EAAC,MAAM;cAAAoD,QAAA,gBACnB1F,OAAA;gBAAMsC,SAAS,EAAC,aAAa;gBAAAoD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC9F,OAAA;gBAAMsC,SAAS,EAAC,WAAW;gBAAAoD,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN9F,OAAA;cAAKsC,SAAS,EAAC,MAAM;cAAAoD,QAAA,gBACnB1F,OAAA;gBAAMsC,SAAS,EAAC,aAAa;gBAAAoD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC9F,OAAA;gBAAMsC,SAAS,EAAC,WAAW;gBAAAoD,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9F,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAoD,QAAA,gBAC9B1F,OAAA;cACEsC,SAAS,EAAC,kBAAkB;cAC5ByD,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnBpE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;gBAChD,IAAI;kBACF,MAAMY,QAAQ,GAAG,MAAMhD,kBAAkB,CAAC,CAAC;kBAC3CmC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEY,QAAQ,CAAC;kBAElD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACV,KAAK,IAAIU,QAAQ,CAACqC,aAAa,KAAK,MAAM,IAAIrC,QAAQ,CAACsC,MAAM,KAAK,QAAQ,EAAE;oBACpGnD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;oBAC5Ce,QAAQ,CAAChD,eAAe,CAAC6C,QAAQ,CAAC,CAAC;oBACnCnD,OAAO,CAACoD,OAAO,CAAC,2CAA2C,CAAC;oBAC5DnC,SAAS,IAAIA,SAAS,CAAC,CAAC;oBACxB+E,UAAU,CAAC,MAAMhF,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;kBACnC,CAAC,MAAM;oBACLhB,OAAO,CAACqJ,IAAI,CAAC,0EAA0E,CAAC;kBAC1F;gBACF,CAAC,CAAC,OAAO5G,KAAK,EAAE;kBACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;kBAC7CzC,OAAO,CAACyC,KAAK,CAAC,+BAA+B,CAAC;gBAChD;cACF,CAAE;cAAA4D,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET9F,OAAA;cAAQsC,SAAS,EAAC,UAAU;cAACyD,OAAO,EAAER,WAAY;cAAAG,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvF,EAAA,CA9lBIJ,iBAAiB;EAAA,QAuFJhB,WAAW,EACXC,WAAW;AAAA;AAAAuJ,EAAA,GAxFxBxI,iBAAiB;AAgmBvB,eAAeA,iBAAiB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}