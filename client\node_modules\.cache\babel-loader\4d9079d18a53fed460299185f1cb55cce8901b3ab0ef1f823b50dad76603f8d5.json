{"ast": null, "code": "import usersSlice from\"./usersSlice\";import{configureStore}from\"@reduxjs/toolkit\";import loaderSlice from\"./loaderSlice\";import subscriptionSlice from\"./subscriptionSlice\";import paymentSlice from\"./paymentSlice\";const store=configureStore({reducer:{user:usersSlice,loader:loaderSlice,subscription:subscriptionSlice,payment:paymentSlice}});export default store;", "map": {"version": 3, "names": ["usersSlice", "configureStore", "loaderSlice", "subscriptionSlice", "paymentSlice", "store", "reducer", "user", "loader", "subscription", "payment"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/redux/store.js"], "sourcesContent": ["import usersSlice from \"./usersSlice\";\r\nimport { configureStore } from \"@reduxjs/toolkit\";\r\nimport loaderSlice from \"./loaderSlice\";\r\nimport subscriptionSlice from \"./subscriptionSlice\";\r\nimport paymentSlice from \"./paymentSlice\";\r\n\r\nconst store = configureStore({\r\n  reducer: {\r\n    user: usersSlice,\r\n    loader: loaderSlice,\r\n    subscription: subscriptionSlice,\r\n    payment: paymentSlice\r\n  },\r\n});\r\n\r\nexport default store;\r\n"], "mappings": "AAAA,MAAO,CAAAA,UAAU,KAAM,cAAc,CACrC,OAASC,cAAc,KAAQ,kBAAkB,CACjD,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CACnD,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CAEzC,KAAM,CAAAC,KAAK,CAAGJ,cAAc,CAAC,CAC3BK,OAAO,CAAE,CACPC,IAAI,CAAEP,UAAU,CAChBQ,MAAM,CAAEN,WAAW,CACnBO,YAAY,CAAEN,iBAAiB,CAC/BO,OAAO,CAAEN,YACX,CACF,CAAC,CAAC,CAEF,cAAe,CAAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}