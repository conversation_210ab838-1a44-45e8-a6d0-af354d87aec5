// Comprehensive Payment System Test - ZenoPay Integration
const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

console.log('🧪 Comprehensive ZenoPay Payment System Test\n');

// Configuration
const BASE_URL = 'http://localhost:5000';
const ZENOPAY_API_URL = 'https://zenoapi.com/api/payments/mobile_money_tanzania';
const ZENOPAY_STATUS_URL = 'https://zenoapi.com/api/payments/order-status';

// Test data
const testPaymentData = {
  order_id: `TEST_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  buyer_email: '<EMAIL>',
  buyer_name: 'Test User BrainWave',
  buyer_phone: '**********', // Valid Tanzanian format
  amount: 1000 // 1000 TZS for testing
};

// Add webhook URL if configured
if (process.env.ZENOPAY_WEBHOOK_URL) {
  testPaymentData.webhook_url = process.env.ZENOPAY_WEBHOOK_URL;
}

// Test functions
async function testEnvironmentConfiguration() {
  console.log('📋 1. Testing Environment Configuration...');
  
  const requiredVars = [
    'ZENOPAY_ACCOUNT_ID',
    'ZENOPAY_API_KEY',
    'ZENOPAY_WEBHOOK_URL'
  ];
  
  let allConfigured = true;
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: SET`);
    } else {
      console.log(`❌ ${varName}: MISSING`);
      allConfigured = false;
    }
  });
  
  console.log(`\n📊 Configuration Status: ${allConfigured ? '✅ COMPLETE' : '❌ INCOMPLETE'}\n`);
  return allConfigured;
}

async function testServerHealth() {
  console.log('🏥 2. Testing Server Health...');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/health`, { timeout: 5000 });
    console.log('✅ Server is running and healthy');
    console.log(`📊 Response: ${response.status} - ${response.data?.message || 'OK'}\n`);
    return true;
  } catch (error) {
    console.log('❌ Server health check failed');
    console.log(`📊 Error: ${error.message}\n`);
    return false;
  }
}

async function testWebhookEndpoint() {
  console.log('🔔 3. Testing Webhook Endpoint...');
  
  try {
    const response = await axios.get(`${BASE_URL}/api/payment/webhook-test`, { timeout: 5000 });
    console.log('✅ Webhook endpoint is accessible');
    console.log(`📊 Response: ${response.status} - ${response.data?.message || 'OK'}\n`);
    return true;
  } catch (error) {
    console.log('❌ Webhook endpoint test failed');
    console.log(`📊 Error: ${error.message}\n`);
    return false;
  }
}

async function testZenoPayAPIConnection() {
  console.log('🌐 4. Testing ZenoPay API Connection...');
  
  try {
    console.log('📤 Sending test payment request to ZenoPay...');
    console.log('📋 Test data:', JSON.stringify(testPaymentData, null, 2));
    
    const response = await axios.post(ZENOPAY_API_URL, testPaymentData, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 30000
    });

    console.log('✅ ZenoPay API connection successful');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.status === 'success') {
      console.log('🎉 Payment initiation successful!');
      console.log(`📱 SMS should be sent to: ${testPaymentData.buyer_phone}`);
      return { success: true, orderId: response.data.order_id };
    } else {
      console.log('⚠️ Payment initiation returned error:', response.data.message);
      return { success: false, error: response.data.message };
    }
    
  } catch (error) {
    console.log('❌ ZenoPay API connection failed');
    console.log(`📊 Error: ${error.message}`);
    
    if (error.response) {
      console.log(`📊 HTTP Status: ${error.response.status}`);
      console.log(`📊 Response Data:`, error.response.data);
      
      if (error.response.status === 403) {
        console.log('\n🚨 IP WHITELIST ISSUE:');
        console.log('Your server IP needs to be whitelisted in ZenoPay.');
        console.log('Contact ZenoPay support to whitelist your IP address.');
      } else if (error.response.status === 401) {
        console.log('\n🔑 AUTHENTICATION ISSUE:');
        console.log('Check if your ZENOPAY_API_KEY is correct.');
      }
    }
    
    console.log('');
    return { success: false, error: error.message };
  }
}

async function testOrderStatusAPI() {
  console.log('🔍 5. Testing Order Status API...');
  
  try {
    const testOrderId = testPaymentData.order_id;
    const response = await axios.get(`${ZENOPAY_STATUS_URL}?order_id=${testOrderId}`, {
      headers: {
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 15000
    });

    console.log('✅ Order Status API is accessible');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.result === 'SUCCESS') {
      console.log('🎉 Order status check successful!');
    } else {
      console.log('📝 Order not found (expected for test order)');
    }
    
    console.log('');
    return true;
    
  } catch (error) {
    if (error.response && error.response.status === 404) {
      console.log('✅ Order Status API is working (test order not found - expected)');
    } else {
      console.log('❌ Order Status API test failed');
      console.log(`📊 Error: ${error.message}`);
      if (error.response) {
        console.log(`📊 Status: ${error.response.status}`);
        console.log(`📊 Data:`, error.response.data);
      }
    }
    
    console.log('');
    return false;
  }
}

async function testWebhookSimulation() {
  console.log('🔔 6. Testing Webhook Simulation...');
  
  try {
    const webhookPayload = {
      order_id: testPaymentData.order_id,
      payment_status: "COMPLETED",
      reference: `ref_${Date.now()}`,
      metadata: {
        test: true
      }
    };
    
    console.log('📤 Simulating webhook call...');
    console.log('📋 Webhook payload:', JSON.stringify(webhookPayload, null, 2));
    
    const response = await axios.post(`${BASE_URL}/api/payment/webhook`, webhookPayload, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 10000
    });

    console.log('✅ Webhook simulation successful');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));
    console.log('');
    return true;
    
  } catch (error) {
    console.log('❌ Webhook simulation failed');
    console.log(`📊 Error: ${error.message}`);
    if (error.response) {
      console.log(`📊 Status: ${error.response.status}`);
      console.log(`📊 Data:`, error.response.data);
    }
    console.log('');
    return false;
  }
}

// Main test runner
async function runComprehensiveTests() {
  console.log('🚀 Starting Comprehensive Payment System Tests...\n');
  
  const results = {
    environment: false,
    serverHealth: false,
    webhook: false,
    zenoPayAPI: false,
    orderStatus: false,
    webhookSimulation: false
  };
  
  // Run all tests
  results.environment = await testEnvironmentConfiguration();
  results.serverHealth = await testServerHealth();
  results.webhook = await testWebhookEndpoint();
  results.zenoPayAPI = (await testZenoPayAPIConnection()).success;
  results.orderStatus = await testOrderStatusAPI();
  results.webhookSimulation = await testWebhookSimulation();
  
  // Summary
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('========================');
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.padEnd(20)}: ${status}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n📈 Overall Score: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! Payment system is ready for use.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  console.log('\n💡 Next Steps:');
  console.log('1. If ZenoPay API tests failed, contact <EMAIL>');
  console.log('2. If server tests failed, check your server configuration');
  console.log('3. Test the full payment flow in your application');
  console.log('4. Monitor webhook calls during real payments');
}

// Run the tests
runComprehensiveTests().catch(console.error);
