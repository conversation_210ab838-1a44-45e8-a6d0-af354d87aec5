<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainWave Demo Mode Tester</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .mode-selector {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }
        .mode-btn {
            padding: 12px 24px;
            border: 2px solid #4f46e5;
            border-radius: 10px;
            background: white;
            color: #4f46e5;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        .mode-btn.active {
            background: #4f46e5;
            color: white;
        }
        .test-section {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .test-result {
            background: #f1f5f9;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .btn {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-1px);
        }
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 BrainWave Demo Mode Tester</h1>
            <p>Test different user scenarios: Paid User vs New User</p>
        </div>

        <div class="mode-selector">
            <button class="mode-btn active" onclick="setMode('paid_user')">
                👑 Paid User (Has Subscription)
            </button>
            <button class="mode-btn" onclick="setMode('new_user')">
                🆕 New User (Needs Subscription)
            </button>
        </div>

        <div id="currentMode" class="status info">
            Current Mode: Paid User (paymentRequired: false)
        </div>

        <div class="test-section">
            <h3>🔧 API Tests</h3>
            <button class="btn" onclick="testLogin()">🔐 Test Login</button>
            <button class="btn" onclick="testGetUserInfo()">👤 Get User Info</button>
            <button class="btn" onclick="testSubscriptionStatus()">📋 Subscription Status</button>
            <button class="btn" onclick="testPaymentStatus()">💳 Payment Status</button>
            <button class="btn" onclick="testAllAPIs()">🚀 Test All</button>
        </div>

        <div id="results"></div>

        <div class="test-section">
            <h3>📱 Expected Behavior</h3>
            <div id="expectedBehavior">
                <div class="status info">
                    <strong>Paid User Mode:</strong><br>
                    • Should NOT see subscription modal<br>
                    • Should have full access to all features<br>
                    • Profile shows active subscription<br>
                    • paymentRequired: false
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🌐 Quick Links</h3>
            <button class="btn" onclick="openApp()">📱 Open React App</button>
            <button class="btn" onclick="openProfile()">👤 Profile Demo</button>
            <button class="btn" onclick="restartServer('paid_user')">🔄 Restart as Paid User</button>
            <button class="btn" onclick="restartServer('new_user')">🔄 Restart as New User</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        let currentMode = 'paid_user';

        function setMode(mode) {
            currentMode = mode;
            
            // Update button states
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update current mode display
            const modeText = mode === 'paid_user' ? 'Paid User (paymentRequired: false)' : 'New User (paymentRequired: true)';
            document.getElementById('currentMode').textContent = `Current Mode: ${modeText}`;
            
            // Update expected behavior
            const expectedDiv = document.getElementById('expectedBehavior');
            if (mode === 'paid_user') {
                expectedDiv.innerHTML = `
                    <div class="status info">
                        <strong>Paid User Mode:</strong><br>
                        • Should NOT see subscription modal<br>
                        • Should have full access to all features<br>
                        • Profile shows active subscription<br>
                        • paymentRequired: false
                    </div>
                `;
            } else {
                expectedDiv.innerHTML = `
                    <div class="status info">
                        <strong>New User Mode:</strong><br>
                        • Should see subscription modal<br>
                        • Should be redirected to profile for subscription<br>
                        • Profile shows subscription options<br>
                        • paymentRequired: true
                    </div>
                `;
            }
            
            showResult('info', `Mode switched to: ${mode}`);
        }

        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/users/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: '<EMAIL>', password: 'test123' })
                });
                const result = await response.json();
                showResult('success', `Login: ${result.message}`, result);
            } catch (error) {
                showResult('error', 'Login failed: ' + error.message);
            }
        }

        async function testGetUserInfo() {
            try {
                const response = await fetch(`${API_BASE}/api/users/get-user-info`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: 'demo-user-id' })
                });
                const result = await response.json();
                showResult('success', `User Info: ${result.message}`, result.data);
            } catch (error) {
                showResult('error', 'Get user info failed: ' + error.message);
            }
        }

        async function testSubscriptionStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/subscription/status`);
                const result = await response.json();
                showResult('success', `Subscription: ${result.message}`, result.data);
            } catch (error) {
                showResult('error', 'Subscription status failed: ' + error.message);
            }
        }

        async function testPaymentStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/payment/check-payment-status`);
                const result = await response.json();
                if (response.ok) {
                    showResult('success', `Payment Status: ${result.message}`, result);
                } else {
                    showResult('info', `Payment Status: ${result.error} (Expected for new users)`, result);
                }
            } catch (error) {
                showResult('error', 'Payment status failed: ' + error.message);
            }
        }

        async function testAllAPIs() {
            showResult('info', 'Running all API tests...');
            await testLogin();
            await testGetUserInfo();
            await testSubscriptionStatus();
            await testPaymentStatus();
            showResult('success', 'All API tests completed!');
        }

        function showResult(type, message, data = null) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result`;
            
            let content = `[${new Date().toLocaleTimeString()}] ${message}`;
            if (data) {
                content += '\n\nData:\n' + JSON.stringify(data, null, 2);
            }
            
            resultDiv.textContent = content;
            resultsDiv.appendChild(resultDiv);
            
            // Scroll to bottom
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function openApp() {
            window.open('http://localhost:3000', '_blank');
        }

        function openProfile() {
            window.open('file:///c:/Users/<USER>/Desktop/20/New folder/profile-demo.html', '_blank');
        }

        function restartServer(mode) {
            showResult('info', `Note: To restart server in ${mode} mode, run:\nDEMO_MODE=${mode} node basic-server.js`);
        }

        // Test server connection on load
        window.addEventListener('load', () => {
            fetch(`${API_BASE}/api/health`)
                .then(response => response.json())
                .then(result => {
                    showResult('success', '✅ Server connected successfully!');
                })
                .catch(error => {
                    showResult('error', '❌ Server connection failed: ' + error.message);
                });
        });
    </script>
</body>
</html>
