// Test Mushi's Phone Number Validation
console.log('🧪 Testing Mushi\'s Phone Number Validation...\n');

// Your exact phone number
const mushiPhone = "0765528549";

// Updated Tanzania phone regex
const phoneRegex = /^0[67][1-9]\d{7}$/;

console.log('📱 Testing phone number:', mushiPhone);
console.log('📱 Phone length:', mushiPhone.length);
console.log('📱 Regex pattern:', phoneRegex.toString());
console.log('📱 Regex test result:', phoneRegex.test(mushiPhone));

// Break down the validation
console.log('\n🔍 Detailed validation:');
console.log('  - Starts with 0?', mushiPhone[0] === '0');
console.log('  - Second digit is 6 or 7?', ['6', '7'].includes(mushiPhone[1]));
console.log('  - Third digit is 1-9?', /[1-9]/.test(mushiPhone[2]));
console.log('  - Remaining 7 digits are numbers?', /\d{7}$/.test(mushiPhone.slice(3)));
console.log('  - Total length is 10?', mushiPhone.length === 10);

// Test the exact pattern parts
console.log('\n📋 Pattern breakdown:');
console.log('  - Pattern: 0[67][1-9]\\d{7}');
console.log('  - Your number: 0765528549');
console.log('  - 0: ✅ matches 0');
console.log('  - 7: ✅ matches [67]');
console.log('  - 6: ✅ matches [1-9]');
console.log('  - 5528549: ✅ matches \\d{7}');

if (phoneRegex.test(mushiPhone)) {
    console.log('\n✅ SUCCESS: Your phone number should now pass validation!');
} else {
    console.log('\n❌ FAILED: Phone number still doesn\'t match pattern');
}

// Test other common Tanzania numbers
console.log('\n🧪 Testing other Tanzania numbers:');
const testNumbers = [
    "0712345678", // Vodacom
    "0765528549", // Your number
    "0754123456", // Airtel
    "0689123456", // Tigo
    "0621234567", // TTCL
    "0812345678", // Invalid (should fail)
    "071234567",  // Invalid (too short)
    "07123456789" // Invalid (too long)
];

testNumbers.forEach(number => {
    const isValid = phoneRegex.test(number);
    console.log(`  ${number}: ${isValid ? '✅' : '❌'}`);
});

console.log('\n🎯 Conclusion:');
console.log('Your phone number 0765528549 should now work with the updated validation!');
console.log('Try the payment again - it should pass the phone validation step.');
