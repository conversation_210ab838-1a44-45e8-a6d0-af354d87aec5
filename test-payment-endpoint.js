// Test Payment Endpoint Directly
const axios = require('axios');

async function testPaymentEndpoint() {
  console.log('🧪 Testing Payment Endpoint Directly...\n');

  // Test 1: Check if endpoint exists (should get 401 without auth)
  console.log('📍 Test 1: Check endpoint existence...');
  try {
    const response = await axios.post('http://localhost:5000/api/payment/create-invoice', {
      test: 'data'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });
    
    console.log('✅ Unexpected success:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('📝 Response Status:', error.response.status);
      console.log('📝 Response Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.log('✅ Endpoint exists (401 Unauthorized - expected without token)');
      } else if (error.response.status === 404) {
        console.log('❌ Endpoint NOT FOUND (404) - Route registration issue');
      } else {
        console.log('📝 Other status:', error.response.status);
      }
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  // Test 2: Check with invalid token (should get 401 with better message)
  console.log('\n📍 Test 2: Check with invalid token...');
  try {
    const response = await axios.post('http://localhost:5000/api/payment/create-invoice', {
      plan: {
        _id: 'test_plan',
        title: 'Test Plan',
        discountedPrice: 1000
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid_token_12345'
      },
      timeout: 5000
    });
    
    console.log('✅ Unexpected success:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('📝 Response Status:', error.response.status);
      console.log('📝 Response Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.log('✅ Authentication working correctly');
      } else if (error.response.status === 400) {
        console.log('⚠️ Got 400 Bad Request - might be validation issue');
      }
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  // Test 3: Check other payment endpoints
  console.log('\n📍 Test 3: Check other payment endpoints...');
  
  const endpoints = [
    '/api/payment/webhook-test',
    '/api/payment/check-payment-status'
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`http://localhost:5000${endpoint}`, {
        timeout: 5000
      });
      console.log(`✅ ${endpoint}: Working (${response.status})`);
    } catch (error) {
      if (error.response) {
        console.log(`📝 ${endpoint}: Status ${error.response.status} - ${error.response.data?.message || 'No message'}`);
      } else {
        console.log(`❌ ${endpoint}: Network error - ${error.message}`);
      }
    }
  }

  // Test 4: Check server health
  console.log('\n📍 Test 4: Check server health...');
  try {
    const response = await axios.get('http://localhost:5000/api/health', {
      timeout: 5000
    });
    console.log('✅ Server health:', response.data);
  } catch (error) {
    console.log('❌ Server health check failed:', error.message);
  }
}

// Test route registration
async function testRouteRegistration() {
  console.log('\n🔍 Testing Route Registration...\n');
  
  // Test various endpoints to see which are working
  const testEndpoints = [
    { path: '/api/health', method: 'GET', description: 'Server health' },
    { path: '/api/users/login', method: 'POST', description: 'User login' },
    { path: '/api/payment/webhook-test', method: 'GET', description: 'Payment webhook test' },
    { path: '/api/payment/create-invoice', method: 'POST', description: 'Payment create invoice' },
    { path: '/api/plans', method: 'GET', description: 'Plans endpoint' }
  ];

  for (const endpoint of testEndpoints) {
    try {
      let response;
      if (endpoint.method === 'GET') {
        response = await axios.get(`http://localhost:5000${endpoint.path}`, { timeout: 3000 });
      } else {
        response = await axios.post(`http://localhost:5000${endpoint.path}`, {}, { timeout: 3000 });
      }
      
      console.log(`✅ ${endpoint.description}: Working (${response.status})`);
    } catch (error) {
      if (error.response) {
        const status = error.response.status;
        if (status === 401) {
          console.log(`🔐 ${endpoint.description}: Requires authentication (${status})`);
        } else if (status === 404) {
          console.log(`❌ ${endpoint.description}: NOT FOUND (${status})`);
        } else {
          console.log(`📝 ${endpoint.description}: Status ${status}`);
        }
      } else {
        console.log(`❌ ${endpoint.description}: Network error`);
      }
    }
  }
}

// Main function
async function runTests() {
  console.log('🚀 Starting Payment Endpoint Tests...\n');
  
  await testPaymentEndpoint();
  await testRouteRegistration();
  
  console.log('\n✅ Tests completed!');
  console.log('\n💡 Summary:');
  console.log('- If you see 404 errors, the route is not registered properly');
  console.log('- If you see 401 errors, the route exists but needs authentication');
  console.log('- If you see 400 errors, the route exists but has validation issues');
}

runTests().catch(console.error);
