{"ast": null, "code": "const {\n  default: axiosInstance\n} = require(\".\");\nexport const addPayment = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/payment/create-invoice\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const checkPaymentStatus = async () => {\n  try {\n    const response = await axiosInstance.get(`/api/payment/check-payment-status`);\n    return response.data;\n  } catch (error) {\n    var _error$response;\n    return ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || {\n      success: false,\n      message: error.message\n    };\n  }\n};", "map": {"version": 3, "names": ["default", "axiosInstance", "require", "addPayment", "payload", "response", "post", "data", "error", "checkPaymentStatus", "get", "_error$response", "success", "message"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/payment.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\nexport const addPayment = async (payload) => {\r\n  try {\r\n    const response = await axiosInstance.post(\"/api/payment/create-invoice\", payload);\r\n    return response.data;\r\n  } catch (error) {\r\n    return error.response.data;\r\n  }\r\n};\r\n\r\n\r\nexport const checkPaymentStatus = async () => {\r\n  try {\r\n    const response = await axiosInstance.get(`/api/payment/check-payment-status`,);\r\n    return response.data;\r\n  } catch (error) {\r\n    return error.response?.data || { success: false, message: error.message };\r\n  }\r\n};"], "mappings": "AAAA,MAAM;EAAEA,OAAO,EAAEC;AAAc,CAAC,GAAGC,OAAO,CAAC,GAAG,CAAC;AAE/C,OAAO,MAAMC,UAAU,GAAG,MAAOC,OAAO,IAAK;EAC3C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,6BAA6B,EAAEF,OAAO,CAAC;IACjF,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;AAGD,OAAO,MAAME,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF,MAAMJ,QAAQ,GAAG,MAAMJ,aAAa,CAACS,GAAG,CAAE,mCAAmC,CAAC;IAC9E,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAG,eAAA;IACd,OAAO,EAAAA,eAAA,GAAAH,KAAK,CAACH,QAAQ,cAAAM,eAAA,uBAAdA,eAAA,CAAgBJ,IAAI,KAAI;MAAEK,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAEL,KAAK,CAACK;IAAQ,CAAC;EAC3E;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}