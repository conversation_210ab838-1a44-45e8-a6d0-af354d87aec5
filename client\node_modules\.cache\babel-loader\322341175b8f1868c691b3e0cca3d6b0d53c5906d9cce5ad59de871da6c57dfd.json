{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Plans\\\\components\\\\ConfirmModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport Modal from \"react-modal\";\nimport \"./ConfirmationModal.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\n\nconst ConfirmModal = ({\n  isOpen,\n  onClose,\n  transaction\n}) => {\n  _s();\n  const [showConfetti, setShowConfetti] = useState(false);\n  useEffect(() => {\n    if (isOpen) {\n      // Trigger confetti animation\n      setShowConfetti(true);\n      setTimeout(() => setShowConfetti(false), 3000);\n\n      // Play success sound if available\n      try {\n        const audio = new Audio('/sounds/success.mp3');\n        audio.volume = 0.3;\n        audio.play().catch(() => {});\n      } catch (error) {\n        console.log('Sound not available');\n      }\n    }\n  }, [isOpen]);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: isOpen,\n    onRequestClose: onClose,\n    className: \"modal-content success-modal\",\n    overlayClassName: \"modal-overlay\",\n    children: [showConfetti && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"confetti-container\",\n      children: [...Array(50)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `confetti confetti-${i % 5}`\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 25\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-icon-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-checkmark\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"80px\",\n            height: \"80px\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              fill: \"#10B981\",\n              className: \"check-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8 12l2 2 4-4\",\n              stroke: \"white\",\n              strokeWidth: \"3\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              className: \"check-mark\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-glow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"modal-title\",\n      children: \"\\uD83C\\uDF89 Payment Successful!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"modal-subtitle\",\n      children: \"Your subscription has been activated successfully\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-label\",\n          children: \"TYPE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-value\",\n          children: \"Sent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-label\",\n          children: \"AMOUNT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-value\",\n          children: [transaction.amount, \" \", transaction.amount !== 'N/A' ? transaction.currency : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-label\",\n          children: \"DESTINATION\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-value\",\n          children: transaction.destination\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"detail-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-label\",\n          children: \"Note: \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"detail-value\",\n          children: \"Regards from Henry Vitalis Mushi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"modal-button\",\n      onClick: onClose,\n      children: \"OK\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n};\n_s(ConfirmModal, \"afVoOF5p981x2nuP0kU5CKUCRJM=\");\n_c = ConfirmModal;\nexport default ConfirmModal;\nvar _c;\n$RefreshReg$(_c, \"ConfirmModal\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Modal", "jsxDEV", "_jsxDEV", "setAppElement", "ConfirmModal", "isOpen", "onClose", "transaction", "_s", "showConfetti", "setShowConfetti", "setTimeout", "audio", "Audio", "volume", "play", "catch", "error", "console", "log", "onRequestClose", "className", "overlayClassName", "children", "Array", "map", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "xmlns", "cx", "cy", "r", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "amount", "currency", "destination", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Plans/components/ConfirmModal.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport <PERSON><PERSON> from \"react-modal\";\r\nimport \"./ConfirmationModal.css\";\r\n\r\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\r\n\r\nconst ConfirmModal = ({ isOpen, onClose, transaction }) => {\r\n    const [showConfetti, setShowConfetti] = useState(false);\r\n\r\n    useEffect(() => {\r\n        if (isOpen) {\r\n            // Trigger confetti animation\r\n            setShowConfetti(true);\r\n            setTimeout(() => setShowConfetti(false), 3000);\r\n\r\n            // Play success sound if available\r\n            try {\r\n                const audio = new Audio('/sounds/success.mp3');\r\n                audio.volume = 0.3;\r\n                audio.play().catch(() => {});\r\n            } catch (error) {\r\n                console.log('Sound not available');\r\n            }\r\n        }\r\n    }, [isOpen]);\r\n\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            className=\"modal-content success-modal\"\r\n            overlayClassName=\"modal-overlay\"\r\n        >\r\n            {showConfetti && (\r\n                <div className=\"confetti-container\">\r\n                    {[...Array(50)].map((_, i) => (\r\n                        <div key={i} className={`confetti confetti-${i % 5}`}></div>\r\n                    ))}\r\n                </div>\r\n            )}\r\n\r\n            <div className=\"modal-header\">\r\n                <div className=\"success-icon-container\">\r\n                    <div className=\"success-checkmark\">\r\n                        <svg width=\"80px\" height=\"80px\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                            <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#10B981\" className=\"check-circle\"/>\r\n                            <path d=\"M8 12l2 2 4-4\" stroke=\"white\" strokeWidth=\"3\" strokeLinecap=\"round\" strokeLinejoin=\"round\" className=\"check-mark\"/>\r\n                        </svg>\r\n                    </div>\r\n                    <div className=\"success-glow\"></div>\r\n                </div>\r\n            </div>\r\n\r\n            <h2 className=\"modal-title\">🎉 Payment Successful!</h2>\r\n            <p className=\"modal-subtitle\">Your subscription has been activated successfully</p>\r\n            <div className=\"modal-details\">\r\n                <div className=\"detail-item\">\r\n                    <span className=\"detail-label\">TYPE</span>\r\n                    <span className=\"detail-value\">Sent</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                    <span className=\"detail-label\">AMOUNT</span>\r\n                    <span className=\"detail-value\">{transaction.amount} {transaction.amount !== 'N/A' ? transaction.currency : ''}</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                    <span className=\"detail-label\">DESTINATION</span>\r\n                    <span className=\"detail-value\">{transaction.destination}</span>\r\n                </div>\r\n                <div className=\"detail-item\">\r\n                    <span className=\"detail-label\">Note: </span>\r\n                    <span className=\"detail-value\">Regards from Henry Vitalis Mushi</span>\r\n                </div>\r\n            </div>\r\n            <button className=\"modal-button\" onClick={onClose}>OK</button>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default ConfirmModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjCF,KAAK,CAACG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;;AAE9B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACvD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAEvDD,SAAS,CAAC,MAAM;IACZ,IAAIO,MAAM,EAAE;MACR;MACAK,eAAe,CAAC,IAAI,CAAC;MACrBC,UAAU,CAAC,MAAMD,eAAe,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;;MAE9C;MACA,IAAI;QACA,MAAME,KAAK,GAAG,IAAIC,KAAK,CAAC,qBAAqB,CAAC;QAC9CD,KAAK,CAACE,MAAM,GAAG,GAAG;QAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACtC;IACJ;EACJ,CAAC,EAAE,CAACd,MAAM,CAAC,CAAC;EAEZ,oBACIH,OAAA,CAACF,KAAK;IACFK,MAAM,EAAEA,MAAO;IACfe,cAAc,EAAEd,OAAQ;IACxBe,SAAS,EAAC,6BAA6B;IACvCC,gBAAgB,EAAC,eAAe;IAAAC,QAAA,GAE/Bd,YAAY,iBACTP,OAAA;MAAKmB,SAAS,EAAC,oBAAoB;MAAAE,QAAA,EAC9B,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACrBzB,OAAA;QAAamB,SAAS,EAAG,qBAAoBM,CAAC,GAAG,CAAE;MAAE,GAA3CA,CAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgD,CAC9D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAED7B,OAAA;MAAKmB,SAAS,EAAC,cAAc;MAAAE,QAAA,eACzBrB,OAAA;QAAKmB,SAAS,EAAC,wBAAwB;QAAAE,QAAA,gBACnCrB,OAAA;UAAKmB,SAAS,EAAC,mBAAmB;UAAAE,QAAA,eAC9BrB,OAAA;YAAK8B,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAAb,QAAA,gBAC9FrB,OAAA;cAAQmC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAACJ,IAAI,EAAC,SAAS;cAACd,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACxE7B,OAAA;cAAMsC,CAAC,EAAC,eAAe;cAACC,MAAM,EAAC,OAAO;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACvB,SAAS,EAAC;YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN7B,OAAA;UAAKmB,SAAS,EAAC;QAAc;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN7B,OAAA;MAAImB,SAAS,EAAC,aAAa;MAAAE,QAAA,EAAC;IAAsB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvD7B,OAAA;MAAGmB,SAAS,EAAC,gBAAgB;MAAAE,QAAA,EAAC;IAAiD;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACnF7B,OAAA;MAAKmB,SAAS,EAAC,eAAe;MAAAE,QAAA,gBAC1BrB,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBACxBrB,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1C7B,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACN7B,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBACxBrB,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5C7B,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,GAAEhB,WAAW,CAACsC,MAAM,EAAC,GAAC,EAACtC,WAAW,CAACsC,MAAM,KAAK,KAAK,GAAGtC,WAAW,CAACuC,QAAQ,GAAG,EAAE;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpH,CAAC,eACN7B,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBACxBrB,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAAW;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjD7B,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAEhB,WAAW,CAACwC;QAAW;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACN7B,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBACxBrB,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5C7B,OAAA;UAAMmB,SAAS,EAAC,cAAc;UAAAE,QAAA,EAAC;QAAgC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACN7B,OAAA;MAAQmB,SAAS,EAAC,cAAc;MAAC2B,OAAO,EAAE1C,OAAQ;MAAAiB,QAAA,EAAC;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3D,CAAC;AAEhB,CAAC;AAACvB,EAAA,CAtEIJ,YAAY;AAAA6C,EAAA,GAAZ7C,YAAY;AAwElB,eAAeA,YAAY;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}