{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionModal\\\\SubscriptionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionModal = ({\n  isOpen,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  var _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = phone => {\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile (simplified approach)\n  const updateUserPhoneNumber = async newPhone => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n      console.log('👤 Current user data:', user);\n\n      // Ensure we have all required fields\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Try direct API call first\n      console.log('🔄 Attempting direct API call...');\n      const directPayload = {\n        userId: user._id,\n        name: user.name || 'Unknown',\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone\n      };\n      console.log('📤 Direct API payload:', directPayload);\n      const directResponse = await axiosInstance.post('/api/users/update-user-info', directPayload);\n      console.log('📥 Direct API response:', directResponse.data);\n      if (directResponse.data.success) {\n        // Update Redux store with new user data\n        dispatch(SetUser(directResponse.data.data));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(directResponse.data.data));\n        console.log('✅ User phone number updated successfully (direct API)');\n        console.log('📱 New user data:', directResponse.data.data);\n        return true;\n      } else {\n        console.error('❌ Direct API failed, trying updateUserInfo...');\n\n        // Fallback to original method\n        const response = await updateUserInfo(directPayload);\n        console.log('📥 Fallback response:', response);\n        if (response.success) {\n          // Update Redux store with new user data\n          dispatch(SetUser(response.data));\n\n          // Update localStorage\n          localStorage.setItem('user', JSON.stringify(response.data));\n          console.log('✅ User phone number updated successfully (fallback)');\n          console.log('📱 New user data:', response.data);\n          return true;\n        } else {\n          console.error('❌ Both methods failed');\n          console.error('❌ Direct response:', directResponse.data);\n          console.error('❌ Fallback response:', response);\n          return false;\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('❌ Error updating user phone number:', error);\n      console.error('❌ Error details:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n\n      // Show specific error message\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.message) {\n        message.error(`Update failed: ${error.response.data.message}`);\n      }\n      return false;\n    }\n  };\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    }\n  }, [isOpen, user === null || user === void 0 ? void 0 : user.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.phoneNumber && !isEditingPhone) {\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user === null || user === void 0 ? void 0 : user.phoneNumber, isEditingPhone]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = plan => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone,\n        // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n          return true;\n        }\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: [step === 'plans' && '🚀 Choose Your Learning Plan', step === 'payment' && '💳 Complete Your Payment', step === 'success' && '⏳ Processing Payment...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [step === 'plans' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading plans...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 17\n          }, this) : plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$features, _plan$features2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-card\",\n              onClick: () => handlePlanSelect(plan),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 23\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-amount\",\n                  children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-original\",\n                  children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-period\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: [(_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 25\n                }, this)), ((_plan$features2 = plan.features) === null || _plan$features2 === void 0 ? void 0 : _plan$features2.length) > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: [plan.features.length - 4, \" more features\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                children: [\"Choose \", plan.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), step === 'payment' && selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-plan-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Selected Plan: \", selectedPlan.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"plan-price-summary\",\n              children: [(_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS for \", selectedPlan.duration, \" month\", selectedPlan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-label\",\n                  children: \"Phone Number for Payment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), !isEditingPhone ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `info-value ${phoneUpdated ? 'updated' : ''}`,\n                    children: [paymentPhone || 'Not provided', phoneUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"updated-indicator\",\n                      children: \"\\u2705 Updated\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 44\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"edit-phone-btn\",\n                    onClick: () => setIsEditingPhone(true),\n                    type: \"button\",\n                    children: \"\\u270F\\uFE0F Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-edit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    value: paymentPhone,\n                    onChange: e => setPaymentPhone(e.target.value),\n                    placeholder: \"Enter phone number (e.g., 0744963858)\",\n                    className: `phone-input ${paymentPhone ? isValidPhone(paymentPhone) ? 'valid' : 'invalid' : ''}`,\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this), paymentPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`,\n                    children: isValidPhone(paymentPhone) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message valid\",\n                      children: \"\\u2705 Valid phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message invalid\",\n                      children: \"\\u274C Must start with 06 or 07 and be 10 digits\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"phone-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"save-phone-btn\",\n                      onClick: async e => {\n                        e.preventDefault();\n                        console.log('🔥 SAVE BUTTON CLICKED!');\n                        console.log('📱 Payment phone:', paymentPhone);\n                        console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n                        if (!isValidPhone(paymentPhone)) {\n                          console.log('❌ Invalid phone number');\n                          message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                          return;\n                        }\n                        try {\n                          console.log('💾 Starting phone number save process...');\n\n                          // Simple approach - just close editing and update\n                          setIsEditingPhone(false);\n                          setPhoneUpdated(true);\n\n                          // Show immediate success\n                          message.success({\n                            content: '📱 Phone number saved for payment!',\n                            duration: 3,\n                            style: {\n                              marginTop: '20vh',\n                              fontSize: '15px',\n                              fontWeight: '600'\n                            }\n                          });\n\n                          // Try to update user profile in background\n                          try {\n                            console.log('🔄 Updating user profile in background...');\n                            const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n                            if (updateSuccess) {\n                              console.log('✅ Profile updated successfully');\n                              setTimeout(() => {\n                                message.info({\n                                  content: '💡 Your profile has been updated permanently.',\n                                  duration: 3,\n                                  style: {\n                                    marginTop: '20vh',\n                                    fontSize: '14px'\n                                  }\n                                });\n                              }, 1000);\n                            } else {\n                              console.log('⚠️ Profile update failed, but phone saved for payment');\n                            }\n                          } catch (profileError) {\n                            console.error('⚠️ Profile update error (non-critical):', profileError);\n                          }\n\n                          // Reset the updated indicator after 5 seconds\n                          setTimeout(() => {\n                            setPhoneUpdated(false);\n                          }, 5000);\n                        } catch (error) {\n                          console.error('❌ Error saving phone number:', error);\n                          message.error('Failed to save phone number. Please try again.');\n                        }\n                      },\n                      disabled: !isValidPhone(paymentPhone),\n                      type: \"button\",\n                      children: \"\\u2705 Save\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"cancel-phone-btn\",\n                      onClick: () => {\n                        setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n                        setIsEditingPhone(false);\n                      },\n                      type: \"button\",\n                      children: \"\\u274C Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-note\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"\\uD83D\\uDCA1 This number will receive the payment SMS. You can use a different number than your profile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Payment Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: \"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"back-btn\",\n              onClick: () => setStep('plans'),\n              children: \"\\u2190 Back to Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pay-btn\",\n              onClick: e => {\n                e.preventDefault();\n                console.log('💳 PAY BUTTON CLICKED!');\n                console.log('📱 Payment phone:', paymentPhone);\n                console.log('✏️ Is editing phone:', isEditingPhone);\n                console.log('⏳ Payment loading:', paymentLoading);\n                if (isEditingPhone) {\n                  message.warning('Please save your phone number first');\n                  return;\n                }\n                if (!paymentPhone) {\n                  message.error('Please enter a phone number');\n                  return;\n                }\n                if (!isValidPhone(paymentPhone)) {\n                  message.error('Please enter a valid phone number');\n                  return;\n                }\n                handlePayment();\n              },\n              disabled: paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone),\n              children: paymentLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 23\n                }, this), \"Processing...\"]\n              }, void 0, true) : isEditingPhone ? 'Save phone number first' : !paymentPhone ? 'Enter phone number' : !isValidPhone(paymentPhone) ? 'Invalid phone number' : `Pay ${(_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString()} TZS`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this), step === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-animation\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Request Sent!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation and complete the payment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Check your phone for SMS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Follow the payment instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Your subscription will activate automatically\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"check-status-btn\",\n              onClick: async () => {\n                console.log('🔍 Manual payment check triggered');\n                try {\n                  const response = await checkPaymentStatus();\n                  console.log('📥 Manual check response:', response);\n                  if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                    console.log('✅ Payment confirmed manually!');\n                    dispatch(SetSubscription(response));\n                    message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                    onSuccess && onSuccess();\n                    setTimeout(() => onClose(), 1000);\n                  } else {\n                    message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                  }\n                } catch (error) {\n                  console.error('❌ Manual check error:', error);\n                  message.error('Error checking payment status');\n                }\n              },\n              children: \"\\u2705 Check Payment Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"done-btn\",\n              onClick: handleClose,\n              children: \"I'll complete the payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n};\n_s(SubscriptionModal, \"aZxc1xREfgbzjCdSZ68Mez4hUuk=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = SubscriptionModal;\nexport default SubscriptionModal;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "message", "getPlans", "addPayment", "checkPaymentStatus", "updateUserInfo", "axiosInstance", "SetSubscription", "SetUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionModal", "isOpen", "onClose", "onSuccess", "_s", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "paymentPhone", "setPaymentPhone", "isEditingPhone", "setIsEditingPhone", "phoneUpdated", "setPhoneUpdated", "isValidPhone", "phone", "test", "updateUserPhoneNumber", "newPhone", "console", "log", "user", "_id", "error", "directPayload", "userId", "name", "email", "school", "class_", "class", "className", "level", "phoneNumber", "directResponse", "post", "data", "success", "dispatch", "localStorage", "setItem", "JSON", "stringify", "response", "_error$response", "_error$response2", "_error$response2$data", "state", "fetchPlans", "Array", "isArray", "handlePlanSelect", "plan", "handlePayment", "length", "_user$name", "paymentData", "userPhone", "userEmail", "replace", "toLowerCase", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "checkStatus", "paymentStatus", "status", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "warning", "handleClose", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "title", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "type", "value", "onChange", "e", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "preventDefault", "updateSuccess", "info", "profileError", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionModal/SubscriptionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = (phone) => {\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile (simplified approach)\n  const updateUserPhoneNumber = async (newPhone) => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n      console.log('👤 Current user data:', user);\n\n      // Ensure we have all required fields\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Try direct API call first\n      console.log('🔄 Attempting direct API call...');\n\n      const directPayload = {\n        userId: user._id,\n        name: user.name || 'Unknown',\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone\n      };\n\n      console.log('📤 Direct API payload:', directPayload);\n\n      const directResponse = await axiosInstance.post('/api/users/update-user-info', directPayload);\n\n      console.log('📥 Direct API response:', directResponse.data);\n\n      if (directResponse.data.success) {\n        // Update Redux store with new user data\n        dispatch(SetUser(directResponse.data.data));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(directResponse.data.data));\n\n        console.log('✅ User phone number updated successfully (direct API)');\n        console.log('📱 New user data:', directResponse.data.data);\n        return true;\n      } else {\n        console.error('❌ Direct API failed, trying updateUserInfo...');\n\n        // Fallback to original method\n        const response = await updateUserInfo(directPayload);\n\n        console.log('📥 Fallback response:', response);\n\n        if (response.success) {\n          // Update Redux store with new user data\n          dispatch(SetUser(response.data));\n\n          // Update localStorage\n          localStorage.setItem('user', JSON.stringify(response.data));\n\n          console.log('✅ User phone number updated successfully (fallback)');\n          console.log('📱 New user data:', response.data);\n          return true;\n        } else {\n          console.error('❌ Both methods failed');\n          console.error('❌ Direct response:', directResponse.data);\n          console.error('❌ Fallback response:', response);\n          return false;\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error updating user phone number:', error);\n      console.error('❌ Error details:', error.response?.data);\n\n      // Show specific error message\n      if (error.response?.data?.message) {\n        message.error(`Update failed: ${error.response.data.message}`);\n      }\n\n      return false;\n    }\n  };\n  \n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone(user?.phoneNumber || '');\n    }\n  }, [isOpen, user?.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user?.phoneNumber && !isEditingPhone) {\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user?.phoneNumber, isEditingPhone]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone, // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n        \n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone(user?.phoneNumber || '');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n            {step === 'success' && '⏳ Processing Payment...'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('glimp') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"phone-section\">\n                  <div className=\"info-item\">\n                    <span className=\"info-label\">Phone Number for Payment:</span>\n                    {!isEditingPhone ? (\n                      <div className=\"phone-display\">\n                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>\n                          {paymentPhone || 'Not provided'}\n                          {phoneUpdated && <span className=\"updated-indicator\">✅ Updated</span>}\n                        </span>\n                        <button\n                          className=\"edit-phone-btn\"\n                          onClick={() => setIsEditingPhone(true)}\n                          type=\"button\"\n                        >\n                          ✏️ Change\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"phone-edit\">\n                        <input\n                          type=\"tel\"\n                          value={paymentPhone}\n                          onChange={(e) => setPaymentPhone(e.target.value)}\n                          placeholder=\"Enter phone number (e.g., 0744963858)\"\n                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}\n                          maxLength=\"10\"\n                        />\n                        {paymentPhone && (\n                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>\n                            {isValidPhone(paymentPhone) ? (\n                              <span className=\"validation-message valid\">✅ Valid phone number</span>\n                            ) : (\n                              <span className=\"validation-message invalid\">❌ Must start with 06 or 07 and be 10 digits</span>\n                            )}\n                          </div>\n                        )}\n                        <div className=\"phone-actions\">\n                          <button\n                            className=\"save-phone-btn\"\n                            onClick={async (e) => {\n                              e.preventDefault();\n                              console.log('🔥 SAVE BUTTON CLICKED!');\n                              console.log('📱 Payment phone:', paymentPhone);\n                              console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n\n                              if (!isValidPhone(paymentPhone)) {\n                                console.log('❌ Invalid phone number');\n                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                                return;\n                              }\n\n                              try {\n                                console.log('💾 Starting phone number save process...');\n\n                                // Simple approach - just close editing and update\n                                setIsEditingPhone(false);\n                                setPhoneUpdated(true);\n\n                                // Show immediate success\n                                message.success({\n                                  content: '📱 Phone number saved for payment!',\n                                  duration: 3,\n                                  style: {\n                                    marginTop: '20vh',\n                                    fontSize: '15px',\n                                    fontWeight: '600'\n                                  }\n                                });\n\n                                // Try to update user profile in background\n                                try {\n                                  console.log('🔄 Updating user profile in background...');\n                                  const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n\n                                  if (updateSuccess) {\n                                    console.log('✅ Profile updated successfully');\n                                    setTimeout(() => {\n                                      message.info({\n                                        content: '💡 Your profile has been updated permanently.',\n                                        duration: 3,\n                                        style: {\n                                          marginTop: '20vh',\n                                          fontSize: '14px'\n                                        }\n                                      });\n                                    }, 1000);\n                                  } else {\n                                    console.log('⚠️ Profile update failed, but phone saved for payment');\n                                  }\n                                } catch (profileError) {\n                                  console.error('⚠️ Profile update error (non-critical):', profileError);\n                                }\n\n                                // Reset the updated indicator after 5 seconds\n                                setTimeout(() => {\n                                  setPhoneUpdated(false);\n                                }, 5000);\n\n                              } catch (error) {\n                                console.error('❌ Error saving phone number:', error);\n                                message.error('Failed to save phone number. Please try again.');\n                              }\n                            }}\n                            disabled={!isValidPhone(paymentPhone)}\n                            type=\"button\"\n                          >\n                            ✅ Save\n                          </button>\n                          <button\n                            className=\"cancel-phone-btn\"\n                            onClick={() => {\n                              setPaymentPhone(user?.phoneNumber || '');\n                              setIsEditingPhone(false);\n                            }}\n                            type=\"button\"\n                          >\n                            ❌ Cancel\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"phone-note\">\n                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    console.log('💳 PAY BUTTON CLICKED!');\n                    console.log('📱 Payment phone:', paymentPhone);\n                    console.log('✏️ Is editing phone:', isEditingPhone);\n                    console.log('⏳ Payment loading:', paymentLoading);\n\n                    if (isEditingPhone) {\n                      message.warning('Please save your phone number first');\n                      return;\n                    }\n\n                    if (!paymentPhone) {\n                      message.error('Please enter a phone number');\n                      return;\n                    }\n\n                    if (!isValidPhone(paymentPhone)) {\n                      message.error('Please enter a valid phone number');\n                      return;\n                    }\n\n                    handlePayment();\n                  }}\n                  disabled={paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone)}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : isEditingPhone ? (\n                    'Save phone number first'\n                  ) : !paymentPhone ? (\n                    'Enter phone number'\n                  ) : !isValidPhone(paymentPhone) ? (\n                    'Invalid phone number'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {step === 'success' && (\n            <div className=\"success-step\">\n              <div className=\"success-animation\">\n                <div className=\"pulse-circle\">\n                  <div className=\"phone-icon\">📱</div>\n                </div>\n              </div>\n              \n              <h3>Payment Request Sent!</h3>\n              <p>Please check your phone for SMS confirmation and complete the payment.</p>\n              \n              <div className=\"payment-steps\">\n                <div className=\"step\">\n                  <span className=\"step-number\">1</span>\n                  <span className=\"step-text\">Check your phone for SMS</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">2</span>\n                  <span className=\"step-text\">Follow the payment instructions</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">3</span>\n                  <span className=\"step-text\">Your subscription will activate automatically</span>\n                </div>\n              </div>\n\n              <div className=\"success-actions\">\n                <button\n                  className=\"check-status-btn\"\n                  onClick={async () => {\n                    console.log('🔍 Manual payment check triggered');\n                    try {\n                      const response = await checkPaymentStatus();\n                      console.log('📥 Manual check response:', response);\n\n                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                        console.log('✅ Payment confirmed manually!');\n                        dispatch(SetSubscription(response));\n                        message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                        onSuccess && onSuccess();\n                        setTimeout(() => onClose(), 1000);\n                      } else {\n                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                      }\n                    } catch (error) {\n                      console.error('❌ Manual check error:', error);\n                      message.error('Error checking payment status');\n                    }\n                  }}\n                >\n                  ✅ Check Payment Status\n                </button>\n\n                <button className=\"done-btn\" onClick={handleClose}>\n                  I'll complete the payment\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,wBAAwB;AACvE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAClE,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMyC,YAAY,GAAIC,KAAK,IAAK;IAC9B,OAAOA,KAAK,IAAI,gBAAgB,CAACC,IAAI,CAACD,KAAK,CAAC;EAC9C,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEF,QAAQ,CAAC;MAClEC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,IAAI,CAAC;;MAE1C;MACA,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE;QACbH,OAAO,CAACI,KAAK,CAAC,sBAAsB,CAAC;QACrC,OAAO,KAAK;MACd;;MAEA;MACAJ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAE/C,MAAMI,aAAa,GAAG;QACpBC,MAAM,EAAEJ,IAAI,CAACC,GAAG;QAChBI,IAAI,EAAEL,IAAI,CAACK,IAAI,IAAI,SAAS;QAC5BC,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,EAAE;QACvBC,MAAM,EAAEP,IAAI,CAACO,MAAM,IAAI,EAAE;QACzBC,MAAM,EAAER,IAAI,CAACS,KAAK,IAAIT,IAAI,CAACU,SAAS,IAAI,EAAE;QAC1CC,KAAK,EAAEX,IAAI,CAACW,KAAK,IAAI,SAAS;QAC9BC,WAAW,EAAEf;MACf,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,aAAa,CAAC;MAEpD,MAAMU,cAAc,GAAG,MAAMpD,aAAa,CAACqD,IAAI,CAAC,6BAA6B,EAAEX,aAAa,CAAC;MAE7FL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEc,cAAc,CAACE,IAAI,CAAC;MAE3D,IAAIF,cAAc,CAACE,IAAI,CAACC,OAAO,EAAE;QAC/B;QACAC,QAAQ,CAACtD,OAAO,CAACkD,cAAc,CAACE,IAAI,CAACA,IAAI,CAAC,CAAC;;QAE3C;QACAG,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,cAAc,CAACE,IAAI,CAACA,IAAI,CAAC,CAAC;QAEtEjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEc,cAAc,CAACE,IAAI,CAACA,IAAI,CAAC;QAC1D,OAAO,IAAI;MACb,CAAC,MAAM;QACLjB,OAAO,CAACI,KAAK,CAAC,+CAA+C,CAAC;;QAE9D;QACA,MAAMoB,QAAQ,GAAG,MAAM9D,cAAc,CAAC2C,aAAa,CAAC;QAEpDL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuB,QAAQ,CAAC;QAE9C,IAAIA,QAAQ,CAACN,OAAO,EAAE;UACpB;UACAC,QAAQ,CAACtD,OAAO,CAAC2D,QAAQ,CAACP,IAAI,CAAC,CAAC;;UAEhC;UACAG,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAACP,IAAI,CAAC,CAAC;UAE3DjB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClED,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEuB,QAAQ,CAACP,IAAI,CAAC;UAC/C,OAAO,IAAI;QACb,CAAC,MAAM;UACLjB,OAAO,CAACI,KAAK,CAAC,uBAAuB,CAAC;UACtCJ,OAAO,CAACI,KAAK,CAAC,oBAAoB,EAAEW,cAAc,CAACE,IAAI,CAAC;UACxDjB,OAAO,CAACI,KAAK,CAAC,sBAAsB,EAAEoB,QAAQ,CAAC;UAC/C,OAAO,KAAK;QACd;MACF;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAA,IAAAqB,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd3B,OAAO,CAACI,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DJ,OAAO,CAACI,KAAK,CAAC,kBAAkB,GAAAqB,eAAA,GAAErB,KAAK,CAACoB,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBR,IAAI,CAAC;;MAEvD;MACA,KAAAS,gBAAA,GAAItB,KAAK,CAACoB,QAAQ,cAAAE,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,eAApBA,qBAAA,CAAsBrE,OAAO,EAAE;QACjCA,OAAO,CAAC8C,KAAK,CAAE,kBAAiBA,KAAK,CAACoB,QAAQ,CAACP,IAAI,CAAC3D,OAAQ,EAAC,CAAC;MAChE;MAEA,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM;IAAE4C;EAAK,CAAC,GAAG9C,WAAW,CAAEwE,KAAK,IAAKA,KAAK,CAAC1B,IAAI,CAAC;EACnD,MAAMiB,QAAQ,GAAG9D,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,EAAE;MACVwD,UAAU,CAAC,CAAC;MACZ;MACAvC,eAAe,CAAC,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;IAC1C;EACF,CAAC,EAAE,CAACzC,MAAM,EAAE6B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,CAAC,CAAC;;EAE/B;EACA3D,SAAS,CAAC,MAAM;IACd,IAAI+C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEY,WAAW,IAAI,CAACvB,cAAc,EAAE;MACxCD,eAAe,CAACY,IAAI,CAACY,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,EAAEvB,cAAc,CAAC,CAAC;EAEvC,MAAMsC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwC,QAAQ,GAAG,MAAMjE,QAAQ,CAAC,CAAC;MACjCqB,QAAQ,CAACkD,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C9C,OAAO,CAAC8C,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgD,gBAAgB,GAAIC,IAAI,IAAK;IACjCnD,eAAe,CAACmD,IAAI,CAAC;IACrB7C,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAM8C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACrD,YAAY,EAAE;MACjBvB,OAAO,CAAC8C,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF;IAEA,IAAI,CAACf,YAAY,IAAIA,YAAY,CAAC8C,MAAM,GAAG,EAAE,EAAE;MAC7C7E,OAAO,CAAC8C,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,IAAI,CAAC,gBAAgB,CAACP,IAAI,CAACR,YAAY,CAAC,EAAE;MACxC/B,OAAO,CAAC8C,KAAK,CAAC,wEAAwE,CAAC;MACvF;IACF;IAEA,IAAI;MAAA,IAAAgC,UAAA;MACFlD,iBAAiB,CAAC,IAAI,CAAC;MACvBiC,QAAQ,CAACpD,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMsE,WAAW,GAAG;QAClBJ,IAAI,EAAEpD,YAAY;QAClByB,MAAM,EAAEJ,IAAI,CAACC,GAAG;QAChBmC,SAAS,EAAEjD,YAAY;QAAE;QACzBkD,SAAS,EAAErC,IAAI,CAACM,KAAK,IAAK,IAAA4B,UAAA,GAAElC,IAAI,CAACK,IAAI,cAAA6B,UAAA,uBAATA,UAAA,CAAWI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAMjB,QAAQ,GAAG,MAAMhE,UAAU,CAAC6E,WAAW,CAAC;MAE9C,IAAIb,QAAQ,CAACN,OAAO,EAAE;QACpB5D,OAAO,CAAC4D,OAAO,CAAC,kEAAkE,CAAC;QACnF9B,OAAO,CAAC,SAAS,CAAC;;QAElB;QACAsD,wBAAwB,CAAClB,QAAQ,CAACmB,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACpB,QAAQ,CAAClE,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC9C,OAAO,CAAC8C,KAAK,CAACA,KAAK,CAAC9C,OAAO,IAAI,mCAAmC,CAAC;IACrE,CAAC,SAAS;MACR4B,iBAAiB,CAAC,KAAK,CAAC;MACxBiC,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM4E,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFF,QAAQ,EAAE;QACV9C,OAAO,CAACC,GAAG,CAAE,yCAAwC6C,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAMvB,QAAQ,GAAG,MAAM/D,kBAAkB,CAAC,CAAC;QAC3CuC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEuB,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACpB,KAAK,IAAIoB,QAAQ,CAACyB,aAAa,KAAK,MAAM,IAAIzB,QAAQ,CAAC0B,MAAM,KAAK,QAAQ,EAAE;UACpGlD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACAkB,QAAQ,CAACvD,eAAe,CAAC4D,QAAQ,CAAC,CAAC;;UAEnC;UACAlE,OAAO,CAAC4D,OAAO,CAAC;YACdiC,OAAO,EAAE,2CAA2C;YACpDC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;;UAEF;UACAjF,SAAS,IAAIA,SAAS,CAAC,CAAC;;UAExB;UACAkF,UAAU,CAAC,MAAM;YACfnF,OAAO,CAAC,CAAC;UACX,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,IAAI;QACb;QAEA,IAAIwE,QAAQ,IAAIC,WAAW,EAAE;UAC3B/C,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C3C,OAAO,CAACoG,OAAO,CAAC;YACdP,OAAO,EAAE,sGAAsG;YAC/GC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,OAAO,KAAK;QACd;;QAEA;QACAK,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI0C,QAAQ,IAAIC,WAAW,EAAE;UAC3BzF,OAAO,CAAC8C,KAAK,CAAC,4EAA4E,CAAC;QAC7F,CAAC,MAAM;UACLqD,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACAA,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBvE,OAAO,CAAC,OAAO,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,KAAK,CAAC;IACxBM,iBAAiB,CAAC,KAAK,CAAC;IACxBE,eAAe,CAAC,KAAK,CAAC;IACtBJ,eAAe,CAAC,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;IACxCxC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAK2C,SAAS,EAAC,4BAA4B;IAAAgD,QAAA,eACzC3F,OAAA;MAAK2C,SAAS,EAAC,oBAAoB;MAAAgD,QAAA,gBACjC3F,OAAA;QAAK2C,SAAS,EAAC,cAAc;QAAAgD,QAAA,gBAC3B3F,OAAA;UAAI2C,SAAS,EAAC,aAAa;UAAAgD,QAAA,GACxBzE,IAAI,KAAK,OAAO,IAAI,8BAA8B,EAClDA,IAAI,KAAK,SAAS,IAAI,0BAA0B,EAChDA,IAAI,KAAK,SAAS,IAAI,yBAAyB;QAAA;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACL/F,OAAA;UAAQ2C,SAAS,EAAC,cAAc;UAACqD,OAAO,EAAEN,WAAY;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEN/F,OAAA;QAAK2C,SAAS,EAAC,eAAe;QAAAgD,QAAA,GAC3BzE,IAAI,KAAK,OAAO,iBACflB,OAAA;UAAK2C,SAAS,EAAC,YAAY;UAAAgD,QAAA,EACxB7E,OAAO,gBACNd,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAgD,QAAA,gBAC5B3F,OAAA;cAAK2C,SAAS,EAAC;YAAS;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/B/F,OAAA;cAAA2F,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAENrF,KAAK,CAACuF,GAAG,CAAEjC,IAAI;YAAA,IAAAkC,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA;YAAA,oBACbrG,OAAA;cAAoB2C,SAAS,EAAC,WAAW;cAACqD,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACC,IAAI,CAAE;cAAA2B,QAAA,gBAC9E3F,OAAA;gBAAK2C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,gBAC1B3F,OAAA;kBAAI2C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,EAAE3B,IAAI,CAACsC;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAG,WAAA,GAAAlC,IAAI,CAACsC,KAAK,cAAAJ,WAAA,uBAAVA,WAAA,CAAY1B,WAAW,CAAC,CAAC,CAAC+B,QAAQ,CAAC,OAAO,CAAC,kBAC1CvG,OAAA;kBAAM2C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/F,OAAA;gBAAK2C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,gBACzB3F,OAAA;kBAAM2C,SAAS,EAAC,cAAc;kBAAAgD,QAAA,IAAAQ,qBAAA,GAAEnC,IAAI,CAACwC,eAAe,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACjF/B,IAAI,CAAC0C,WAAW,IAAI1C,IAAI,CAAC0C,WAAW,KAAK1C,IAAI,CAACwC,eAAe,iBAC5DxG,OAAA;kBAAM2C,SAAS,EAAC,gBAAgB;kBAAAgD,QAAA,GAAE3B,IAAI,CAAC0C,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/E,eACD/F,OAAA;kBAAM2C,SAAS,EAAC,cAAc;kBAAAgD,QAAA,GAAE3B,IAAI,CAACmB,QAAQ,EAAC,QAAM,EAACnB,IAAI,CAACmB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eAEN/F,OAAA;gBAAK2C,SAAS,EAAC,eAAe;gBAAAgD,QAAA,IAAAS,cAAA,GAC3BpC,IAAI,CAAC2C,QAAQ,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,KAAK,kBAC7C9G,OAAA;kBAAiB2C,SAAS,EAAC,SAAS;kBAAAgD,QAAA,gBAClC3F,OAAA;oBAAM2C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC/F,OAAA;oBAAM2C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,EAAEkB;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFvCe,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC,EACD,EAAAM,eAAA,GAAArC,IAAI,CAAC2C,QAAQ,cAAAN,eAAA,uBAAbA,eAAA,CAAenC,MAAM,IAAG,CAAC,iBACxBlE,OAAA;kBAAK2C,SAAS,EAAC,SAAS;kBAAAgD,QAAA,gBACtB3F,OAAA;oBAAM2C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC/F,OAAA;oBAAM2C,SAAS,EAAC,cAAc;oBAAAgD,QAAA,GAAE3B,IAAI,CAAC2C,QAAQ,CAACzC,MAAM,GAAG,CAAC,EAAC,gBAAc;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/F,OAAA;gBAAQ2C,SAAS,EAAC,iBAAiB;gBAAAgD,QAAA,GAAC,SAC3B,EAAC3B,IAAI,CAACsC,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjCD/B,IAAI,CAAC9B,GAAG;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCb,CAAC;UAAA,CACP;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEA7E,IAAI,KAAK,SAAS,IAAIN,YAAY,iBACjCZ,OAAA;UAAK2C,SAAS,EAAC,cAAc;UAAAgD,QAAA,gBAC3B3F,OAAA;YAAK2C,SAAS,EAAC,uBAAuB;YAAAgD,QAAA,gBACpC3F,OAAA;cAAA2F,QAAA,GAAI,iBAAe,EAAC/E,YAAY,CAAC0F,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C/F,OAAA;cAAG2C,SAAS,EAAC,oBAAoB;cAAAgD,QAAA,IAAAnF,qBAAA,GAC9BI,YAAY,CAAC4F,eAAe,cAAAhG,qBAAA,uBAA5BA,qBAAA,CAA8BiG,cAAc,CAAC,CAAC,EAAC,WAAS,EAAC7F,YAAY,CAACuE,QAAQ,EAAC,QAAM,EAACvE,YAAY,CAACuE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN/F,OAAA;YAAK2C,SAAS,EAAC,cAAc;YAAAgD,QAAA,gBAC3B3F,OAAA;cAAK2C,SAAS,EAAC,eAAe;cAAAgD,QAAA,gBAC5B3F,OAAA;gBAAK2C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,gBACxB3F,OAAA;kBAAM2C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC5D,CAACzE,cAAc,gBACdtB,OAAA;kBAAK2C,SAAS,EAAC,eAAe;kBAAAgD,QAAA,gBAC5B3F,OAAA;oBAAM2C,SAAS,EAAG,cAAanB,YAAY,GAAG,SAAS,GAAG,EAAG,EAAE;oBAAAmE,QAAA,GAC5DvE,YAAY,IAAI,cAAc,EAC9BI,YAAY,iBAAIxB,OAAA;sBAAM2C,SAAS,EAAC,mBAAmB;sBAAAgD,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACP/F,OAAA;oBACE2C,SAAS,EAAC,gBAAgB;oBAC1BqD,OAAO,EAAEA,CAAA,KAAMzE,iBAAiB,CAAC,IAAI,CAAE;oBACvCwF,IAAI,EAAC,QAAQ;oBAAApB,QAAA,EACd;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAEN/F,OAAA;kBAAK2C,SAAS,EAAC,YAAY;kBAAAgD,QAAA,gBACzB3F,OAAA;oBACE+G,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAE5F,YAAa;oBACpB6F,QAAQ,EAAGC,CAAC,IAAK7F,eAAe,CAAC6F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,WAAW,EAAC,uCAAuC;oBACnDzE,SAAS,EAAG,eAAcvB,YAAY,GAAIM,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAS,GAAI,EAAG,EAAE;oBACnGiG,SAAS,EAAC;kBAAI;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACD3E,YAAY,iBACXpB,OAAA;oBAAK2C,SAAS,EAAG,oBAAmBjB,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAU,EAAE;oBAAAuE,QAAA,EACpFjE,YAAY,CAACN,YAAY,CAAC,gBACzBpB,OAAA;sBAAM2C,SAAS,EAAC,0BAA0B;sBAAAgD,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEtE/F,OAAA;sBAAM2C,SAAS,EAAC,4BAA4B;sBAAAgD,QAAA,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC/F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eACD/F,OAAA;oBAAK2C,SAAS,EAAC,eAAe;oBAAAgD,QAAA,gBAC5B3F,OAAA;sBACE2C,SAAS,EAAC,gBAAgB;sBAC1BqD,OAAO,EAAE,MAAOkB,CAAC,IAAK;wBACpBA,CAAC,CAACI,cAAc,CAAC,CAAC;wBAClBvF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;wBACtCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEZ,YAAY,CAAC;wBAC9CW,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEN,YAAY,CAACN,YAAY,CAAC,CAAC;wBAE5D,IAAI,CAACM,YAAY,CAACN,YAAY,CAAC,EAAE;0BAC/BW,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;0BACrC3C,OAAO,CAAC8C,KAAK,CAAC,wEAAwE,CAAC;0BACvF;wBACF;wBAEA,IAAI;0BACFJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;0BAEvD;0BACAT,iBAAiB,CAAC,KAAK,CAAC;0BACxBE,eAAe,CAAC,IAAI,CAAC;;0BAErB;0BACApC,OAAO,CAAC4D,OAAO,CAAC;4BACdiC,OAAO,EAAE,oCAAoC;4BAC7CC,QAAQ,EAAE,CAAC;4BACXC,KAAK,EAAE;8BACLC,SAAS,EAAE,MAAM;8BACjBC,QAAQ,EAAE,MAAM;8BAChBC,UAAU,EAAE;4BACd;0BACF,CAAC,CAAC;;0BAEF;0BACA,IAAI;4BACFxD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;4BACxD,MAAMuF,aAAa,GAAG,MAAM1F,qBAAqB,CAACT,YAAY,CAAC;4BAE/D,IAAImG,aAAa,EAAE;8BACjBxF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;8BAC7CwD,UAAU,CAAC,MAAM;gCACfnG,OAAO,CAACmI,IAAI,CAAC;kCACXtC,OAAO,EAAE,+CAA+C;kCACxDC,QAAQ,EAAE,CAAC;kCACXC,KAAK,EAAE;oCACLC,SAAS,EAAE,MAAM;oCACjBC,QAAQ,EAAE;kCACZ;gCACF,CAAC,CAAC;8BACJ,CAAC,EAAE,IAAI,CAAC;4BACV,CAAC,MAAM;8BACLvD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;4BACtE;0BACF,CAAC,CAAC,OAAOyF,YAAY,EAAE;4BACrB1F,OAAO,CAACI,KAAK,CAAC,yCAAyC,EAAEsF,YAAY,CAAC;0BACxE;;0BAEA;0BACAjC,UAAU,CAAC,MAAM;4BACf/D,eAAe,CAAC,KAAK,CAAC;0BACxB,CAAC,EAAE,IAAI,CAAC;wBAEV,CAAC,CAAC,OAAOU,KAAK,EAAE;0BACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;0BACpD9C,OAAO,CAAC8C,KAAK,CAAC,gDAAgD,CAAC;wBACjE;sBACF,CAAE;sBACFuF,QAAQ,EAAE,CAAChG,YAAY,CAACN,YAAY,CAAE;sBACtC2F,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT/F,OAAA;sBACE2C,SAAS,EAAC,kBAAkB;sBAC5BqD,OAAO,EAAEA,CAAA,KAAM;wBACb3E,eAAe,CAAC,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;wBACxCtB,iBAAiB,CAAC,KAAK,CAAC;sBAC1B,CAAE;sBACFwF,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN/F,OAAA;gBAAK2C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,eACzB3F,OAAA;kBAAA2F,QAAA,EAAO;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/F,OAAA;cAAK2C,SAAS,EAAC,WAAW;cAAAgD,QAAA,gBACxB3F,OAAA;gBAAM2C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnD/F,OAAA;gBAAM2C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAK2C,SAAS,EAAC,iBAAiB;YAAAgD,QAAA,gBAC9B3F,OAAA;cAAQ2C,SAAS,EAAC,UAAU;cAACqD,OAAO,EAAEA,CAAA,KAAM7E,OAAO,CAAC,OAAO,CAAE;cAAAwE,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/F,OAAA;cACE2C,SAAS,EAAC,SAAS;cACnBqD,OAAO,EAAGkB,CAAC,IAAK;gBACdA,CAAC,CAACI,cAAc,CAAC,CAAC;gBAClBvF,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;gBACrCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEZ,YAAY,CAAC;gBAC9CW,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEV,cAAc,CAAC;gBACnDS,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEhB,cAAc,CAAC;gBAEjD,IAAIM,cAAc,EAAE;kBAClBjC,OAAO,CAACoG,OAAO,CAAC,qCAAqC,CAAC;kBACtD;gBACF;gBAEA,IAAI,CAACrE,YAAY,EAAE;kBACjB/B,OAAO,CAAC8C,KAAK,CAAC,6BAA6B,CAAC;kBAC5C;gBACF;gBAEA,IAAI,CAACT,YAAY,CAACN,YAAY,CAAC,EAAE;kBAC/B/B,OAAO,CAAC8C,KAAK,CAAC,mCAAmC,CAAC;kBAClD;gBACF;gBAEA8B,aAAa,CAAC,CAAC;cACjB,CAAE;cACFyD,QAAQ,EAAE1G,cAAc,IAAI,CAACI,YAAY,IAAIE,cAAc,IAAI,CAACI,YAAY,CAACN,YAAY,CAAE;cAAAuE,QAAA,EAE1F3E,cAAc,gBACbhB,OAAA,CAAAE,SAAA;gBAAAyF,QAAA,gBACE3F,OAAA;kBAAM2C,SAAS,EAAC;gBAAa;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEvC;cAAA,eAAE,CAAC,GACDzE,cAAc,GAChB,yBAAyB,GACvB,CAACF,YAAY,GACf,oBAAoB,GAClB,CAACM,YAAY,CAACN,YAAY,CAAC,GAC7B,sBAAsB,GAErB,OAAI,CAAAX,sBAAA,GAAEG,YAAY,CAAC4F,eAAe,cAAA/F,sBAAA,uBAA5BA,sBAAA,CAA8BgG,cAAc,CAAC,CAAE;YACvD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA7E,IAAI,KAAK,SAAS,iBACjBlB,OAAA;UAAK2C,SAAS,EAAC,cAAc;UAAAgD,QAAA,gBAC3B3F,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAgD,QAAA,eAChC3F,OAAA;cAAK2C,SAAS,EAAC,cAAc;cAAAgD,QAAA,eAC3B3F,OAAA;gBAAK2C,SAAS,EAAC,YAAY;gBAAAgD,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B/F,OAAA;YAAA2F,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE7E/F,OAAA;YAAK2C,SAAS,EAAC,eAAe;YAAAgD,QAAA,gBAC5B3F,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAgD,QAAA,gBACnB3F,OAAA;gBAAM2C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC/F,OAAA;gBAAM2C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN/F,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAgD,QAAA,gBACnB3F,OAAA;gBAAM2C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC/F,OAAA;gBAAM2C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN/F,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAgD,QAAA,gBACnB3F,OAAA;gBAAM2C,SAAS,EAAC,aAAa;gBAAAgD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC/F,OAAA;gBAAM2C,SAAS,EAAC,WAAW;gBAAAgD,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAK2C,SAAS,EAAC,iBAAiB;YAAAgD,QAAA,gBAC9B3F,OAAA;cACE2C,SAAS,EAAC,kBAAkB;cAC5BqD,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnBjE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;gBAChD,IAAI;kBACF,MAAMuB,QAAQ,GAAG,MAAM/D,kBAAkB,CAAC,CAAC;kBAC3CuC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuB,QAAQ,CAAC;kBAElD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACpB,KAAK,IAAIoB,QAAQ,CAACyB,aAAa,KAAK,MAAM,IAAIzB,QAAQ,CAAC0B,MAAM,KAAK,QAAQ,EAAE;oBACpGlD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;oBAC5CkB,QAAQ,CAACvD,eAAe,CAAC4D,QAAQ,CAAC,CAAC;oBACnClE,OAAO,CAAC4D,OAAO,CAAC,2CAA2C,CAAC;oBAC5D3C,SAAS,IAAIA,SAAS,CAAC,CAAC;oBACxBkF,UAAU,CAAC,MAAMnF,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;kBACnC,CAAC,MAAM;oBACLhB,OAAO,CAACmI,IAAI,CAAC,0EAA0E,CAAC;kBAC1F;gBACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;kBACdJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;kBAC7C9C,OAAO,CAAC8C,KAAK,CAAC,+BAA+B,CAAC;gBAChD;cACF,CAAE;cAAAwD,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET/F,OAAA;cAAQ2C,SAAS,EAAC,UAAU;cAACqD,OAAO,EAAEN,WAAY;cAAAC,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxF,EAAA,CA9jBIJ,iBAAiB;EAAA,QA8FJhB,WAAW,EACXC,WAAW;AAAA;AAAAuI,EAAA,GA/FxBxH,iBAAiB;AAgkBvB,eAAeA,iBAAiB;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}