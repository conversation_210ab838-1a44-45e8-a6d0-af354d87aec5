{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Register\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input, Select } from \"antd\";\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser } from \"../../../apicalls/users\";\nimport Logo from \"../../../assets/logo.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction Register() {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\");\n  const [suggestedUsername, setSuggestedUsername] = useState(\"\");\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n\n  // Generate username from names (simplified - no middle name)\n  const generateUsername = (firstName, lastName) => {\n    if (!firstName) return \"\";\n    const cleanName = name => (name === null || name === void 0 ? void 0 : name.toLowerCase().replace(/[^a-z]/g, '')) || '';\n    const first = cleanName(firstName);\n    const last = cleanName(lastName);\n\n    // Generate different username options\n    const options = [`${first}.${last}`, `${first}${last}`, `${first}_${last}`, `${first}${last}${Math.floor(Math.random() * 100)}`].filter(option => option.length >= 3);\n    return options[0] || `user${Math.floor(Math.random() * 10000)}`;\n  };\n\n  // Generate automatic email from username\n  const generateEmail = username => {\n    if (!username) return '';\n    const timestamp = Date.now();\n    return `${username}.${timestamp}@brainwave.temp`;\n  };\n\n  // Handle name changes to auto-generate username and email\n  const handleNameChange = () => {\n    const firstName = form.getFieldValue('firstName');\n    const lastName = form.getFieldValue('lastName');\n    if (firstName) {\n      const username = generateUsername(firstName, lastName);\n      const email = generateEmail(username);\n      setSuggestedUsername(username);\n      form.setFieldsValue({\n        username,\n        email // Auto-generate email\n      });\n    }\n  };\n\n  const onFinish = async values => {\n    console.log(\"🚀 Registration data:\", values);\n    try {\n      setLoading(true);\n\n      // Prepare registration data\n      const registrationData = {\n        firstName: values.firstName,\n        middleName: values.middleName,\n        lastName: values.lastName,\n        username: values.username,\n        school: values.school,\n        level: values.level,\n        class: values.class,\n        phoneNumber: values.phoneNumber,\n        password: values.password\n      };\n      const response = await registerUser(registrationData);\n      if (response.success) {\n        message.success({\n          content: \"🎉 Registration successful! Redirecting to login...\",\n          duration: 3,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        // Add a small delay to let user see the success message, then redirect with pre-filled data\n        setTimeout(() => {\n          navigate(\"/login\", {\n            state: {\n              username: values.username,\n              password: values.password,\n              autoFill: true,\n              message: \"Account created successfully! Please login with your credentials.\"\n            }\n          });\n        }, 1500);\n      } else {\n        message.error(response.message || \"Registration failed\");\n      }\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      message.error(\"Registration failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"register-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: Logo,\n          alt: \"BrainWave Logo\",\n          className: \"register-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"register-title\",\n          children: \"Join BrainWave\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"register-subtitle\",\n          children: \"Create your account and start learning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: onFinish,\n        className: \"register-form\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"firstName\",\n          label: \"First Name\",\n          rules: [{\n            required: true,\n            message: \"Please enter your first name\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            className: \"form-input\",\n            placeholder: \"Enter your first name\",\n            onChange: handleNameChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"middleName\",\n          label: \"Middle Name\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            className: \"form-input\",\n            placeholder: \"Enter your middle name (optional)\",\n            onChange: handleNameChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"lastName\",\n          label: \"Last Name\",\n          rules: [{\n            required: true,\n            message: \"Please enter your last name\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            className: \"form-input\",\n            placeholder: \"Enter your last name\",\n            onChange: handleNameChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"Username\",\n          rules: [{\n            required: true,\n            message: \"Please enter a username\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            className: \"form-input\",\n            placeholder: \"Your username will be auto-generated\",\n            suffix: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: suggestedUsername && `Suggested: ${suggestedUsername}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"school\",\n          label: \"School Name\",\n          rules: [{\n            required: true,\n            message: \"Please enter your school name\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            className: \"form-input\",\n            placeholder: \"Enter your school name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"level\",\n          label: \"Education Level\",\n          rules: [{\n            required: true,\n            message: \"Please select your education level\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            className: \"form-input\",\n            placeholder: \"Select your education level\",\n            onChange: value => setSchoolType(value),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"Primary\",\n              children: \"Primary Education (Classes 1-7)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"Secondary\",\n              children: \"Secondary Education (Forms 1-4)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"Advance\",\n              children: \"Advanced Level (Forms 5-6)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"class\",\n          label: \"Class/Form\",\n          rules: [{\n            required: true,\n            message: \"Please select your class or form\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            className: \"form-input\",\n            placeholder: schoolType ? \"Select your class/form\" : \"Please select education level first\",\n            disabled: !schoolType,\n            children: [schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map(i => /*#__PURE__*/_jsxDEV(Option, {\n              value: i,\n              children: `Class ${i}`\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)), schoolType === \"Secondary\" && [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(Option, {\n              value: `Form-${i}`,\n              children: `Form ${i}`\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)), schoolType === \"Advance\" && [5, 6].map(i => /*#__PURE__*/_jsxDEV(Option, {\n              value: `Form-${i}`,\n              children: `Form ${i}`\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"phoneNumber\",\n          label: \"Phone Number\",\n          rules: [{\n            required: true,\n            message: \"Please enter your phone number\"\n          }, {\n            pattern: /^0[67]\\d{8}$/,\n            message: \"Phone number must start with 06 or 07 and be 10 digits\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"tel\",\n            className: \"form-input\",\n            placeholder: \"Enter mobile number (e.g., 0712345678)\",\n            maxLength: 10\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"Password\",\n          rules: [{\n            required: true,\n            message: \"Please enter your password\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            className: \"form-input\",\n            placeholder: \"Create a password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"register-btn\",\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"loading-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), \"Registering...\"]\n            }, void 0, true) : \"🚀 Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-link\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              children: \"Login here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 108,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"cYtGySinwHu363NFtWUU229ohlM=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["Form", "message", "Input", "Select", "React", "useState", "Link", "useNavigate", "registerUser", "Logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "Register", "_s", "loading", "setLoading", "schoolType", "setSchoolType", "suggestedUsername", "setSuggestedUsername", "form", "useForm", "navigate", "generateUsername", "firstName", "lastName", "cleanName", "name", "toLowerCase", "replace", "first", "last", "options", "Math", "floor", "random", "filter", "option", "length", "generateEmail", "username", "timestamp", "Date", "now", "handleNameChange", "getFieldValue", "email", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onFinish", "values", "console", "log", "registrationData", "middleName", "school", "level", "class", "phoneNumber", "password", "response", "success", "content", "duration", "style", "marginTop", "setTimeout", "state", "autoFill", "error", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "suffix", "fontSize", "color", "value", "disabled", "map", "i", "pattern", "type", "max<PERSON><PERSON><PERSON>", "Password", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Register/index.js"], "sourcesContent": ["import { Form, message, Input, Select } from \"antd\";\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser } from \"../../../apicalls/users\";\nimport Logo from \"../../../assets/logo.png\";\n\nconst { Option } = Select;\n\nfunction Register() {\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\");\n  const [suggestedUsername, setSuggestedUsername] = useState(\"\");\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n\n  // Generate username from names (simplified - no middle name)\n  const generateUsername = (firstName, lastName) => {\n    if (!firstName) return \"\";\n\n    const cleanName = (name) => name?.toLowerCase().replace(/[^a-z]/g, '') || '';\n    const first = cleanName(firstName);\n    const last = cleanName(lastName);\n\n    // Generate different username options\n    const options = [\n      `${first}.${last}`,\n      `${first}${last}`,\n      `${first}_${last}`,\n      `${first}${last}${Math.floor(Math.random() * 100)}`,\n    ].filter(option => option.length >= 3);\n\n    return options[0] || `user${Math.floor(Math.random() * 10000)}`;\n  };\n\n  // Generate automatic email from username\n  const generateEmail = (username) => {\n    if (!username) return '';\n    const timestamp = Date.now();\n    return `${username}.${timestamp}@brainwave.temp`;\n  };\n\n  // Handle name changes to auto-generate username and email\n  const handleNameChange = () => {\n    const firstName = form.getFieldValue('firstName');\n    const lastName = form.getFieldValue('lastName');\n\n    if (firstName) {\n      const username = generateUsername(firstName, lastName);\n      const email = generateEmail(username);\n      setSuggestedUsername(username);\n      form.setFieldsValue({\n        username,\n        email // Auto-generate email\n      });\n    }\n  };\n\n  const onFinish = async (values) => {\n    console.log(\"🚀 Registration data:\", values);\n    \n    try {\n      setLoading(true);\n      \n      // Prepare registration data\n      const registrationData = {\n        firstName: values.firstName,\n        middleName: values.middleName,\n        lastName: values.lastName,\n        username: values.username,\n        school: values.school,\n        level: values.level,\n        class: values.class,\n        phoneNumber: values.phoneNumber,\n        password: values.password\n      };\n      \n      const response = await registerUser(registrationData);\n      if (response.success) {\n        message.success({\n          content: \"🎉 Registration successful! Redirecting to login...\",\n          duration: 3,\n          style: { marginTop: '20px' }\n        });\n        // Add a small delay to let user see the success message, then redirect with pre-filled data\n        setTimeout(() => {\n          navigate(\"/login\", {\n            state: {\n              username: values.username,\n              password: values.password,\n              autoFill: true,\n              message: \"Account created successfully! Please login with your credentials.\"\n            }\n          });\n        }, 1500);\n      } else {\n        message.error(response.message || \"Registration failed\");\n      }\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      message.error(\"Registration failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"register-container\">\n      <div className=\"register-card\">\n        <div className=\"register-header\">\n          <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\n          <h1 className=\"register-title\">Join BrainWave</h1>\n          <p className=\"register-subtitle\">Create your account and start learning</p>\n        </div>\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          className=\"register-form\"\n        >\n          {/* First Name */}\n          <Form.Item\n            name=\"firstName\"\n            label=\"First Name\"\n            rules={[{ required: true, message: \"Please enter your first name\" }]}\n          >\n            <Input\n              className=\"form-input\"\n              placeholder=\"Enter your first name\"\n              onChange={handleNameChange}\n            />\n          </Form.Item>\n\n          {/* Middle Name */}\n          <Form.Item\n            name=\"middleName\"\n            label=\"Middle Name\"\n          >\n            <Input\n              className=\"form-input\"\n              placeholder=\"Enter your middle name (optional)\"\n              onChange={handleNameChange}\n            />\n          </Form.Item>\n\n          {/* Last Name */}\n          <Form.Item\n            name=\"lastName\"\n            label=\"Last Name\"\n            rules={[{ required: true, message: \"Please enter your last name\" }]}\n          >\n            <Input\n              className=\"form-input\"\n              placeholder=\"Enter your last name\"\n              onChange={handleNameChange}\n            />\n          </Form.Item>\n\n          {/* Username */}\n          <Form.Item\n            name=\"username\"\n            label=\"Username\"\n            rules={[{ required: true, message: \"Please enter a username\" }]}\n          >\n            <Input\n              className=\"form-input\"\n              placeholder=\"Your username will be auto-generated\"\n              suffix={\n                <span style={{ fontSize: '12px', color: '#666' }}>\n                  {suggestedUsername && `Suggested: ${suggestedUsername}`}\n                </span>\n              }\n            />\n          </Form.Item>\n\n          {/* School */}\n          <Form.Item\n            name=\"school\"\n            label=\"School Name\"\n            rules={[{ required: true, message: \"Please enter your school name\" }]}\n          >\n            <Input\n              className=\"form-input\"\n              placeholder=\"Enter your school name\"\n            />\n          </Form.Item>\n\n          {/* Education Level */}\n          <Form.Item\n            name=\"level\"\n            label=\"Education Level\"\n            rules={[{ required: true, message: \"Please select your education level\" }]}\n          >\n            <Select\n              className=\"form-input\"\n              placeholder=\"Select your education level\"\n              onChange={(value) => setSchoolType(value)}\n            >\n              <Option value=\"Primary\">Primary Education (Classes 1-7)</Option>\n              <Option value=\"Secondary\">Secondary Education (Forms 1-4)</Option>\n              <Option value=\"Advance\">Advanced Level (Forms 5-6)</Option>\n            </Select>\n          </Form.Item>\n\n          {/* Class/Form */}\n          <Form.Item\n            name=\"class\"\n            label=\"Class/Form\"\n            rules={[{ required: true, message: \"Please select your class or form\" }]}\n          >\n            <Select\n              className=\"form-input\"\n              placeholder={schoolType ? \"Select your class/form\" : \"Please select education level first\"}\n              disabled={!schoolType}\n            >\n              {schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map((i) => (\n                <Option key={i} value={i}>{`Class ${i}`}</Option>\n              ))}\n              {schoolType === \"Secondary\" && [1, 2, 3, 4].map((i) => (\n                <Option key={i} value={`Form-${i}`}>{`Form ${i}`}</Option>\n              ))}\n              {schoolType === \"Advance\" && [5, 6].map((i) => (\n                <Option key={i} value={`Form-${i}`}>{`Form ${i}`}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          {/* Phone Number */}\n          <Form.Item\n            name=\"phoneNumber\"\n            label=\"Phone Number\"\n            rules={[\n              { required: true, message: \"Please enter your phone number\" },\n              { pattern: /^0[67]\\d{8}$/, message: \"Phone number must start with 06 or 07 and be 10 digits\" }\n            ]}\n          >\n            <Input\n              type=\"tel\"\n              className=\"form-input\"\n              placeholder=\"Enter mobile number (e.g., 0712345678)\"\n              maxLength={10}\n            />\n          </Form.Item>\n\n          {/* Password */}\n          <Form.Item\n            name=\"password\"\n            label=\"Password\"\n            rules={[{ required: true, message: \"Please enter your password\" }]}\n          >\n            <Input.Password\n              className=\"form-input\"\n              placeholder=\"Create a password\"\n            />\n          </Form.Item>\n\n          {/* Submit Button */}\n          <Form.Item>\n            <button type=\"submit\" className=\"register-btn\" disabled={loading}>\n              {loading ? (\n                <>\n                  <span className=\"loading-spinner\"></span>\n                  Registering...\n                </>\n              ) : (\n                \"🚀 Create Account\"\n              )}\n            </button>\n          </Form.Item>\n\n          {/* Login Link */}\n          <div className=\"login-link\">\n            <p>Already have an account? <Link to=\"/login\">Login here</Link></p>\n          </div>\n        </Form>\n      </div>\n    </div>\n  );\n}\n\nexport default Register;\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACnD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAM;EAAEC;AAAO,CAAC,GAAGX,MAAM;AAEzB,SAASY,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkB,IAAI,CAAC,GAAGvB,IAAI,CAACwB,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMmB,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;IAChD,IAAI,CAACD,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAME,SAAS,GAAIC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,KAAI,EAAE;IAC5E,MAAMC,KAAK,GAAGJ,SAAS,CAACF,SAAS,CAAC;IAClC,MAAMO,IAAI,GAAGL,SAAS,CAACD,QAAQ,CAAC;;IAEhC;IACA,MAAMO,OAAO,GAAG,CACb,GAAEF,KAAM,IAAGC,IAAK,EAAC,EACjB,GAAED,KAAM,GAAEC,IAAK,EAAC,EAChB,GAAED,KAAM,IAAGC,IAAK,EAAC,EACjB,GAAED,KAAM,GAAEC,IAAK,GAAEE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAE,EAAC,CACpD,CAACC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,MAAM,IAAI,CAAC,CAAC;IAEtC,OAAON,OAAO,CAAC,CAAC,CAAC,IAAK,OAAMC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,CAAE,EAAC;EACjE,CAAC;;EAED;EACA,MAAMI,aAAa,GAAIC,QAAQ,IAAK;IAClC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IACxB,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,OAAQ,GAAEH,QAAS,IAAGC,SAAU,iBAAgB;EAClD,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMpB,SAAS,GAAGJ,IAAI,CAACyB,aAAa,CAAC,WAAW,CAAC;IACjD,MAAMpB,QAAQ,GAAGL,IAAI,CAACyB,aAAa,CAAC,UAAU,CAAC;IAE/C,IAAIrB,SAAS,EAAE;MACb,MAAMgB,QAAQ,GAAGjB,gBAAgB,CAACC,SAAS,EAAEC,QAAQ,CAAC;MACtD,MAAMqB,KAAK,GAAGP,aAAa,CAACC,QAAQ,CAAC;MACrCrB,oBAAoB,CAACqB,QAAQ,CAAC;MAC9BpB,IAAI,CAAC2B,cAAc,CAAC;QAClBP,QAAQ;QACRM,KAAK,CAAC;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED,MAAME,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,MAAM,CAAC;IAE5C,IAAI;MACFlC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMqC,gBAAgB,GAAG;QACvB5B,SAAS,EAAEyB,MAAM,CAACzB,SAAS;QAC3B6B,UAAU,EAAEJ,MAAM,CAACI,UAAU;QAC7B5B,QAAQ,EAAEwB,MAAM,CAACxB,QAAQ;QACzBe,QAAQ,EAAES,MAAM,CAACT,QAAQ;QACzBc,MAAM,EAAEL,MAAM,CAACK,MAAM;QACrBC,KAAK,EAAEN,MAAM,CAACM,KAAK;QACnBC,KAAK,EAAEP,MAAM,CAACO,KAAK;QACnBC,WAAW,EAAER,MAAM,CAACQ,WAAW;QAC/BC,QAAQ,EAAET,MAAM,CAACS;MACnB,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMtD,YAAY,CAAC+C,gBAAgB,CAAC;MACrD,IAAIO,QAAQ,CAACC,OAAO,EAAE;QACpB9D,OAAO,CAAC8D,OAAO,CAAC;UACdC,OAAO,EAAE,qDAAqD;UAC9DC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACF;QACAC,UAAU,CAAC,MAAM;UACf3C,QAAQ,CAAC,QAAQ,EAAE;YACjB4C,KAAK,EAAE;cACL1B,QAAQ,EAAES,MAAM,CAACT,QAAQ;cACzBkB,QAAQ,EAAET,MAAM,CAACS,QAAQ;cACzBS,QAAQ,EAAE,IAAI;cACdrE,OAAO,EAAE;YACX;UACF,CAAC,CAAC;QACJ,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLA,OAAO,CAACsE,KAAK,CAACT,QAAQ,CAAC7D,OAAO,IAAI,qBAAqB,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOsE,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CtE,OAAO,CAACsE,KAAK,CAAC,wCAAwC,CAAC;IACzD,CAAC,SAAS;MACRrD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA;IAAK6D,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjC9D,OAAA;MAAK6D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B9D,OAAA;QAAK6D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B9D,OAAA;UAAK+D,GAAG,EAAEjE,IAAK;UAACkE,GAAG,EAAC,gBAAgB;UAACH,SAAS,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEpE,OAAA;UAAI6D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDpE,OAAA;UAAG6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAsC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAENpE,OAAA,CAACX,IAAI;QACHuB,IAAI,EAAEA,IAAK;QACXyD,MAAM,EAAC,UAAU;QACjB7B,QAAQ,EAAEA,QAAS;QACnBqB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAGzB9D,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,WAAW;UAChBoD,KAAK,EAAC,YAAY;UAClBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAA+B,CAAC,CAAE;UAAAwE,QAAA,eAErE9D,OAAA,CAACT,KAAK;YACJsE,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC,uBAAuB;YACnCC,QAAQ,EAAEvC;UAAiB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,YAAY;UACjBoD,KAAK,EAAC,aAAa;UAAAT,QAAA,eAEnB9D,OAAA,CAACT,KAAK;YACJsE,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC,mCAAmC;YAC/CC,QAAQ,EAAEvC;UAAiB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,UAAU;UACfoD,KAAK,EAAC,WAAW;UACjBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAA8B,CAAC,CAAE;UAAAwE,QAAA,eAEpE9D,OAAA,CAACT,KAAK;YACJsE,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC,sBAAsB;YAClCC,QAAQ,EAAEvC;UAAiB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,UAAU;UACfoD,KAAK,EAAC,UAAU;UAChBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAA0B,CAAC,CAAE;UAAAwE,QAAA,eAEhE9D,OAAA,CAACT,KAAK;YACJsE,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC,sCAAsC;YAClDE,MAAM,eACJ5E,OAAA;cAAMuD,KAAK,EAAE;gBAAEsB,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAhB,QAAA,EAC9CpD,iBAAiB,IAAK,cAAaA,iBAAkB;YAAC;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,QAAQ;UACboD,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAAgC,CAAC,CAAE;UAAAwE,QAAA,eAEtE9D,OAAA,CAACT,KAAK;YACJsE,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC;UAAwB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,OAAO;UACZoD,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAAqC,CAAC,CAAE;UAAAwE,QAAA,eAE3E9D,OAAA,CAACR,MAAM;YACLqE,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC,6BAA6B;YACzCC,QAAQ,EAAGI,KAAK,IAAKtE,aAAa,CAACsE,KAAK,CAAE;YAAAjB,QAAA,gBAE1C9D,OAAA,CAACG,MAAM;cAAC4E,KAAK,EAAC,SAAS;cAAAjB,QAAA,EAAC;YAA+B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChEpE,OAAA,CAACG,MAAM;cAAC4E,KAAK,EAAC,WAAW;cAAAjB,QAAA,EAAC;YAA+B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClEpE,OAAA,CAACG,MAAM;cAAC4E,KAAK,EAAC,SAAS;cAAAjB,QAAA,EAAC;YAA0B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,OAAO;UACZoD,KAAK,EAAC,YAAY;UAClBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAAmC,CAAC,CAAE;UAAAwE,QAAA,eAEzE9D,OAAA,CAACR,MAAM;YACLqE,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAElE,UAAU,GAAG,wBAAwB,GAAG,qCAAsC;YAC3FwE,QAAQ,EAAE,CAACxE,UAAW;YAAAsD,QAAA,GAErBtD,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACyE,GAAG,CAAEC,CAAC,iBACvDlF,OAAA,CAACG,MAAM;cAAS4E,KAAK,EAAEG,CAAE;cAAApB,QAAA,EAAG,SAAQoB,CAAE;YAAC,GAA1BA,CAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkC,CACjD,CAAC,EACD5D,UAAU,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACyE,GAAG,CAAEC,CAAC,iBAChDlF,OAAA,CAACG,MAAM;cAAS4E,KAAK,EAAG,QAAOG,CAAE,EAAE;cAAApB,QAAA,EAAG,QAAOoB,CAAE;YAAC,GAAnCA,CAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA2C,CAC1D,CAAC,EACD5D,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACyE,GAAG,CAAEC,CAAC,iBACxClF,OAAA,CAACG,MAAM;cAAS4E,KAAK,EAAG,QAAOG,CAAE,EAAE;cAAApB,QAAA,EAAG,QAAOoB,CAAE;YAAC,GAAnCA,CAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA2C,CAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,aAAa;UAClBoD,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAAiC,CAAC,EAC7D;YAAE6F,OAAO,EAAE,cAAc;YAAE7F,OAAO,EAAE;UAAyD,CAAC,CAC9F;UAAAwE,QAAA,eAEF9D,OAAA,CAACT,KAAK;YACJ6F,IAAI,EAAC,KAAK;YACVvB,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC,wCAAwC;YACpDW,SAAS,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UACRnD,IAAI,EAAC,UAAU;UACfoD,KAAK,EAAC,UAAU;UAChBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnF,OAAO,EAAE;UAA6B,CAAC,CAAE;UAAAwE,QAAA,eAEnE9D,OAAA,CAACT,KAAK,CAAC+F,QAAQ;YACbzB,SAAS,EAAC,YAAY;YACtBa,WAAW,EAAC;UAAmB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGZpE,OAAA,CAACX,IAAI,CAACiF,IAAI;UAAAR,QAAA,eACR9D,OAAA;YAAQoF,IAAI,EAAC,QAAQ;YAACvB,SAAS,EAAC,cAAc;YAACmB,QAAQ,EAAE1E,OAAQ;YAAAwD,QAAA,EAC9DxD,OAAO,gBACNN,OAAA,CAAAE,SAAA;cAAA4D,QAAA,gBACE9D,OAAA;gBAAM6D,SAAS,EAAC;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,kBAE3C;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGZpE,OAAA;UAAK6D,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB9D,OAAA;YAAA8D,QAAA,GAAG,2BAAyB,eAAA9D,OAAA,CAACL,IAAI;cAAC4F,EAAE,EAAC,QAAQ;cAAAzB,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/D,EAAA,CA9QQD,QAAQ;EAAA,QAIAf,IAAI,CAACwB,OAAO,EACVjB,WAAW;AAAA;AAAA4F,EAAA,GALrBpF,QAAQ;AAgRjB,eAAeA,QAAQ;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}