{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment } from '../../../apicalls/payment';\nimport { checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      if (response.success) {\n        setPlans(response.data);\n      } else {\n        message.error('Failed to load plans');\n      }\n    } catch (error) {\n      message.error('Error loading plans: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n  const handlePlanSelect = async plan => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      message.error('Payment failed: ' + error.message);\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? 'Upgrade Plans' : 'Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"current-price\",\n                    children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading,\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), paymentLoading ? 'Processing...' : 'Select Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"tZLgixGClPY3hTKA3A9bj2iUpiM=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "jsxDEV", "_jsxDEV", "Subscription", "_s", "_subscriptionData$act", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "user", "state", "subscriptionData", "subscription", "dispatch", "fetchPlans", "checkCurrentSubscription", "response", "success", "data", "error", "console", "log", "handlePlanSelect", "plan", "phoneNumber", "test", "_user$name", "paymentData", "userId", "_id", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "Error", "getSubscriptionStatus", "paymentStatus", "status", "endDate", "Date", "now", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "Math", "ceil", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "title", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "onClick", "disabled", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment } from '../../../apicalls/payment';\nimport { checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      if (response.success) {\n        setPlans(response.data);\n      } else {\n        message.error('Failed to load plans');\n      }\n    } catch (error) {\n      message.error('Error loading plans: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  const handlePlanSelect = async (plan) => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      message.error('Payment failed: ' + error.message);\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active' ? 'Upgrade Plans' : 'Choose Your Plan'}\n          </h2>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('glimp') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <span className=\"current-price\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading ? 'Processing...' : 'Select Plan'}\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,kBAAkB,QAAQ,2BAA2B;AAC9D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM;IAAE4B;EAAK,CAAC,GAAG1B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAG5B,WAAW,CAAE2B,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACdgC,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMU,QAAQ,GAAG,MAAMvB,QAAQ,CAAC,CAAC;MACjC,IAAIuB,QAAQ,CAACC,OAAO,EAAE;QACpBb,QAAQ,CAACY,QAAQ,CAACE,IAAI,CAAC;MACzB,CAAC,MAAM;QACLhC,OAAO,CAACiC,KAAK,CAAC,sBAAsB,CAAC;MACvC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,uBAAuB,GAAGA,KAAK,CAACjC,OAAO,CAAC;IACxD,CAAC,SAAS;MACRoB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,kBAAkB,CAAC,CAAC;MAC3CyB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEL,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACvC,IAAI,CAACd,IAAI,CAACe,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAChB,IAAI,CAACe,WAAW,CAAC,EAAE;MACjEtC,OAAO,CAACiC,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAO,UAAA;MACFlB,iBAAiB,CAAC,IAAI,CAAC;MACvBK,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM+B,WAAW,GAAG;QAClBJ,IAAI,EAAEA,IAAI;QACVK,MAAM,EAAEnB,IAAI,CAACoB,GAAG;QAChBC,SAAS,EAAErB,IAAI,CAACe,WAAW;QAC3BO,SAAS,EAAEtB,IAAI,CAACuB,KAAK,IAAK,IAAAN,UAAA,GAAEjB,IAAI,CAACwB,IAAI,cAAAP,UAAA,uBAATA,UAAA,CAAWQ,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAMnB,QAAQ,GAAG,MAAMtB,UAAU,CAACiC,WAAW,CAAC;MAE9C,IAAIX,QAAQ,CAACC,OAAO,EAAE;QACpB/B,OAAO,CAAC+B,OAAO,CAAC,kEAAkE,CAAC;MACrF,CAAC,MAAM;QACL,MAAM,IAAImB,KAAK,CAACpB,QAAQ,CAAC9B,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAACjC,OAAO,CAAC;IACnD,CAAC,SAAS;MACRsB,iBAAiB,CAAC,KAAK,CAAC;MACxBK,QAAQ,CAAChB,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMwC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI1B,gBAAgB,IAAIA,gBAAgB,CAAC2B,aAAa,KAAK,MAAM,IAAI3B,gBAAgB,CAAC4B,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAAC9B,gBAAgB,CAAC6B,OAAO,CAAC;MAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGE,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAAjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC,kBAAkB,MAAK,SAAS,IAAKhC,gBAAgB,IAAIA,gBAAgB,CAAC4B,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAMK,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIJ,IAAI,CAACI,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACvC,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE6B,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAAC9B,gBAAgB,CAAC6B,OAAO,CAAC;IAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMU,QAAQ,GAAGX,OAAO,GAAGE,GAAG;IAC9B,MAAMU,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGN,qBAAqB,CAAC,CAAC;EAElD,oBACEtC,OAAA;IAAKyD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC1D,OAAA;MAAKyD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC1D,OAAA,CAACd,MAAM,CAACyE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAE/B1D,OAAA;UAAIyD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB1D,OAAA,CAACZ,OAAO;YAACqE,SAAS,EAAC;UAAY;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrE,OAAA;UAAGyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGbrE,OAAA,CAACd,MAAM,CAACyE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhC1D,OAAA;UAAIyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDzB,kBAAkB,KAAK,QAAQ,iBAC9B5C,OAAA;UAAKyD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC1D,OAAA;YAAKyD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC1D,OAAA,CAACV,aAAa;cAACmE,SAAS,EAAC;YAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDrE,OAAA;cAAMyD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNrE,OAAA;YAAKyD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC1D,OAAA;cAAKyD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1D,OAAA,CAACZ,OAAO;gBAACqE,SAAS,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCrE,OAAA;gBAAA0D,QAAA,GAAM,QAAM,EAAC,CAAA9C,gBAAgB,aAAhBA,gBAAgB,wBAAAT,qBAAA,GAAhBS,gBAAgB,CAAE2D,UAAU,cAAApE,qBAAA,uBAA5BA,qBAAA,CAA8BqE,KAAK,KAAI,cAAc;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNrE,OAAA;cAAKyD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1D,OAAA,CAACX,aAAa;gBAACoE,SAAS,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCrE,OAAA;gBAAA0D,QAAA,GAAM,WAAS,EAACb,UAAU,CAACjC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6B,OAAO,CAAC;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNrE,OAAA;cAAKyD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1D,OAAA,CAACV,aAAa;gBAACmE,SAAS,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCrE,OAAA;gBAAA0D,QAAA,GAAM,kBAAgB,EAACP,gBAAgB,CAAC,CAAC;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAzB,kBAAkB,KAAK,SAAS,iBAC/B5C,OAAA;UAAKyD,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC1D,OAAA;YAAKyD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC1D,OAAA,CAACT,aAAa;cAACkE,SAAS,EAAC;YAAqB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDrE,OAAA;cAAMyD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNrE,OAAA;YAAKyD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC1D,OAAA;cAAKyD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1D,OAAA,CAACX,aAAa;gBAACoE,SAAS,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCrE,OAAA;gBAAA0D,QAAA,GAAM,WAAS,EAACb,UAAU,CAACjC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6B,OAAO,CAAC;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNrE,OAAA;cAAGyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAzB,kBAAkB,KAAK,MAAM,iBAC5B5C,OAAA;UAAKyD,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC1D,OAAA;YAAKyD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC1D,OAAA,CAACP,MAAM;cAACgE,SAAS,EAAC;YAAkB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCrE,OAAA;cAAMyD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNrE,OAAA;YAAKyD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC1D,OAAA;cAAGyD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGbrE,OAAA,CAACd,MAAM,CAACyE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3B1D,OAAA;UAAIyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1Bd,kBAAkB,KAAK,QAAQ,GAAG,eAAe,GAAG;QAAkB;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,EAEJ/D,OAAO,gBACNN,OAAA;UAAKyD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B1D,OAAA;YAAKyD,SAAS,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BrE,OAAA;YAAA0D,QAAA,EAAG;UAAgB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,gBAENrE,OAAA;UAAKyD,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBtD,KAAK,CAACqE,GAAG,CAAEjD,IAAI;YAAA,IAAAkD,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACd7E,OAAA,CAACd,MAAM,CAACyE,GAAG;cAETmB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BtB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErB1D,OAAA;gBAAKyD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B1D,OAAA;kBAAIyD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAElC,IAAI,CAACgD;gBAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAK,WAAA,GAAAlD,IAAI,CAACgD,KAAK,cAAAE,WAAA,uBAAVA,WAAA,CAAYtC,WAAW,CAAC,CAAC,CAAC6C,QAAQ,CAAC,OAAO,CAAC,kBAC1CjF,OAAA;kBAAMyD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENrE,OAAA;gBAAKyD,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B1D,OAAA;kBAAKyD,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B1D,OAAA;oBAAMyD,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAAiB,qBAAA,GAAEnD,IAAI,CAAC0D,eAAe,cAAAP,qBAAA,uBAApBA,qBAAA,CAAsBQ,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClF7C,IAAI,CAAC4D,WAAW,GAAG5D,IAAI,CAAC0D,eAAe,iBACtClF,OAAA;oBAAMyD,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,IAAAkB,iBAAA,GAAEpD,IAAI,CAAC4D,WAAW,cAAAR,iBAAA,uBAAhBA,iBAAA,CAAkBO,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAChF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNrE,OAAA;kBAAKyD,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAElC,IAAI,CAACyC,QAAQ,EAAC,QAAM,EAACzC,IAAI,CAACyC,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC,eAENrE,OAAA;gBAAKyD,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAmB,cAAA,GAC3BrD,IAAI,CAAC6D,QAAQ,cAAAR,cAAA,uBAAbA,cAAA,CAAeS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACb,GAAG,CAAC,CAACc,OAAO,EAAEC,KAAK,kBAC7CxF,OAAA;kBAAiByD,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvC1D,OAAA,CAACV,aAAa;oBAACmE,SAAS,EAAC;kBAAc;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1CrE,OAAA;oBAAA0D,QAAA,EAAO6B;kBAAO;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrE,OAAA;gBACEyD,SAAS,EAAC,iBAAiB;gBAC3BgC,OAAO,EAAEA,CAAA,KAAMlE,gBAAgB,CAACC,IAAI,CAAE;gBACtCkE,QAAQ,EAAElF,cAAe;gBAAAkD,QAAA,gBAEzB1D,OAAA,CAACR,YAAY;kBAACiE,SAAS,EAAC;gBAAU;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpC7D,cAAc,GAAG,eAAe,GAAG,aAAa;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA,GAtCJ7C,IAAI,CAACM,GAAG;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuCH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAAC3D,IAAI,CAACe,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAChB,IAAI,CAACe,WAAW,CAAC,kBAC7DzB,OAAA,CAACd,MAAM,CAACyE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzB1D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B1D,OAAA,CAACT,aAAa;YAACkE,SAAS,EAAC;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CrE,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAA0D,QAAA,EAAI;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BrE,OAAA;cAAA0D,QAAA,EAAG;YAAuE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9ErE,OAAA;cACEyD,SAAS,EAAC,kBAAkB;cAC5BgC,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAAnC,QAAA,EAClD;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAvRID,YAAY;EAAA,QAICjB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAA6G,EAAA,GANxB7F,YAAY;AAyRlB,eAAeA,YAAY;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}