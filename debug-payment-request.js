// Debug Payment Request - Check what data is being sent
const axios = require('axios');

async function debugPaymentRequest() {
  console.log('🔍 Debugging Payment Request...\n');

  // Test with a sample user and plan data
  const testPaymentData = {
    plan: {
      _id: "test_plan_id",
      title: "Test Plan",
      discountedPrice: 5000,
      duration: 1
    },
    userId: "test_user_id",
    userPhone: "0712345678",
    userEmail: "<EMAIL>"
  };

  console.log('📤 Test payment data being sent:');
  console.log(JSON.stringify(testPaymentData, null, 2));
  console.log('');

  try {
    console.log('🔄 Sending request to local server...');
    const response = await axios.post('http://localhost:5000/api/payment/create-invoice', testPaymentData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test_token' // This will fail auth but we can see the validation
      },
      timeout: 10000
    });

    console.log('✅ Response received:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('📝 Response details:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Status Text:', error.response.statusText);
      console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.log('\n✅ Authentication error (expected) - endpoint is working');
      } else if (error.response.status === 400) {
        console.log('\n❌ Validation error - this is what we need to fix:');
        console.log('Error Type:', error.response.data.errorType);
        console.log('Message:', error.response.data.message);
      }
    } else {
      console.error('❌ Network error:', error.message);
    }
  }
}

// Test server health first
async function testServerHealth() {
  try {
    console.log('🔍 Checking server health...');
    const response = await axios.get('http://localhost:5000/api/health', {
      timeout: 5000
    });
    
    console.log('✅ Server is running');
    console.log('Health data:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Server health check failed:', error.message);
    return false;
  }
}

// Main function
async function runDebug() {
  console.log('🚀 Starting Payment Debug...\n');
  
  const serverHealthy = await testServerHealth();
  console.log('');
  
  if (serverHealthy) {
    await debugPaymentRequest();
  } else {
    console.log('❌ Server is not running. Please start the server first:');
    console.log('cd server && npm start');
  }
  
  console.log('\n✅ Debug completed!');
}

runDebug().catch(console.error);
