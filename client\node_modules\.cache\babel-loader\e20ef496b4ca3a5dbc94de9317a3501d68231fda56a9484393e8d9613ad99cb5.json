{"ast": null, "code": "// Modern UI Components\nexport{default as But<PERSON>}from'./Button';export{default as Card}from'./Card';export{default as Input}from'./Input';export{default as Loading}from'./Loading';// Quiz Components\nexport{default as QuizCard}from'./QuizCard';export{default as QuizQuestion}from'./QuizQuestion';export{default as QuizTimer,QuizTimerOverlay}from'./QuizTimer';// Theme & Performance Components\nexport{default as ThemeToggle,ThemeSwitch,ThemeToggleWithLabel}from'./ThemeToggle';export{default as LazyImage}from'./LazyImage';export{default as ErrorBoundary,ErrorFallback}from'./ErrorBoundary';// Responsive Components\nexport{default as ResponsiveContainer,ResponsiveGrid,ResponsiveText,MobileFirst,DesktopFirst,ResponsiveStack,ResponsiveShow}from'./ResponsiveContainer';// Performance Components (PerformanceIndicator removed)\nexport{usePerformanceMonitor,LazyWrapper,OptimizedImage,useDebouncedSearch,VirtualList}from'./PerformanceMonitor';// Theme Context\nexport{ThemeProvider,useTheme}from'../../contexts/ThemeContext';", "map": {"version": 3, "names": ["default", "<PERSON><PERSON>", "Card", "Input", "Loading", "QuizCard", "QuizQuestion", "QuizTimer", "QuizTimer<PERSON><PERSON>lay", "ThemeToggle", "ThemeSwitch", "ThemeToggleWithLabel", "LazyImage", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ResponsiveContainer", "ResponsiveGrid", "ResponsiveText", "MobileFirst", "DesktopFirst", "ResponsiveStack", "ResponsiveShow", "usePerformanceMonitor", "LazyWrapper", "OptimizedImage", "useDebouncedSearch", "VirtualList", "ThemeProvider", "useTheme"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/index.js"], "sourcesContent": ["// Modern UI Components\nexport { default as But<PERSON> } from './Button';\nexport { default as Card } from './Card';\nexport { default as Input } from './Input';\nexport { default as Loading } from './Loading';\n\n// Quiz Components\nexport { default as QuizCard } from './QuizCard';\nexport { default as QuizQuestion } from './QuizQuestion';\nexport { default as QuizTimer, QuizTimerOverlay } from './QuizTimer';\n\n// Theme & Performance Components\nexport { default as ThemeToggle, ThemeSwitch, ThemeToggleWithLabel } from './ThemeToggle';\nexport { default as LazyImage } from './LazyImage';\nexport { default as ErrorBoundary, ErrorFallback } from './ErrorBoundary';\n\n// Responsive Components\nexport {\n  default as ResponsiveContainer,\n  ResponsiveGrid,\n  ResponsiveText,\n  MobileFirst,\n  DesktopFirst,\n  ResponsiveStack,\n  ResponsiveShow\n} from './ResponsiveContainer';\n\n// Performance Components (PerformanceIndicator removed)\nexport {\n  usePerformanceMonitor,\n  LazyWrapper,\n  OptimizedImage,\n  useDebouncedSearch,\n  VirtualList\n} from './PerformanceMonitor';\n\n// Theme Context\nexport { ThemeProvider, useTheme } from '../../contexts/ThemeContext';\n"], "mappings": "AAAA;AACA,OAASA,OAAO,GAAI,CAAAC,MAAM,KAAQ,UAAU,CAC5C,OAASD,OAAO,GAAI,CAAAE,IAAI,KAAQ,QAAQ,CACxC,OAASF,OAAO,GAAI,CAAAG,KAAK,KAAQ,SAAS,CAC1C,OAASH,OAAO,GAAI,CAAAI,OAAO,KAAQ,WAAW,CAE9C;AACA,OAASJ,OAAO,GAAI,CAAAK,QAAQ,KAAQ,YAAY,CAChD,OAASL,OAAO,GAAI,CAAAM,YAAY,KAAQ,gBAAgB,CACxD,OAASN,OAAO,GAAI,CAAAO,SAAS,CAAEC,gBAAgB,KAAQ,aAAa,CAEpE;AACA,OAASR,OAAO,GAAI,CAAAS,WAAW,CAAEC,WAAW,CAAEC,oBAAoB,KAAQ,eAAe,CACzF,OAASX,OAAO,GAAI,CAAAY,SAAS,KAAQ,aAAa,CAClD,OAASZ,OAAO,GAAI,CAAAa,aAAa,CAAEC,aAAa,KAAQ,iBAAiB,CAEzE;AACA,OACEd,OAAO,GAAI,CAAAe,mBAAmB,CAC9BC,cAAc,CACdC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,eAAe,CACfC,cAAc,KACT,uBAAuB,CAE9B;AACA,OACEC,qBAAqB,CACrBC,WAAW,CACXC,cAAc,CACdC,kBAAkB,CAClBC,WAAW,KACN,sBAAsB,CAE7B;AACA,OAASC,aAAa,CAAEC,QAAQ,KAAQ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}