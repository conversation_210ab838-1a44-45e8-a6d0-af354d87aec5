import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';
import { getPlans } from '../../../apicalls/plans';
import { addPayment, checkPaymentStatus } from '../../../apicalls/payment';
import { ShowLoading, HideLoading } from '../../../redux/loaderSlice';
import './Subscription.css';

const Subscription = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState('');
  const { user } = useSelector((state) => state.user);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const dispatch = useDispatch();

  // Fallback sample plans in case API fails
  const samplePlans = [
    {
      _id: "glimp-plan-sample",
      title: "Glimp Plan",
      features: [
        "1-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings"
      ],
      actualPrice: 15000,
      discountedPrice: 13000,
      discountPercentage: 13,
      duration: 1,
      status: true
    },
    {
      _id: "basic-plan-sample",
      title: "Basic Membership",
      features: [
        "2-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings"
      ],
      actualPrice: 28570,
      discountedPrice: 20000,
      discountPercentage: 30,
      duration: 2,
      status: true
    },
    {
      _id: "premium-plan-sample",
      title: "Premium Plan",
      features: [
        "3-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings",
        "Priority support"
      ],
      actualPrice: 45000,
      discountedPrice: 35000,
      discountPercentage: 22,
      duration: 3,
      status: true
    }
  ];

  useEffect(() => {
    fetchPlans();
    checkCurrentSubscription();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      console.log('Fetching plans...');
      const response = await getPlans();
      console.log('Plans response:', response);

      if (response.success && response.data && response.data.length > 0) {
        setPlans(response.data);
        console.log('Plans loaded successfully from API:', response.data);
      } else if (Array.isArray(response) && response.length > 0) {
        // Handle case where response is directly an array of plans
        setPlans(response);
        console.log('Plans loaded as array from API:', response);
      } else {
        console.warn('No plans from API, using sample plans');
        setPlans(samplePlans);
        message.info('Showing sample plans. Please check your connection.');
      }
    } catch (error) {
      console.error('Error loading plans from API:', error);
      console.log('Using fallback sample plans');
      setPlans(samplePlans);
      message.warning('Using sample plans. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const checkCurrentSubscription = async () => {
    try {
      const response = await checkPaymentStatus();
      console.log('Current subscription:', response);
    } catch (error) {
      console.log('No active subscription found');
    }
  };

  const handlePlanSelect = async (plan) => {
    if (!user.phoneNumber || !/^(06|07)\d{8}$/.test(user.phoneNumber)) {
      message.error('Please update your phone number in your profile before subscribing');
      return;
    }

    try {
      setSelectedPlan(plan);
      setPaymentLoading(true);
      setShowProcessingModal(true);
      setPaymentStatus('Initiating payment...');

      const paymentData = {
        plan: plan,
        userId: user._id,
        userPhone: user.phoneNumber,
        userEmail: user.email || `${user.name?.replace(/\s+/g, '').toLowerCase()}@brainwave.temp`
      };

      setPaymentStatus('Sending payment request...');
      const response = await addPayment(paymentData);

      if (response.success) {
        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');

        // Start checking payment status
        setTimeout(() => {
          checkPaymentConfirmation(response.order_id || 'demo_order');
        }, 3000);

      } else {
        throw new Error(response.message || 'Payment failed');
      }
    } catch (error) {
      setShowProcessingModal(false);
      message.error('Payment failed: ' + error.message);
      setPaymentLoading(false);
    }
  };

  const checkPaymentConfirmation = async (orderId) => {
    try {
      setPaymentStatus('📱 Complete the payment on your phone, we\'ll detect it automatically...');

      // Poll payment status every 1 second for up to 1 minute (very responsive)
      let attempts = 0;
      const maxAttempts = 60; // 60 attempts * 1 second = 1 minute
      let isPolling = true;

      // Add visibility change listener to check immediately when user returns to tab
      const handleVisibilityChange = () => {
        if (!document.hidden && isPolling) {
          console.log('User returned to tab, checking payment status immediately...');
          setPaymentStatus('🔍 Checking payment status...');
          // Trigger immediate check
          setTimeout(() => pollPaymentStatus(), 100);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      const pollPaymentStatus = async () => {
        attempts++;

        try {
          const statusResponse = await checkPaymentStatus({ orderId });

          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {
            // Payment confirmed immediately!
            isPolling = false; // Stop polling
            document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener

            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');

            // Show success immediately
            setTimeout(() => {
              setShowProcessingModal(false);
              setShowSuccessModal(true);
              setPaymentLoading(false);

              // Refresh subscription data
              checkCurrentSubscription();

              // Show immediate success message
              message.success({
                content: '🎉 Payment confirmed! All features are now unlocked!',
                duration: 5,
                style: {
                  marginTop: '20vh',
                  fontSize: '16px'
                }
              });

            }, 500); // Reduced delay for immediate feedback

          } else if (attempts >= maxAttempts) {
            // Timeout - but don't fail completely
            isPolling = false; // Stop polling
            document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener

            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');

            setTimeout(() => {
              setShowProcessingModal(false);
              setPaymentLoading(false);
              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');
            }, 2000);

          } else {
            // Continue polling with very frequent checks
            const remainingSeconds = (maxAttempts - attempts);
            if (remainingSeconds > 30) {
              setPaymentStatus(`🔍 Checking for payment confirmation... (${Math.ceil(remainingSeconds / 60)} minutes remaining)`);
            } else {
              setPaymentStatus(`🔍 Checking for payment confirmation... (${remainingSeconds} seconds remaining)`);
            }
            setTimeout(pollPaymentStatus, 1000); // Check every 1 second
          }

        } catch (error) {
          console.error('Payment status check error:', error);
          if (attempts >= maxAttempts) {
            isPolling = false; // Stop polling
            document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener
            setShowProcessingModal(false);
            setPaymentLoading(false);
            message.error('Unable to confirm payment status. Please check your subscription status manually.');
          } else {
            // Continue polling even if there's an error
            setTimeout(pollPaymentStatus, 1000);
          }
        }
      };

      // Start polling immediately (no delay) - check right away
      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds

    } catch (error) {
      isPolling = false; // Stop polling
      document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener
      setShowProcessingModal(false);
      message.error('Payment confirmation failed: ' + error.message);
      setPaymentLoading(false);
    }
  };

  const getSubscriptionStatus = () => {
    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {
      const endDate = new Date(subscriptionData.endDate);
      const now = new Date();
      if (endDate > now) {
        return 'active';
      }
    }
    
    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {
      return 'expired';
    }
    
    return 'none';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = () => {
    if (!subscriptionData?.endDate) return 0;
    const endDate = new Date(subscriptionData.endDate);
    const now = new Date();
    const diffTime = endDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const subscriptionStatus = getSubscriptionStatus();

  return (
    <div className="subscription-page">
      <div className="subscription-container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="subscription-header"
        >
          <h1 className="page-title">
            <FaCrown className="title-icon" />
            Subscription Management
          </h1>
          <p className="page-subtitle">Manage your subscription and access premium features</p>
        </motion.div>

        {/* Current Subscription Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="current-subscription"
        >
          <h2 className="section-title">Current Subscription</h2>
          
          {subscriptionStatus === 'active' && (
            <div className="subscription-card active">
              <div className="subscription-status">
                <FaCheckCircle className="status-icon active" />
                <span className="status-text">Active Subscription</span>
              </div>
              <div className="subscription-details">
                <div className="detail-item">
                  <FaCrown className="detail-icon" />
                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>
                </div>
                <div className="detail-item">
                  <FaCalendarAlt className="detail-icon" />
                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>
                </div>
                <div className="detail-item">
                  <FaCheckCircle className="detail-icon" />
                  <span>Days Remaining: {getDaysRemaining()}</span>
                </div>
              </div>
            </div>
          )}

          {subscriptionStatus === 'expired' && (
            <div className="subscription-card expired">
              <div className="subscription-status">
                <FaTimesCircle className="status-icon expired" />
                <span className="status-text">Subscription Expired</span>
              </div>
              <div className="subscription-details">
                <div className="detail-item">
                  <FaCalendarAlt className="detail-icon" />
                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>
                </div>
                <p className="renewal-message">
                  Your subscription has expired. Choose a new plan below to continue accessing premium features.
                </p>
              </div>
            </div>
          )}

          {subscriptionStatus === 'none' && (
            <div className="subscription-card none">
              <div className="subscription-status">
                <FaUser className="status-icon none" />
                <span className="status-text">Free Account</span>
              </div>
              <div className="subscription-details">
                <p className="upgrade-message">
                  You're currently using a free account. Upgrade to a premium plan to unlock all features.
                </p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Available Plans */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="available-plans"
        >
          <h2 className="section-title">
            {subscriptionStatus === 'active'
              ? '🚀 Upgrade Your Plan'
              : subscriptionStatus === 'expired'
                ? '🔄 Renew Your Subscription'
                : '🎯 Choose Your Plan'
            }
          </h2>
          <p className="section-subtitle">
            {subscriptionStatus === 'active'
              ? 'Upgrade to a longer plan for better value and extended access'
              : subscriptionStatus === 'expired'
                ? 'Your subscription has expired. Renew now to continue accessing premium features'
                : 'Select a subscription plan to unlock all premium features and start your learning journey'
            }
          </p>
          
          {loading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Loading plans...</p>
            </div>
          ) : plans.length === 0 ? (
            <div className="no-plans-state">
              <div className="no-plans-icon">📋</div>
              <h3>No Plans Available</h3>
              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>
              <button className="refresh-btn" onClick={fetchPlans}>
                🔄 Refresh Plans
              </button>
            </div>
          ) : (
            <div className="plans-grid">
              {plans.map((plan) => (
                <motion.div
                  key={plan._id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="plan-card"
                >
                  <div className="plan-header">
                    <h3 className="plan-title">{plan.title}</h3>
                    {plan.title?.toLowerCase().includes('glimp') && (
                      <span className="plan-badge">🔥 Popular</span>
                    )}
                  </div>
                  
                  <div className="plan-pricing">
                    <div className="price-display">
                      <span className="current-price">{plan.discountedPrice?.toLocaleString()} TZS</span>
                      {plan.actualPrice > plan.discountedPrice && (
                        <span className="original-price">{plan.actualPrice?.toLocaleString()} TZS</span>
                      )}
                    </div>
                    <div className="plan-duration">{plan.duration} month{plan.duration > 1 ? 's' : ''}</div>
                  </div>

                  <div className="plan-features">
                    {plan.features?.slice(0, 5).map((feature, index) => (
                      <div key={index} className="feature-item">
                        <FaCheckCircle className="feature-icon" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <button
                    className="select-plan-btn"
                    onClick={() => handlePlanSelect(plan)}
                    disabled={paymentLoading}
                  >
                    <FaCreditCard className="btn-icon" />
                    {paymentLoading
                      ? 'Processing...'
                      : subscriptionStatus === 'active'
                        ? 'Upgrade to This Plan'
                        : subscriptionStatus === 'expired'
                          ? 'Renew with This Plan'
                          : 'Select This Plan'
                    }
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Phone Number Warning */}
        {(!user.phoneNumber || !/^(06|07)\d{8}$/.test(user.phoneNumber)) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="phone-warning"
          >
            <div className="warning-content">
              <FaTimesCircle className="warning-icon" />
              <div>
                <h4>Phone Number Required</h4>
                <p>Please update your phone number in your profile to subscribe to a plan.</p>
                <button 
                  className="update-phone-btn"
                  onClick={() => window.location.href = '/profile'}
                >
                  Update Phone Number
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Payment Processing Modal */}
        {showProcessingModal && (
          <div className="payment-modal-overlay">
            <div className="payment-modal">
              <div className="payment-modal-content">
                <div className="payment-processing-animation">
                  <div className="payment-spinner"></div>
                  <div className="payment-pulse"></div>
                </div>
                <h3>Processing Payment</h3>
                <p className="payment-status">{paymentStatus}</p>
                <div className="payment-details">
                  <div className="payment-plan-info">
                    <h4>{selectedPlan?.title}</h4>
                    <p>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</p>
                    <p>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</p>
                  </div>
                </div>
                <div className="payment-instructions">
                  <p>📱 Check your phone for SMS confirmation</p>
                  <p>💳 Follow the instructions to complete payment</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Payment Success Modal */}
        {showSuccessModal && (
          <div className="payment-modal-overlay">
            <div className="payment-modal success-modal">
              <div className="payment-modal-content">
                <div className="success-animation">
                  <div className="success-checkmark">🎉✨🚀</div>
                  <div className="success-confetti"></div>
                </div>
                <h2 style={{
                  color: '#52c41a',
                  fontSize: '32px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  textAlign: 'center'
                }}>
                  🎊 Payment Successful! 🎊
                </h2>
                <p className="success-message" style={{
                  fontSize: '18px',
                  color: '#1890ff',
                  fontWeight: '500',
                  textAlign: 'center',
                  marginBottom: '24px'
                }}>
                  🎉 Welcome to {selectedPlan?.title}! Your subscription is now ACTIVE and all features are unlocked! 🎉
                </p>

                <div className="success-details" style={{
                  background: 'linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%)',
                  border: '2px solid #52c41a',
                  borderRadius: '12px',
                  padding: '20px',
                  marginBottom: '20px',
                  textAlign: 'center'
                }}>
                  <div className="success-plan-info">
                    <h3 style={{ color: '#389e0d', marginBottom: '12px' }}>
                      🎯 Your Premium Plan Details:
                    </h3>
                    <div style={{ display: 'grid', gap: '8px' }}>
                      <p style={{ margin: '0', fontSize: '16px' }}>
                        <strong>📋 Plan:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.title}</span>
                      </p>
                      <p style={{ margin: '0', fontSize: '16px' }}>
                        <strong>⏰ Duration:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</span>
                      </p>
                      <p style={{ margin: '0', fontSize: '16px' }}>
                        <strong>💰 Amount:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</span>
                      </p>
                      <p style={{ margin: '0', fontSize: '16px' }}>
                        <strong>💎 Status:</strong> <span style={{ color: '#52c41a', fontWeight: 'bold' }}>ACTIVE & PREMIUM</span>
                      </p>
                    </div>
                  </div>
                </div>

                <div className="success-features" style={{
                  background: '#fff7e6',
                  border: '1px solid #ffd666',
                  borderRadius: '12px',
                  padding: '20px',
                  marginBottom: '24px'
                }}>
                  <h3 style={{ color: '#d48806', marginBottom: '16px', textAlign: 'center' }}>
                    🚀 Everything is now unlocked for you:
                  </h3>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '8px',
                    textAlign: 'left'
                  }}>
                    <div>
                      <p style={{ margin: '4px 0', fontSize: '15px' }}>✅ <strong>Unlimited Quizzes</strong></p>
                      <p style={{ margin: '4px 0', fontSize: '15px' }}>🤖 <strong>AI Chat Assistant</strong></p>
                      <p style={{ margin: '4px 0', fontSize: '15px' }}>📚 <strong>Study Materials</strong></p>
                    </div>
                    <div>
                      <p style={{ margin: '4px 0', fontSize: '15px' }}>📊 <strong>Progress Tracking</strong></p>
                      <p style={{ margin: '4px 0', fontSize: '15px' }}>🎥 <strong>Learning Videos</strong></p>
                      <p style={{ margin: '4px 0', fontSize: '15px' }}>💬 <strong>Forum Access</strong></p>
                    </div>
                  </div>
                </div>

                <div className="success-actions" style={{
                  display: 'flex',
                  gap: '16px',
                  justifyContent: 'center',
                  flexWrap: 'wrap'
                }}>
                  <button
                    className="success-btn primary"
                    onClick={() => {
                      setShowSuccessModal(false);
                      window.location.href = '/user/hub';
                    }}
                    style={{
                      background: 'linear-gradient(45deg, #1890ff, #52c41a)',
                      border: 'none',
                      color: 'white',
                      padding: '12px 24px',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      minWidth: '160px'
                    }}
                  >
                    🏠 Go to Dashboard
                  </button>
                  <button
                    className="success-btn secondary"
                    onClick={() => setShowSuccessModal(false)}
                    style={{
                      background: 'white',
                      border: '2px solid #1890ff',
                      color: '#1890ff',
                      padding: '12px 24px',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      cursor: 'pointer',
                      minWidth: '160px'
                    }}
                  >
                    Continue Here
                  </button>
                </div>

                <p style={{
                  marginTop: '20px',
                  fontSize: '14px',
                  color: '#8c8c8c',
                  fontStyle: 'italic',
                  textAlign: 'center'
                }}>
                  🎉 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies!
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Subscription;
