import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';
import { getPlans } from '../../../apicalls/plans';
import { addPayment, checkPaymentStatus } from '../../../apicalls/payment';
import { ShowLoading, HideLoading } from '../../../redux/loaderSlice';
import './Subscription.css';

const Subscription = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState('');
  const { user } = useSelector((state) => state.user);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const dispatch = useDispatch();

  // Fallback sample plans in case API fails
  const samplePlans = [
    {
      _id: "glimp-plan-sample",
      title: "Glimp Plan",
      features: [
        "1-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings"
      ],
      actualPrice: 15000,
      discountedPrice: 13000,
      discountPercentage: 13,
      duration: 1,
      status: true
    },
    {
      _id: "basic-plan-sample",
      title: "Basic Membership",
      features: [
        "2-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings"
      ],
      actualPrice: 28570,
      discountedPrice: 20000,
      discountPercentage: 30,
      duration: 2,
      status: true
    },
    {
      _id: "premium-plan-sample",
      title: "Premium Plan",
      features: [
        "3-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings",
        "Priority support"
      ],
      actualPrice: 45000,
      discountedPrice: 35000,
      discountPercentage: 22,
      duration: 3,
      status: true
    }
  ];

  useEffect(() => {
    fetchPlans();
    checkCurrentSubscription();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      console.log('Fetching plans...');
      const response = await getPlans();
      console.log('Plans response:', response);

      if (response.success && response.data && response.data.length > 0) {
        setPlans(response.data);
        console.log('Plans loaded successfully from API:', response.data);
      } else if (Array.isArray(response) && response.length > 0) {
        // Handle case where response is directly an array of plans
        setPlans(response);
        console.log('Plans loaded as array from API:', response);
      } else {
        console.warn('No plans from API, using sample plans');
        setPlans(samplePlans);
        message.info('Showing sample plans. Please check your connection.');
      }
    } catch (error) {
      console.error('Error loading plans from API:', error);
      console.log('Using fallback sample plans');
      setPlans(samplePlans);
      message.warning('Using sample plans. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const checkCurrentSubscription = async () => {
    try {
      const response = await checkPaymentStatus();
      console.log('Current subscription:', response);
    } catch (error) {
      console.log('No active subscription found');
    }
  };

  const handlePlanSelect = async (plan) => {
    if (!user.phoneNumber || !/^(06|07)\d{8}$/.test(user.phoneNumber)) {
      message.error('Please update your phone number in your profile before subscribing');
      return;
    }

    try {
      setSelectedPlan(plan);
      setPaymentLoading(true);
      setShowProcessingModal(true);
      setPaymentStatus('Initiating payment...');

      const paymentData = {
        plan: plan,
        userId: user._id,
        userPhone: user.phoneNumber,
        userEmail: user.email || `${user.name?.replace(/\s+/g, '').toLowerCase()}@brainwave.temp`
      };

      setPaymentStatus('Sending payment request...');
      const response = await addPayment(paymentData);

      if (response.success) {
        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');

        // Start checking payment status
        setTimeout(() => {
          checkPaymentConfirmation(response.order_id || 'demo_order');
        }, 3000);

      } else {
        throw new Error(response.message || 'Payment failed');
      }
    } catch (error) {
      setShowProcessingModal(false);
      message.error('Payment failed: ' + error.message);
      setPaymentLoading(false);
    }
  };

  const checkPaymentConfirmation = async (orderId) => {
    try {
      setPaymentStatus('Waiting for payment confirmation...');

      // Poll payment status every 3 seconds for up to 2 minutes
      let attempts = 0;
      const maxAttempts = 40; // 40 attempts * 3 seconds = 2 minutes

      const pollPaymentStatus = async () => {
        attempts++;

        try {
          const statusResponse = await checkPaymentStatus({ orderId });

          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {
            // Payment confirmed!
            setPaymentStatus('Payment confirmed! Activating your subscription...');

            setTimeout(() => {
              setShowProcessingModal(false);
              setShowSuccessModal(true);
              setPaymentLoading(false);

              // Refresh subscription data
              checkCurrentSubscription();
            }, 1500);

          } else if (attempts >= maxAttempts) {
            // Timeout - but don't fail completely
            setPaymentStatus('Payment is taking longer than expected. Please check your phone and try again if needed.');

            setTimeout(() => {
              setShowProcessingModal(false);
              setPaymentLoading(false);
              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status in a few minutes.');
            }, 3000);

          } else {
            // Continue polling
            setPaymentStatus(`Waiting for payment confirmation... (${Math.ceil((maxAttempts - attempts) * 3 / 60)} minutes remaining)`);
            setTimeout(pollPaymentStatus, 3000);
          }

        } catch (error) {
          console.error('Payment status check error:', error);
          if (attempts >= maxAttempts) {
            setShowProcessingModal(false);
            setPaymentLoading(false);
            message.error('Unable to confirm payment status. Please check your subscription status manually.');
          } else {
            // Continue polling even if there's an error
            setTimeout(pollPaymentStatus, 3000);
          }
        }
      };

      // Start polling after 3 seconds
      setTimeout(pollPaymentStatus, 3000);

    } catch (error) {
      setShowProcessingModal(false);
      message.error('Payment confirmation failed: ' + error.message);
      setPaymentLoading(false);
    }
  };

  const getSubscriptionStatus = () => {
    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {
      const endDate = new Date(subscriptionData.endDate);
      const now = new Date();
      if (endDate > now) {
        return 'active';
      }
    }
    
    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {
      return 'expired';
    }
    
    return 'none';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = () => {
    if (!subscriptionData?.endDate) return 0;
    const endDate = new Date(subscriptionData.endDate);
    const now = new Date();
    const diffTime = endDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const subscriptionStatus = getSubscriptionStatus();

  return (
    <div className="subscription-page">
      <div className="subscription-container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="subscription-header"
        >
          <h1 className="page-title">
            <FaCrown className="title-icon" />
            Subscription Management
          </h1>
          <p className="page-subtitle">Manage your subscription and access premium features</p>
        </motion.div>

        {/* Current Subscription Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="current-subscription"
        >
          <h2 className="section-title">Current Subscription</h2>
          
          {subscriptionStatus === 'active' && (
            <div className="subscription-card active">
              <div className="subscription-status">
                <FaCheckCircle className="status-icon active" />
                <span className="status-text">Active Subscription</span>
              </div>
              <div className="subscription-details">
                <div className="detail-item">
                  <FaCrown className="detail-icon" />
                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>
                </div>
                <div className="detail-item">
                  <FaCalendarAlt className="detail-icon" />
                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>
                </div>
                <div className="detail-item">
                  <FaCheckCircle className="detail-icon" />
                  <span>Days Remaining: {getDaysRemaining()}</span>
                </div>
              </div>
            </div>
          )}

          {subscriptionStatus === 'expired' && (
            <div className="subscription-card expired">
              <div className="subscription-status">
                <FaTimesCircle className="status-icon expired" />
                <span className="status-text">Subscription Expired</span>
              </div>
              <div className="subscription-details">
                <div className="detail-item">
                  <FaCalendarAlt className="detail-icon" />
                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>
                </div>
                <p className="renewal-message">
                  Your subscription has expired. Choose a new plan below to continue accessing premium features.
                </p>
              </div>
            </div>
          )}

          {subscriptionStatus === 'none' && (
            <div className="subscription-card none">
              <div className="subscription-status">
                <FaUser className="status-icon none" />
                <span className="status-text">Free Account</span>
              </div>
              <div className="subscription-details">
                <p className="upgrade-message">
                  You're currently using a free account. Upgrade to a premium plan to unlock all features.
                </p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Available Plans */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="available-plans"
        >
          <h2 className="section-title">
            {subscriptionStatus === 'active'
              ? '🚀 Upgrade Your Plan'
              : subscriptionStatus === 'expired'
                ? '🔄 Renew Your Subscription'
                : '🎯 Choose Your Plan'
            }
          </h2>
          <p className="section-subtitle">
            {subscriptionStatus === 'active'
              ? 'Upgrade to a longer plan for better value and extended access'
              : subscriptionStatus === 'expired'
                ? 'Your subscription has expired. Renew now to continue accessing premium features'
                : 'Select a subscription plan to unlock all premium features and start your learning journey'
            }
          </p>
          
          {loading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Loading plans...</p>
            </div>
          ) : plans.length === 0 ? (
            <div className="no-plans-state">
              <div className="no-plans-icon">📋</div>
              <h3>No Plans Available</h3>
              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>
              <button className="refresh-btn" onClick={fetchPlans}>
                🔄 Refresh Plans
              </button>
            </div>
          ) : (
            <div className="plans-grid">
              {plans.map((plan) => (
                <motion.div
                  key={plan._id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="plan-card"
                >
                  <div className="plan-header">
                    <h3 className="plan-title">{plan.title}</h3>
                    {plan.title?.toLowerCase().includes('glimp') && (
                      <span className="plan-badge">🔥 Popular</span>
                    )}
                  </div>
                  
                  <div className="plan-pricing">
                    <div className="price-display">
                      <span className="current-price">{plan.discountedPrice?.toLocaleString()} TZS</span>
                      {plan.actualPrice > plan.discountedPrice && (
                        <span className="original-price">{plan.actualPrice?.toLocaleString()} TZS</span>
                      )}
                    </div>
                    <div className="plan-duration">{plan.duration} month{plan.duration > 1 ? 's' : ''}</div>
                  </div>

                  <div className="plan-features">
                    {plan.features?.slice(0, 5).map((feature, index) => (
                      <div key={index} className="feature-item">
                        <FaCheckCircle className="feature-icon" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <button
                    className="select-plan-btn"
                    onClick={() => handlePlanSelect(plan)}
                    disabled={paymentLoading}
                  >
                    <FaCreditCard className="btn-icon" />
                    {paymentLoading
                      ? 'Processing...'
                      : subscriptionStatus === 'active'
                        ? 'Upgrade to This Plan'
                        : subscriptionStatus === 'expired'
                          ? 'Renew with This Plan'
                          : 'Select This Plan'
                    }
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Phone Number Warning */}
        {(!user.phoneNumber || !/^(06|07)\d{8}$/.test(user.phoneNumber)) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="phone-warning"
          >
            <div className="warning-content">
              <FaTimesCircle className="warning-icon" />
              <div>
                <h4>Phone Number Required</h4>
                <p>Please update your phone number in your profile to subscribe to a plan.</p>
                <button 
                  className="update-phone-btn"
                  onClick={() => window.location.href = '/profile'}
                >
                  Update Phone Number
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Payment Processing Modal */}
        {showProcessingModal && (
          <div className="payment-modal-overlay">
            <div className="payment-modal">
              <div className="payment-modal-content">
                <div className="payment-processing-animation">
                  <div className="payment-spinner"></div>
                  <div className="payment-pulse"></div>
                </div>
                <h3>Processing Payment</h3>
                <p className="payment-status">{paymentStatus}</p>
                <div className="payment-details">
                  <div className="payment-plan-info">
                    <h4>{selectedPlan?.title}</h4>
                    <p>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</p>
                    <p>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</p>
                  </div>
                </div>
                <div className="payment-instructions">
                  <p>📱 Check your phone for SMS confirmation</p>
                  <p>💳 Follow the instructions to complete payment</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payment Success Modal */}
        {showSuccessModal && (
          <div className="payment-modal-overlay">
            <div className="payment-modal success-modal">
              <div className="payment-modal-content">
                <div className="success-animation">
                  <div className="success-checkmark">✅</div>
                  <div className="success-confetti"></div>
                </div>
                <h3>🎉 Payment Successful!</h3>
                <p className="success-message">
                  Welcome to {selectedPlan?.title}! Your subscription is now active.
                </p>
                <div className="success-details">
                  <div className="success-plan-info">
                    <h4>{selectedPlan?.title}</h4>
                    <p>Duration: {selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</p>
                    <p>Amount: {selectedPlan?.discountedPrice?.toLocaleString()} TZS</p>
                  </div>
                </div>
                <div className="success-features">
                  <h4>🚀 You now have access to:</h4>
                  <ul>
                    <li>✅ Unlimited quizzes</li>
                    <li>✅ AI chat assistance</li>
                    <li>✅ Study materials</li>
                    <li>✅ Progress tracking</li>
                    <li>✅ All premium features</li>
                  </ul>
                </div>
                <div className="success-actions">
                  <button
                    className="success-btn primary"
                    onClick={() => {
                      setShowSuccessModal(false);
                      window.location.href = '/user/hub';
                    }}
                  >
                    🏠 Go to Dashboard
                  </button>
                  <button
                    className="success-btn secondary"
                    onClick={() => setShowSuccessModal(false)}
                  >
                    Continue Here
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Subscription;
