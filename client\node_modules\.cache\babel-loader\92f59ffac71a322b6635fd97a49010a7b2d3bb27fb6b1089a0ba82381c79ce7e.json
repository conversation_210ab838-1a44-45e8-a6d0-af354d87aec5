{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionModal\\\\SubscriptionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionModal = ({\n  isOpen,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  var _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = phone => {\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile\n  const updateUserPhoneNumber = async newPhone => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n      console.log('👤 Current user data:', user);\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || '',\n        level: user.level || '',\n        phoneNumber: newPhone\n      };\n      console.log('📤 Sending update payload:', updatePayload);\n      const response = await updateUserInfo(updatePayload);\n      console.log('📥 Update response:', response);\n      if (response.success) {\n        // Update Redux store with new user data\n        dispatch(SetUser(response.data));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(response.data));\n        console.log('✅ User phone number updated successfully');\n        console.log('📱 New user data:', response.data);\n        return true;\n      } else {\n        console.error('❌ Failed to update user phone number:', response.message);\n        console.error('❌ Full response:', response);\n        return false;\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('❌ Error updating user phone number:', error);\n      console.error('❌ Error details:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n      return false;\n    }\n  };\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    }\n  }, [isOpen, user === null || user === void 0 ? void 0 : user.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.phoneNumber && !isEditingPhone) {\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user === null || user === void 0 ? void 0 : user.phoneNumber, isEditingPhone]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = plan => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone,\n        // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n          return true;\n        }\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: [step === 'plans' && '🚀 Choose Your Learning Plan', step === 'payment' && '💳 Complete Your Payment', step === 'success' && '⏳ Processing Payment...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [step === 'plans' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading plans...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 17\n          }, this) : plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$features, _plan$features2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-card\",\n              onClick: () => handlePlanSelect(plan),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-amount\",\n                  children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 23\n                }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-original\",\n                  children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-period\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: [(_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 25\n                }, this)), ((_plan$features2 = plan.features) === null || _plan$features2 === void 0 ? void 0 : _plan$features2.length) > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: [plan.features.length - 4, \" more features\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                children: [\"Choose \", plan.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), step === 'payment' && selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-plan-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Selected Plan: \", selectedPlan.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"plan-price-summary\",\n              children: [(_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS for \", selectedPlan.duration, \" month\", selectedPlan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-label\",\n                  children: \"Phone Number for Payment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), !isEditingPhone ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `info-value ${phoneUpdated ? 'updated' : ''}`,\n                    children: [paymentPhone || 'Not provided', phoneUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"updated-indicator\",\n                      children: \"\\u2705 Updated\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 44\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"edit-phone-btn\",\n                    onClick: () => setIsEditingPhone(true),\n                    type: \"button\",\n                    children: \"\\u270F\\uFE0F Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-edit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    value: paymentPhone,\n                    onChange: e => setPaymentPhone(e.target.value),\n                    placeholder: \"Enter phone number (e.g., 0744963858)\",\n                    className: `phone-input ${paymentPhone ? isValidPhone(paymentPhone) ? 'valid' : 'invalid' : ''}`,\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this), paymentPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`,\n                    children: isValidPhone(paymentPhone) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message valid\",\n                      children: \"\\u2705 Valid phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message invalid\",\n                      children: \"\\u274C Must start with 06 or 07 and be 10 digits\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"phone-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"save-phone-btn\",\n                      onClick: async () => {\n                        if (isValidPhone(paymentPhone)) {\n                          try {\n                            // Show loading state\n                            const btn = document.querySelector('.save-phone-btn');\n                            const originalText = btn.textContent;\n                            btn.textContent = '⏳ Saving...';\n                            btn.disabled = true;\n                            console.log('💾 Starting phone number save process...');\n                            console.log('📱 Payment phone:', paymentPhone);\n                            console.log('👤 User phone:', user === null || user === void 0 ? void 0 : user.phoneNumber);\n\n                            // Check if phone number is different from current user's phone\n                            const isPhoneChanged = paymentPhone !== (user === null || user === void 0 ? void 0 : user.phoneNumber);\n                            console.log('🔄 Phone changed:', isPhoneChanged);\n                            if (isPhoneChanged) {\n                              console.log('📞 Updating user profile with new phone number...');\n                              // Update user's profile with new phone number\n                              const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n                              if (updateSuccess) {\n                                console.log('✅ Phone number update successful');\n                                setIsEditingPhone(false);\n                                setPhoneUpdated(true);\n\n                                // Show success messages\n                                message.success({\n                                  content: '📱 Phone number updated successfully!',\n                                  duration: 4,\n                                  style: {\n                                    marginTop: '20vh',\n                                    fontSize: '15px',\n                                    fontWeight: '600'\n                                  }\n                                });\n\n                                // Additional success message for payment\n                                setTimeout(() => {\n                                  message.info({\n                                    content: '💡 Your profile has been updated. This number will receive payment SMS.',\n                                    duration: 5,\n                                    style: {\n                                      marginTop: '20vh',\n                                      fontSize: '14px'\n                                    }\n                                  });\n                                }, 1000);\n\n                                // Reset the updated indicator after 5 seconds\n                                setTimeout(() => {\n                                  setPhoneUpdated(false);\n                                }, 5000);\n                              } else {\n                                console.log('❌ Phone number update failed');\n                                message.error('Failed to update phone number in profile. Please try again.');\n\n                                // Restore button state on failure\n                                btn.textContent = originalText;\n                                btn.disabled = !isValidPhone(paymentPhone);\n                              }\n                            } else {\n                              console.log('📱 Phone number is the same, just confirming...');\n                              // Phone number is the same, just close editing\n                              setIsEditingPhone(false);\n                              message.success({\n                                content: '📱 Phone number confirmed for payment',\n                                duration: 3,\n                                style: {\n                                  marginTop: '20vh',\n                                  fontSize: '15px',\n                                  fontWeight: '600'\n                                }\n                              });\n\n                              // Restore button state\n                              btn.textContent = originalText;\n                              btn.disabled = !isValidPhone(paymentPhone);\n                            }\n                          } catch (error) {\n                            console.error('❌ Error saving phone number:', error);\n                            message.error('Failed to save phone number. Please try again.');\n\n                            // Restore button state on error\n                            const btn = document.querySelector('.save-phone-btn');\n                            if (btn) {\n                              btn.textContent = '✅ Save';\n                              btn.disabled = !isValidPhone(paymentPhone);\n                            }\n                          }\n                        } else {\n                          message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                        }\n                      },\n                      disabled: !isValidPhone(paymentPhone),\n                      type: \"button\",\n                      children: \"\\u2705 Save\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"cancel-phone-btn\",\n                      onClick: () => {\n                        setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n                        setIsEditingPhone(false);\n                      },\n                      type: \"button\",\n                      children: \"\\u274C Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-note\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"\\uD83D\\uDCA1 This number will receive the payment SMS. You can use a different number than your profile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Payment Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: \"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"back-btn\",\n              onClick: () => setStep('plans'),\n              children: \"\\u2190 Back to Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pay-btn\",\n              onClick: handlePayment,\n              disabled: paymentLoading || !paymentPhone || isEditingPhone,\n              children: paymentLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 23\n                }, this), \"Processing...\"]\n              }, void 0, true) : isEditingPhone ? 'Save phone number first' : !paymentPhone ? 'Enter phone number' : `Pay ${(_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString()} TZS`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), step === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-animation\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Request Sent!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation and complete the payment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Check your phone for SMS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Follow the payment instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Your subscription will activate automatically\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"check-status-btn\",\n              onClick: async () => {\n                console.log('🔍 Manual payment check triggered');\n                try {\n                  const response = await checkPaymentStatus();\n                  console.log('📥 Manual check response:', response);\n                  if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                    console.log('✅ Payment confirmed manually!');\n                    dispatch(SetSubscription(response));\n                    message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                    onSuccess && onSuccess();\n                    setTimeout(() => onClose(), 1000);\n                  } else {\n                    message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                  }\n                } catch (error) {\n                  console.error('❌ Manual check error:', error);\n                  message.error('Error checking payment status');\n                }\n              },\n              children: \"\\u2705 Check Payment Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"done-btn\",\n              onClick: handleClose,\n              children: \"I'll complete the payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s(SubscriptionModal, \"aZxc1xREfgbzjCdSZ68Mez4hUuk=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = SubscriptionModal;\nexport default SubscriptionModal;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "message", "getPlans", "addPayment", "checkPaymentStatus", "updateUserInfo", "SetSubscription", "SetUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionModal", "isOpen", "onClose", "onSuccess", "_s", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "paymentPhone", "setPaymentPhone", "isEditingPhone", "setIsEditingPhone", "phoneUpdated", "setPhoneUpdated", "isValidPhone", "phone", "test", "updateUserPhoneNumber", "newPhone", "console", "log", "user", "updatePayload", "userId", "_id", "name", "email", "school", "class_", "class", "level", "phoneNumber", "response", "success", "dispatch", "data", "localStorage", "setItem", "JSON", "stringify", "error", "_error$response", "state", "fetchPlans", "Array", "isArray", "handlePlanSelect", "plan", "handlePayment", "length", "_user$name", "paymentData", "userPhone", "userEmail", "replace", "toLowerCase", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "checkStatus", "paymentStatus", "status", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "warning", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "title", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "type", "value", "onChange", "e", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "btn", "document", "querySelector", "originalText", "textContent", "disabled", "isPhoneChanged", "updateSuccess", "info", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionModal/SubscriptionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = (phone) => {\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile\n  const updateUserPhoneNumber = async (newPhone) => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n      console.log('👤 Current user data:', user);\n\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || '',\n        level: user.level || '',\n        phoneNumber: newPhone\n      };\n\n      console.log('📤 Sending update payload:', updatePayload);\n\n      const response = await updateUserInfo(updatePayload);\n\n      console.log('📥 Update response:', response);\n\n      if (response.success) {\n        // Update Redux store with new user data\n        dispatch(SetUser(response.data));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(response.data));\n\n        console.log('✅ User phone number updated successfully');\n        console.log('📱 New user data:', response.data);\n        return true;\n      } else {\n        console.error('❌ Failed to update user phone number:', response.message);\n        console.error('❌ Full response:', response);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error updating user phone number:', error);\n      console.error('❌ Error details:', error.response?.data);\n      return false;\n    }\n  };\n  \n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone(user?.phoneNumber || '');\n    }\n  }, [isOpen, user?.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user?.phoneNumber && !isEditingPhone) {\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user?.phoneNumber, isEditingPhone]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone, // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n        \n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone(user?.phoneNumber || '');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n            {step === 'success' && '⏳ Processing Payment...'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('glimp') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"phone-section\">\n                  <div className=\"info-item\">\n                    <span className=\"info-label\">Phone Number for Payment:</span>\n                    {!isEditingPhone ? (\n                      <div className=\"phone-display\">\n                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>\n                          {paymentPhone || 'Not provided'}\n                          {phoneUpdated && <span className=\"updated-indicator\">✅ Updated</span>}\n                        </span>\n                        <button\n                          className=\"edit-phone-btn\"\n                          onClick={() => setIsEditingPhone(true)}\n                          type=\"button\"\n                        >\n                          ✏️ Change\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"phone-edit\">\n                        <input\n                          type=\"tel\"\n                          value={paymentPhone}\n                          onChange={(e) => setPaymentPhone(e.target.value)}\n                          placeholder=\"Enter phone number (e.g., 0744963858)\"\n                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}\n                          maxLength=\"10\"\n                        />\n                        {paymentPhone && (\n                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>\n                            {isValidPhone(paymentPhone) ? (\n                              <span className=\"validation-message valid\">✅ Valid phone number</span>\n                            ) : (\n                              <span className=\"validation-message invalid\">❌ Must start with 06 or 07 and be 10 digits</span>\n                            )}\n                          </div>\n                        )}\n                        <div className=\"phone-actions\">\n                          <button\n                            className=\"save-phone-btn\"\n                            onClick={async () => {\n                              if (isValidPhone(paymentPhone)) {\n                                try {\n                                  // Show loading state\n                                  const btn = document.querySelector('.save-phone-btn');\n                                  const originalText = btn.textContent;\n                                  btn.textContent = '⏳ Saving...';\n                                  btn.disabled = true;\n\n                                  console.log('💾 Starting phone number save process...');\n                                  console.log('📱 Payment phone:', paymentPhone);\n                                  console.log('👤 User phone:', user?.phoneNumber);\n\n                                  // Check if phone number is different from current user's phone\n                                  const isPhoneChanged = paymentPhone !== user?.phoneNumber;\n                                  console.log('🔄 Phone changed:', isPhoneChanged);\n\n                                  if (isPhoneChanged) {\n                                    console.log('📞 Updating user profile with new phone number...');\n                                    // Update user's profile with new phone number\n                                    const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n\n                                    if (updateSuccess) {\n                                      console.log('✅ Phone number update successful');\n                                      setIsEditingPhone(false);\n                                      setPhoneUpdated(true);\n\n                                      // Show success messages\n                                      message.success({\n                                        content: '📱 Phone number updated successfully!',\n                                        duration: 4,\n                                        style: {\n                                          marginTop: '20vh',\n                                          fontSize: '15px',\n                                          fontWeight: '600'\n                                        }\n                                      });\n\n                                      // Additional success message for payment\n                                      setTimeout(() => {\n                                        message.info({\n                                          content: '💡 Your profile has been updated. This number will receive payment SMS.',\n                                          duration: 5,\n                                          style: {\n                                            marginTop: '20vh',\n                                            fontSize: '14px'\n                                          }\n                                        });\n                                      }, 1000);\n\n                                      // Reset the updated indicator after 5 seconds\n                                      setTimeout(() => {\n                                        setPhoneUpdated(false);\n                                      }, 5000);\n                                    } else {\n                                      console.log('❌ Phone number update failed');\n                                      message.error('Failed to update phone number in profile. Please try again.');\n\n                                      // Restore button state on failure\n                                      btn.textContent = originalText;\n                                      btn.disabled = !isValidPhone(paymentPhone);\n                                    }\n                                  } else {\n                                    console.log('📱 Phone number is the same, just confirming...');\n                                    // Phone number is the same, just close editing\n                                    setIsEditingPhone(false);\n                                    message.success({\n                                      content: '📱 Phone number confirmed for payment',\n                                      duration: 3,\n                                      style: {\n                                        marginTop: '20vh',\n                                        fontSize: '15px',\n                                        fontWeight: '600'\n                                      }\n                                    });\n\n                                    // Restore button state\n                                    btn.textContent = originalText;\n                                    btn.disabled = !isValidPhone(paymentPhone);\n                                  }\n                                } catch (error) {\n                                  console.error('❌ Error saving phone number:', error);\n                                  message.error('Failed to save phone number. Please try again.');\n\n                                  // Restore button state on error\n                                  const btn = document.querySelector('.save-phone-btn');\n                                  if (btn) {\n                                    btn.textContent = '✅ Save';\n                                    btn.disabled = !isValidPhone(paymentPhone);\n                                  }\n                                }\n                              } else {\n                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                              }\n                            }}\n                            disabled={!isValidPhone(paymentPhone)}\n                            type=\"button\"\n                          >\n                            ✅ Save\n                          </button>\n                          <button\n                            className=\"cancel-phone-btn\"\n                            onClick={() => {\n                              setPaymentPhone(user?.phoneNumber || '');\n                              setIsEditingPhone(false);\n                            }}\n                            type=\"button\"\n                          >\n                            ❌ Cancel\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"phone-note\">\n                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={handlePayment}\n                  disabled={paymentLoading || !paymentPhone || isEditingPhone}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : isEditingPhone ? (\n                    'Save phone number first'\n                  ) : !paymentPhone ? (\n                    'Enter phone number'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {step === 'success' && (\n            <div className=\"success-step\">\n              <div className=\"success-animation\">\n                <div className=\"pulse-circle\">\n                  <div className=\"phone-icon\">📱</div>\n                </div>\n              </div>\n              \n              <h3>Payment Request Sent!</h3>\n              <p>Please check your phone for SMS confirmation and complete the payment.</p>\n              \n              <div className=\"payment-steps\">\n                <div className=\"step\">\n                  <span className=\"step-number\">1</span>\n                  <span className=\"step-text\">Check your phone for SMS</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">2</span>\n                  <span className=\"step-text\">Follow the payment instructions</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">3</span>\n                  <span className=\"step-text\">Your subscription will activate automatically</span>\n                </div>\n              </div>\n\n              <div className=\"success-actions\">\n                <button\n                  className=\"check-status-btn\"\n                  onClick={async () => {\n                    console.log('🔍 Manual payment check triggered');\n                    try {\n                      const response = await checkPaymentStatus();\n                      console.log('📥 Manual check response:', response);\n\n                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                        console.log('✅ Payment confirmed manually!');\n                        dispatch(SetSubscription(response));\n                        message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                        onSuccess && onSuccess();\n                        setTimeout(() => onClose(), 1000);\n                      } else {\n                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                      }\n                    } catch (error) {\n                      console.error('❌ Manual check error:', error);\n                      message.error('Error checking payment status');\n                    }\n                  }}\n                >\n                  ✅ Check Payment Status\n                </button>\n\n                <button className=\"done-btn\" onClick={handleClose}>\n                  I'll complete the payment\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,wBAAwB;AACvE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAClE,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgC,IAAI,EAAEC,OAAO,CAAC,GAAGjC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMwC,YAAY,GAAIC,KAAK,IAAK;IAC9B,OAAOA,KAAK,IAAI,gBAAgB,CAACC,IAAI,CAACD,KAAK,CAAC;EAC9C,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEF,QAAQ,CAAC;MAClEC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,IAAI,CAAC;MAE1C,MAAMC,aAAa,GAAG;QACpBC,MAAM,EAAEF,IAAI,CAACG,GAAG;QAChBC,IAAI,EAAEJ,IAAI,CAACI,IAAI;QACfC,KAAK,EAAEL,IAAI,CAACK,KAAK,IAAI,EAAE;QACvBC,MAAM,EAAEN,IAAI,CAACM,MAAM,IAAI,EAAE;QACzBC,MAAM,EAAEP,IAAI,CAACQ,KAAK,IAAI,EAAE;QACxBC,KAAK,EAAET,IAAI,CAACS,KAAK,IAAI,EAAE;QACvBC,WAAW,EAAEb;MACf,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEE,aAAa,CAAC;MAExD,MAAMU,QAAQ,GAAG,MAAMlD,cAAc,CAACwC,aAAa,CAAC;MAEpDH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEY,QAAQ,CAAC;MAE5C,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACAC,QAAQ,CAAClD,OAAO,CAACgD,QAAQ,CAACG,IAAI,CAAC,CAAC;;QAEhC;QACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACG,IAAI,CAAC,CAAC;QAE3DhB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,QAAQ,CAACG,IAAI,CAAC;QAC/C,OAAO,IAAI;MACb,CAAC,MAAM;QACLhB,OAAO,CAACqB,KAAK,CAAC,uCAAuC,EAAER,QAAQ,CAACtD,OAAO,CAAC;QACxEyC,OAAO,CAACqB,KAAK,CAAC,kBAAkB,EAAER,QAAQ,CAAC;QAC3C,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdtB,OAAO,CAACqB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DrB,OAAO,CAACqB,KAAK,CAAC,kBAAkB,GAAAC,eAAA,GAAED,KAAK,CAACR,QAAQ,cAAAS,eAAA,uBAAdA,eAAA,CAAgBN,IAAI,CAAC;MACvD,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM;IAAEd;EAAK,CAAC,GAAG7C,WAAW,CAAEkE,KAAK,IAAKA,KAAK,CAACrB,IAAI,CAAC;EACnD,MAAMa,QAAQ,GAAGzD,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIiB,MAAM,EAAE;MACVmD,UAAU,CAAC,CAAC;MACZ;MACAlC,eAAe,CAAC,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,WAAW,KAAI,EAAE,CAAC;IAC1C;EACF,CAAC,EAAE,CAACvC,MAAM,EAAE6B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,WAAW,CAAC,CAAC;;EAE/B;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI8C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEU,WAAW,IAAI,CAACrB,cAAc,EAAE;MACxCD,eAAe,CAACY,IAAI,CAACU,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,WAAW,EAAErB,cAAc,CAAC,CAAC;EAEvC,MAAMiC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,QAAQ,GAAG,MAAMrD,QAAQ,CAAC,CAAC;MACjCoB,QAAQ,CAAC6C,KAAK,CAACC,OAAO,CAACb,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C9D,OAAO,CAAC8D,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,gBAAgB,GAAIC,IAAI,IAAK;IACjC9C,eAAe,CAAC8C,IAAI,CAAC;IACrBxC,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAMyC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAChD,YAAY,EAAE;MACjBtB,OAAO,CAAC8D,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF;IAEA,IAAI,CAAChC,YAAY,IAAIA,YAAY,CAACyC,MAAM,GAAG,EAAE,EAAE;MAC7CvE,OAAO,CAAC8D,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,IAAI,CAAC,gBAAgB,CAACxB,IAAI,CAACR,YAAY,CAAC,EAAE;MACxC9B,OAAO,CAAC8D,KAAK,CAAC,wEAAwE,CAAC;MACvF;IACF;IAEA,IAAI;MAAA,IAAAU,UAAA;MACF7C,iBAAiB,CAAC,IAAI,CAAC;MACvB6B,QAAQ,CAAChD,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMiE,WAAW,GAAG;QAClBJ,IAAI,EAAE/C,YAAY;QAClBuB,MAAM,EAAEF,IAAI,CAACG,GAAG;QAChB4B,SAAS,EAAE5C,YAAY;QAAE;QACzB6C,SAAS,EAAEhC,IAAI,CAACK,KAAK,IAAK,IAAAwB,UAAA,GAAE7B,IAAI,CAACI,IAAI,cAAAyB,UAAA,uBAATA,UAAA,CAAWI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAMvB,QAAQ,GAAG,MAAMpD,UAAU,CAACuE,WAAW,CAAC;MAE9C,IAAInB,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAAC,kEAAkE,CAAC;QACnF1B,OAAO,CAAC,SAAS,CAAC;;QAElB;QACAiD,wBAAwB,CAACxB,QAAQ,CAACyB,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC1B,QAAQ,CAACtD,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC9D,OAAO,CAAC8D,KAAK,CAACA,KAAK,CAAC9D,OAAO,IAAI,mCAAmC,CAAC;IACrE,CAAC,SAAS;MACR2B,iBAAiB,CAAC,KAAK,CAAC;MACxB6B,QAAQ,CAACjD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMuE,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFF,QAAQ,EAAE;QACVzC,OAAO,CAACC,GAAG,CAAE,yCAAwCwC,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAM7B,QAAQ,GAAG,MAAMnD,kBAAkB,CAAC,CAAC;QAC3CsC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEY,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACQ,KAAK,IAAIR,QAAQ,CAAC+B,aAAa,KAAK,MAAM,IAAI/B,QAAQ,CAACgC,MAAM,KAAK,QAAQ,EAAE;UACpG7C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACAc,QAAQ,CAACnD,eAAe,CAACiD,QAAQ,CAAC,CAAC;;UAEnC;UACAtD,OAAO,CAACuD,OAAO,CAAC;YACdgC,OAAO,EAAE,2CAA2C;YACpDC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;;UAEF;UACA5E,SAAS,IAAIA,SAAS,CAAC,CAAC;;UAExB;UACA6E,UAAU,CAAC,MAAM;YACf9E,OAAO,CAAC,CAAC;UACX,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,IAAI;QACb;QAEA,IAAImE,QAAQ,IAAIC,WAAW,EAAE;UAC3B1C,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C1C,OAAO,CAAC8F,OAAO,CAAC;YACdP,OAAO,EAAE,sGAAsG;YAC/GC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,OAAO,KAAK;QACd;;QAEA;QACAK,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAIoB,QAAQ,IAAIC,WAAW,EAAE;UAC3BnF,OAAO,CAAC8D,KAAK,CAAC,4EAA4E,CAAC;QAC7F,CAAC,MAAM;UACL+B,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACAA,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBlE,OAAO,CAAC,OAAO,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,KAAK,CAAC;IACxBM,iBAAiB,CAAC,KAAK,CAAC;IACxBE,eAAe,CAAC,KAAK,CAAC;IACtBJ,eAAe,CAAC,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,WAAW,KAAI,EAAE,CAAC;IACxCtC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKsF,SAAS,EAAC,4BAA4B;IAAAC,QAAA,eACzCvF,OAAA;MAAKsF,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCvF,OAAA;QAAKsF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvF,OAAA;UAAIsF,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBrE,IAAI,KAAK,OAAO,IAAI,8BAA8B,EAClDA,IAAI,KAAK,SAAS,IAAI,0BAA0B,EAChDA,IAAI,KAAK,SAAS,IAAI,yBAAyB;QAAA;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACL3F,OAAA;UAAQsF,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEP,WAAY;UAAAE,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEN3F,OAAA;QAAKsF,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC3BrE,IAAI,KAAK,OAAO,iBACflB,OAAA;UAAKsF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBzE,OAAO,gBACNd,OAAA;YAAKsF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvF,OAAA;cAAKsF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/B3F,OAAA;cAAAuF,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAENjF,KAAK,CAACmF,GAAG,CAAElC,IAAI;YAAA,IAAAmC,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA;YAAA,oBACbjG,OAAA;cAAoBsF,SAAS,EAAC,WAAW;cAACM,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAACC,IAAI,CAAE;cAAA4B,QAAA,gBAC9EvF,OAAA;gBAAKsF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BvF,OAAA;kBAAIsF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE5B,IAAI,CAACuC;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAG,WAAA,GAAAnC,IAAI,CAACuC,KAAK,cAAAJ,WAAA,uBAAVA,WAAA,CAAY3B,WAAW,CAAC,CAAC,CAACgC,QAAQ,CAAC,OAAO,CAAC,kBAC1CnG,OAAA;kBAAMsF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3F,OAAA;gBAAKsF,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBvF,OAAA;kBAAMsF,SAAS,EAAC,cAAc;kBAAAC,QAAA,IAAAQ,qBAAA,GAAEpC,IAAI,CAACyC,eAAe,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACjFhC,IAAI,CAAC2C,WAAW,IAAI3C,IAAI,CAAC2C,WAAW,KAAK3C,IAAI,CAACyC,eAAe,iBAC5DpG,OAAA;kBAAMsF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAE5B,IAAI,CAAC2C,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/E,eACD3F,OAAA;kBAAMsF,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAE5B,IAAI,CAACmB,QAAQ,EAAC,QAAM,EAACnB,IAAI,CAACmB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eAEN3F,OAAA;gBAAKsF,SAAS,EAAC,eAAe;gBAAAC,QAAA,IAAAS,cAAA,GAC3BrC,IAAI,CAAC4C,QAAQ,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,KAAK,kBAC7C1G,OAAA;kBAAiBsF,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAClCvF,OAAA;oBAAMsF,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC3F,OAAA;oBAAMsF,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEkB;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFvCe,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC,EACD,EAAAM,eAAA,GAAAtC,IAAI,CAAC4C,QAAQ,cAAAN,eAAA,uBAAbA,eAAA,CAAepC,MAAM,IAAG,CAAC,iBACxB7D,OAAA;kBAAKsF,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBvF,OAAA;oBAAMsF,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC3F,OAAA;oBAAMsF,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAE5B,IAAI,CAAC4C,QAAQ,CAAC1C,MAAM,GAAG,CAAC,EAAC,gBAAc;kBAAA;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN3F,OAAA;gBAAQsF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,SAC3B,EAAC5B,IAAI,CAACuC,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjCDhC,IAAI,CAACvB,GAAG;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCb,CAAC;UAAA,CACP;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEAzE,IAAI,KAAK,SAAS,IAAIN,YAAY,iBACjCZ,OAAA;UAAKsF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvF,OAAA;YAAKsF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCvF,OAAA;cAAAuF,QAAA,GAAI,iBAAe,EAAC3E,YAAY,CAACsF,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C3F,OAAA;cAAGsF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,IAAA/E,qBAAA,GAC9BI,YAAY,CAACwF,eAAe,cAAA5F,qBAAA,uBAA5BA,qBAAA,CAA8B6F,cAAc,CAAC,CAAC,EAAC,WAAS,EAACzF,YAAY,CAACkE,QAAQ,EAAC,QAAM,EAAClE,YAAY,CAACkE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN3F,OAAA;YAAKsF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvF,OAAA;cAAKsF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BvF,OAAA;gBAAKsF,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBvF,OAAA;kBAAMsF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC5D,CAACrE,cAAc,gBACdtB,OAAA;kBAAKsF,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvF,OAAA;oBAAMsF,SAAS,EAAG,cAAa9D,YAAY,GAAG,SAAS,GAAG,EAAG,EAAE;oBAAA+D,QAAA,GAC5DnE,YAAY,IAAI,cAAc,EAC9BI,YAAY,iBAAIxB,OAAA;sBAAMsF,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACP3F,OAAA;oBACEsF,SAAS,EAAC,gBAAgB;oBAC1BM,OAAO,EAAEA,CAAA,KAAMrE,iBAAiB,CAAC,IAAI,CAAE;oBACvCoF,IAAI,EAAC,QAAQ;oBAAApB,QAAA,EACd;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAEN3F,OAAA;kBAAKsF,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBvF,OAAA;oBACE2G,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAExF,YAAa;oBACpByF,QAAQ,EAAGC,CAAC,IAAKzF,eAAe,CAACyF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,WAAW,EAAC,uCAAuC;oBACnD1B,SAAS,EAAG,eAAclE,YAAY,GAAIM,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAS,GAAI,EAAG,EAAE;oBACnG6F,SAAS,EAAC;kBAAI;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACDvE,YAAY,iBACXpB,OAAA;oBAAKsF,SAAS,EAAG,oBAAmB5D,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAU,EAAE;oBAAAmE,QAAA,EACpF7D,YAAY,CAACN,YAAY,CAAC,gBACzBpB,OAAA;sBAAMsF,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEtE3F,OAAA;sBAAMsF,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC/F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eACD3F,OAAA;oBAAKsF,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BvF,OAAA;sBACEsF,SAAS,EAAC,gBAAgB;sBAC1BM,OAAO,EAAE,MAAAA,CAAA,KAAY;wBACnB,IAAIlE,YAAY,CAACN,YAAY,CAAC,EAAE;0BAC9B,IAAI;4BACF;4BACA,MAAM8F,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAC;4BACrD,MAAMC,YAAY,GAAGH,GAAG,CAACI,WAAW;4BACpCJ,GAAG,CAACI,WAAW,GAAG,aAAa;4BAC/BJ,GAAG,CAACK,QAAQ,GAAG,IAAI;4BAEnBxF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;4BACvDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEZ,YAAY,CAAC;4BAC9CW,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,WAAW,CAAC;;4BAEhD;4BACA,MAAM6E,cAAc,GAAGpG,YAAY,MAAKa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,WAAW;4BACzDZ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEwF,cAAc,CAAC;4BAEhD,IAAIA,cAAc,EAAE;8BAClBzF,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;8BAChE;8BACA,MAAMyF,aAAa,GAAG,MAAM5F,qBAAqB,CAACT,YAAY,CAAC;8BAE/D,IAAIqG,aAAa,EAAE;gCACjB1F,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;gCAC/CT,iBAAiB,CAAC,KAAK,CAAC;gCACxBE,eAAe,CAAC,IAAI,CAAC;;gCAErB;gCACAnC,OAAO,CAACuD,OAAO,CAAC;kCACdgC,OAAO,EAAE,uCAAuC;kCAChDC,QAAQ,EAAE,CAAC;kCACXC,KAAK,EAAE;oCACLC,SAAS,EAAE,MAAM;oCACjBC,QAAQ,EAAE,MAAM;oCAChBC,UAAU,EAAE;kCACd;gCACF,CAAC,CAAC;;gCAEF;gCACAC,UAAU,CAAC,MAAM;kCACf7F,OAAO,CAACoI,IAAI,CAAC;oCACX7C,OAAO,EAAE,yEAAyE;oCAClFC,QAAQ,EAAE,CAAC;oCACXC,KAAK,EAAE;sCACLC,SAAS,EAAE,MAAM;sCACjBC,QAAQ,EAAE;oCACZ;kCACF,CAAC,CAAC;gCACJ,CAAC,EAAE,IAAI,CAAC;;gCAER;gCACAE,UAAU,CAAC,MAAM;kCACf1D,eAAe,CAAC,KAAK,CAAC;gCACxB,CAAC,EAAE,IAAI,CAAC;8BACV,CAAC,MAAM;gCACLM,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;gCAC3C1C,OAAO,CAAC8D,KAAK,CAAC,6DAA6D,CAAC;;gCAE5E;gCACA8D,GAAG,CAACI,WAAW,GAAGD,YAAY;gCAC9BH,GAAG,CAACK,QAAQ,GAAG,CAAC7F,YAAY,CAACN,YAAY,CAAC;8BAC5C;4BACF,CAAC,MAAM;8BACLW,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;8BAC9D;8BACAT,iBAAiB,CAAC,KAAK,CAAC;8BACxBjC,OAAO,CAACuD,OAAO,CAAC;gCACdgC,OAAO,EAAE,uCAAuC;gCAChDC,QAAQ,EAAE,CAAC;gCACXC,KAAK,EAAE;kCACLC,SAAS,EAAE,MAAM;kCACjBC,QAAQ,EAAE,MAAM;kCAChBC,UAAU,EAAE;gCACd;8BACF,CAAC,CAAC;;8BAEF;8BACAgC,GAAG,CAACI,WAAW,GAAGD,YAAY;8BAC9BH,GAAG,CAACK,QAAQ,GAAG,CAAC7F,YAAY,CAACN,YAAY,CAAC;4BAC5C;0BACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;4BACdrB,OAAO,CAACqB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;4BACpD9D,OAAO,CAAC8D,KAAK,CAAC,gDAAgD,CAAC;;4BAE/D;4BACA,MAAM8D,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAC;4BACrD,IAAIF,GAAG,EAAE;8BACPA,GAAG,CAACI,WAAW,GAAG,QAAQ;8BAC1BJ,GAAG,CAACK,QAAQ,GAAG,CAAC7F,YAAY,CAACN,YAAY,CAAC;4BAC5C;0BACF;wBACF,CAAC,MAAM;0BACL9B,OAAO,CAAC8D,KAAK,CAAC,wEAAwE,CAAC;wBACzF;sBACF,CAAE;sBACFmE,QAAQ,EAAE,CAAC7F,YAAY,CAACN,YAAY,CAAE;sBACtCuF,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT3F,OAAA;sBACEsF,SAAS,EAAC,kBAAkB;sBAC5BM,OAAO,EAAEA,CAAA,KAAM;wBACbvE,eAAe,CAAC,CAAAY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,WAAW,KAAI,EAAE,CAAC;wBACxCpB,iBAAiB,CAAC,KAAK,CAAC;sBAC1B,CAAE;sBACFoF,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN3F,OAAA;gBAAKsF,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBvF,OAAA;kBAAAuF,QAAA,EAAO;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3F,OAAA;cAAKsF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBvF,OAAA;gBAAMsF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnD3F,OAAA;gBAAMsF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3F,OAAA;YAAKsF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvF,OAAA;cAAQsF,SAAS,EAAC,UAAU;cAACM,OAAO,EAAEA,CAAA,KAAMzE,OAAO,CAAC,OAAO,CAAE;cAAAoE,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3F,OAAA;cACEsF,SAAS,EAAC,SAAS;cACnBM,OAAO,EAAEhC,aAAc;cACvB2D,QAAQ,EAAEvG,cAAc,IAAI,CAACI,YAAY,IAAIE,cAAe;cAAAiE,QAAA,EAE3DvE,cAAc,gBACbhB,OAAA,CAAAE,SAAA;gBAAAqF,QAAA,gBACEvF,OAAA;kBAAMsF,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEvC;cAAA,eAAE,CAAC,GACDrE,cAAc,GAChB,yBAAyB,GACvB,CAACF,YAAY,GACf,oBAAoB,GAEnB,OAAI,CAAAX,sBAAA,GAAEG,YAAY,CAACwF,eAAe,cAAA3F,sBAAA,uBAA5BA,sBAAA,CAA8B4F,cAAc,CAAC,CAAE;YACvD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAzE,IAAI,KAAK,SAAS,iBACjBlB,OAAA;UAAKsF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BvF,OAAA;YAAKsF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCvF,OAAA;cAAKsF,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BvF,OAAA;gBAAKsF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3F,OAAA;YAAAuF,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B3F,OAAA;YAAAuF,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE7E3F,OAAA;YAAKsF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvF,OAAA;cAAKsF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBvF,OAAA;gBAAMsF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC3F,OAAA;gBAAMsF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN3F,OAAA;cAAKsF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBvF,OAAA;gBAAMsF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC3F,OAAA;gBAAMsF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN3F,OAAA;cAAKsF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBvF,OAAA;gBAAMsF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC3F,OAAA;gBAAMsF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3F,OAAA;YAAKsF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvF,OAAA;cACEsF,SAAS,EAAC,kBAAkB;cAC5BM,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnB7D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;gBAChD,IAAI;kBACF,MAAMY,QAAQ,GAAG,MAAMnD,kBAAkB,CAAC,CAAC;kBAC3CsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEY,QAAQ,CAAC;kBAElD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACQ,KAAK,IAAIR,QAAQ,CAAC+B,aAAa,KAAK,MAAM,IAAI/B,QAAQ,CAACgC,MAAM,KAAK,QAAQ,EAAE;oBACpG7C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;oBAC5Cc,QAAQ,CAACnD,eAAe,CAACiD,QAAQ,CAAC,CAAC;oBACnCtD,OAAO,CAACuD,OAAO,CAAC,2CAA2C,CAAC;oBAC5DvC,SAAS,IAAIA,SAAS,CAAC,CAAC;oBACxB6E,UAAU,CAAC,MAAM9E,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;kBACnC,CAAC,MAAM;oBACLf,OAAO,CAACoI,IAAI,CAAC,0EAA0E,CAAC;kBAC1F;gBACF,CAAC,CAAC,OAAOtE,KAAK,EAAE;kBACdrB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;kBAC7C9D,OAAO,CAAC8D,KAAK,CAAC,+BAA+B,CAAC;gBAChD;cACF,CAAE;cAAAmC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET3F,OAAA;cAAQsF,SAAS,EAAC,UAAU;cAACM,OAAO,EAAEP,WAAY;cAAAE,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpF,EAAA,CAjiBIJ,iBAAiB;EAAA,QA2DJf,WAAW,EACXC,WAAW;AAAA;AAAAsI,EAAA,GA5DxBxH,iBAAiB;AAmiBvB,eAAeA,iBAAiB;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}