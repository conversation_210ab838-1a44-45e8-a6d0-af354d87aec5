// Enhanced basic server for client compatibility
const http = require('http');
const url = require('url');

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;

  res.writeHead(200, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });

  if (req.method === 'OPTIONS') {
    res.end();
    return;
  }

  // Handle different API endpoints
  if (path === '/api/health') {
    res.end(JSON.stringify({
      status: 'success',
      message: 'Server running (basic mode)',
      timestamp: new Date().toISOString(),
      database: 'Simulated (no MongoDB connection)'
    }));
  } else if (path === '/api/users/login' && req.method === 'POST') {
    // Handle login
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const loginData = JSON.parse(body);
        console.log('Login attempt:', loginData);

        res.end(JSON.stringify({
          message: "Login successful (demo mode)",
          success: true,
          data: "demo-token-123",
          response: {
            _id: "demo-user-id",
            email: loginData.email || loginData.username,
            firstName: "Demo",
            lastName: "User",
            isAdmin: false,
            subscriptionStatus: "active"
          }
        }));
      } catch (error) {
        res.end(JSON.stringify({
          message: "Invalid login data",
          success: false
        }));
      }
    });
  } else if (path === '/api/users/register' && req.method === 'POST') {
    // Handle registration
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const regData = JSON.parse(body);
        console.log('Registration attempt:', regData);

        res.end(JSON.stringify({
          message: "Registration successful (demo mode)",
          success: true
        }));
      } catch (error) {
        res.end(JSON.stringify({
          message: "Invalid registration data",
          success: false
        }));
      }
    });
  } else if (path === '/api/plans' && req.method === 'GET') {
    // Handle plans fetch
    console.log('Plans requested');

    const samplePlans = [
      {
        _id: "glimp-plan-demo",
        title: "Glimp Plan",
        features: [
          "1-month full access",
          "Unlimited quizzes",
          "Personalized profile",
          "AI chat for instant help",
          "Forum for student discussions",
          "Study notes",
          "Past papers",
          "Books",
          "Learning videos",
          "Track progress with rankings"
        ],
        actualPrice: 15000,
        discountedPrice: 13000,
        discountPercentage: 13,
        duration: 1,
        status: true
      },
      {
        _id: "basic-plan-demo",
        title: "Basic Membership",
        features: [
          "2-month full access",
          "Unlimited quizzes",
          "Personalized profile",
          "AI chat for instant help",
          "Forum for student discussions",
          "Study notes",
          "Past papers",
          "Books",
          "Learning videos",
          "Track progress with rankings"
        ],
        actualPrice: 28570,
        discountedPrice: 20000,
        discountPercentage: 30,
        duration: 2,
        status: true
      },
      {
        _id: "premium-plan-demo",
        title: "Premium Plan",
        features: [
          "3-month full access",
          "Unlimited quizzes",
          "Personalized profile",
          "AI chat for instant help",
          "Forum for student discussions",
          "Study notes",
          "Past papers",
          "Books",
          "Learning videos",
          "Track progress with rankings",
          "Priority support"
        ],
        actualPrice: 45000,
        discountedPrice: 35000,
        discountPercentage: 22,
        duration: 3,
        status: true
      }
    ];

    res.end(JSON.stringify(samplePlans));
  } else if (path === '/api/payment/create-invoice' && req.method === 'POST') {
    // Handle payment creation
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const paymentData = JSON.parse(body);
        console.log('Payment request:', paymentData);

        res.end(JSON.stringify({
          message: "Payment initiated (demo mode)",
          success: true,
          data: {
            orderId: `demo-order-${Date.now()}`,
            status: "pending",
            note: "This is demo mode. In production, this would connect to ZenoPay API."
          }
        }));
      } catch (error) {
        res.end(JSON.stringify({
          message: "Invalid payment data",
          success: false
        }));
      }
    });
  } else if (path === '/api/users/get-user-info' && req.method === 'POST') {
    // Handle get user info
    console.log('Get user info requested');

    res.end(JSON.stringify({
      message: "User info retrieved (demo mode)",
      success: true,
      data: {
        _id: "demo-user-id",
        firstName: "Demo",
        lastName: "User",
        email: "<EMAIL>",
        username: "demouser",
        phoneNumber: "0712345678",
        level: "Primary",
        class: "5",
        isAdmin: false,
        subscriptionStatus: "pending",
        paymentRequired: true,
        createdAt: new Date().toISOString()
      }
    }));
  } else if (path === '/api/users/update-user-info' && req.method === 'POST') {
    // Handle update user info
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const updateData = JSON.parse(body);
        console.log('Update user info:', updateData);

        res.end(JSON.stringify({
          message: "User info updated successfully (demo mode)",
          success: true,
          data: {
            ...updateData,
            _id: "demo-user-id",
            updatedAt: new Date().toISOString()
          }
        }));
      } catch (error) {
        res.end(JSON.stringify({
          message: "Invalid update data",
          success: false
        }));
      }
    });
  } else if (path.startsWith('/api/subscription') && req.method === 'GET') {
    // Handle subscription status
    console.log('Subscription status requested');

    res.end(JSON.stringify({
      message: "Subscription status retrieved (demo mode)",
      success: true,
      data: {
        status: "pending",
        plan: null,
        startDate: null,
        endDate: null,
        paymentRequired: true
      }
    }));
  } else {
    res.end(JSON.stringify({
      message: 'Basic server is running (demo mode)',
      url: req.url,
      method: req.method,
      note: 'This is a basic server for testing. Full features require MongoDB connection.'
    }));
  }
});

const port = 5000;
server.listen(port, () => {
  console.log(`✅ Basic server running on port ${port}`);
  console.log(`🔗 Test: http://localhost:${port}/api/health`);
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
});
