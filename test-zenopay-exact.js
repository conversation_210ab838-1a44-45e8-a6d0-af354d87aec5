// Test ZenoPay API with EXACT format from documentation
const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

console.log('🧪 Testing ZenoPay API with EXACT documentation format...\n');

// Check environment variables
console.log('📋 Environment Check:');
console.log('✓ ZENOPAY_API_KEY:', process.env.ZENOPAY_API_KEY ? 'SET' : '❌ MISSING');
console.log('✓ API Key preview:', process.env.ZENOPAY_API_KEY ? process.env.ZENOPAY_API_KEY.substring(0, 10) + '...' : 'NOT SET');
console.log('');

// Test data using EXACT format from documentation
const testData = {
  order_id: `3rer407fe-3ee8-4525-456f-ccb95de38250`, // Using format from docs
  buyer_email: "<EMAIL>",                       // Using format from docs
  buyer_name: "<PERSON>",                             // Using format from docs  
  buyer_phone: "0744963858",                          // Using format from docs
  amount: 1000                                        // Using format from docs
};

console.log('📤 Test data (EXACT format from documentation):');
console.log(JSON.stringify(testData, null, 2));
console.log('');

async function testZenoPayAPI() {
  try {
    console.log('🔄 Sending request to ZenoPay API...');
    console.log('🌐 Endpoint: https://zenoapi.com/api/payments/mobile_money_tanzania');
    console.log('🔑 Authentication: x-api-key header');
    console.log('📦 Content-Type: application/json');
    console.log('');
    
    const response = await axios.post('https://zenoapi.com/api/payments/mobile_money_tanzania', testData, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 30000
    });

    console.log('✅ ZenoPay API Response:');
    console.log('Status Code:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));

    // Check for success response format from documentation
    if (response.data.status === 'success') {
      console.log('\n🎉 SUCCESS! Payment initiated successfully!');
      console.log('📱 Expected SMS to be sent to:', testData.buyer_phone);
      console.log('🆔 Order ID:', response.data.order_id);
      console.log('📝 Message:', response.data.message);
      console.log('🔢 Result Code:', response.data.resultcode);
      
      // Test order status check
      if (response.data.order_id) {
        console.log('\n🔍 Testing order status check...');
        await testOrderStatus(response.data.order_id);
      }
    } else {
      console.log('\n⚠️ ZenoPay returned an error:');
      console.log('Message:', response.data.message || 'No message provided');
      console.log('Status:', response.data.status || 'No status provided');
    }

  } catch (error) {
    console.error('\n❌ ZenoPay API Test Failed:');
    console.error('Error Message:', error.message);
    
    if (error.response) {
      console.error('HTTP Status:', error.response.status);
      console.error('Response Headers:', JSON.stringify(error.response.headers, null, 2));
      console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      
      // Analyze specific errors
      if (error.response.status === 403) {
        console.error('\n🚨 403 Forbidden Error Analysis:');
        if (error.response.data?.detail === 'Invalid API key') {
          console.error('❌ API Key is invalid or expired');
          console.error('💡 Solution: Verify API key with ZenoPay support');
        } else {
          console.error('❌ Access denied - possible IP whitelist issue');
          console.error('💡 Solution: Contact ZenoPay to whitelist your IP');
        }
      } else if (error.response.status === 400) {
        console.error('\n🚨 400 Bad Request Error Analysis:');
        console.error('❌ Request data validation failed');
        console.error('💡 Check data format against documentation');
      } else if (error.response.status === 401) {
        console.error('\n🚨 401 Unauthorized Error Analysis:');
        console.error('❌ Authentication failed');
        console.error('💡 Check API key format and header');
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.error('\n🌐 Connection Error:');
      console.error('❌ Cannot connect to ZenoPay API');
      console.error('💡 Check internet connection');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('\n⏰ Timeout Error:');
      console.error('❌ Request timed out');
      console.error('💡 ZenoPay API might be slow or unavailable');
    }
  }
}

// Test order status endpoint from documentation
async function testOrderStatus(orderId) {
  try {
    console.log(`🔍 Checking order status for: ${orderId}`);
    
    const response = await axios.get(`https://zenoapi.com/api/payments/order-status?order_id=${orderId}`, {
      headers: {
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 15000
    });

    console.log('✅ Order Status Response:');
    console.log('Status Code:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));

    // Check response format from documentation
    if (response.data.result === 'SUCCESS') {
      console.log('\n📊 Order Status Details:');
      const orderData = response.data.data[0];
      console.log('Order ID:', orderData.order_id);
      console.log('Payment Status:', orderData.payment_status);
      console.log('Amount:', orderData.amount);
      console.log('Channel:', orderData.channel);
      console.log('Reference:', orderData.reference);
      console.log('Creation Date:', orderData.creation_date);
    }

  } catch (error) {
    console.log('\n📝 Order Status Check Result:');
    if (error.response && error.response.status === 404) {
      console.log('ℹ️ Order not found (expected for test order)');
    } else if (error.response && error.response.status === 403) {
      console.log('❌ API key invalid for order status endpoint');
    } else {
      console.error('❌ Order status check failed:', error.message);
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Data:', error.response.data);
      }
    }
  }
}

// Run the test
async function runTest() {
  console.log('🚀 Starting ZenoPay API Test with Documentation Format...\n');
  
  // Validate environment
  if (!process.env.ZENOPAY_API_KEY) {
    console.error('❌ ZENOPAY_API_KEY not found in environment variables');
    console.error('Please check your server/.env file');
    return;
  }
  
  await testZenoPayAPI();
  
  console.log('\n✅ Test completed!');
  console.log('\n📚 Documentation Reference:');
  console.log('- Endpoint: https://zenoapi.com/api/payments/mobile_money_tanzania');
  console.log('- Method: POST');
  console.log('- Auth: x-api-key header');
  console.log('- Format: JSON');
  console.log('- Support: <EMAIL>');
}

runTest().catch(console.error);
