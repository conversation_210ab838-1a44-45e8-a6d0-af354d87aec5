const axios = require('axios');
const qs = require('qs');

async function debugZenoPayRequest() {
  console.log('🔍 Testing ZenoPay with current configuration...\n');

  // Test data similar to what the app would send
  const testData = {
    buyer_name: 'Test User',
    buyer_phone: '**********',
    buyer_email: '<EMAIL>',
    amount: 5000,
    account_id: 'zp38236',
    secret_key: 'your_secret_key_here',
    api_key: '-YIkdkUWpqEyy9DOaKPTDeaEZ5O97_DkSxmZdBLwYrE',
    webhook_url: 'https://d6df-39-52-5-75.ngrok-free.app/api/payment/webhook'
  };

  console.log('📤 Test data being sent to ZenoPay:');
  console.log(JSON.stringify(testData, null, 2));
  console.log('');

  try {
    const formattedData = qs.stringify(testData);
    console.log('📝 URL-encoded data:');
    console.log(formattedData);
    console.log('');

    console.log('🔄 Sending request to ZenoPay...');
    const response = await axios.post('https://api.zeno.africa', formattedData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 30000
    });

    console.log('✅ ZenoPay Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ ZenoPay Error:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
      
      // Check for specific validation errors
      const errorMsg = error.response.data?.message || '';
      if (errorMsg.includes('Invalid input data')) {
        console.log('\n🔍 "Invalid input data" error detected!');
        console.log('This usually means:');
        console.log('1. Phone number format is wrong');
        console.log('2. Amount is invalid');
        console.log('3. Account ID is incorrect');
        console.log('4. Webhook URL is not accessible');
        console.log('5. Required fields are missing');
      }
    } else {
      console.log('Network error:', error.message);
    }
  }

  // Test webhook URL accessibility
  console.log('\n🔗 Testing webhook URL accessibility...');
  try {
    const webhookResponse = await axios.get('https://d6df-39-52-5-75.ngrok-free.app/api/payment/webhook', {
      timeout: 10000
    });
    console.log('✅ Webhook URL is accessible');
  } catch (webhookError) {
    console.log('❌ Webhook URL is NOT accessible:', webhookError.message);
    console.log('   This could be causing the "Invalid input data" error');
    console.log('   ZenoPay might be validating webhook URL accessibility');
  }
}

debugZenoPayRequest().catch(console.error);
