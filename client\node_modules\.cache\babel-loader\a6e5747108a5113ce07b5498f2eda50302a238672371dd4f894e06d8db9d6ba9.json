{"ast": null, "code": "/**\n * React Router DOM v6.15.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_mapRouteProperties, Router, UNSAFE_NavigationContext, useHref, useResolvedPath, useLocation, UNSAFE_DataRouterStateContext, useNavigate, createPath, UNSAFE_useRouteId, UNSAFE_RouteContext, useMatches, useNavigation, unstable_useBlocker, UNSAFE_DataRouterContext } from 'react-router';\nexport { AbortedDeferredError, Await, MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, RouterProvider, Routes, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, UNSAFE_useRouteId, createMemoryRouter, createPath, createRoutesFromChildren, createRoutesFromElements, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, renderMatches, resolvePath, unstable_useBlocker, useActionData, useAsyncError, useAsyncValue, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes } from 'react-router';\nimport { stripBasename, UNSAFE_warning, createRouter, createBrowserHistory, createHashHistory, ErrorResponse, UNSAFE_invariant, joinPaths } from '@remix-run/router';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && (\n  // Ignore everything but left clicks\n  !target || target === \"_self\") &&\n  // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event) // Ignore clicks with modifier keys\n  ;\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach(value => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n  return searchParams;\n}\n// One-time check for submitter support\nlet _formDataSupportsSubmitter = null;\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(document.createElement(\"form\"),\n      // @ts-expect-error if FormData supports the submitter parameter, this will throw\n      0);\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\nconst supportedFormEncTypes = new Set([\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"]);\nfunction getFormEncType(encType) {\n  if (encType != null && !supportedFormEncTypes.has(encType)) {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"\\\"\" + encType + \"\\\" is not a valid `encType` for `<Form>`/`<fetcher.Form>` \" + (\"and will default to \\\"\" + defaultEncType + \"\\\"\")) : void 0;\n    return null;\n  }\n  return encType;\n}\nfunction getFormSubmissionInfo(target, basename) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n  let body;\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n    formData = new FormData(target);\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n    if (form == null) {\n      throw new Error(\"Cannot submit a <button> or <input type=\\\"submit\\\"> without a <form>\");\n    }\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"formenctype\")) || getFormEncType(form.getAttribute(\"enctype\")) || defaultEncType;\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let {\n        name,\n        type,\n        value\n      } = target;\n      if (type === \"image\") {\n        let prefix = name ? name + \".\" : \"\";\n        formData.append(prefix + \"x\", \"0\");\n        formData.append(prefix + \"y\", \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\"Cannot submit element that is not <form>, <button>, or \" + \"<input type=\\\"submit|image\\\">\");\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n  return {\n    action,\n    method: method.toLowerCase(),\n    encType,\n    formData,\n    body\n  };\n}\nconst _excluded = [\"onClick\", \"relative\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\", \"preventScrollReset\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"children\"],\n  _excluded3 = [\"reloadDocument\", \"replace\", \"state\", \"method\", \"action\", \"onSubmit\", \"submit\", \"relative\", \"preventScrollReset\"];\nfunction createBrowserRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createBrowserHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createHashHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties\n  }).initialize();\n}\nfunction parseHydrationData() {\n  var _window;\n  let state = (_window = window) == null ? void 0 : _window.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = _extends({}, state, {\n      errors: deserializeErrors(state.errors)\n    });\n  }\n  return state;\n}\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponse(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nfunction BrowserRouter(_ref) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nfunction HashRouter(_ref2) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref2;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    future,\n    history\n  } = _ref3;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n/**\n * The public API for rendering a history-aware <a>.\n */\nconst Link = /*#__PURE__*/React.forwardRef(function LinkWithRef(_ref4, ref) {\n  let {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset\n    } = _ref4,\n    rest = _objectWithoutPropertiesLoose(_ref4, _excluded);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  // Rendered into <a href> for absolute URLs\n  let absoluteHref;\n  let isExternal = false;\n  if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n    // Render the absolute href server- and client-side\n    absoluteHref = to;\n    // Only check for external origins client-side\n    if (isBrowser) {\n      try {\n        let currentUrl = new URL(window.location.href);\n        let targetUrl = to.startsWith(\"//\") ? new URL(currentUrl.protocol + to) : new URL(to);\n        let path = stripBasename(targetUrl.pathname, basename);\n        if (targetUrl.origin === currentUrl.origin && path != null) {\n          // Strip the protocol/origin/basename for same-origin absolute URLs\n          to = path + targetUrl.search + targetUrl.hash;\n        } else {\n          isExternal = true;\n        }\n      } catch (e) {\n        // We can't do external URL detection without a valid URL\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"<Link to=\\\"\" + to + \"\\\"> contains an invalid URL which will probably break \" + \"when clicked - please update to a valid URL path.\") : void 0;\n      }\n    }\n  }\n  // Rendered into <a href> for relative URLs\n  let href = useHref(to, {\n    relative\n  });\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target,\n    preventScrollReset,\n    relative\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n  return /*#__PURE__*/(\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    React.createElement(\"a\", _extends({}, rest, {\n      href: absoluteHref || href,\n      onClick: isExternal || reloadDocument ? onClick : handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nconst NavLink = /*#__PURE__*/React.forwardRef(function NavLinkWithRef(_ref5, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children\n    } = _ref5,\n    rest = _objectWithoutPropertiesLoose(_ref5, _excluded2);\n  let path = useResolvedPath(to, {\n    relative: rest.relative\n  });\n  let location = useLocation();\n  let routerState = React.useContext(UNSAFE_DataRouterStateContext);\n  let {\n    navigator\n  } = React.useContext(UNSAFE_NavigationContext);\n  let toPathname = navigator.encodeLocation ? navigator.encodeLocation(path).pathname : path.pathname;\n  let locationPathname = location.pathname;\n  let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n    toPathname = toPathname.toLowerCase();\n  }\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(toPathname.length) === \"/\";\n  let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp({\n      isActive,\n      isPending\n    });\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null, isPending ? \"pending\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp({\n    isActive,\n    isPending\n  }) : styleProp;\n  return /*#__PURE__*/React.createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to\n  }), typeof children === \"function\" ? children({\n    isActive,\n    isPending\n  }) : children);\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n}\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nconst Form = /*#__PURE__*/React.forwardRef((props, ref) => {\n  let submit = useSubmit();\n  return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n    submit: submit,\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\nconst FormImpl = /*#__PURE__*/React.forwardRef((_ref6, forwardedRef) => {\n  let {\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      submit,\n      relative,\n      preventScrollReset\n    } = _ref6,\n    props = _objectWithoutPropertiesLoose(_ref6, _excluded3);\n  let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n  let formAction = useFormAction(action, {\n    relative\n  });\n  let submitHandler = event => {\n    onSubmit && onSubmit(event);\n    if (event.defaultPrevented) return;\n    event.preventDefault();\n    let submitter = event.nativeEvent.submitter;\n    let submitMethod = (submitter == null ? void 0 : submitter.getAttribute(\"formmethod\")) || method;\n    submit(submitter || event.currentTarget, {\n      method: submitMethod,\n      replace,\n      state,\n      relative,\n      preventScrollReset\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"form\", _extends({\n    ref: forwardedRef,\n    method: formMethod,\n    action: formAction,\n    onSubmit: reloadDocument ? onSubmit : submitHandler\n  }, props));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  FormImpl.displayName = \"FormImpl\";\n}\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nfunction ScrollRestoration(_ref7) {\n  let {\n    getKey,\n    storageKey\n  } = _ref7;\n  useScrollRestoration({\n    getKey,\n    storageKey\n  });\n  return null;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\nvar DataRouterHook;\n(function (DataRouterHook) {\n  DataRouterHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n  DataRouterHook[\"UseSubmit\"] = \"useSubmit\";\n  DataRouterHook[\"UseSubmitFetcher\"] = \"useSubmitFetcher\";\n  DataRouterHook[\"UseFetcher\"] = \"useFetcher\";\n})(DataRouterHook || (DataRouterHook = {}));\nvar DataRouterStateHook;\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseFetchers\"] = \"useFetchers\";\n  DataRouterStateHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(UNSAFE_DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(UNSAFE_DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, {\n    relative\n  });\n  return React.useCallback(event => {\n    if (shouldProcessLinkClick(event, target)) {\n      event.preventDefault();\n      // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here unless the replace prop is explicitly set\n      let replace = replaceProp !== undefined ? replaceProp : createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state,\n        preventScrollReset,\n        relative\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to, preventScrollReset, relative]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params\\n\\n\" + \"If you're unsure how to load polyfills, we recommend you check out \" + \"https://polyfill.io/v3/ which provides some recommendations about how \" + \"to load polyfills only for users that need them, instead of for every \" + \"user.\") : void 0;\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n  let location = useLocation();\n  let searchParams = React.useMemo(() =>\n  // Only merge in the defaults if we haven't yet called setSearchParams.\n  // Once we call that we want those to take precedence, otherwise you can't\n  // remove a param with setSearchParams({}) if it has an initial value\n  getSearchParamsForLocation(location.search, hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current), [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback((nextInit, navigateOptions) => {\n    const newSearchParams = createSearchParams(typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit);\n    hasSetSearchParamsRef.current = true;\n    navigate(\"?\" + newSearchParams, navigateOptions);\n  }, [navigate, searchParams]);\n  return [searchParams, setSearchParams];\n}\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\"You are calling submit during the server render. \" + \"Try calling submit within a `useEffect` or callback instead.\");\n  }\n}\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nfunction useSubmit() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let currentRouteId = UNSAFE_useRouteId();\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    validateClientSideSubmission();\n    let {\n      action,\n      method,\n      encType,\n      formData,\n      body\n    } = getFormSubmissionInfo(target, basename);\n    router.navigate(options.action || action, {\n      preventScrollReset: options.preventScrollReset,\n      formData,\n      body,\n      formMethod: options.method || method,\n      formEncType: options.encType || encType,\n      replace: options.replace,\n      state: options.state,\n      fromRouteId: currentRouteId\n    });\n  }, [router, basename, currentRouteId]);\n}\n/**\n * Returns the implementation for fetcher.submit\n */\nfunction useSubmitFetcher(fetcherKey, fetcherRouteId) {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmitFetcher);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    validateClientSideSubmission();\n    let {\n      action,\n      method,\n      encType,\n      formData,\n      body\n    } = getFormSubmissionInfo(target, basename);\n    !(fetcherRouteId != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"No routeId available for useFetcher()\") : UNSAFE_invariant(false) : void 0;\n    router.fetch(fetcherKey, fetcherRouteId, options.action || action, {\n      preventScrollReset: options.preventScrollReset,\n      formData,\n      body,\n      formMethod: options.method || method,\n      formEncType: options.encType || encType\n    });\n  }, [router, basename, fetcherKey, fetcherRouteId]);\n}\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nfunction useFormAction(action, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let routeContext = React.useContext(UNSAFE_RouteContext);\n  !routeContext ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFormAction must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = _extends({}, useResolvedPath(action ? action : \".\", {\n    relative\n  }));\n  // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params of the resolved URL. This is\n  // the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n    // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? \"?\" + params.toString() : \"\";\n    }\n  }\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  }\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\nfunction createFetcherForm(fetcherKey, routeId) {\n  let FetcherForm = /*#__PURE__*/React.forwardRef((props, ref) => {\n    let submit = useSubmitFetcher(fetcherKey, routeId);\n    return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n      ref: ref,\n      submit: submit\n    }));\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\nlet fetcherId = 0;\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nfunction useFetcher() {\n  var _route$matches;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let route = React.useContext(UNSAFE_RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  let routeId = (_route$matches = route.matches[route.matches.length - 1]) == null ? void 0 : _route$matches.route.id;\n  !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    !routeId ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"No routeId available for fetcher.Form()\") : UNSAFE_invariant(false) : void 0;\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => href => {\n    !router ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"No router available for fetcher.load()\") : UNSAFE_invariant(false) : void 0;\n    !routeId ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"No routeId available for fetcher.load()\") : UNSAFE_invariant(false) : void 0;\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitFetcher(fetcherKey, routeId);\n  let fetcher = router.getFetcher(fetcherKey);\n  let fetcherWithComponents = React.useMemo(() => _extends({\n    Form,\n    submit,\n    load\n  }, fetcher), [fetcher, Form, submit, load]);\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(\"No router available to clean up from useFetcher()\");\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n  return fetcherWithComponents;\n}\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nfunction useFetchers() {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions = {};\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration(_temp3) {\n  let {\n    getKey,\n    storageKey\n  } = _temp3 === void 0 ? {} : _temp3;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let {\n    restoreScrollPosition,\n    preventScrollReset\n  } = useDataRouterState(DataRouterStateHook.UseScrollRestoration);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n  // Save positions on pagehide\n  usePageHide(React.useCallback(() => {\n    if (navigation.state === \"idle\") {\n      let key = (getKey ? getKey(location, matches) : null) || location.key;\n      savedScrollPositions[key] = window.scrollY;\n    }\n    sessionStorage.setItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY, JSON.stringify(savedScrollPositions));\n    window.history.scrollRestoration = \"auto\";\n  }, [storageKey, getKey, navigation.state, location, matches]));\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY);\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename = getKey && basename !== \"/\" ? (location, matches) => getKey(\n      // Strip the basename to match useLocation()\n      _extends({}, location, {\n        pathname: stripBasename(location.pathname, basename) || location.pathname\n      }), matches) : getKey;\n      let disableScrollRestoration = router == null ? void 0 : router.enableScrollRestoration(savedScrollPositions, () => window.scrollY, getKeyWithoutBasename);\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(decodeURIComponent(location.hash.slice(1)));\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction useBeforeUnload(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt(_ref8) {\n  let {\n    when,\n    message\n  } = _ref8;\n  let blocker = unstable_useBlocker(when);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n//#endregion\n\nexport { BrowserRouter, Form, HashRouter, Link, NavLink, ScrollRestoration, useScrollRestoration as UNSAFE_useScrollRestoration, createBrowserRouter, createHashRouter, createSearchParams, HistoryRouter as unstable_HistoryRouter, usePrompt as unstable_usePrompt, useBeforeUnload, useFetcher, useFetchers, useFormAction, useLinkClickHandler, useSearchParams, useSubmit };", "map": {"version": 3, "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "env", "NODE_ENV", "UNSAFE_warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "createBrowserRouter", "routes", "opts", "createRouter", "future", "_extends", "v7_prependBasename", "history", "createBrowserHistory", "window", "hydrationData", "parseHydrationData", "mapRouteProperties", "UNSAFE_mapRouteProperties", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponse", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "START_TRANSITION", "startTransitionImpl", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "historyRef", "useRef", "current", "v5Compat", "setStateImpl", "useState", "location", "v7_startTransition", "setState", "useCallback", "newState", "useLayoutEffect", "listen", "Router", "navigationType", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "HistoryRouter", "_ref3", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref4", "ref", "onClick", "relative", "reloadDocument", "replace", "to", "preventScrollReset", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "UNSAFE_NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref5", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "UNSAFE_DataRouterStateContext", "toPathname", "encodeLocation", "locationPathname", "nextLocationPathname", "navigation", "isActive", "char<PERSON>t", "length", "isPending", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "props", "submit", "useSubmit", "FormImpl", "_ref6", "forwardedRef", "onSubmit", "_excluded3", "formMethod", "formAction", "useFormAction", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref7", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "UNSAFE_DataRouterContext", "UNSAFE_invariant", "useDataRouterState", "_temp", "replaceProp", "navigate", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "useMemo", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "router", "UseSubmit", "currentRouteId", "UNSAFE_useRouteId", "options", "formEncType", "fromRouteId", "useSubmitFetcher", "fetcher<PERSON>ey", "fetcherRouteId", "UseSubmitFetcher", "fetch", "_temp2", "routeContext", "UNSAFE_RouteContext", "match", "matches", "slice", "route", "index", "params", "delete", "toString", "joinPaths", "createFetcherForm", "routeId", "FetcherForm", "fetcherId", "useFetcher", "_route$matches", "UseFetcher", "id", "String", "load", "fetcher", "getFetcher", "fetcherWithComponents", "useEffect", "console", "warn", "deleteFetcher", "useFetchers", "UseFetchers", "fetchers", "values", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp3", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref8", "when", "blocker", "unstable_useBlocker", "proceed", "confirm", "setTimeout", "reset"], "sources": ["C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\node_modules\\react-router-dom\\dom.ts", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\node_modules\\react-router-dom\\index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\nexport interface SubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type {\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  RelativeRoutingType,\n  RouteObject,\n  To,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  unstable_useBlocker as useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n} from \"react-router\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>er,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  ErrorResponse,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  unstable_Blocker,\n  unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponse(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware <a>.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?:\n    | React.ReactNode\n    | ((props: { isActive: boolean; isPending: boolean }) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?:\n    | string\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: {\n        isActive: boolean;\n        isPending: boolean;\n      }) => React.CSSProperties | undefined);\n}\n\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator } = React.useContext(NavigationContext);\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp({ isActive, isPending });\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\"\n        ? styleProp({ isActive, isPending })\n        : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n      >\n        {typeof children === \"function\"\n          ? children({ isActive, isPending })\n          : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\nexport interface FetcherFormProps\n  extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\nexport interface FormProps extends FetcherFormProps {\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n}\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (props, ref) => {\n    let submit = useSubmit();\n    return <FormImpl {...props} submit={submit} ref={ref} />;\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\ninterface FormImplProps extends FormProps {\n  submit: SubmitFunction | FetcherSubmitFunction;\n}\n\nconst FormImpl = React.forwardRef<HTMLFormElement, FormImplProps>(\n  (\n    {\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      submit,\n      relative,\n      preventScrollReset,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n    let formAction = useFormAction(action, { relative });\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        method: submitMethod,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  FormImpl.displayName = \"FormImpl\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n}\n\nenum DataRouterStateHook {\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, { replace, state, preventScrollReset, relative });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: Omit<SubmitOptions, \"replace\" | \"state\">\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      router.navigate(options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || (method as HTMLFormMethod),\n        formEncType: options.encType || (encType as FormEncType),\n        replace: options.replace,\n        state: options.state,\n        fromRouteId: currentRouteId,\n      });\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n/**\n * Returns the implementation for fetcher.submit\n */\nfunction useSubmitFetcher(\n  fetcherKey: string,\n  fetcherRouteId: string\n): FetcherSubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmitFetcher);\n  let { basename } = React.useContext(NavigationContext);\n\n  return React.useCallback<FetcherSubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      invariant(\n        fetcherRouteId != null,\n        \"No routeId available for useFetcher()\"\n      );\n      router.fetch(fetcherKey, fetcherRouteId, options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || (method as HTMLFormMethod),\n        formEncType: options.encType || (encType as FormEncType),\n      });\n    },\n    [router, basename, fetcherKey, fetcherRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params of the resolved URL. This is\n  // the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? `?${params.toString()}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nfunction createFetcherForm(fetcherKey: string, routeId: string) {\n  let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n    (props, ref) => {\n      let submit = useSubmitFetcher(fetcherKey, routeId);\n      return <FormImpl {...props} ref={ref} submit={submit} />;\n    }\n  );\n  if (__DEV__) {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\n\nlet fetcherId = 0;\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: ReturnType<typeof createFetcherForm>;\n  submit: FetcherSubmitFunction;\n  load: (href: string) => void;\n};\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>(): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    invariant(routeId, `No routeId available for fetcher.Form()`);\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => (href: string) => {\n    invariant(router, \"No router available for fetcher.load()\");\n    invariant(routeId, \"No routeId available for fetcher.load()\");\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitFetcher(fetcherKey, routeId);\n\n  let fetcher = router.getFetcher<TData>(fetcherKey);\n\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form,\n      submit,\n      load,\n      ...fetcher,\n    }),\n    [fetcher, Form, submit, load]\n  );\n\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(`No router available to clean up from useFetcher()`);\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): Fetcher[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      sessionStorage.setItem(\n        storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n        JSON.stringify(savedScrollPositions)\n      );\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({ when, message }: { when: boolean; message: string }) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAMA,aAAa,GAAmB,KAAK;AAClD,MAAMC,cAAc,GAAgB,mCAAmC;AAEjE,SAAUC,aAAaA,CAACC,MAAW;EACvC,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ;AAC7D;AAEM,SAAUC,eAAeA,CAACF,MAAW;EACzC,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,QAAQ;AAC3E;AAEM,SAAUC,aAAaA,CAACJ,MAAW;EACvC,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,MAAM;AACzE;AAEM,SAAUE,cAAcA,CAACL,MAAW;EACxC,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,OAAO;AAC1E;AAOA,SAASG,eAAeA,CAACC,KAAwB;EAC/C,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,QAAQ,CAAC;AAC7E;AAEgB,SAAAC,sBAAsBA,CACpCL,KAAwB,EACxBM,MAAe;EAEf,OACEN,KAAK,CAACO,MAAM,KAAK,CAAC;EAAI;EACrB,CAACD,MAAM,IAAIA,MAAM,KAAK,OAAO,CAAC;EAAI;EACnC,CAACP,eAAe,CAACC,KAAK,CAAC;EAAA;AAE3B;AAUA;;;;;;;;;;;;;;;;;;;;AAoBG;AACa,SAAAQ,kBAAkBA,CAChCC,IAAA,EAA8B;EAAA,IAA9BA,IAAA;IAAAA,IAAA,GAA4B,EAAE;EAAA;EAE9B,OAAO,IAAIC,eAAe,CACxB,OAAOD,IAAI,KAAK,QAAQ,IACxBE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IACnBA,IAAI,YAAYC,eAAe,GAC3BD,IAAI,GACJI,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAI;IACrC,IAAIC,KAAK,GAAGT,IAAI,CAACQ,GAAG,CAAC;IACrB,OAAOD,IAAI,CAACG,MAAM,CAChBR,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,CAAEC,CAAC,IAAK,CAACJ,GAAG,EAAEI,CAAC,CAAC,CAAC,GAAG,CAAC,CAACJ,GAAG,EAAEC,KAAK,CAAC,CAAC,CACnE;GACF,EAAE,EAAyB,CAAC,CAClC;AACH;AAEgB,SAAAI,0BAA0BA,CACxCC,cAAsB,EACtBC,mBAA2C;EAE3C,IAAIC,YAAY,GAAGjB,kBAAkB,CAACe,cAAc,CAAC;EAErD,IAAIC,mBAAmB,EAAE;IACvB;IACA;IACA;IACA;IACA;IACAA,mBAAmB,CAACE,OAAO,CAAC,CAACC,CAAC,EAAEV,GAAG,KAAI;MACrC,IAAI,CAACQ,YAAY,CAACG,GAAG,CAACX,GAAG,CAAC,EAAE;QAC1BO,mBAAmB,CAACK,MAAM,CAACZ,GAAG,CAAC,CAACS,OAAO,CAAER,KAAK,IAAI;UAChDO,YAAY,CAACK,MAAM,CAACb,GAAG,EAAEC,KAAK,CAAC;QACjC,CAAC,CAAC;MACH;IACH,CAAC,CAAC;EACH;EAED,OAAOO,YAAY;AACrB;AAmBA;AACA,IAAIM,0BAA0B,GAAmB,IAAI;AAErD,SAASC,4BAA4BA,CAAA;EACnC,IAAID,0BAA0B,KAAK,IAAI,EAAE;IACvC,IAAI;MACF,IAAIE,QAAQ,CACVC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC9B;MACA,CAAC,CACF;MACDJ,0BAA0B,GAAG,KAAK;KACnC,CAAC,OAAOK,CAAC,EAAE;MACVL,0BAA0B,GAAG,IAAI;IAClC;EACF;EACD,OAAOA,0BAA0B;AACnC;AA+CA,MAAMM,qBAAqB,GAAqB,IAAIC,GAAG,CAAC,CACtD,mCAAmC,EACnC,qBAAqB,EACrB,YAAY,CACb,CAAC;AAEF,SAASC,cAAcA,CAACC,OAAsB;EAC5C,IAAIA,OAAO,IAAI,IAAI,IAAI,CAACH,qBAAqB,CAACT,GAAG,CAACY,OAAsB,CAAC,EAAE;IACzEC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,KAAK,EACL,IAAI,GAAAJ,OAAO,GACe,2FAAAjD,cAAc,QAAG,CAC5C;IAED,OAAO,IAAI;EACZ;EACD,OAAOiD,OAAO;AAChB;AAEgB,SAAAK,qBAAqBA,CACnCvC,MAAoB,EACpBwC,QAAgB;EAQhB,IAAIC,MAAc;EAClB,IAAIC,MAAqB;EACzB,IAAIR,OAAe;EACnB,IAAIS,QAA8B;EAClC,IAAIC,IAAS;EAEb,IAAIrD,aAAa,CAACS,MAAM,CAAC,EAAE;IACzB;IACA;IACA;IACA,IAAI6C,IAAI,GAAG7C,MAAM,CAAC8C,YAAY,CAAC,QAAQ,CAAC;IACxCJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI;IACpDC,MAAM,GAAGzC,MAAM,CAAC8C,YAAY,CAAC,QAAQ,CAAC,IAAI9D,aAAa;IACvDkD,OAAO,GAAGD,cAAc,CAACjC,MAAM,CAAC8C,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI7D,cAAc;IAE1E0D,QAAQ,GAAG,IAAIhB,QAAQ,CAAC3B,MAAM,CAAC;GAChC,MAAM,IACLX,eAAe,CAACW,MAAM,CAAC,IACtBR,cAAc,CAACQ,MAAM,CAAC,KACpBA,MAAM,CAACgD,IAAI,KAAK,QAAQ,IAAIhD,MAAM,CAACgD,IAAI,KAAK,OAAO,CAAE,EACxD;IACA,IAAIC,IAAI,GAAGjD,MAAM,CAACiD,IAAI;IAEtB,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,MAAM,IAAIC,KAAK,uEACuD,CACrE;IACF;IAED;IAEA;IACA;IACA;IACA,IAAIL,IAAI,GAAG7C,MAAM,CAAC8C,YAAY,CAAC,YAAY,CAAC,IAAIG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC;IAC3EJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI;IAEpDC,MAAM,GACJzC,MAAM,CAAC8C,YAAY,CAAC,YAAY,CAAC,IACjCG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,IAC3B9D,aAAa;IACfkD,OAAO,GACLD,cAAc,CAACjC,MAAM,CAAC8C,YAAY,CAAC,aAAa,CAAC,CAAC,IAClDb,cAAc,CAACgB,IAAI,CAACH,YAAY,CAAC,SAAS,CAAC,CAAC,IAC5C7D,cAAc;IAEhB;IACA0D,QAAQ,GAAG,IAAIhB,QAAQ,CAACsB,IAAI,EAAEjD,MAAM,CAAC;IAErC;IACA;IACA;IACA;IACA,IAAI,CAAC0B,4BAA4B,EAAE,EAAE;MACnC,IAAI;QAAEyB,IAAI;QAAEH,IAAI;QAAEpC;MAAK,CAAE,GAAGZ,MAAM;MAClC,IAAIgD,IAAI,KAAK,OAAO,EAAE;QACpB,IAAII,MAAM,GAAGD,IAAI,GAAMA,IAAI,SAAM,EAAE;QACnCR,QAAQ,CAACnB,MAAM,CAAI4B,MAAM,QAAK,GAAG,CAAC;QAClCT,QAAQ,CAACnB,MAAM,CAAI4B,MAAM,QAAK,GAAG,CAAC;OACnC,MAAM,IAAID,IAAI,EAAE;QACfR,QAAQ,CAACnB,MAAM,CAAC2B,IAAI,EAAEvC,KAAK,CAAC;MAC7B;IACF;EACF,OAAM,IAAI1B,aAAa,CAACc,MAAM,CAAC,EAAE;IAChC,MAAM,IAAIkD,KAAK,CACb,2FAC+B,CAChC;EACF,OAAM;IACLT,MAAM,GAAGzD,aAAa;IACtB0D,MAAM,GAAG,IAAI;IACbR,OAAO,GAAGjD,cAAc;IACxB2D,IAAI,GAAG5C,MAAM;EACd;EAED;EACA,IAAI2C,QAAQ,IAAIT,OAAO,KAAK,YAAY,EAAE;IACxCU,IAAI,GAAGD,QAAQ;IACfA,QAAQ,GAAGU,SAAS;EACrB;EAED,OAAO;IAAEX,MAAM;IAAED,MAAM,EAAEA,MAAM,CAACnD,WAAW,EAAE;IAAE4C,OAAO;IAAES,QAAQ;IAAEC;GAAM;AAC1E;;;;ACzFgB,SAAAU,mBAAmBA,CACjCC,MAAqB,EACrBC,IAAoB;EAEpB,OAAOC,YAAY,CAAC;IAClBjB,QAAQ,EAAEgB,IAAI,IAAJ,gBAAAA,IAAI,CAAEhB,QAAQ;IACxBkB,MAAM,EAAAC,QAAA,KACDH,IAAI,IAAJ,gBAAAA,IAAI,CAAEE,MAAM;MACfE,kBAAkB,EAAE;KACrB;IACDC,OAAO,EAAEC,oBAAoB,CAAC;MAAEC,MAAM,EAAEP,IAAI,IAAJ,gBAAAA,IAAI,CAAEO;IAAM,CAAE,CAAC;IACvDC,aAAa,EAAE,CAAAR,IAAI,IAAJ,gBAAAA,IAAI,CAAEQ,aAAa,KAAIC,kBAAkB,EAAE;IAC1DV,MAAM;IACNW,kBAAA,EAAAC;GACD,CAAC,CAACC,UAAU,EAAE;AACjB;AAEgB,SAAAC,gBAAgBA,CAC9Bd,MAAqB,EACrBC,IAAoB;EAEpB,OAAOC,YAAY,CAAC;IAClBjB,QAAQ,EAAEgB,IAAI,IAAJ,gBAAAA,IAAI,CAAEhB,QAAQ;IACxBkB,MAAM,EAAAC,QAAA,KACDH,IAAI,IAAJ,gBAAAA,IAAI,CAAEE,MAAM;MACfE,kBAAkB,EAAE;KACrB;IACDC,OAAO,EAAES,iBAAiB,CAAC;MAAEP,MAAM,EAAEP,IAAI,IAAJ,gBAAAA,IAAI,CAAEO;IAAM,CAAE,CAAC;IACpDC,aAAa,EAAE,CAAAR,IAAI,IAAJ,gBAAAA,IAAI,CAAEQ,aAAa,KAAIC,kBAAkB,EAAE;IAC1DV,MAAM;IACNW,kBAAA,EAAAC;GACD,CAAC,CAACC,UAAU,EAAE;AACjB;AAEA,SAASH,kBAAkBA,CAAA;EAAA,IAAAM,OAAA;EACzB,IAAIC,KAAK,IAAAD,OAAA,GAAGR,MAAM,KAAN,gBAAAQ,OAAA,CAAQE,2BAA2B;EAC/C,IAAID,KAAK,IAAIA,KAAK,CAACE,MAAM,EAAE;IACzBF,KAAK,GAAAb,QAAA,KACAa,KAAK;MACRE,MAAM,EAAEC,iBAAiB,CAACH,KAAK,CAACE,MAAM;KACvC;EACF;EACD,OAAOF,KAAK;AACd;AAEA,SAASG,iBAAiBA,CACxBD,MAAsC;EAEtC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIE,OAAO,GAAGrE,MAAM,CAACqE,OAAO,CAACF,MAAM,CAAC;EACpC,IAAIG,UAAU,GAAmC,EAAE;EACnD,KAAK,IAAI,CAAClE,GAAG,EAAEmE,GAAG,CAAC,IAAIF,OAAO,EAAE;IAC9B;IACA;IACA,IAAIE,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAAClE,GAAG,CAAC,GAAG,IAAIqE,aAAa,CACjCF,GAAG,CAACG,MAAM,EACVH,GAAG,CAACI,UAAU,EACdJ,GAAG,CAACK,IAAI,EACRL,GAAG,CAACM,QAAQ,KAAK,IAAI,CACtB;KACF,MAAM,IAAIN,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC;MACA,IAAID,GAAG,CAACO,SAAS,EAAE;QACjB,IAAIC,gBAAgB,GAAGvB,MAAM,CAACe,GAAG,CAACO,SAAS,CAAC;QAC5C,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF;YACA,IAAIC,KAAK,GAAG,IAAID,gBAAgB,CAACR,GAAG,CAACU,OAAO,CAAC;YAC7C;YACA;YACAD,KAAK,CAACE,KAAK,GAAG,EAAE;YAChBZ,UAAU,CAAClE,GAAG,CAAC,GAAG4E,KAAK;WACxB,CAAC,OAAOzD,CAAC,EAAE;YACV;UAAA;QAEH;MACF;MAED,IAAI+C,UAAU,CAAClE,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAI4E,KAAK,GAAG,IAAIrC,KAAK,CAAC4B,GAAG,CAACU,OAAO,CAAC;QAClC;QACA;QACAD,KAAK,CAACE,KAAK,GAAG,EAAE;QAChBZ,UAAU,CAAClE,GAAG,CAAC,GAAG4E,KAAK;MACxB;IACF,OAAM;MACLV,UAAU,CAAClE,GAAG,CAAC,GAAGmE,GAAG;IACtB;EACF;EACD,OAAOD,UAAU;AACnB;AAEA;AAEA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;AAoBE;AACF,MAAMa,gBAAgB,GAAG,iBAAiB;AAC1C,MAAMC,mBAAmB,GAAGC,KAAK,CAACF,gBAAgB,CAAC;AASnD;;AAEG;AACG,SAAUG,aAAaA,CAAAC,IAAA,EAKR;EAAA,IALS;IAC5BtD,QAAQ;IACRuD,QAAQ;IACRrC,MAAM;IACNK;EACmB,IAAA+B,IAAA;EACnB,IAAIE,UAAU,GAAGJ,KAAK,CAACK,MAAM,EAAkB;EAC/C,IAAID,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;IAC9BF,UAAU,CAACE,OAAO,GAAGpC,oBAAoB,CAAC;MAAEC,MAAM;MAAEoC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACtE;EAED,IAAItC,OAAO,GAAGmC,UAAU,CAACE,OAAO;EAChC,IAAI,CAAC1B,KAAK,EAAE4B,YAAY,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC;IACzC3D,MAAM,EAAEmB,OAAO,CAACnB,MAAM;IACtB4D,QAAQ,EAAEzC,OAAO,CAACyC;EACnB,EAAC;EACF,IAAI;IAAEC;EAAkB,CAAE,GAAG7C,MAAM,IAAI,EAAE;EACzC,IAAI8C,QAAQ,GAAGZ,KAAK,CAACa,WAAW,CAC7BC,QAAwD,IAAI;IAC3DH,kBAAkB,IAAIZ,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMS,YAAY,CAACM,QAAQ,CAAC,CAAC,GACjDN,YAAY,CAACM,QAAQ,CAAC;EAC5B,CAAC,EACD,CAACN,YAAY,EAAEG,kBAAkB,CAAC,CACnC;EAEDX,KAAK,CAACe,eAAe,CAAC,MAAM9C,OAAO,CAAC+C,MAAM,CAACJ,QAAQ,CAAC,EAAE,CAAC3C,OAAO,EAAE2C,QAAQ,CAAC,CAAC;EAE1E,oBACEZ,KAAA,CAAA/D,aAAA,CAACgF,MAAM;IACLrE,QAAQ,EAAEA,QAAQ;IAClBuD,QAAQ,EAAEA,QAAQ;IAClBO,QAAQ,EAAE9B,KAAK,CAAC8B,QAAQ;IACxBQ,cAAc,EAAEtC,KAAK,CAAC9B,MAAM;IAC5BqE,SAAS,EAAElD;EAAO,EAClB;AAEN;AASA;;;AAGG;AACG,SAAUmD,UAAUA,CAAAC,KAAA,EAKR;EAAA,IALS;IACzBzE,QAAQ;IACRuD,QAAQ;IACRrC,MAAM;IACNK;EACgB,IAAAkD,KAAA;EAChB,IAAIjB,UAAU,GAAGJ,KAAK,CAACK,MAAM,EAAe;EAC5C,IAAID,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;IAC9BF,UAAU,CAACE,OAAO,GAAG5B,iBAAiB,CAAC;MAAEP,MAAM;MAAEoC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACnE;EAED,IAAItC,OAAO,GAAGmC,UAAU,CAACE,OAAO;EAChC,IAAI,CAAC1B,KAAK,EAAE4B,YAAY,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC;IACzC3D,MAAM,EAAEmB,OAAO,CAACnB,MAAM;IACtB4D,QAAQ,EAAEzC,OAAO,CAACyC;EACnB,EAAC;EACF,IAAI;IAAEC;EAAkB,CAAE,GAAG7C,MAAM,IAAI,EAAE;EACzC,IAAI8C,QAAQ,GAAGZ,KAAK,CAACa,WAAW,CAC7BC,QAAwD,IAAI;IAC3DH,kBAAkB,IAAIZ,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMS,YAAY,CAACM,QAAQ,CAAC,CAAC,GACjDN,YAAY,CAACM,QAAQ,CAAC;EAC5B,CAAC,EACD,CAACN,YAAY,EAAEG,kBAAkB,CAAC,CACnC;EAEDX,KAAK,CAACe,eAAe,CAAC,MAAM9C,OAAO,CAAC+C,MAAM,CAACJ,QAAQ,CAAC,EAAE,CAAC3C,OAAO,EAAE2C,QAAQ,CAAC,CAAC;EAE1E,oBACEZ,KAAA,CAAA/D,aAAA,CAACgF,MAAM;IACLrE,QAAQ,EAAEA,QAAQ;IAClBuD,QAAQ,EAAEA,QAAQ;IAClBO,QAAQ,EAAE9B,KAAK,CAAC8B,QAAQ;IACxBQ,cAAc,EAAEtC,KAAK,CAAC9B,MAAM;IAC5BqE,SAAS,EAAElD;EAAO,EAClB;AAEN;AASA;;;;;AAKG;AACH,SAASqD,aAAaA,CAAAC,KAAA,EAKD;EAAA,IALE;IACrB3E,QAAQ;IACRuD,QAAQ;IACRrC,MAAM;IACNG;EACmB,IAAAsD,KAAA;EACnB,IAAI,CAAC3C,KAAK,EAAE4B,YAAY,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC;IACzC3D,MAAM,EAAEmB,OAAO,CAACnB,MAAM;IACtB4D,QAAQ,EAAEzC,OAAO,CAACyC;EACnB,EAAC;EACF,IAAI;IAAEC;EAAkB,CAAE,GAAG7C,MAAM,IAAI,EAAE;EACzC,IAAI8C,QAAQ,GAAGZ,KAAK,CAACa,WAAW,CAC7BC,QAAwD,IAAI;IAC3DH,kBAAkB,IAAIZ,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMS,YAAY,CAACM,QAAQ,CAAC,CAAC,GACjDN,YAAY,CAACM,QAAQ,CAAC;EAC5B,CAAC,EACD,CAACN,YAAY,EAAEG,kBAAkB,CAAC,CACnC;EAEDX,KAAK,CAACe,eAAe,CAAC,MAAM9C,OAAO,CAAC+C,MAAM,CAACJ,QAAQ,CAAC,EAAE,CAAC3C,OAAO,EAAE2C,QAAQ,CAAC,CAAC;EAE1E,oBACEZ,KAAA,CAAA/D,aAAA,CAACgF,MAAM;IACLrE,QAAQ,EAAEA,QAAQ;IAClBuD,QAAQ,EAAEA,QAAQ;IAClBO,QAAQ,EAAE9B,KAAK,CAAC8B,QAAQ;IACxBQ,cAAc,EAAEtC,KAAK,CAAC9B,MAAM;IAC5BqE,SAAS,EAAElD;EAAO,EAClB;AAEN;AAEA,IAAA1B,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACX6E,aAAa,CAACE,WAAW,GAAG,wBAAwB;AACrD;AAcD,MAAMC,SAAS,GACb,OAAOtD,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAACnC,QAAQ,KAAK,WAAW,IACtC,OAAOmC,MAAM,CAACnC,QAAQ,CAACC,aAAa,KAAK,WAAW;AAEtD,MAAMyF,kBAAkB,GAAG,+BAA+B;AAE1D;;AAEG;AACU,MAAAC,IAAI,gBAAG3B,KAAK,CAAC4B,UAAU,CAClC,SAASC,WAAWA,CAAAC,KAAA,EAYlBC,GAAG;EAAA,IAXH;MACEC,OAAO;MACPC,QAAQ;MACRC,cAAc;MACdC,OAAO;MACPvD,KAAK;MACLxE,MAAM;MACNgI,EAAE;MACFC;IACO,CACR,GAAAP,KAAA;IADIQ,IAAI,GAAAC,6BAAA,CAAAT,KAAA,EAAAU,SAAA;EAIT,IAAI;IAAE5F;EAAQ,CAAE,GAAGoD,KAAK,CAACyC,UAAU,CAACC,wBAAiB,CAAC;EAEtD;EACA,IAAIC,YAAY;EAChB,IAAIC,UAAU,GAAG,KAAK;EAEtB,IAAI,OAAOR,EAAE,KAAK,QAAQ,IAAIV,kBAAkB,CAACmB,IAAI,CAACT,EAAE,CAAC,EAAE;IACzD;IACAO,YAAY,GAAGP,EAAE;IAEjB;IACA,IAAIX,SAAS,EAAE;MACb,IAAI;QACF,IAAIqB,UAAU,GAAG,IAAIC,GAAG,CAAC5E,MAAM,CAACuC,QAAQ,CAACsC,IAAI,CAAC;QAC9C,IAAIC,SAAS,GAAGb,EAAE,CAACc,UAAU,CAAC,IAAI,CAAC,GAC/B,IAAIH,GAAG,CAACD,UAAU,CAACK,QAAQ,GAAGf,EAAE,CAAC,GACjC,IAAIW,GAAG,CAACX,EAAE,CAAC;QACf,IAAIgB,IAAI,GAAGjG,aAAa,CAAC8F,SAAS,CAACI,QAAQ,EAAEzG,QAAQ,CAAC;QAEtD,IAAIqG,SAAS,CAACK,MAAM,KAAKR,UAAU,CAACQ,MAAM,IAAIF,IAAI,IAAI,IAAI,EAAE;UAC1D;UACAhB,EAAE,GAAGgB,IAAI,GAAGH,SAAS,CAACM,MAAM,GAAGN,SAAS,CAACO,IAAI;QAC9C,OAAM;UACLZ,UAAU,GAAG,IAAI;QAClB;OACF,CAAC,OAAO1G,CAAC,EAAE;QACV;QACAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,KAAK,EACL,gBAAa0F,EAAE,iHACsC,CACtD;MACF;IACF;EACF;EAED;EACA,IAAIY,IAAI,GAAGS,OAAO,CAACrB,EAAE,EAAE;IAAEH;EAAU,EAAC;EAEpC,IAAIyB,eAAe,GAAGC,mBAAmB,CAACvB,EAAE,EAAE;IAC5CD,OAAO;IACPvD,KAAK;IACLxE,MAAM;IACNiI,kBAAkB;IAClBJ;EACD,EAAC;EACF,SAAS2B,WAAWA,CAClB9J,KAAsD;IAEtD,IAAIkI,OAAO,EAAEA,OAAO,CAAClI,KAAK,CAAC;IAC3B,IAAI,CAACA,KAAK,CAAC+J,gBAAgB,EAAE;MAC3BH,eAAe,CAAC5J,KAAK,CAAC;IACvB;EACH;EAEA;IACE;IACAkG,KAAA,CAAA/D,aAAA,MAAA8B,QAAA,KACMuE,IAAI;MACRU,IAAI,EAAEL,YAAY,IAAIK,IAAI;MAC1BhB,OAAO,EAAEY,UAAU,IAAIV,cAAc,GAAGF,OAAO,GAAG4B,WAAW;MAC7D7B,GAAG,EAAEA,GAAG;MACR3H,MAAM,EAAEA;KAAM;EAAA;AAGpB,CAAC;AAGH,IAAAmC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXkF,IAAI,CAACH,WAAW,GAAG,MAAM;AAC1B;AAuBD;;AAEG;AACU,MAAAsC,OAAO,gBAAG9D,KAAK,CAAC4B,UAAU,CACrC,SAASmC,cAAcA,CAAAC,KAAA,EAWrBjC,GAAG;EAAA,IAVH;MACE,cAAc,EAAEkC,eAAe,GAAG,MAAM;MACxCC,aAAa,GAAG,KAAK;MACrBC,SAAS,EAAEC,aAAa,GAAG,EAAE;MAC7BC,GAAG,GAAG,KAAK;MACXC,KAAK,EAAEC,SAAS;MAChBnC,EAAE;MACFjC;IACO,CACR,GAAA6D,KAAA;IADI1B,IAAI,GAAAC,6BAAA,CAAAyB,KAAA,EAAAQ,UAAA;EAIT,IAAIpB,IAAI,GAAGqB,eAAe,CAACrC,EAAE,EAAE;IAAEH,QAAQ,EAAEK,IAAI,CAACL;EAAQ,CAAE,CAAC;EAC3D,IAAIvB,QAAQ,GAAGgE,WAAW,EAAE;EAC5B,IAAIC,WAAW,GAAG3E,KAAK,CAACyC,UAAU,CAACmC,6BAAsB,CAAC;EAC1D,IAAI;IAAEzD;EAAS,CAAE,GAAGnB,KAAK,CAACyC,UAAU,CAACC,wBAAiB,CAAC;EAEvD,IAAImC,UAAU,GAAG1D,SAAS,CAAC2D,cAAc,GACrC3D,SAAS,CAAC2D,cAAc,CAAC1B,IAAI,CAAC,CAACC,QAAQ,GACvCD,IAAI,CAACC,QAAQ;EACjB,IAAI0B,gBAAgB,GAAGrE,QAAQ,CAAC2C,QAAQ;EACxC,IAAI2B,oBAAoB,GACtBL,WAAW,IAAIA,WAAW,CAACM,UAAU,IAAIN,WAAW,CAACM,UAAU,CAACvE,QAAQ,GACpEiE,WAAW,CAACM,UAAU,CAACvE,QAAQ,CAAC2C,QAAQ,GACxC,IAAI;EAEV,IAAI,CAACa,aAAa,EAAE;IAClBa,gBAAgB,GAAGA,gBAAgB,CAACrL,WAAW,EAAE;IACjDsL,oBAAoB,GAAGA,oBAAoB,GACvCA,oBAAoB,CAACtL,WAAW,EAAE,GAClC,IAAI;IACRmL,UAAU,GAAGA,UAAU,CAACnL,WAAW,EAAE;EACtC;EAED,IAAIwL,QAAQ,GACVH,gBAAgB,KAAKF,UAAU,IAC9B,CAACR,GAAG,IACHU,gBAAgB,CAAC7B,UAAU,CAAC2B,UAAU,CAAC,IACvCE,gBAAgB,CAACI,MAAM,CAACN,UAAU,CAACO,MAAM,CAAC,KAAK,GAAI;EAEvD,IAAIC,SAAS,GACXL,oBAAoB,IAAI,IAAI,KAC3BA,oBAAoB,KAAKH,UAAU,IACjC,CAACR,GAAG,IACHW,oBAAoB,CAAC9B,UAAU,CAAC2B,UAAU,CAAC,IAC3CG,oBAAoB,CAACG,MAAM,CAACN,UAAU,CAACO,MAAM,CAAC,KAAK,GAAI,CAAC;EAE9D,IAAIE,WAAW,GAAGJ,QAAQ,GAAGjB,eAAe,GAAGxG,SAAS;EAExD,IAAI0G,SAA6B;EACjC,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;IACvCD,SAAS,GAAGC,aAAa,CAAC;MAAEc,QAAQ;MAAEG;IAAW,EAAC;EACnD,OAAM;IACL;IACA;IACA;IACA;IACA;IACAlB,SAAS,GAAG,CACVC,aAAa,EACbc,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAC1BG,SAAS,GAAG,SAAS,GAAG,IAAI,CAC7B,CACEE,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC;EACb;EAED,IAAInB,KAAK,GACP,OAAOC,SAAS,KAAK,UAAU,GAC3BA,SAAS,CAAC;IAAEW,QAAQ;IAAEG;GAAW,CAAC,GAClCd,SAAS;EAEf,oBACEvE,KAAC,CAAA/D,aAAA,CAAA0F,IAAI,EAAA5D,QAAA,KACCuE,IAAI;IACM,gBAAAgD,WAAW;IACzBnB,SAAS,EAAEA,SAAS;IACpBpC,GAAG,EAAEA,GAAG;IACRuC,KAAK,EAAEA,KAAK;IACZlC,EAAE,EAAEA;EAAE,IAEL,OAAOjC,QAAQ,KAAK,UAAU,GAC3BA,QAAQ,CAAC;IAAE+E,QAAQ;IAAEG;GAAW,CAAC,GACjClF,QAAQ,CACP;AAEX,CAAC;AAGH,IAAA5D,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXqH,OAAO,CAACtC,WAAW,GAAG,SAAS;AAChC;AA+DD;;;;;AAKG;AACI,MAAMkE,IAAI,gBAAG1F,KAAK,CAAC4B,UAAU,CAClC,CAAC+D,KAAK,EAAE5D,GAAG,KAAI;EACb,IAAI6D,MAAM,GAAGC,SAAS,EAAE;EACxB,oBAAO7F,KAAC,CAAA/D,aAAA,CAAA6J,QAAQ,EAAA/H,QAAA,KAAK4H,KAAK;IAAEC,MAAM,EAAEA,MAAM;IAAE7D,GAAG,EAAEA;EAAG,GAAI;AAC1D,CAAC;AAGH,IAAAxF,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXiJ,IAAI,CAAClE,WAAW,GAAG,MAAM;AAC1B;AAcD,MAAMsE,QAAQ,gBAAG9F,KAAK,CAAC4B,UAAU,CAC/B,CAAAmE,KAAA,EAaEC,YAAY,KACV;EAAA,IAbF;MACE9D,cAAc;MACdC,OAAO;MACPvD,KAAK;MACL/B,MAAM,GAAGzD,aAAa;MACtB0D,MAAM;MACNmJ,QAAQ;MACRL,MAAM;MACN3D,QAAQ;MACRI;IACQ,CACT,GAAA0D,KAAA;IADIJ,KAAK,GAAApD,6BAAA,CAAAwD,KAAA,EAAAG,UAAA;EAIV,IAAIC,UAAU,GACZtJ,MAAM,CAACnD,WAAW,EAAE,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM;EACjD,IAAI0M,UAAU,GAAGC,aAAa,CAACvJ,MAAM,EAAE;IAAEmF;EAAU,EAAC;EACpD,IAAIqE,aAAa,GAA6CxM,KAAK,IAAI;IACrEmM,QAAQ,IAAIA,QAAQ,CAACnM,KAAK,CAAC;IAC3B,IAAIA,KAAK,CAAC+J,gBAAgB,EAAE;IAC5B/J,KAAK,CAACyM,cAAc,EAAE;IAEtB,IAAIC,SAAS,GAAI1M,KAAoC,CAAC2M,WAAW,CAC9DD,SAAqC;IAExC,IAAIE,YAAY,GACb,CAAAF,SAAS,IAAT,gBAAAA,SAAS,CAAEtJ,YAAY,CAAC,YAAY,CAAgC,KACrEL,MAAM;IAER+I,MAAM,CAACY,SAAS,IAAI1M,KAAK,CAAC6M,aAAa,EAAE;MACvC9J,MAAM,EAAE6J,YAAY;MACpBvE,OAAO;MACPvD,KAAK;MACLqD,QAAQ;MACRI;IACD,EAAC;GACH;EAED,oBACErC,KAAA,CAAA/D,aAAA,SAAA8B,QAAA;IACEgE,GAAG,EAAEiE,YAAY;IACjBnJ,MAAM,EAAEsJ,UAAU;IAClBrJ,MAAM,EAAEsJ,UAAU;IAClBH,QAAQ,EAAE/D,cAAc,GAAG+D,QAAQ,GAAGK;GAClC,EAAAX,KAAK,EACT;AAEN,CAAC,CACF;AAED,IAAApJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXqJ,QAAQ,CAACtE,WAAW,GAAG,UAAU;AAClC;AAOD;;;AAGG;SACaoF,iBAAiBA,CAAAC,KAAA,EAGR;EAAA,IAHS;IAChCC,MAAM;IACNC;EACuB,IAAAF,KAAA;EACvBG,oBAAoB,CAAC;IAAEF,MAAM;IAAEC;EAAU,CAAE,CAAC;EAC5C,OAAO,IAAI;AACb;AAEA,IAAAxK,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXmK,iBAAiB,CAACpF,WAAW,GAAG,mBAAmB;AACpD;AACD;AAEA;AACA;AACA;AAEA,IAAKyF,cAKJ;AALD,WAAKA,cAAc;EACjBA,cAAA,iDAA6C;EAC7CA,cAAA,2BAAuB;EACvBA,cAAA,yCAAqC;EACrCA,cAAA,6BAAyB;AAC3B,CAAC,EALIA,cAAc,KAAdA,cAAc,GAKlB;AAED,IAAKC,mBAGJ;AAHD,WAAKA,mBAAmB;EACtBA,mBAAA,+BAA2B;EAC3BA,mBAAA,iDAA6C;AAC/C,CAAC,EAHIA,mBAAmB,KAAnBA,mBAAmB,GAGvB;AAED,SAASC,yBAAyBA,CAChCC,QAA8C;EAE9C,OAAUA,QAAQ;AACpB;AAEA,SAASC,oBAAoBA,CAACD,QAAwB;EACpD,IAAIE,GAAG,GAAGtH,KAAK,CAACyC,UAAU,CAAC8E,wBAAiB,CAAC;EAC7C,CAAUD,GAAG,GAAA/K,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAb,eAAA+K,gBAAS,QAAML,yBAAyB,CAACC,QAAQ,CAAC,IAAlDI,gBAAS;EACT,OAAOF,GAAG;AACZ;AAEA,SAASG,kBAAkBA,CAACL,QAA6B;EACvD,IAAIxI,KAAK,GAAGoB,KAAK,CAACyC,UAAU,CAACmC,6BAAsB,CAAC;EACpD,CAAUhG,KAAK,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAf,eAAA+K,gBAAS,QAAQL,yBAAyB,CAACC,QAAQ,CAAC,IAApDI,gBAAS;EACT,OAAO5I,KAAK;AACd;AAEA;;;;AAIG;SACa+E,mBAAmBA,CACjCvB,EAAM,EAAAsF,KAAA,EAaA;EAAA,IAZN;IACEtN,MAAM;IACN+H,OAAO,EAAEwF,WAAW;IACpB/I,KAAK;IACLyD,kBAAkB;IAClBJ;yBAOE,EAAE,GAAAyF,KAAA;EAEN,IAAIE,QAAQ,GAAGC,WAAW,EAAE;EAC5B,IAAInH,QAAQ,GAAGgE,WAAW,EAAE;EAC5B,IAAItB,IAAI,GAAGqB,eAAe,CAACrC,EAAE,EAAE;IAAEH;EAAU,EAAC;EAE5C,OAAOjC,KAAK,CAACa,WAAW,CACrB/G,KAAsC,IAAI;IACzC,IAAIK,sBAAsB,CAACL,KAAK,EAAEM,MAAM,CAAC,EAAE;MACzCN,KAAK,CAACyM,cAAc,EAAE;MAEtB;MACA;MACA,IAAIpE,OAAO,GACTwF,WAAW,KAAKlK,SAAS,GACrBkK,WAAW,GACXG,UAAU,CAACpH,QAAQ,CAAC,KAAKoH,UAAU,CAAC1E,IAAI,CAAC;MAE/CwE,QAAQ,CAACxF,EAAE,EAAE;QAAED,OAAO;QAAEvD,KAAK;QAAEyD,kBAAkB;QAAEJ;MAAQ,CAAE,CAAC;IAC/D;GACF,EACD,CACEvB,QAAQ,EACRkH,QAAQ,EACRxE,IAAI,EACJuE,WAAW,EACX/I,KAAK,EACLxE,MAAM,EACNgI,EAAE,EACFC,kBAAkB,EAClBJ,QAAQ,CACT,CACF;AACH;AAEA;;;AAGG;AACG,SAAU8F,eAAeA,CAC7BC,WAAiC;EAEjCzL,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,OAAOlC,eAAe,KAAK,WAAW,EACtC,6IACqE,GACX,2GACR,wEACqB,GACG,mJACA,UACjE,CACV;EAED,IAAIyN,sBAAsB,GAAGjI,KAAK,CAACK,MAAM,CAAC/F,kBAAkB,CAAC0N,WAAW,CAAC,CAAC;EAC1E,IAAIE,qBAAqB,GAAGlI,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC;EAE/C,IAAIK,QAAQ,GAAGgE,WAAW,EAAE;EAC5B,IAAInJ,YAAY,GAAGyE,KAAK,CAACmI,OAAO,CAC9B;EACE;EACA;EACA;EACA/M,0BAA0B,CACxBsF,QAAQ,CAAC6C,MAAM,EACf2E,qBAAqB,CAAC5H,OAAO,GAAG,IAAI,GAAG2H,sBAAsB,CAAC3H,OAAO,CACtE,EACH,CAACI,QAAQ,CAAC6C,MAAM,CAAC,CAClB;EAED,IAAIqE,QAAQ,GAAGC,WAAW,EAAE;EAC5B,IAAIO,eAAe,GAAGpI,KAAK,CAACa,WAAW,CACrC,CAACwH,QAAQ,EAAEC,eAAe,KAAI;IAC5B,MAAMC,eAAe,GAAGjO,kBAAkB,CACxC,OAAO+N,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC9M,YAAY,CAAC,GAAG8M,QAAQ,CACnE;IACDH,qBAAqB,CAAC5H,OAAO,GAAG,IAAI;IACpCsH,QAAQ,CAAC,GAAG,GAAGW,eAAe,EAAED,eAAe,CAAC;EAClD,CAAC,EACD,CAACV,QAAQ,EAAErM,YAAY,CAAC,CACzB;EAED,OAAO,CAACA,YAAY,EAAE6M,eAAe,CAAC;AACxC;AA2CA,SAASI,4BAA4BA,CAAA;EACnC,IAAI,OAAOxM,QAAQ,KAAK,WAAW,EAAE;IACnC,MAAM,IAAIsB,KAAK,CACb,mDAAmD,GACjD,8DAA8D,CACjE;EACF;AACH;AAEA;;;AAGG;SACauI,SAASA,CAAA;EACvB,IAAI;IAAE4C;EAAM,CAAE,GAAGpB,oBAAoB,CAACJ,cAAc,CAACyB,SAAS,CAAC;EAC/D,IAAI;IAAE9L;EAAQ,CAAE,GAAGoD,KAAK,CAACyC,UAAU,CAACC,wBAAiB,CAAC;EACtD,IAAIiG,cAAc,GAAGC,iBAAU,EAAE;EAEjC,OAAO5I,KAAK,CAACa,WAAW,CACtB,UAACzG,MAAM,EAAEyO,OAAO,EAAS;IAAA,IAAhBA,OAAO;MAAPA,OAAO,GAAG,EAAE;IAAA;IACnBL,4BAA4B,EAAE;IAE9B,IAAI;MAAE1L,MAAM;MAAED,MAAM;MAAEP,OAAO;MAAES,QAAQ;MAAEC;IAAI,CAAE,GAAGL,qBAAqB,CACrEvC,MAAM,EACNwC,QAAQ,CACT;IAED6L,MAAM,CAACb,QAAQ,CAACiB,OAAO,CAAC/L,MAAM,IAAIA,MAAM,EAAE;MACxCuF,kBAAkB,EAAEwG,OAAO,CAACxG,kBAAkB;MAC9CtF,QAAQ;MACRC,IAAI;MACJmJ,UAAU,EAAE0C,OAAO,CAAChM,MAAM,IAAKA,MAAyB;MACxDiM,WAAW,EAAED,OAAO,CAACvM,OAAO,IAAKA,OAAuB;MACxD6F,OAAO,EAAE0G,OAAO,CAAC1G,OAAO;MACxBvD,KAAK,EAAEiK,OAAO,CAACjK,KAAK;MACpBmK,WAAW,EAAEJ;IACd,EAAC;GACH,EACD,CAACF,MAAM,EAAE7L,QAAQ,EAAE+L,cAAc,CAAC,CACnC;AACH;AAEA;;AAEG;AACH,SAASK,gBAAgBA,CACvBC,UAAkB,EAClBC,cAAsB;EAEtB,IAAI;IAAET;EAAM,CAAE,GAAGpB,oBAAoB,CAACJ,cAAc,CAACkC,gBAAgB,CAAC;EACtE,IAAI;IAAEvM;EAAQ,CAAE,GAAGoD,KAAK,CAACyC,UAAU,CAACC,wBAAiB,CAAC;EAEtD,OAAO1C,KAAK,CAACa,WAAW,CACtB,UAACzG,MAAM,EAAEyO,OAAO,EAAS;IAAA,IAAhBA,OAAO;MAAPA,OAAO,GAAG,EAAE;IAAA;IACnBL,4BAA4B,EAAE;IAE9B,IAAI;MAAE1L,MAAM;MAAED,MAAM;MAAEP,OAAO;MAAES,QAAQ;MAAEC;IAAI,CAAE,GAAGL,qBAAqB,CACrEvC,MAAM,EACNwC,QAAQ,CACT;IAED,EACEsM,cAAc,IAAI,IAAI,IAAA3M,OAAA,CAAAC,GAAA,CAAAC,QAAA,KADxB,eAAA+K,gBAAS,CAEP,8CAAuC,IAFzCA,gBAAS;IAITiB,MAAM,CAACW,KAAK,CAACH,UAAU,EAAEC,cAAc,EAAEL,OAAO,CAAC/L,MAAM,IAAIA,MAAM,EAAE;MACjEuF,kBAAkB,EAAEwG,OAAO,CAACxG,kBAAkB;MAC9CtF,QAAQ;MACRC,IAAI;MACJmJ,UAAU,EAAE0C,OAAO,CAAChM,MAAM,IAAKA,MAAyB;MACxDiM,WAAW,EAAED,OAAO,CAACvM,OAAO,IAAKA;IAClC,EAAC;GACH,EACD,CAACmM,MAAM,EAAE7L,QAAQ,EAAEqM,UAAU,EAAEC,cAAc,CAAC,CAC/C;AACH;AAEA;AACA;AACM,SAAU7C,aAAaA,CAC3BvJ,MAAe,EAAAuM,MAAA,EACsC;EAAA,IAArD;IAAEpH;0BAAiD,EAAE,GAAAoH,MAAA;EAErD,IAAI;IAAEzM;EAAQ,CAAE,GAAGoD,KAAK,CAACyC,UAAU,CAACC,wBAAiB,CAAC;EACtD,IAAI4G,YAAY,GAAGtJ,KAAK,CAACyC,UAAU,CAAC8G,mBAAY,CAAC;EACjD,CAAUD,YAAY,GAAA/M,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAtB+K,gBAAS,QAAe,kDAAkD,IAA1EA,gBAAS;EAET,IAAI,CAACgC,KAAK,CAAC,GAAGF,YAAY,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5C;EACA;EACA,IAAItG,IAAI,GAAArF,QAAA,CAAQ,IAAA0G,eAAe,CAAC3H,MAAM,GAAGA,MAAM,GAAG,GAAG,EAAE;IAAEmF;EAAQ,CAAE,CAAC,CAAE;EAEtE;EACA;EACA;EACA;EACA;EACA,IAAIvB,QAAQ,GAAGgE,WAAW,EAAE;EAC5B,IAAI5H,MAAM,IAAI,IAAI,EAAE;IAClB;IACA;IACAsG,IAAI,CAACG,MAAM,GAAG7C,QAAQ,CAAC6C,MAAM;IAE7B;IACA;IACA;IACA,IAAIiG,KAAK,CAACG,KAAK,CAACC,KAAK,EAAE;MACrB,IAAIC,MAAM,GAAG,IAAIrP,eAAe,CAAC4I,IAAI,CAACG,MAAM,CAAC;MAC7CsG,MAAM,CAACC,MAAM,CAAC,OAAO,CAAC;MACtB1G,IAAI,CAACG,MAAM,GAAGsG,MAAM,CAACE,QAAQ,EAAE,SAAOF,MAAM,CAACE,QAAQ,EAAE,GAAK,EAAE;IAC/D;EACF;EAED,IAAI,CAAC,CAACjN,MAAM,IAAIA,MAAM,KAAK,GAAG,KAAK0M,KAAK,CAACG,KAAK,CAACC,KAAK,EAAE;IACpDxG,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACG,MAAM,GACrBH,IAAI,CAACG,MAAM,CAACpB,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ;EACb;EAED;EACA;EACA;EACA;EACA,IAAIvF,QAAQ,KAAK,GAAG,EAAE;IACpBwG,IAAI,CAACC,QAAQ,GACXD,IAAI,CAACC,QAAQ,KAAK,GAAG,GAAGzG,QAAQ,GAAGoN,SAAS,CAAC,CAACpN,QAAQ,EAAEwG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC1E;EAED,OAAOyE,UAAU,CAAC1E,IAAI,CAAC;AACzB;AAEA,SAAS6G,iBAAiBA,CAAChB,UAAkB,EAAEiB,OAAe;EAC5D,IAAIC,WAAW,gBAAGnK,KAAK,CAAC4B,UAAU,CAChC,CAAC+D,KAAK,EAAE5D,GAAG,KAAI;IACb,IAAI6D,MAAM,GAAGoD,gBAAgB,CAACC,UAAU,EAAEiB,OAAO,CAAC;IAClD,oBAAOlK,KAAC,CAAA/D,aAAA,CAAA6J,QAAQ,EAAA/H,QAAA,KAAK4H,KAAK;MAAE5D,GAAG,EAAEA,GAAG;MAAE6D,MAAM,EAAEA;IAAM,GAAI;EAC1D,CAAC,CACF;EACD,IAAArJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;IACX0N,WAAW,CAAC3I,WAAW,GAAG,cAAc;EACzC;EACD,OAAO2I,WAAW;AACpB;AAEA,IAAIC,SAAS,GAAG,CAAC;AAQjB;;;AAGG;SACaC,UAAUA,CAAA;EAAA,IAAAC,cAAA;EACxB,IAAI;IAAE7B;EAAM,CAAE,GAAGpB,oBAAoB,CAACJ,cAAc,CAACsD,UAAU,CAAC;EAEhE,IAAIZ,KAAK,GAAG3J,KAAK,CAACyC,UAAU,CAAC8G,mBAAY,CAAC;EAC1C,CAAUI,KAAK,GAAApN,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAf+K,gBAAS,2DAATA,gBAAS;EAET,IAAI0C,OAAO,IAAAI,cAAA,GAAGX,KAAK,CAACF,OAAO,CAACE,KAAK,CAACF,OAAO,CAACrE,MAAM,GAAG,CAAC,CAAC,qBAAvCkF,cAAA,CAAyCX,KAAK,CAACa,EAAE;EAC/D,EACEN,OAAO,IAAI,IAAI,IAAA3N,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADjB+K,gBAAS,gFAATA,gBAAS;EAKT,IAAI,CAACyB,UAAU,CAAC,GAAGjJ,KAAK,CAACS,QAAQ,CAAC,MAAMgK,MAAM,CAAC,EAAEL,SAAS,CAAC,CAAC;EAC5D,IAAI,CAAC1E,IAAI,CAAC,GAAG1F,KAAK,CAACS,QAAQ,CAAC,MAAK;IAC/B,CAAUyJ,OAAO,GAAA3N,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAjB+K,gBAAS,qDAATA,gBAAS;IACT,OAAOyC,iBAAiB,CAAChB,UAAU,EAAEiB,OAAO,CAAC;EAC/C,CAAC,CAAC;EACF,IAAI,CAACQ,IAAI,CAAC,GAAG1K,KAAK,CAACS,QAAQ,CAAC,MAAOuC,IAAY,IAAI;IACjD,CAAUyF,MAAM,GAAAlM,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAhB+K,gBAAS,QAAS,wCAAwC,IAA1DA,gBAAS;IACT,CAAU0C,OAAO,GAAA3N,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAjB+K,gBAAS,QAAU,yCAAyC,IAA5DA,gBAAS;IACTiB,MAAM,CAACW,KAAK,CAACH,UAAU,EAAEiB,OAAO,EAAElH,IAAI,CAAC;EACzC,CAAC,CAAC;EACF,IAAI4C,MAAM,GAAGoD,gBAAgB,CAACC,UAAU,EAAEiB,OAAO,CAAC;EAElD,IAAIS,OAAO,GAAGlC,MAAM,CAACmC,UAAU,CAAQ3B,UAAU,CAAC;EAElD,IAAI4B,qBAAqB,GAAG7K,KAAK,CAACmI,OAAO,CACvC,MAAApK,QAAA;IACE2H,IAAI;IACJE,MAAM;IACN8E;EAAI,GACDC,OAAO,CACV,EACF,CAACA,OAAO,EAAEjF,IAAI,EAAEE,MAAM,EAAE8E,IAAI,CAAC,CAC9B;EAED1K,KAAK,CAAC8K,SAAS,CAAC,MAAK;IACnB;IACA;IACA;IACA,OAAO,MAAK;MACV,IAAI,CAACrC,MAAM,EAAE;QACXsC,OAAO,CAACC,IAAI,oDAAoD,CAAC;QACjE;MACD;MACDvC,MAAM,CAACwC,aAAa,CAAChC,UAAU,CAAC;KACjC;EACH,CAAC,EAAE,CAACR,MAAM,EAAEQ,UAAU,CAAC,CAAC;EAExB,OAAO4B,qBAAqB;AAC9B;AAEA;;;AAGG;SACaK,WAAWA,CAAA;EACzB,IAAItM,KAAK,GAAG6I,kBAAkB,CAACP,mBAAmB,CAACiE,WAAW,CAAC;EAC/D,OAAO,CAAC,GAAGvM,KAAK,CAACwM,QAAQ,CAACC,MAAM,EAAE,CAAC;AACrC;AAEA,MAAMC,8BAA8B,GAAG,+BAA+B;AACtE,IAAIC,oBAAoB,GAA2B,EAAE;AAErD;;AAEG;AACH,SAASvE,oBAAoBA,CAAAwE,MAAA,EAMvB;EAAA,IANwB;IAC5B1E,MAAM;IACNC;0BAIE,EAAE,GAAAyE,MAAA;EACJ,IAAI;IAAE/C;EAAM,CAAE,GAAGpB,oBAAoB,CAACJ,cAAc,CAACwE,oBAAoB,CAAC;EAC1E,IAAI;IAAEC,qBAAqB;IAAErJ;EAAoB,IAAGoF,kBAAkB,CACpEP,mBAAmB,CAACuE,oBAAoB,CACzC;EACD,IAAI;IAAE7O;EAAQ,CAAE,GAAGoD,KAAK,CAACyC,UAAU,CAACC,wBAAiB,CAAC;EACtD,IAAIhC,QAAQ,GAAGgE,WAAW,EAAE;EAC5B,IAAI+E,OAAO,GAAGkC,UAAU,EAAE;EAC1B,IAAI1G,UAAU,GAAG2G,aAAa,EAAE;EAEhC;EACA5L,KAAK,CAAC8K,SAAS,CAAC,MAAK;IACnB3M,MAAM,CAACF,OAAO,CAAC4N,iBAAiB,GAAG,QAAQ;IAC3C,OAAO,MAAK;MACV1N,MAAM,CAACF,OAAO,CAAC4N,iBAAiB,GAAG,MAAM;KAC1C;GACF,EAAE,EAAE,CAAC;EAEN;EACAC,WAAW,CACT9L,KAAK,CAACa,WAAW,CAAC,MAAK;IACrB,IAAIoE,UAAU,CAACrG,KAAK,KAAK,MAAM,EAAE;MAC/B,IAAI7D,GAAG,GAAG,CAAC+L,MAAM,GAAGA,MAAM,CAACpG,QAAQ,EAAE+I,OAAO,CAAC,GAAG,IAAI,KAAK/I,QAAQ,CAAC3F,GAAG;MACrEwQ,oBAAoB,CAACxQ,GAAG,CAAC,GAAGoD,MAAM,CAAC4N,OAAO;IAC3C;IACDC,cAAc,CAACC,OAAO,CACpBlF,UAAU,IAAIuE,8BAA8B,EAC5CY,IAAI,CAACC,SAAS,CAACZ,oBAAoB,CAAC,CACrC;IACDpN,MAAM,CAACF,OAAO,CAAC4N,iBAAiB,GAAG,MAAM;EAC3C,CAAC,EAAE,CAAC9E,UAAU,EAAED,MAAM,EAAE7B,UAAU,CAACrG,KAAK,EAAE8B,QAAQ,EAAE+I,OAAO,CAAC,CAAC,CAC9D;EAED;EACA,IAAI,OAAOzN,QAAQ,KAAK,WAAW,EAAE;IACnC;IACAgE,KAAK,CAACe,eAAe,CAAC,MAAK;MACzB,IAAI;QACF,IAAIqL,gBAAgB,GAAGJ,cAAc,CAACK,OAAO,CAC3CtF,UAAU,IAAIuE,8BAA8B,CAC7C;QACD,IAAIc,gBAAgB,EAAE;UACpBb,oBAAoB,GAAGW,IAAI,CAACI,KAAK,CAACF,gBAAgB,CAAC;QACpD;OACF,CAAC,OAAOlQ,CAAC,EAAE;QACV;MAAA;IAEJ,CAAC,EAAE,CAAC6K,UAAU,CAAC,CAAC;IAEhB;IACA;IACA/G,KAAK,CAACe,eAAe,CAAC,MAAK;MACzB,IAAIwL,qBAAqB,GACvBzF,MAAM,IAAIlK,QAAQ,KAAK,GAAG,GACtB,CAAC8D,QAAQ,EAAE+I,OAAO,KAChB3C,MAAM;MAAA;MACJ/I,QAAA,KAEK2C,QAAQ;QACX2C,QAAQ,EACNlG,aAAa,CAACuD,QAAQ,CAAC2C,QAAQ,EAAEzG,QAAQ,CAAC,IAC1C8D,QAAQ,CAAC2C;OAEb,GAAAoG,OAAO,CACR,GACH3C,MAAM;MACZ,IAAI0F,wBAAwB,GAAG/D,MAAM,IAAN,gBAAAA,MAAM,CAAEgE,uBAAuB,CAC5DlB,oBAAoB,EACpB,MAAMpN,MAAM,CAAC4N,OAAO,EACpBQ,qBAAqB,CACtB;MACD,OAAO,MAAMC,wBAAwB,IAAIA,wBAAwB,EAAE;KACpE,EAAE,CAAC/D,MAAM,EAAE7L,QAAQ,EAAEkK,MAAM,CAAC,CAAC;IAE9B;IACA;IACA9G,KAAK,CAACe,eAAe,CAAC,MAAK;MACzB;MACA,IAAI2K,qBAAqB,KAAK,KAAK,EAAE;QACnC;MACD;MAED;MACA,IAAI,OAAOA,qBAAqB,KAAK,QAAQ,EAAE;QAC7CvN,MAAM,CAACuO,QAAQ,CAAC,CAAC,EAAEhB,qBAAqB,CAAC;QACzC;MACD;MAED;MACA,IAAIhL,QAAQ,CAAC8C,IAAI,EAAE;QACjB,IAAImJ,EAAE,GAAG3Q,QAAQ,CAAC4Q,cAAc,CAC9BC,kBAAkB,CAACnM,QAAQ,CAAC8C,IAAI,CAACkG,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3C;QACD,IAAIiD,EAAE,EAAE;UACNA,EAAE,CAACG,cAAc,EAAE;UACnB;QACD;MACF;MAED;MACA,IAAIzK,kBAAkB,KAAK,IAAI,EAAE;QAC/B;MACD;MAED;MACAlE,MAAM,CAACuO,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;KACtB,EAAE,CAAChM,QAAQ,EAAEgL,qBAAqB,EAAErJ,kBAAkB,CAAC,CAAC;EAC1D;AACH;AAIA;;;;;;;AAOG;AACa,SAAA0K,eAAeA,CAC7BC,QAA2C,EAC3CnE,OAA+B;EAE/B,IAAI;IAAEoE;EAAO,CAAE,GAAGpE,OAAO,IAAI,EAAE;EAC/B7I,KAAK,CAAC8K,SAAS,CAAC,MAAK;IACnB,IAAIlN,IAAI,GAAGqP,OAAO,IAAI,IAAI,GAAG;MAAEA;IAAS,IAAGxP,SAAS;IACpDU,MAAM,CAAC+O,gBAAgB,CAAC,cAAc,EAAEF,QAAQ,EAAEpP,IAAI,CAAC;IACvD,OAAO,MAAK;MACVO,MAAM,CAACgP,mBAAmB,CAAC,cAAc,EAAEH,QAAQ,EAAEpP,IAAI,CAAC;KAC3D;EACH,CAAC,EAAE,CAACoP,QAAQ,EAAEC,OAAO,CAAC,CAAC;AACzB;AAEA;;;;;;;AAOG;AACH,SAASnB,WAAWA,CAClBkB,QAA6C,EAC7CnE,OAA+B;EAE/B,IAAI;IAAEoE;EAAO,CAAE,GAAGpE,OAAO,IAAI,EAAE;EAC/B7I,KAAK,CAAC8K,SAAS,CAAC,MAAK;IACnB,IAAIlN,IAAI,GAAGqP,OAAO,IAAI,IAAI,GAAG;MAAEA;IAAS,IAAGxP,SAAS;IACpDU,MAAM,CAAC+O,gBAAgB,CAAC,UAAU,EAAEF,QAAQ,EAAEpP,IAAI,CAAC;IACnD,OAAO,MAAK;MACVO,MAAM,CAACgP,mBAAmB,CAAC,UAAU,EAAEH,QAAQ,EAAEpP,IAAI,CAAC;KACvD;EACH,CAAC,EAAE,CAACoP,QAAQ,EAAEC,OAAO,CAAC,CAAC;AACzB;AAEA;;;;;;;AAOG;AACH,SAASG,SAASA,CAAAC,KAAA,EAAsD;EAAA,IAArD;IAAEC,IAAI;IAAE1N;EAA6C,IAAAyN,KAAA;EACtE,IAAIE,OAAO,GAAGC,mBAAU,CAACF,IAAI,CAAC;EAE9BtN,KAAK,CAAC8K,SAAS,CAAC,MAAK;IACnB,IAAIyC,OAAO,CAAC3O,KAAK,KAAK,SAAS,EAAE;MAC/B,IAAI6O,OAAO,GAAGtP,MAAM,CAACuP,OAAO,CAAC9N,OAAO,CAAC;MACrC,IAAI6N,OAAO,EAAE;QACX;QACA;QACA;QACAE,UAAU,CAACJ,OAAO,CAACE,OAAO,EAAE,CAAC,CAAC;MAC/B,OAAM;QACLF,OAAO,CAACK,KAAK,EAAE;MAChB;IACF;EACH,CAAC,EAAE,CAACL,OAAO,EAAE3N,OAAO,CAAC,CAAC;EAEtBI,KAAK,CAAC8K,SAAS,CAAC,MAAK;IACnB,IAAIyC,OAAO,CAAC3O,KAAK,KAAK,SAAS,IAAI,CAAC0O,IAAI,EAAE;MACxCC,OAAO,CAACK,KAAK,EAAE;IAChB;EACH,CAAC,EAAE,CAACL,OAAO,EAAED,IAAI,CAAC,CAAC;AACrB;AAIA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}