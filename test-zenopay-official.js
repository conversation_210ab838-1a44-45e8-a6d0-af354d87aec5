const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

async function testZenoPayOfficialAPI() {
  console.log('🔧 Testing ZenoPay API with Official Documentation Format...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log('✅ ZENOPAY_API_KEY:', process.env.ZENOPAY_API_KEY ? 'SET' : '❌ MISSING');
  console.log('✅ ZENOPAY_ACCOUNT_ID:', process.env.ZENOPAY_ACCOUNT_ID ? 'SET' : '❌ MISSING');
  console.log('✅ ZENOPAY_WEBHOOK_URL:', process.env.ZENOPAY_WEBHOOK_URL || '❌ MISSING');
  console.log('');

  // Test data exactly as per documentation
  const testData = {
    "order_id": "3rer407fe-3ee8-4525-456f-ccb95de38250",
    "buyer_email": "<EMAIL>",
    "buyer_name": "<PERSON>",
    "buyer_phone": "**********",
    "amount": 1000,
    "webhook_url": process.env.ZENOPAY_WEBHOOK_URL
  };

  console.log('📤 Testing Payment Request (Official Format)...');
  console.log('🌐 Endpoint: https://zenoapi.com/api/payments/mobile_money_tanzania');
  console.log('🔑 Authentication: x-api-key header');
  console.log('📋 Data:', JSON.stringify(testData, null, 2));
  console.log('');

  try {
    // Test payment request
    const response = await axios.post('https://zenoapi.com/api/payments/mobile_money_tanzania', testData, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 30000
    });

    console.log('✅ Payment Request Success!');
    console.log('📊 Response Status:', response.status);
    console.log('📋 Response Data:', JSON.stringify(response.data, null, 2));
    console.log('');

    // Test order status check
    if (response.data.order_id) {
      console.log('🔍 Testing Order Status Check...');
      console.log('🌐 Endpoint: https://zenoapi.com/api/payments/order-status');
      console.log('📋 Order ID:', response.data.order_id);
      
      try {
        const statusResponse = await axios.get(`https://zenoapi.com/api/payments/order-status?order_id=${response.data.order_id}`, {
          headers: {
            'x-api-key': process.env.ZENOPAY_API_KEY
          },
          timeout: 15000
        });

        console.log('✅ Order Status Check Success!');
        console.log('📊 Status Response:', JSON.stringify(statusResponse.data, null, 2));
        
      } catch (statusError) {
        console.log('⚠️ Order Status Check Failed (this is normal for new orders):');
        if (statusError.response) {
          console.log('Status:', statusError.response.status);
          console.log('Response:', JSON.stringify(statusError.response.data, null, 2));
        } else {
          console.log('Error:', statusError.message);
        }
      }
    }

  } catch (error) {
    console.log('❌ Payment Request Failed:');
    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📋 Response:', JSON.stringify(error.response.data, null, 2));
      
      // Analyze specific errors
      const errorData = error.response.data;
      
      if (error.response.status === 403) {
        console.log('\n🔑 API Key Issue Analysis:');
        if (errorData.message && errorData.message.includes('Invalid API key')) {
          console.log('❌ Invalid API Key Error');
          console.log('🔧 Solutions:');
          console.log('  1. Verify API key with ZenoPay support');
          console.log('  2. Check if account needs activation');
          console.log('  3. Ensure API key is for correct environment (sandbox/production)');
          console.log('  4. Request IP whitelisting if required');
        }
      } else if (error.response.status === 400) {
        console.log('\n📝 Request Format Issue:');
        console.log('❌ Bad Request - Check data format');
        console.log('🔧 Verify:');
        console.log('  1. Phone number format (07XXXXXXXX)');
        console.log('  2. Email format');
        console.log('  3. Amount is positive number');
        console.log('  4. Order ID is unique string');
      }
      
    } else {
      console.log('🌐 Network Error:', error.message);
    }
  }

  console.log('\n📞 Support Information:');
  console.log('📧 Email: <EMAIL>');
  console.log('🌐 Website: https://zenoapi.com');
  console.log('🆔 Account ID: zp38236');
  console.log('🔑 API Key: XsW6ND7NmcwIIqCh2iYoSjp5LtVQX1WHEz_FAV3hIlY');

  console.log('\n📋 Next Steps:');
  console.log('1. If API key is invalid, contact ZenoPay support');
  console.log('2. Request IP whitelisting if needed');
  console.log('3. Verify account status and activation');
  console.log('4. Test with small amounts once API is working');
}

// Test server connection first
async function testServerConnection() {
  try {
    const response = await axios.get('http://localhost:5000');
    console.log('✅ Server is running on port 5000\n');
    return true;
  } catch (error) {
    console.log('❌ Server is not running on port 5000');
    console.log('   Please start the server first\n');
    return false;
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 ZenoPay API Integration Test\n');
  console.log('📖 Based on Official Documentation\n');
  
  const serverRunning = await testServerConnection();
  if (!serverRunning) {
    return;
  }
  
  await testZenoPayOfficialAPI();
}

runAllTests().catch(console.error);
