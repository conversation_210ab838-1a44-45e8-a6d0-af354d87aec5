{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Profile\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./index.css\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { getUserInfo, updateUserInfo, updateUserPhoto } from \"../../../apicalls/users\";\nimport { Form, message, Modal, Input, Button } from \"antd\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport SubscriptionModal from \"../../../components/SubscriptionModal/SubscriptionModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _userRankingStats$use, _userRankingStats$use2, _userRankingStats$use3, _userRankingStats$use4, _userRankingStats$use5;\n  const [userDetails, setUserDetails] = useState(null);\n  const [rankingData, setRankingData] = useState(null);\n  const [userRanking, setUserRanking] = useState(null);\n  const [userRankingStats, setUserRankingStats] = useState(null);\n  const [edit, setEdit] = useState(false);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    school: \"\",\n    level: \"\",\n    class_: \"\",\n    phoneNumber: \"\"\n  });\n  const [profileImage, setProfileImage] = useState(null);\n  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      message.error(error.message);\n      dispatch(HideLoading());\n    }\n  };\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userDetails._id));\n    setUserRanking(Ranking);\n  };\n\n  // Fetch user ranking data from the ranking system\n  const fetchUserRankingData = async () => {\n    if (!(userDetails !== null && userDetails !== void 0 && userDetails._id)) return;\n    try {\n      dispatch(ShowLoading());\n\n      // Get user's ranking position and nearby users\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\n      if (rankingResponse.success) {\n        setUserRankingStats(rankingResponse.data);\n      }\n\n      // Also get the full leaderboard to find user's position\n      const leaderboardResponse = await getXPLeaderboard({\n        limit: 1000,\n        levelFilter: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'all'\n      });\n      if (leaderboardResponse.success) {\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\n        if (userIndex >= 0) {\n          const userWithRank = {\n            ...leaderboardResponse.data[userIndex],\n            rank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length\n          };\n          setUserRankingStats(prev => ({\n            ...prev,\n            userRank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length,\n            user: userWithRank\n          }));\n        }\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Error fetching ranking data:', error);\n    }\n  };\n  useEffect(() => {\n    if (rankingData && userDetails) {\n      getUserStats();\n    }\n  }, [rankingData, userDetails]);\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        setUserDetails(response.data);\n        setFormData({\n          name: response.data.name || \"\",\n          email: response.data.email || \"\",\n          school: response.data.school || \"\",\n          class_: response.data.class || \"\",\n          level: response.data.level || \"\",\n          phoneNumber: response.data.phoneNumber || \"\"\n        });\n        if (response.data.profileImage) {\n          setProfileImage(response.data.profileImage);\n        }\n        fetchReports();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === \"phoneNumber\" && value.length > 10) return;\n    if (name === \"level\" && value !== (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) && value !== \"\") {\n      setPendingLevelChange(value);\n      setShowLevelChangeModal(true);\n      return;\n    }\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n      ...(name === \"level\" ? {\n        class_: \"\"\n      } : {})\n    }));\n  };\n  const discardChanges = () => {\n    setFormData({\n      name: userDetails.name,\n      email: userDetails.email,\n      school: userDetails.school,\n      class_: userDetails.class,\n      level: userDetails.level,\n      phoneNumber: userDetails.phoneNumber\n    });\n    setEdit(false);\n  };\n  const handleUpdate = async ({\n    skipOTP\n  } = {}) => {\n    console.log('🔍 Current formData:', formData);\n    console.log('🔍 Current userDetails:', userDetails);\n\n    // Validation\n    if (!formData.name || formData.name.trim() === \"\") {\n      console.log('❌ Validation failed: name is empty');\n      return message.error(\"Please enter your name.\");\n    }\n    if (!formData.class_ || formData.class_.trim() === \"\") {\n      console.log('❌ Validation failed: class is empty');\n      return message.error(\"Please select a class.\");\n    }\n    if (!formData.level || formData.level.trim() === \"\") {\n      console.log('❌ Validation failed: level is empty');\n      return message.error(\"Please select a level.\");\n    }\n    // Email validation (optional - only validate if provided)\n    if (formData.email && formData.email.trim() !== \"\") {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        return message.error(\"Please enter a valid email address.\");\n      }\n    }\n\n    // Since email is optional in username-based system, skip OTP verification\n    // Users can update their email directly without verification\n\n    dispatch(ShowLoading());\n    try {\n      // Prepare update payload - only include email if it has a value\n      const updatePayload = {\n        ...formData,\n        userId: userDetails._id\n      };\n\n      // Only include email if it's provided and not empty\n      if (formData.email && formData.email.trim() !== \"\") {\n        updatePayload.email = formData.email.trim();\n      } else if (userDetails !== null && userDetails !== void 0 && userDetails.email) {\n        updatePayload.email = userDetails.email;\n      }\n      console.log('📤 Sending update data:', updatePayload);\n      const response = await updateUserInfo(updatePayload);\n      console.log('📥 Server response:', response);\n      if (response.success) {\n        message.success(response.message);\n        setEdit(false);\n        getUserData();\n        if (response.levelChanged) {\n          setTimeout(() => window.location.reload(), 2000);\n        }\n      } else {\n        console.error('❌ Update failed:', response);\n        message.error(response.message || \"Failed to update profile. Please try again.\");\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Update error:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || \"An unexpected error occurred.\";\n      message.error(`Update failed: ${errorMessage}`);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleLevelChangeConfirm = () => {\n    setFormData(prev => ({\n      ...prev,\n      level: pendingLevelChange,\n      class_: \"\"\n    }));\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleLevelChangeCancel = () => {\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleImageChange = async e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        message.error('Please select a valid image file');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        message.error('Image size should be less than 5MB');\n        return;\n      }\n      setProfileImage(file);\n\n      // Show preview\n      const reader = new FileReader();\n      reader.onloadend = () => setImagePreview(reader.result);\n      reader.readAsDataURL(file);\n\n      // Auto-upload the image\n      const data = new FormData();\n      data.append(\"profileImage\", file);\n      dispatch(ShowLoading());\n      try {\n        const response = await updateUserPhoto(data);\n        dispatch(HideLoading());\n        if (response.success) {\n          message.success(\"Profile picture updated successfully!\");\n          getUserData(); // Refresh user data to show new image\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message || \"Failed to update profile picture\");\n      }\n    }\n  };\n  const handleImageUpload = async () => {\n    const data = new FormData();\n    data.append(\"profileImage\", profileImage);\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserPhoto(data);\n      if (response.success) {\n        message.success(\"Photo updated successfully!\");\n        getUserData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Load user data on component mount\n  useEffect(() => {\n    getUserData();\n  }, []);\n\n  // Load ranking data when user details are available\n  useEffect(() => {\n    if (userDetails) {\n      fetchUserRankingData();\n    }\n  }, [userDetails]);\n\n  // Ensure formData is synchronized with userDetails\n  useEffect(() => {\n    if (userDetails) {\n      setFormData({\n        name: userDetails.name || \"\",\n        email: userDetails.email || \"\",\n        // Email is optional\n        school: userDetails.school || \"\",\n        class_: userDetails.class || \"\",\n        level: userDetails.level || \"\",\n        phoneNumber: userDetails.phoneNumber || \"\"\n      });\n    }\n  }, [userDetails]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold text-gray-900 mb-2\",\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your account settings and preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mt-8 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: userDetails,\n                size: \"3xl\",\n                showOnlineStatus: true,\n                onClick: () => document.getElementById('profileImageInput').click(),\n                className: \"hover:scale-105 transition-transform duration-200\",\n                style: {\n                  width: '120px',\n                  height: '120px',\n                  border: '4px solid #BFDBFE',\n                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\",\n                onClick: () => document.getElementById('profileImageInput').click(),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 13a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"profileImageInput\",\n                type: \"file\",\n                accept: \"image/*\",\n                className: \"hidden\",\n                onChange: handleImageChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-blue-600 font-medium\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-green-600 font-medium\",\n                    children: \"Username\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900 truncate max-w-[150px]\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.username) || 'username'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-purple-600 font-medium\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), userRankingStats && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-yellow-600 font-medium\",\n                    children: \"Rank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [\"#\", userRankingStats.userRank || 'N/A', userRankingStats.totalUsers && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"/\", userRankingStats.totalUsers]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-orange-600 font-medium\",\n                    children: \"Total XP\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use = userRankingStats.user) === null || _userRankingStats$use === void 0 ? void 0 : (_userRankingStats$use2 = _userRankingStats$use.totalXP) === null || _userRankingStats$use2 === void 0 ? void 0 : _userRankingStats$use2.toLocaleString()) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-indigo-600 font-medium\",\n                    children: \"Avg Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [((_userRankingStats$use3 = userRankingStats.user) === null || _userRankingStats$use3 === void 0 ? void 0 : _userRankingStats$use3.averageScore) || '0', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-pink-600 font-medium\",\n                    children: \"Quizzes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use4 = userRankingStats.user) === null || _userRankingStats$use4 === void 0 ? void 0 : _userRankingStats$use4.totalQuizzesTaken) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-teal-600 font-medium\",\n                    children: \"Streak\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use5 = userRankingStats.user) === null || _userRankingStats$use5 === void 0 ? void 0 : _userRankingStats$use5.currentStreak) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), !edit ?\n            /*#__PURE__*/\n            // View Mode\n            _jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Username\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.username) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: userDetails !== null && userDetails !== void 0 && userDetails.email ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: userDetails.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 478,\n                        columnNumber: 29\n                      }, this), userDetails.email.includes('@brainwave.temp') && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full\",\n                        children: \"Auto-generated\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 480,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"No email set\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 487,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: async () => {\n                          const timestamp = Date.now();\n                          const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\n                          setFormData(prev => ({\n                            ...prev,\n                            email: autoEmail\n                          }));\n                          message.info('Auto-generated email created. Click \"Save Changes\" to update.');\n                        },\n                        className: \"text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full hover:bg-green-200 transition-colors\",\n                        children: \"Generate Email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 488,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // Edit Mode\n            _jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter your name\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Username\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-100 rounded-lg border text-gray-600\",\n                    children: [(userDetails === null || userDetails === void 0 ? void 0 : userDetails.username) || 'Not available', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500 block mt-1\",\n                      children: \"Username cannot be changed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email (Optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      name: \"email\",\n                      value: formData.email || \"\",\n                      onChange: handleChange,\n                      className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-24\",\n                      placeholder: \"Enter your email (optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 25\n                    }, this), (!formData.email || formData.email === '') && /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        const timestamp = Date.now();\n                        const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\n                        setFormData(prev => ({\n                          ...prev,\n                          email: autoEmail\n                        }));\n                        message.success('Auto-generated email created!');\n                      },\n                      className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 transition-colors\",\n                      children: \"Auto-Gen\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 23\n                  }, this), formData.email && formData.email.includes('@brainwave.temp') && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-blue-600 mt-1\",\n                    children: \"\\uD83D\\uDCE7 This is an auto-generated email. You can change it to your real email if you prefer.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"school\",\n                    value: formData.school,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter your school\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"level\",\n                    value: formData.level,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Level\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Primary\",\n                      children: \"Primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Secondary\",\n                      children: \"Secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Advance\",\n                      children: \"Advance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"class_\",\n                    value: formData.class_,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Class\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 623,\n                      columnNumber: 25\n                    }, this), formData.level === \"Primary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"1\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 626,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"2\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 627,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"3\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"4\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 629,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"5\",\n                        children: \"5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"6\",\n                        children: \"6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 631,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"7\",\n                        children: \"7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), formData.level === \"Secondary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"1\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 637,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"2\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 638,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"3\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 639,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"4\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), formData.level === \"Advance\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"5\",\n                        children: \"5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"6\",\n                        children: \"6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"phoneNumber\",\n                    value: formData.phoneNumber,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter phone number\",\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 flex justify-center gap-4\",\n              children: !edit ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  // Ensure formData is properly initialized with current user data\n                  setFormData({\n                    name: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || \"\",\n                    email: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || \"\",\n                    school: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || \"\",\n                    class_: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || \"\",\n                    level: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || \"\",\n                    phoneNumber: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || \"\"\n                  });\n                  setEdit(true);\n                },\n                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\",\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: discardChanges,\n                  className: \"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium\",\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleUpdate,\n                  className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\",\n                  children: \"Save Changes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    console.log('🔍 Debug - Current formData:', formData);\n                    console.log('🔍 Debug - Current userDetails:', userDetails);\n                    alert(`FormData: ${JSON.stringify(formData, null, 2)}`);\n                  },\n                  className: \"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm\",\n                  children: \"Debug\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      id: \"profileImageInput\",\n      accept: \"image/*\",\n      onChange: handleImageChange,\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Confirm Level Change\",\n      open: showLevelChangeModal,\n      onOk: handleLevelChangeConfirm,\n      onCancel: () => {\n        setShowLevelChangeModal(false);\n        setPendingLevelChange(null);\n      },\n      okText: \"Confirm\",\n      cancelText: \"Cancel\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Are you sure you want to change your level to \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: pendingLevelChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 57\n        }, this), \"?\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-orange-600 text-sm mt-2\",\n        children: \"Note: Changing your level will reset your class selection and you'll only have access to content for the new level.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 350,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"8eFm49OoX2fk36cHAjqHwZJ99tw=\", false, function () {\n  return [useDispatch];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Page<PERSON><PERSON>le", "getUserInfo", "updateUserInfo", "updateUserPhoto", "Form", "message", "Modal", "Input", "<PERSON><PERSON>", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "getAllReportsForRanking", "getUserRanking", "getXPLeaderboard", "ProfilePicture", "SubscriptionModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Profile", "_s", "_userRankingStats$use", "_userRankingStats$use2", "_userRankingStats$use3", "_userRankingStats$use4", "_userRankingStats$use5", "userDetails", "setUserDetails", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userRankingStats", "setUserRankingStats", "edit", "setEdit", "imagePreview", "setImagePreview", "formData", "setFormData", "name", "email", "school", "level", "class_", "phoneNumber", "profileImage", "setProfileImage", "showSubscriptionModal", "setShowSubscriptionModal", "showLevelChangeModal", "setShowLevelChangeModal", "pendingLevelChange", "setPendingLevelChange", "dispatch", "fetchReports", "response", "success", "data", "error", "getUserStats", "Ranking", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "fetchUserRankingData", "rankingResponse", "leaderboardResponse", "limit", "levelFilter", "userIndex", "findIndex", "userWithRank", "rank", "totalUsers", "length", "prev", "userRank", "console", "getUserData", "class", "localStorage", "getItem", "handleChange", "e", "value", "target", "discardChanges", "handleUpdate", "skipOTP", "log", "trim", "emailRegex", "test", "updatePayload", "levelChanged", "setTimeout", "window", "location", "reload", "_error$response", "_error$response$data", "errorMessage", "handleLevelChangeConfirm", "handleLevelChangeCancel", "handleImageChange", "file", "files", "type", "startsWith", "size", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "FormData", "append", "handleImageUpload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showOnlineStatus", "onClick", "document", "getElementById", "click", "style", "width", "height", "border", "boxShadow", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "id", "accept", "onChange", "username", "totalXP", "toLocaleString", "averageScore", "totalQuizzesTaken", "currentStreak", "timestamp", "Date", "now", "autoEmail", "info", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "alert", "JSON", "stringify", "display", "title", "open", "onOk", "onCancel", "okText", "cancelText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Profile/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport SubscriptionModal from \"../../../components/SubscriptionModal/SubscriptionModal\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [userRankingStats, setUserRankingStats] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  // Fetch user ranking data from the ranking system\r\n  const fetchUserRankingData = async () => {\r\n    if (!userDetails?._id) return;\r\n\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user's ranking position and nearby users\r\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\r\n\r\n      if (rankingResponse.success) {\r\n        setUserRankingStats(rankingResponse.data);\r\n      }\r\n\r\n      // Also get the full leaderboard to find user's position\r\n      const leaderboardResponse = await getXPLeaderboard({\r\n        limit: 1000,\r\n        levelFilter: userDetails?.level || 'all'\r\n      });\r\n\r\n      if (leaderboardResponse.success) {\r\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\r\n        if (userIndex >= 0) {\r\n          const userWithRank = {\r\n            ...leaderboardResponse.data[userIndex],\r\n            rank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length\r\n          };\r\n          setUserRankingStats(prev => ({\r\n            ...prev,\r\n            userRank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length,\r\n            user: userWithRank\r\n          }));\r\n        }\r\n      }\r\n\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Error fetching ranking data:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    console.log('🔍 Current formData:', formData);\r\n    console.log('🔍 Current userDetails:', userDetails);\r\n\r\n    // Validation\r\n    if (!formData.name || formData.name.trim() === \"\") {\r\n      console.log('❌ Validation failed: name is empty');\r\n      return message.error(\"Please enter your name.\");\r\n    }\r\n    if (!formData.class_ || formData.class_.trim() === \"\") {\r\n      console.log('❌ Validation failed: class is empty');\r\n      return message.error(\"Please select a class.\");\r\n    }\r\n    if (!formData.level || formData.level.trim() === \"\") {\r\n      console.log('❌ Validation failed: level is empty');\r\n      return message.error(\"Please select a level.\");\r\n    }\r\n    // Email validation (optional - only validate if provided)\r\n    if (formData.email && formData.email.trim() !== \"\") {\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      if (!emailRegex.test(formData.email)) {\r\n        return message.error(\"Please enter a valid email address.\");\r\n      }\r\n    }\r\n\r\n    // Since email is optional in username-based system, skip OTP verification\r\n    // Users can update their email directly without verification\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      // Prepare update payload - only include email if it has a value\r\n      const updatePayload = {\r\n        ...formData,\r\n        userId: userDetails._id,\r\n      };\r\n\r\n      // Only include email if it's provided and not empty\r\n      if (formData.email && formData.email.trim() !== \"\") {\r\n        updatePayload.email = formData.email.trim();\r\n      } else if (userDetails?.email) {\r\n        updatePayload.email = userDetails.email;\r\n      }\r\n\r\n      console.log('📤 Sending update data:', updatePayload);\r\n\r\n      const response = await updateUserInfo(updatePayload);\r\n\r\n      console.log('📥 Server response:', response);\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        console.error('❌ Update failed:', response);\r\n        message.error(response.message || \"Failed to update profile. Please try again.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Update error:', error);\r\n      const errorMessage = error.response?.data?.message || error.message || \"An unexpected error occurred.\";\r\n      message.error(`Update failed: ${errorMessage}`);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('Please select a valid image file');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        message.error('Image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setProfileImage(file);\r\n\r\n      // Show preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n\r\n      // Auto-upload the image\r\n      const data = new FormData();\r\n      data.append(\"profileImage\", file);\r\n      dispatch(ShowLoading());\r\n\r\n      try {\r\n        const response = await updateUserPhoto(data);\r\n        dispatch(HideLoading());\r\n        if (response.success) {\r\n          message.success(\"Profile picture updated successfully!\");\r\n          getUserData(); // Refresh user data to show new image\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message || \"Failed to update profile picture\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Load user data on component mount\r\n  useEffect(() => {\r\n    getUserData();\r\n  }, []);\r\n\r\n  // Load ranking data when user details are available\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      fetchUserRankingData();\r\n    }\r\n  }, [userDetails]);\r\n\r\n  // Ensure formData is synchronized with userDetails\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      setFormData({\r\n        name: userDetails.name || \"\",\r\n        email: userDetails.email || \"\", // Email is optional\r\n        school: userDetails.school || \"\",\r\n        class_: userDetails.class || \"\",\r\n        level: userDetails.level || \"\",\r\n        phoneNumber: userDetails.phoneNumber || \"\",\r\n      });\r\n    }\r\n  }, [userDetails]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Profile</h1>\r\n            <p className=\"text-gray-600\">Manage your account settings and preferences</p>\r\n\r\n            {/* Profile Picture with Online Status - Centered Below Header */}\r\n            <div className=\"relative mt-8 flex justify-center\">\r\n              <div className=\"relative\">\r\n                <ProfilePicture\r\n                  user={userDetails}\r\n                  size=\"3xl\"\r\n                  showOnlineStatus={true}\r\n                  onClick={() => document.getElementById('profileImageInput').click()}\r\n                  className=\"hover:scale-105 transition-transform duration-200\"\r\n                  style={{\r\n                    width: '120px',\r\n                    height: '120px',\r\n                    border: '4px solid #BFDBFE',\r\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\r\n                  }}\r\n                />\r\n\r\n                {/* Camera Icon Overlay */}\r\n                <div className=\"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\"\r\n                     onClick={() => document.getElementById('profileImageInput').click()}>\r\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  </svg>\r\n                </div>\r\n\r\n                {/* Hidden File Input */}\r\n                <input\r\n                  id=\"profileImageInput\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleImageChange}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Profile Content */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n            <div className=\"p-8\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                {/* User Info - Horizontal Layout */}\r\n                <div className=\"flex flex-wrap justify-center gap-4 text-center mb-6\">\r\n                  <div className=\"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-blue-600 font-medium\">Name</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.name || 'User'}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-green-600 font-medium\">Username</p>\r\n                    <p className=\"text-lg font-bold text-gray-900 truncate max-w-[150px]\">{userDetails?.username || 'username'}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-purple-600 font-medium\">Class</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.class || 'N/A'}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Ranking Stats - Horizontal Layout */}\r\n                {userRankingStats && (\r\n                  <div className=\"flex flex-wrap justify-center gap-4 text-center\">\r\n                    <div className=\"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-yellow-600 font-medium\">Rank</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        #{userRankingStats.userRank || 'N/A'}\r\n                        {userRankingStats.totalUsers && (\r\n                          <span className=\"text-sm text-gray-500\">/{userRankingStats.totalUsers}</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-orange-600 font-medium\">Total XP</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalXP?.toLocaleString() || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-indigo-600 font-medium\">Avg Score</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.averageScore || '0'}%\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-pink-600 font-medium\">Quizzes</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalQuizzesTaken || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-teal-600 font-medium\">Streak</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.currentStreak || '0'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Profile Details */}\r\n              {!edit ? (\r\n                // View Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.name || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.username || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.email ? (\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span>{userDetails.email}</span>\r\n                            {userDetails.email.includes('@brainwave.temp') && (\r\n                              <span className=\"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full\">\r\n                                Auto-generated\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span className=\"text-gray-500\">No email set</span>\r\n                            <button\r\n                              onClick={async () => {\r\n                                const timestamp = Date.now();\r\n                                const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\r\n                                setFormData(prev => ({ ...prev, email: autoEmail }));\r\n                                message.info('Auto-generated email created. Click \"Save Changes\" to update.');\r\n                              }}\r\n                              className=\"text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full hover:bg-green-200 transition-colors\"\r\n                            >\r\n                              Generate Email\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.school || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.level || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.class || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.phoneNumber || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                // Edit Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name *</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"name\"\r\n                        value={formData.name}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your name\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                      <div className=\"p-3 bg-gray-100 rounded-lg border text-gray-600\">\r\n                        {userDetails?.username || 'Not available'}\r\n                        <span className=\"text-xs text-gray-500 block mt-1\">Username cannot be changed</span>\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email (Optional)</label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"email\"\r\n                          name=\"email\"\r\n                          value={formData.email || \"\"}\r\n                          onChange={handleChange}\r\n                          className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-24\"\r\n                          placeholder=\"Enter your email (optional)\"\r\n                        />\r\n                        {(!formData.email || formData.email === '') && (\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              const timestamp = Date.now();\r\n                              const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\r\n                              setFormData(prev => ({ ...prev, email: autoEmail }));\r\n                              message.success('Auto-generated email created!');\r\n                            }}\r\n                            className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 transition-colors\"\r\n                          >\r\n                            Auto-Gen\r\n                          </button>\r\n                        )}\r\n                      </div>\r\n                      {formData.email && formData.email.includes('@brainwave.temp') && (\r\n                        <p className=\"text-xs text-blue-600 mt-1\">\r\n                          📧 This is an auto-generated email. You can change it to your real email if you prefer.\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"school\"\r\n                        value={formData.school}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your school\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level *</label>\r\n                      <select\r\n                        name=\"level\"\r\n                        value={formData.level}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Level</option>\r\n                        <option value=\"Primary\">Primary</option>\r\n                        <option value=\"Secondary\">Secondary</option>\r\n                        <option value=\"Advance\">Advance</option>\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class *</label>\r\n                      <select\r\n                        name=\"class_\"\r\n                        value={formData.class_}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Class</option>\r\n                        {formData.level === \"Primary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                            <option value=\"7\">7</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Secondary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Advance\" && (\r\n                          <>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                          </>\r\n                        )}\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <input\r\n                        type=\"tel\"\r\n                        name=\"phoneNumber\"\r\n                        value={formData.phoneNumber}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter phone number\"\r\n                        maxLength=\"10\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"mt-8 flex justify-center gap-4\">\r\n                {!edit ? (\r\n                  <button\r\n                    onClick={() => {\r\n                      // Ensure formData is properly initialized with current user data\r\n                      setFormData({\r\n                        name: userDetails?.name || \"\",\r\n                        email: userDetails?.email || \"\",\r\n                        school: userDetails?.school || \"\",\r\n                        class_: userDetails?.class || \"\",\r\n                        level: userDetails?.level || \"\",\r\n                        phoneNumber: userDetails?.phoneNumber || \"\",\r\n                      });\r\n                      setEdit(true);\r\n                    }}\r\n                    className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\"\r\n                  >\r\n                    Edit Profile\r\n                  </button>\r\n                ) : (\r\n                  <>\r\n                    <button\r\n                      onClick={discardChanges}\r\n                      className=\"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                    <button\r\n                      onClick={handleUpdate}\r\n                      className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Save Changes\r\n                    </button>\r\n                    {/* Debug button - remove in production */}\r\n                    <button\r\n                      onClick={() => {\r\n                        console.log('🔍 Debug - Current formData:', formData);\r\n                        console.log('🔍 Debug - Current userDetails:', userDetails);\r\n                        alert(`FormData: ${JSON.stringify(formData, null, 2)}`);\r\n                      }}\r\n                      className=\"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm\"\r\n                    >\r\n                      Debug\r\n                    </button>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hidden file input for profile image upload */}\r\n      <input\r\n        type=\"file\"\r\n        id=\"profileImageInput\"\r\n        accept=\"image/*\"\r\n        onChange={handleImageChange}\r\n        style={{ display: 'none' }}\r\n      />\r\n\r\n      {/* Level Change Confirmation Modal */}\r\n      <Modal\r\n        title=\"Confirm Level Change\"\r\n        open={showLevelChangeModal}\r\n        onOk={handleLevelChangeConfirm}\r\n        onCancel={() => {\r\n          setShowLevelChangeModal(false);\r\n          setPendingLevelChange(null);\r\n        }}\r\n        okText=\"Confirm\"\r\n        cancelText=\"Cancel\"\r\n      >\r\n        <p>\r\n          Are you sure you want to change your level to <strong>{pendingLevelChange}</strong>?\r\n        </p>\r\n        <p className=\"text-orange-600 text-sm mt-2\">\r\n          Note: Changing your level will reset your class selection and you'll only have access to content for the new level.\r\n        </p>\r\n      </Modal>\r\n\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SACEC,WAAW,EACXC,cAAc,EACdC,eAAe,QACV,yBAAyB;AAChC,SAASC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,2BAA2B;AACrG,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,iBAAiB,MAAM,yDAAyD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExF,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACsD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACwD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM0D,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAE9B,MAAMiD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9C,uBAAuB,CAAC,CAAC;MAChD,IAAI8C,QAAQ,CAACC,OAAO,EAAE;QACpB5B,cAAc,CAAC2B,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLxD,OAAO,CAACyD,KAAK,CAACH,QAAQ,CAACtD,OAAO,CAAC;MACjC;MACAoD,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACdzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;MAC5BoD,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMoD,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAGjC,WAAW,CACxBkC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACrBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACnB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC3C,WAAW,CAAC4C,GAAG,CAAC,CAAC;IAC/DvC,cAAc,CAAC8B,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,EAAC7C,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE4C,GAAG,GAAE;IAEvB,IAAI;MACFhB,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAM+D,eAAe,GAAG,MAAM7D,cAAc,CAACe,WAAW,CAAC4C,GAAG,EAAE,CAAC,CAAC;MAEhE,IAAIE,eAAe,CAACf,OAAO,EAAE;QAC3BxB,mBAAmB,CAACuC,eAAe,CAACd,IAAI,CAAC;MAC3C;;MAEA;MACA,MAAMe,mBAAmB,GAAG,MAAM7D,gBAAgB,CAAC;QACjD8D,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,CAAAjD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;MACrC,CAAC,CAAC;MAEF,IAAI8B,mBAAmB,CAAChB,OAAO,EAAE;QAC/B,MAAMmB,SAAS,GAAGH,mBAAmB,CAACf,IAAI,CAACmB,SAAS,CAACd,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK5C,WAAW,CAAC4C,GAAG,CAAC;QAC1F,IAAIM,SAAS,IAAI,CAAC,EAAE;UAClB,MAAME,YAAY,GAAG;YACnB,GAAGL,mBAAmB,CAACf,IAAI,CAACkB,SAAS,CAAC;YACtCG,IAAI,EAAEH,SAAS,GAAG,CAAC;YACnBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB;UACvC,CAAC;UACDhD,mBAAmB,CAACiD,IAAI,KAAK;YAC3B,GAAGA,IAAI;YACPC,QAAQ,EAAEP,SAAS,GAAG,CAAC;YACvBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB,MAAM;YAC3ClB,IAAI,EAAEe;UACR,CAAC,CAAC,CAAC;QACL;MACF;MAEAxB,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACdL,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;MACvB4E,OAAO,CAACzB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAEDhE,SAAS,CAAC,MAAM;IACd,IAAIiC,WAAW,IAAIF,WAAW,EAAE;MAC9BkC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAChC,WAAW,EAAEF,WAAW,CAAC,CAAC;EAE9B,MAAM2D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B/B,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM+C,QAAQ,GAAG,MAAM1D,WAAW,CAAC,CAAC;MACpC,IAAI0D,QAAQ,CAACC,OAAO,EAAE;QACpB9B,cAAc,CAAC6B,QAAQ,CAACE,IAAI,CAAC;QAC7BnB,WAAW,CAAC;UACVC,IAAI,EAAEgB,QAAQ,CAACE,IAAI,CAAClB,IAAI,IAAI,EAAE;UAC9BC,KAAK,EAAEe,QAAQ,CAACE,IAAI,CAACjB,KAAK,IAAI,EAAE;UAChCC,MAAM,EAAEc,QAAQ,CAACE,IAAI,CAAChB,MAAM,IAAI,EAAE;UAClCE,MAAM,EAAEY,QAAQ,CAACE,IAAI,CAAC4B,KAAK,IAAI,EAAE;UACjC3C,KAAK,EAAEa,QAAQ,CAACE,IAAI,CAACf,KAAK,IAAI,EAAE;UAChCE,WAAW,EAAEW,QAAQ,CAACE,IAAI,CAACb,WAAW,IAAI;QAC5C,CAAC,CAAC;QACF,IAAIW,QAAQ,CAACE,IAAI,CAACZ,YAAY,EAAE;UAC9BC,eAAe,CAACS,QAAQ,CAACE,IAAI,CAACZ,YAAY,CAAC;QAC7C;QACAS,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLrD,OAAO,CAACyD,KAAK,CAACH,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACdzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRoD,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd,IAAI4F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElD,IAAI;MAAEmD;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAIpD,IAAI,KAAK,aAAa,IAAImD,KAAK,CAACV,MAAM,GAAG,EAAE,EAAE;IACjD,IAAIzC,IAAI,KAAK,OAAO,IAAImD,KAAK,MAAKjE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAIgD,KAAK,KAAK,EAAE,EAAE;MACpEtC,qBAAqB,CAACsC,KAAK,CAAC;MAC5BxC,uBAAuB,CAAC,IAAI,CAAC;MAC7B;IACF;IACAZ,WAAW,CAAE2C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAAC1C,IAAI,GAAGmD,KAAK;MACb,IAAInD,IAAI,KAAK,OAAO,GAAG;QAAEI,MAAM,EAAE;MAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiD,cAAc,GAAGA,CAAA,KAAM;IAC3BtD,WAAW,CAAC;MACVC,IAAI,EAAEd,WAAW,CAACc,IAAI;MACtBC,KAAK,EAAEf,WAAW,CAACe,KAAK;MACxBC,MAAM,EAAEhB,WAAW,CAACgB,MAAM;MAC1BE,MAAM,EAAElB,WAAW,CAAC4D,KAAK;MACzB3C,KAAK,EAAEjB,WAAW,CAACiB,KAAK;MACxBE,WAAW,EAAEnB,WAAW,CAACmB;IAC3B,CAAC,CAAC;IACFV,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAID,MAAM2D,YAAY,GAAG,MAAAA,CAAO;IAAEC;EAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;IAC/CX,OAAO,CAACY,GAAG,CAAC,sBAAsB,EAAE1D,QAAQ,CAAC;IAC7C8C,OAAO,CAACY,GAAG,CAAC,yBAAyB,EAAEtE,WAAW,CAAC;;IAEnD;IACA,IAAI,CAACY,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACyD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjDb,OAAO,CAACY,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAO9F,OAAO,CAACyD,KAAK,CAAC,yBAAyB,CAAC;IACjD;IACA,IAAI,CAACrB,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAACM,MAAM,CAACqD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrDb,OAAO,CAACY,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO9F,OAAO,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IAChD;IACA,IAAI,CAACrB,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnDb,OAAO,CAACY,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO9F,OAAO,CAACyD,KAAK,CAAC,wBAAwB,CAAC;IAChD;IACA;IACA,IAAIrB,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAClD,MAAMC,UAAU,GAAG,4BAA4B;MAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC7D,QAAQ,CAACG,KAAK,CAAC,EAAE;QACpC,OAAOvC,OAAO,CAACyD,KAAK,CAAC,qCAAqC,CAAC;MAC7D;IACF;;IAEA;IACA;;IAEAL,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF;MACA,MAAM2F,aAAa,GAAG;QACpB,GAAG9D,QAAQ;QACX8B,MAAM,EAAE1C,WAAW,CAAC4C;MACtB,CAAC;;MAED;MACA,IAAIhC,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClDG,aAAa,CAAC3D,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAACwD,IAAI,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAIvE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEe,KAAK,EAAE;QAC7B2D,aAAa,CAAC3D,KAAK,GAAGf,WAAW,CAACe,KAAK;MACzC;MAEA2C,OAAO,CAACY,GAAG,CAAC,yBAAyB,EAAEI,aAAa,CAAC;MAErD,MAAM5C,QAAQ,GAAG,MAAMzD,cAAc,CAACqG,aAAa,CAAC;MAEpDhB,OAAO,CAACY,GAAG,CAAC,qBAAqB,EAAExC,QAAQ,CAAC;MAE5C,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAACD,QAAQ,CAACtD,OAAO,CAAC;QACjCiC,OAAO,CAAC,KAAK,CAAC;QACdkD,WAAW,CAAC,CAAC;QACb,IAAI7B,QAAQ,CAAC6C,YAAY,EAAE;UACzBC,UAAU,CAAC,MAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,MAAM;QACLrB,OAAO,CAACzB,KAAK,CAAC,kBAAkB,EAAEH,QAAQ,CAAC;QAC3CtD,OAAO,CAACyD,KAAK,CAACH,QAAQ,CAACtD,OAAO,IAAI,6CAA6C,CAAC;MAClF;IACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;MAAA,IAAA+C,eAAA,EAAAC,oBAAA;MACdvB,OAAO,CAACzB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,MAAMiD,YAAY,GAAG,EAAAF,eAAA,GAAA/C,KAAK,CAACH,QAAQ,cAAAkD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhD,IAAI,cAAAiD,oBAAA,uBAApBA,oBAAA,CAAsBzG,OAAO,KAAIyD,KAAK,CAACzD,OAAO,IAAI,+BAA+B;MACtGA,OAAO,CAACyD,KAAK,CAAE,kBAAiBiD,YAAa,EAAC,CAAC;IACjD,CAAC,SAAS;MACRtD,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMqG,wBAAwB,GAAGA,CAAA,KAAM;IACrCtE,WAAW,CAAE2C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPvC,KAAK,EAAES,kBAAkB;MACzBR,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHO,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMyD,uBAAuB,GAAGA,CAAA,KAAM;IACpC3D,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM0D,iBAAiB,GAAG,MAAOrB,CAAC,IAAK;IACrC,MAAMsB,IAAI,GAAGtB,CAAC,CAACE,MAAM,CAACqB,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCjH,OAAO,CAACyD,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACF;;MAEA;MACA,IAAIqD,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/BlH,OAAO,CAACyD,KAAK,CAAC,oCAAoC,CAAC;QACnD;MACF;MAEAZ,eAAe,CAACiE,IAAI,CAAC;;MAErB;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMlF,eAAe,CAACgF,MAAM,CAACG,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;;MAE1B;MACA,MAAMtD,IAAI,GAAG,IAAIgE,QAAQ,CAAC,CAAC;MAC3BhE,IAAI,CAACiE,MAAM,CAAC,cAAc,EAAEX,IAAI,CAAC;MACjC1D,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI;QACF,MAAM+C,QAAQ,GAAG,MAAMxD,eAAe,CAAC0D,IAAI,CAAC;QAC5CJ,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;QACvB,IAAIgD,QAAQ,CAACC,OAAO,EAAE;UACpBvD,OAAO,CAACuD,OAAO,CAAC,uCAAuC,CAAC;UACxD4B,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UACLnF,OAAO,CAACyD,KAAK,CAACH,QAAQ,CAACtD,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;QACdL,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;QACvBN,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,IAAI,kCAAkC,CAAC;MACpE;IACF;EACF,CAAC;EAED,MAAM0H,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMlE,IAAI,GAAG,IAAIgE,QAAQ,CAAC,CAAC;IAC3BhE,IAAI,CAACiE,MAAM,CAAC,cAAc,EAAE7E,YAAY,CAAC;IACzCQ,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM+C,QAAQ,GAAG,MAAMxD,eAAe,CAAC0D,IAAI,CAAC;MAC5C,IAAIF,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAAC,6BAA6B,CAAC;QAC9C4B,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLnF,OAAO,CAACyD,KAAK,CAACH,QAAQ,CAACtD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACdzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRoD,QAAQ,CAAC9C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAID;EACAb,SAAS,CAAC,MAAM;IACd0F,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1F,SAAS,CAAC,MAAM;IACd,IAAI+B,WAAW,EAAE;MACf6C,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC7C,WAAW,CAAC,CAAC;;EAEjB;EACA/B,SAAS,CAAC,MAAM;IACd,IAAI+B,WAAW,EAAE;MACfa,WAAW,CAAC;QACVC,IAAI,EAAEd,WAAW,CAACc,IAAI,IAAI,EAAE;QAC5BC,KAAK,EAAEf,WAAW,CAACe,KAAK,IAAI,EAAE;QAAE;QAChCC,MAAM,EAAEhB,WAAW,CAACgB,MAAM,IAAI,EAAE;QAChCE,MAAM,EAAElB,WAAW,CAAC4D,KAAK,IAAI,EAAE;QAC/B3C,KAAK,EAAEjB,WAAW,CAACiB,KAAK,IAAI,EAAE;QAC9BE,WAAW,EAAEnB,WAAW,CAACmB,WAAW,IAAI;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACnB,WAAW,CAAC,CAAC;EAEjB,oBACEV,OAAA;IAAK6G,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjF9G,OAAA;MAAK6G,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1C9G,OAAA;QAAK6G,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhC9G,OAAA;UAAK6G,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B9G,OAAA;YAAI6G,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClElH,OAAA;YAAG6G,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAG7ElH,OAAA;YAAK6G,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChD9G,OAAA;cAAK6G,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB9G,OAAA,CAACH,cAAc;gBACbkD,IAAI,EAAErC,WAAY;gBAClB0F,IAAI,EAAC,KAAK;gBACVe,gBAAgB,EAAE,IAAK;gBACvBC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;gBACpEV,SAAS,EAAC,mDAAmD;gBAC7DW,KAAK,EAAE;kBACLC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfC,MAAM,EAAE,mBAAmB;kBAC3BC,SAAS,EAAE;gBACb;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFlH,OAAA;gBAAK6G,SAAS,EAAC,kIAAkI;gBAC5IO,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;gBAAAT,QAAA,eACvE9G,OAAA;kBAAK6G,SAAS,EAAC,oBAAoB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,gBACvF9G,OAAA;oBAAMgI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1OlH,OAAA;oBAAMgI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlH,OAAA;gBACEoI,EAAE,EAAC,mBAAmB;gBACtBlC,IAAI,EAAC,MAAM;gBACXmC,MAAM,EAAC,SAAS;gBAChBxB,SAAS,EAAC,QAAQ;gBAClByB,QAAQ,EAAEvC;cAAkB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlH,OAAA;UAAK6G,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7D9G,OAAA;YAAK6G,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB9G,OAAA;cAAK6G,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAE9C9G,OAAA;gBAAK6G,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnE9G,OAAA;kBAAK6G,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnF9G,OAAA;oBAAG6G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzDlH,OAAA;oBAAG6G,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAM;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACNlH,OAAA;kBAAK6G,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBACrF9G,OAAA;oBAAG6G,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC9DlH,OAAA;oBAAG6G,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,EAAE,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6H,QAAQ,KAAI;kBAAU;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5G,CAAC,eACNlH,OAAA;kBAAK6G,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF9G,OAAA;oBAAG6G,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5DlH,OAAA;oBAAG6G,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI;kBAAK;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLlG,gBAAgB,iBACfhB,OAAA;gBAAK6G,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC9D9G,OAAA;kBAAK6G,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF9G,OAAA;oBAAG6G,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3DlH,OAAA;oBAAG6G,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAAC,GAC5C,EAAC9F,gBAAgB,CAACmD,QAAQ,IAAI,KAAK,EACnCnD,gBAAgB,CAACgD,UAAU,iBAC1BhE,OAAA;sBAAM6G,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAAC9F,gBAAgB,CAACgD,UAAU;oBAAA;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAC7E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlH,OAAA;kBAAK6G,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF9G,OAAA;oBAAG6G,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/DlH,OAAA;oBAAG6G,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAAzG,qBAAA,GAAAW,gBAAgB,CAAC+B,IAAI,cAAA1C,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBmI,OAAO,cAAAlI,sBAAA,uBAA9BA,sBAAA,CAAgCmI,cAAc,CAAC,CAAC,KAAI;kBAAG;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlH,OAAA;kBAAK6G,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvF9G,OAAA;oBAAG6G,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChElH,OAAA;oBAAG6G,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAC3C,EAAAvG,sBAAA,GAAAS,gBAAgB,CAAC+B,IAAI,cAAAxC,sBAAA,uBAArBA,sBAAA,CAAuBmI,YAAY,KAAI,GAAG,EAAC,GAC9C;kBAAA;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlH,OAAA;kBAAK6G,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnF9G,OAAA;oBAAG6G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5DlH,OAAA;oBAAG6G,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAAtG,sBAAA,GAAAQ,gBAAgB,CAAC+B,IAAI,cAAAvC,sBAAA,uBAArBA,sBAAA,CAAuBmI,iBAAiB,KAAI;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlH,OAAA;kBAAK6G,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnF9G,OAAA;oBAAG6G,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3DlH,OAAA;oBAAG6G,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAArG,sBAAA,GAAAO,gBAAgB,CAAC+B,IAAI,cAAAtC,sBAAA,uBAArBA,sBAAA,CAAuBmI,aAAa,KAAI;kBAAG;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAAChG,IAAI;YAAA;YACJ;YACAlB,OAAA;cAAK6G,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9G,OAAA;gBAAK6G,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9G,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5ElH,OAAA;oBAAK6G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAc;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChFlH,OAAA;oBAAK6G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6H,QAAQ,KAAI;kBAAc;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7ElH,OAAA;oBAAK6G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9CpG,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEe,KAAK,gBACjBzB,OAAA;sBAAK6G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChD9G,OAAA;wBAAA8G,QAAA,EAAOpG,WAAW,CAACe;sBAAK;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EAC/BxG,WAAW,CAACe,KAAK,CAAC4B,QAAQ,CAAC,iBAAiB,CAAC,iBAC5CrD,OAAA;wBAAM6G,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,EAAC;sBAE3E;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,gBAENlH,OAAA;sBAAK6G,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChD9G,OAAA;wBAAM6G,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnDlH,OAAA;wBACEoH,OAAO,EAAE,MAAAA,CAAA,KAAY;0BACnB,MAAMyB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;0BAC5B,MAAMC,SAAS,GAAI,GAAEtI,WAAW,CAAC6H,QAAS,IAAGM,SAAU,iBAAgB;0BACvEtH,WAAW,CAAC2C,IAAI,KAAK;4BAAE,GAAGA,IAAI;4BAAEzC,KAAK,EAAEuH;0BAAU,CAAC,CAAC,CAAC;0BACpD9J,OAAO,CAAC+J,IAAI,CAAC,+DAA+D,CAAC;wBAC/E,CAAE;wBACFpC,SAAS,EAAC,iGAAiG;wBAAAC,QAAA,EAC5G;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9ElH,OAAA;oBAAK6G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI;kBAAc;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlH,OAAA;gBAAK6G,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9G,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7ElH,OAAA;oBAAK6G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;kBAAc;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7ElH,OAAA;oBAAK6G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI;kBAAc;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFlH,OAAA;oBAAK6G,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,WAAW,KAAI;kBAAc;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACAlH,OAAA;cAAK6G,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9G,OAAA;gBAAK6G,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9G,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9ElH,OAAA;oBACEkG,IAAI,EAAC,MAAM;oBACX1E,IAAI,EAAC,MAAM;oBACXmD,KAAK,EAAErD,QAAQ,CAACE,IAAK;oBACrB8G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIqC,WAAW,EAAC,iBAAiB;oBAC7BC,QAAQ;kBAAA;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChFlH,OAAA;oBAAK6G,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,GAC7D,CAAApG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE6H,QAAQ,KAAI,eAAe,eACzCvI,OAAA;sBAAM6G,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAA0B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxFlH,OAAA;oBAAK6G,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB9G,OAAA;sBACEkG,IAAI,EAAC,OAAO;sBACZ1E,IAAI,EAAC,OAAO;sBACZmD,KAAK,EAAErD,QAAQ,CAACG,KAAK,IAAI,EAAG;sBAC5B6G,QAAQ,EAAE7D,YAAa;sBACvBoC,SAAS,EAAC,6HAA6H;sBACvIqC,WAAW,EAAC;oBAA6B;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,EACD,CAAC,CAAC5F,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,KAAK,EAAE,kBACxCzB,OAAA;sBACEkG,IAAI,EAAC,QAAQ;sBACbkB,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAMyB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;wBAC5B,MAAMC,SAAS,GAAI,GAAEtI,WAAW,CAAC6H,QAAS,IAAGM,SAAU,iBAAgB;wBACvEtH,WAAW,CAAC2C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEzC,KAAK,EAAEuH;wBAAU,CAAC,CAAC,CAAC;wBACpD9J,OAAO,CAACuD,OAAO,CAAC,+BAA+B,CAAC;sBAClD,CAAE;sBACFoE,SAAS,EAAC,6IAA6I;sBAAAC,QAAA,EACxJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EACL5F,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAAC4B,QAAQ,CAAC,iBAAiB,CAAC,iBAC3DrD,OAAA;oBAAG6G,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAE1C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9ElH,OAAA;oBACEkG,IAAI,EAAC,MAAM;oBACX1E,IAAI,EAAC,QAAQ;oBACbmD,KAAK,EAAErD,QAAQ,CAACI,MAAO;oBACvB4G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIqC,WAAW,EAAC;kBAAmB;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlH,OAAA;gBAAK6G,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9G,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/ElH,OAAA;oBACEwB,IAAI,EAAC,OAAO;oBACZmD,KAAK,EAAErD,QAAQ,CAACK,KAAM;oBACtB2G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIsC,QAAQ;oBAAArC,QAAA,gBAER9G,OAAA;sBAAQ2E,KAAK,EAAC,EAAE;sBAAAmC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtClH,OAAA;sBAAQ2E,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxClH,OAAA;sBAAQ2E,KAAK,EAAC,WAAW;sBAAAmC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5ClH,OAAA;sBAAQ2E,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/ElH,OAAA;oBACEwB,IAAI,EAAC,QAAQ;oBACbmD,KAAK,EAAErD,QAAQ,CAACM,MAAO;oBACvB0G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIsC,QAAQ;oBAAArC,QAAA,gBAER9G,OAAA;sBAAQ2E,KAAK,EAAC,EAAE;sBAAAmC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACrC5F,QAAQ,CAACK,KAAK,KAAK,SAAS,iBAC3B3B,OAAA,CAAAE,SAAA;sBAAA4G,QAAA,gBACE9G,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH,EACA5F,QAAQ,CAACK,KAAK,KAAK,WAAW,iBAC7B3B,OAAA,CAAAE,SAAA;sBAAA4G,QAAA,gBACE9G,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH,EACA5F,QAAQ,CAACK,KAAK,KAAK,SAAS,iBAC3B3B,OAAA,CAAAE,SAAA;sBAAA4G,QAAA,gBACE9G,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BlH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNlH,OAAA;kBAAA8G,QAAA,gBACE9G,OAAA;oBAAO6G,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFlH,OAAA;oBACEkG,IAAI,EAAC,KAAK;oBACV1E,IAAI,EAAC,aAAa;oBAClBmD,KAAK,EAAErD,QAAQ,CAACO,WAAY;oBAC5ByG,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIqC,WAAW,EAAC,oBAAoB;oBAChCE,SAAS,EAAC;kBAAI;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAGDlH,OAAA;cAAK6G,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAC5C,CAAC5F,IAAI,gBACJlB,OAAA;gBACEoH,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACA7F,WAAW,CAAC;oBACVC,IAAI,EAAE,CAAAd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI,EAAE;oBAC7BC,KAAK,EAAE,CAAAf,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI,EAAE;oBAC/BC,MAAM,EAAE,CAAAhB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI,EAAE;oBACjCE,MAAM,EAAE,CAAAlB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI,EAAE;oBAChC3C,KAAK,EAAE,CAAAjB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI,EAAE;oBAC/BE,WAAW,EAAE,CAAAnB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,WAAW,KAAI;kBAC3C,CAAC,CAAC;kBACFV,OAAO,CAAC,IAAI,CAAC;gBACf,CAAE;gBACF0F,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EACrH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gBAETlH,OAAA,CAAAE,SAAA;gBAAA4G,QAAA,gBACE9G,OAAA;kBACEoH,OAAO,EAAEvC,cAAe;kBACxBgC,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,EACrH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlH,OAAA;kBACEoH,OAAO,EAAEtC,YAAa;kBACtB+B,SAAS,EAAC,4GAA4G;kBAAAC,QAAA,EACvH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAETlH,OAAA;kBACEoH,OAAO,EAAEA,CAAA,KAAM;oBACbhD,OAAO,CAACY,GAAG,CAAC,8BAA8B,EAAE1D,QAAQ,CAAC;oBACrD8C,OAAO,CAACY,GAAG,CAAC,iCAAiC,EAAEtE,WAAW,CAAC;oBAC3D2I,KAAK,CAAE,aAAYC,IAAI,CAACC,SAAS,CAACjI,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;kBACzD,CAAE;kBACFuF,SAAS,EAAC,kHAAkH;kBAAAC,QAAA,EAC7H;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlH,OAAA;MACEkG,IAAI,EAAC,MAAM;MACXkC,EAAE,EAAC,mBAAmB;MACtBC,MAAM,EAAC,SAAS;MAChBC,QAAQ,EAAEvC,iBAAkB;MAC5ByB,KAAK,EAAE;QAAEgC,OAAO,EAAE;MAAO;IAAE;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGFlH,OAAA,CAACb,KAAK;MACJsK,KAAK,EAAC,sBAAsB;MAC5BC,IAAI,EAAExH,oBAAqB;MAC3ByH,IAAI,EAAE9D,wBAAyB;MAC/B+D,QAAQ,EAAEA,CAAA,KAAM;QACdzH,uBAAuB,CAAC,KAAK,CAAC;QAC9BE,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MACFwH,MAAM,EAAC,SAAS;MAChBC,UAAU,EAAC,QAAQ;MAAAhD,QAAA,gBAEnB9G,OAAA;QAAA8G,QAAA,GAAG,gDAC6C,eAAA9G,OAAA;UAAA8G,QAAA,EAAS1E;QAAkB;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,KACrF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJlH,OAAA;QAAG6G,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAAC;MAE5C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGL,CAAC;AAEV,CAAC;AAAC9G,EAAA,CAhuBID,OAAO;EAAA,QAmBMb,WAAW;AAAA;AAAAyK,EAAA,GAnBxB5J,OAAO;AAkuBb,eAAeA,OAAO;AAAC,IAAA4J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}