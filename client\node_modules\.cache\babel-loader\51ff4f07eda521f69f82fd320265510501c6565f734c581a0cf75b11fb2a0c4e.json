{"ast": null, "code": "import{createSlice}from\"@reduxjs/toolkit\";const usersSlice=createSlice({name:\"users\",initialState:{user:null},reducers:{SetUser:(state,action)=>{state.user=action.payload;}}});export const{SetUser}=usersSlice.actions;export default usersSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "usersSlice", "name", "initialState", "user", "reducers", "SetUser", "state", "action", "payload", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/redux/usersSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\nconst usersSlice = createSlice({\r\n  name: \"users\",\r\n  initialState: {\r\n    user: null,\r\n  },\r\n  reducers: {\r\n    SetUser: (state, action) => {\r\n      state.user = action.payload;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { SetUser } = usersSlice.actions;\r\nexport default usersSlice.reducer;"], "mappings": "AAAA,OAASA,WAAW,KAAQ,kBAAkB,CAE9C,KAAM,CAAAC,UAAU,CAAGD,WAAW,CAAC,CAC7BE,IAAI,CAAE,OAAO,CACbC,YAAY,CAAE,CACZC,IAAI,CAAE,IACR,CAAC,CACDC,QAAQ,CAAE,CACRC,OAAO,CAAEA,CAACC,KAAK,CAAEC,MAAM,GAAK,CAC1BD,KAAK,CAACH,IAAI,CAAGI,MAAM,CAACC,OAAO,CAC7B,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEH,OAAQ,CAAC,CAAGL,UAAU,CAACS,OAAO,CAC7C,cAAe,CAAAT,UAAU,CAACU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}