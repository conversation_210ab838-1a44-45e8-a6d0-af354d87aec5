<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainWave - Profile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 800;
            color: #4f46e5;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            text-decoration: none;
            color: #6b7280;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-link:hover {
            color: #4f46e5;
        }
        
        .container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .profile-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .profile-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            color: white;
        }
        
        .profile-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .profile-status {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .form-section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .form-input {
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }
        
        .btn-secondary {
            background: #6b7280;
        }
        
        .subscription-section {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .subscription-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 0.5rem;
        }
        
        .subscription-text {
            color: #a16207;
            margin-bottom: 1rem;
        }
        
        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .plan-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            border: 2px solid #e5e7eb;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .plan-card:hover {
            border-color: #4f46e5;
            transform: translateY(-2px);
        }
        
        .plan-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .plan-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4f46e5;
            margin-bottom: 0.5rem;
        }
        
        .plan-duration {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .status-message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">🧠 BrainWave</div>
            <nav class="nav-links">
                <a href="#" class="nav-link" onclick="goToHub()">🏠 Hub</a>
                <a href="#" class="nav-link" onclick="goToQuiz()">🧠 Quiz</a>
                <a href="#" class="nav-link" onclick="goToReports()">📊 Reports</a>
                <a href="#" class="nav-link" onclick="logout()">🚪 Logout</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="profile-card">
            <div class="profile-header">
                <div class="profile-avatar">👤</div>
                <div class="profile-name" id="profileName">Demo User</div>
                <div class="profile-status">Student • Class 5 • Primary Level</div>
            </div>

            <div id="statusMessage"></div>

            <form id="profileForm">
                <div class="form-section">
                    <h3 class="section-title">📝 Personal Information</h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">First Name</label>
                            <input type="text" class="form-input" id="firstName" name="firstName" value="Demo" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Last Name</label>
                            <input type="text" class="form-input" id="lastName" name="lastName" value="User" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-input" id="email" name="email" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" class="form-input" id="phoneNumber" name="phoneNumber" value="0712345678" required>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="section-title">🎓 Academic Information</h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Level</label>
                            <select class="form-input" id="level" name="level" required>
                                <option value="Primary" selected>Primary</option>
                                <option value="Secondary">Secondary</option>
                                <option value="Advance">Advance</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Class</label>
                            <select class="form-input" id="class" name="class" required>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5" selected>5</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="submit" class="btn">💾 Update Profile</button>
                    <button type="button" class="btn btn-secondary" onclick="loadUserData()" style="margin-left: 1rem;">🔄 Refresh</button>
                </div>
            </form>
        </div>

        <div class="subscription-section">
            <h3 class="subscription-title">🚀 Choose Your Subscription Plan</h3>
            <p class="subscription-text">Unlock full access to all features with a subscription plan</p>
            
            <div class="plans-grid" id="plansGrid">
                <div class="plan-card" onclick="selectPlan('glimp')">
                    <div class="plan-title">⭐ Glimp Plan</div>
                    <div class="plan-price">13,000 TZS</div>
                    <div class="plan-duration">1 month access</div>
                </div>
                <div class="plan-card" onclick="selectPlan('basic')">
                    <div class="plan-title">📚 Basic Plan</div>
                    <div class="plan-price">20,000 TZS</div>
                    <div class="plan-duration">2 months access</div>
                </div>
                <div class="plan-card" onclick="selectPlan('premium')">
                    <div class="plan-title">👑 Premium Plan</div>
                    <div class="plan-price">35,000 TZS</div>
                    <div class="plan-duration">3 months access</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';

        // Load user data on page load
        window.addEventListener('load', () => {
            loadUserData();
            loadPlans();
        });

        // Handle profile form submission
        document.getElementById('profileForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const profileData = Object.fromEntries(formData);
            
            try {
                const response = await fetch(`${API_BASE}/api/users/update-user-info`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(profileData)
                });
                
                const result = await response.json();
                showStatus(result.success ? 'success' : 'error', result.message);
                
                if (result.success) {
                    updateProfileDisplay(profileData);
                }
            } catch (error) {
                showStatus('error', 'Failed to update profile: ' + error.message);
            }
        });

        async function loadUserData() {
            try {
                const response = await fetch(`${API_BASE}/api/users/get-user-info`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: 'demo-user-id' })
                });
                
                const result = await response.json();
                if (result.success && result.data) {
                    populateForm(result.data);
                    updateProfileDisplay(result.data);
                }
            } catch (error) {
                showStatus('error', 'Failed to load user data: ' + error.message);
            }
        }

        async function loadPlans() {
            try {
                const response = await fetch(`${API_BASE}/api/plans`);
                const plans = await response.json();
                
                if (Array.isArray(plans) && plans.length > 0) {
                    displayPlans(plans);
                }
            } catch (error) {
                console.error('Failed to load plans:', error);
            }
        }

        function populateForm(userData) {
            Object.keys(userData).forEach(key => {
                const element = document.getElementById(key);
                if (element && userData[key]) {
                    element.value = userData[key];
                }
            });
        }

        function updateProfileDisplay(userData) {
            const nameElement = document.getElementById('profileName');
            if (nameElement) {
                nameElement.textContent = `${userData.firstName || 'Demo'} ${userData.lastName || 'User'}`;
            }
        }

        function displayPlans(plans) {
            const plansGrid = document.getElementById('plansGrid');
            plansGrid.innerHTML = plans.map(plan => `
                <div class="plan-card" onclick="selectPlan('${plan._id}', ${plan.discountedPrice})">
                    <div class="plan-title">${plan.title}</div>
                    <div class="plan-price">${plan.discountedPrice.toLocaleString()} TZS</div>
                    <div class="plan-duration">${plan.duration} month${plan.duration > 1 ? 's' : ''} access</div>
                </div>
            `).join('');
        }

        async function selectPlan(planId, price) {
            try {
                const response = await fetch(`${API_BASE}/api/payment/create-invoice`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        plan: { _id: planId, discountedPrice: price || 13000 },
                        userId: 'demo-user-id'
                    })
                });
                
                const result = await response.json();
                showStatus(result.success ? 'success' : 'error', 
                    result.success ? 'Payment initiated! (Demo Mode)' : result.message);
            } catch (error) {
                showStatus('error', 'Payment failed: ' + error.message);
            }
        }

        function showStatus(type, message) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            
            setTimeout(() => {
                statusDiv.textContent = '';
                statusDiv.className = '';
            }, 5000);
        }

        // Navigation functions
        function goToHub() {
            showStatus('success', 'Navigating to Hub... (Demo Mode)');
        }

        function goToQuiz() {
            showStatus('success', 'Navigating to Quiz... (Demo Mode)');
        }

        function goToReports() {
            showStatus('success', 'Navigating to Reports... (Demo Mode)');
        }

        function logout() {
            showStatus('success', 'Logging out... (Demo Mode)');
        }
    </script>
</body>
</html>
