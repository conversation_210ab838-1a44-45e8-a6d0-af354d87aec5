{"ast": null, "code": "import{message,Table}from\"antd\";import React,{useEffect}from\"react\";import{useDispatch}from\"react-redux\";import{useNavigate}from\"react-router-dom\";import{motion}from\"framer-motion\";import{TbDashboard,TbPlus}from\"react-icons/tb\";import{deleteExamById,getAllExams}from\"../../../apicalls/exams\";import PageTitle from\"../../../components/PageTitle\";import{HideLoading,ShowLoading}from\"../../../redux/loaderSlice\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";function Exams(){const navigate=useNavigate();const[exams,setExams]=React.useState([]);const dispatch=useDispatch();const getExamsData=async()=>{try{dispatch(ShowLoading());const response=await getAllExams();dispatch(HideLoading());if(response.success){setExams(response.data.reverse());console.log(response,\"exam\");}else{message.error(response.message);}}catch(error){dispatch(HideLoading());message.error(error.message);}};const deleteExam=async examId=>{try{dispatch(ShowLoading());const response=await deleteExamById({examId});dispatch(HideLoading());if(response.success){message.success(response.message);getExamsData();}else{message.error(response.message);}}catch(error){dispatch(HideLoading());message.error(error.message);}};const columns=[{title:\"Exam Name\",dataIndex:\"name\"},{title:\"Duration\",dataIndex:\"duration\",render:duration=>\"\".concat(Math.round(duration/60),\" min\")},{title:\"Class\",dataIndex:\"class\"},{title:\"Category\",dataIndex:\"category\"},{title:\"Total Marks\",dataIndex:\"totalMarks\"},{title:\"Passing Marks\",dataIndex:\"passingMarks\"},{title:\"Action\",dataIndex:\"action\",render:(text,record)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(\"i\",{className:\"ri-pencil-line\",onClick:()=>navigate(\"/admin/exams/edit/\".concat(record._id))}),/*#__PURE__*/_jsx(\"i\",{className:\"ri-delete-bin-line\",onClick:()=>deleteExam(record._id)})]})}];useEffect(()=>{getExamsData();},[]);return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between mt-2 items-end\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4\",children:[/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>navigate('/admin/dashboard'),className:\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\",children:[/*#__PURE__*/_jsx(TbDashboard,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline text-sm font-medium\",children:\"Dashboard\"})]}),/*#__PURE__*/_jsx(PageTitle,{title:\"Exams\"})]}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},className:\"primary-outlined-btn flex items-center gap-2\",onClick:()=>navigate(\"/admin/exams/add\"),children:[/*#__PURE__*/_jsx(TbPlus,{className:\"w-4 h-4\"}),\"Add Exam\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"divider\"}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:exams})]});}export default Exams;", "map": {"version": 3, "names": ["message", "Table", "React", "useEffect", "useDispatch", "useNavigate", "motion", "TbDashboard", "TbPlus", "deleteExamById", "getAllExams", "Page<PERSON><PERSON>le", "HideLoading", "ShowLoading", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "navigate", "exams", "setExams", "useState", "dispatch", "getExamsData", "response", "success", "data", "reverse", "console", "log", "error", "deleteExam", "examId", "columns", "title", "dataIndex", "render", "duration", "concat", "Math", "round", "text", "record", "className", "children", "onClick", "_id", "button", "whileHover", "scale", "whileTap", "dataSource"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Exams/index.js"], "sourcesContent": ["import { message, Table } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport { TbDashboard, TbPlus } from \"react-icons/tb\";\r\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Exams() {\r\n  const navigate = useNavigate();\r\n  const [exams, setExams] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getExamsData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExams(response.data.reverse());\r\n        console.log(response, \"exam\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteExam = async (examId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteExamById({\r\n        examId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamsData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Duration\",\r\n      dataIndex: \"duration\",\r\n      render: (duration) => `${Math.round(duration / 60)} min`,\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Category\",\r\n      dataIndex: \"category\",\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalMarks\",\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"passingMarks\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2\">\r\n          <i\r\n            className=\"ri-pencil-line\"\r\n            onClick={() => navigate(`/admin/exams/edit/${record._id}`)}\r\n          ></i>\r\n          <i\r\n            className=\"ri-delete-bin-line\"\r\n            onClick={() => deleteExam(record._id)}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getExamsData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <div className=\"flex items-center gap-4\">\r\n          {/* Dashboard Shortcut */}\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={() => navigate('/admin/dashboard')}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\"\r\n          >\r\n            <TbDashboard className=\"w-4 h-4\" />\r\n            <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\r\n          </motion.button>\r\n\r\n          <PageTitle title=\"Exams\" />\r\n        </div>\r\n\r\n        <motion.button\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"primary-outlined-btn flex items-center gap-2\"\r\n          onClick={() => navigate(\"/admin/exams/add\")}\r\n        >\r\n          <TbPlus className=\"w-4 h-4\" />\r\n          Add Exam\r\n        </motion.button>\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table columns={columns} dataSource={exams} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Exams;\r\n"], "mappings": "AAAA,OAASA,OAAO,CAAEC,KAAK,KAAQ,MAAM,CACrC,MAAO,CAAAC,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,CAAEC,MAAM,KAAQ,gBAAgB,CACpD,OAASC,cAAc,CAAEC,WAAW,KAAQ,yBAAyB,CACrE,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,OAASC,WAAW,CAAEC,WAAW,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEtE,QAAS,CAAAC,KAAKA,CAAA,CAAG,CACf,KAAM,CAAAC,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACe,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,KAAK,CAACoB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAAC,QAAQ,CAAGnB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAoB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACFD,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC,CACvB,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAf,WAAW,CAAC,CAAC,CACpCa,QAAQ,CAACX,WAAW,CAAC,CAAC,CAAC,CACvB,GAAIa,QAAQ,CAACC,OAAO,CAAE,CACpBL,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CACjCC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAE,MAAM,CAAC,CAC/B,CAAC,IAAM,CACLzB,OAAO,CAAC+B,KAAK,CAACN,QAAQ,CAACzB,OAAO,CAAC,CACjC,CACF,CAAE,MAAO+B,KAAK,CAAE,CACdR,QAAQ,CAACX,WAAW,CAAC,CAAC,CAAC,CACvBZ,OAAO,CAAC+B,KAAK,CAACA,KAAK,CAAC/B,OAAO,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAgC,UAAU,CAAG,KAAO,CAAAC,MAAM,EAAK,CACnC,GAAI,CACFV,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC,CACvB,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAhB,cAAc,CAAC,CACpCwB,MACF,CAAC,CAAC,CACFV,QAAQ,CAACX,WAAW,CAAC,CAAC,CAAC,CACvB,GAAIa,QAAQ,CAACC,OAAO,CAAE,CACpB1B,OAAO,CAAC0B,OAAO,CAACD,QAAQ,CAACzB,OAAO,CAAC,CACjCwB,YAAY,CAAC,CAAC,CAChB,CAAC,IAAM,CACLxB,OAAO,CAAC+B,KAAK,CAACN,QAAQ,CAACzB,OAAO,CAAC,CACjC,CACF,CAAE,MAAO+B,KAAK,CAAE,CACdR,QAAQ,CAACX,WAAW,CAAC,CAAC,CAAC,CACvBZ,OAAO,CAAC+B,KAAK,CAACA,KAAK,CAAC/B,OAAO,CAAC,CAC9B,CACF,CAAC,CACD,KAAM,CAAAkC,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,WAAW,CAClBC,SAAS,CAAE,MACb,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBC,SAAS,CAAE,UAAU,CACrBC,MAAM,CAAGC,QAAQ,KAAAC,MAAA,CAAQC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAG,EAAE,CAAC,QACpD,CAAC,CACD,CACEH,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,OACb,CAAC,CACD,CACED,KAAK,CAAE,UAAU,CACjBC,SAAS,CAAE,UACb,CAAC,CACD,CACED,KAAK,CAAE,aAAa,CACpBC,SAAS,CAAE,YACb,CAAC,CACD,CACED,KAAK,CAAE,eAAe,CACtBC,SAAS,CAAE,cACb,CAAC,CACD,CACED,KAAK,CAAE,QAAQ,CACfC,SAAS,CAAE,QAAQ,CACnBC,MAAM,CAAEA,CAACK,IAAI,CAAEC,MAAM,gBACnB1B,KAAA,QAAK2B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB9B,IAAA,MACE6B,SAAS,CAAC,gBAAgB,CAC1BE,OAAO,CAAEA,CAAA,GAAM3B,QAAQ,sBAAAoB,MAAA,CAAsBI,MAAM,CAACI,GAAG,CAAE,CAAE,CACzD,CAAC,cACLhC,IAAA,MACE6B,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAEA,CAAA,GAAMd,UAAU,CAACW,MAAM,CAACI,GAAG,CAAE,CACpC,CAAC,EACF,CAET,CAAC,CACF,CACD5C,SAAS,CAAC,IAAM,CACdqB,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CACN,mBACEP,KAAA,QAAA4B,QAAA,eACE5B,KAAA,QAAK2B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClD5B,KAAA,QAAK2B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAEtC5B,KAAA,CAACX,MAAM,CAAC0C,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BJ,OAAO,CAAEA,CAAA,GAAM3B,QAAQ,CAAC,kBAAkB,CAAE,CAC5CyB,SAAS,CAAC,gIAAgI,CAAAC,QAAA,eAE1I9B,IAAA,CAACR,WAAW,EAACqC,SAAS,CAAC,SAAS,CAAE,CAAC,cACnC7B,IAAA,SAAM6B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EAC1D,CAAC,cAEhB9B,IAAA,CAACJ,SAAS,EAACwB,KAAK,CAAC,OAAO,CAAE,CAAC,EACxB,CAAC,cAENlB,KAAA,CAACX,MAAM,CAAC0C,MAAM,EACZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BN,SAAS,CAAC,8CAA8C,CACxDE,OAAO,CAAEA,CAAA,GAAM3B,QAAQ,CAAC,kBAAkB,CAAE,CAAA0B,QAAA,eAE5C9B,IAAA,CAACP,MAAM,EAACoC,SAAS,CAAC,SAAS,CAAE,CAAC,WAEhC,EAAe,CAAC,EACb,CAAC,cACN7B,IAAA,QAAK6B,SAAS,CAAC,SAAS,CAAM,CAAC,cAE/B7B,IAAA,CAACd,KAAK,EAACiC,OAAO,CAAEA,OAAQ,CAACkB,UAAU,CAAEhC,KAAM,CAAE,CAAC,EAC3C,CAAC,CAEV,CAEA,cAAe,CAAAF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}