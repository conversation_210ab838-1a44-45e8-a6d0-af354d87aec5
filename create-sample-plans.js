const mongoose = require('mongoose');
const Plan = require('./server/models/planModel');

async function createSamplePlans() {
  try {
    await mongoose.connect('mongodb+srv://henrymushi:<EMAIL>/brainwave');
    console.log('✅ Connected to MongoDB');
    
    // Check existing plans
    const existingPlans = await Plan.find({});
    console.log('📋 Existing plans:', existingPlans.length);
    
    if (existingPlans.length === 0) {
      console.log('🔄 Creating sample plans...');
      
      const samplePlans = [
        {
          title: 'Glimp Plan',
          features: [
            '1-month full access',
            'Unlimited quizzes',
            'Personalized profile',
            'AI chat for instant help',
            'Forum for student discussions',
            'Study notes',
            'Past papers',
            'Books',
            'Learning videos',
            'Track progress with rankings'
          ],
          actualPrice: 15000,
          discountedPrice: 13000,
          discountPercentage: 13,
          duration: 1,
          status: true,
        },
        {
          title: 'Basic Membership',
          features: [
            '2-month full access',
            'Unlimited quizzes',
            'Personalized profile',
            'AI chat for instant help',
            'Forum for student discussions',
            'Study notes',
            'Past papers',
            'Books',
            'Learning videos',
            'Track progress with rankings'
          ],
          actualPrice: 28570,
          discountedPrice: 20000,
          discountPercentage: 30,
          duration: 2,
          status: true,
        },
        {
          title: 'Premium Plan',
          features: [
            '3-month full access',
            'Unlimited quizzes',
            'Personalized profile',
            'AI chat for instant help',
            'Forum for student discussions',
            'Study notes',
            'Past papers',
            'Books',
            'Learning videos',
            'Track progress with rankings',
            'Priority support'
          ],
          actualPrice: 45000,
          discountedPrice: 35000,
          discountPercentage: 22,
          duration: 3,
          status: true,
        }
      ];
      
      await Plan.insertMany(samplePlans);
      console.log('✅ Sample plans created successfully!');
    } else {
      console.log('📋 Plans already exist:');
      existingPlans.forEach(plan => {
        console.log(`- ${plan.title}: ${plan.duration} month(s), ${plan.discountedPrice} TZS`);
      });
    }
    
    // List all plans
    const allPlans = await Plan.find({});
    console.log('\n📋 All available plans:');
    allPlans.forEach((plan, index) => {
      console.log(`${index + 1}. ${plan.title}`);
      console.log(`   Price: ${plan.discountedPrice} TZS (was ${plan.actualPrice} TZS)`);
      console.log(`   Duration: ${plan.duration} month(s)`);
      console.log(`   Features: ${plan.features.length} items`);
      console.log(`   Status: ${plan.status ? 'Active' : 'Inactive'}`);
      console.log('');
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

createSamplePlans();
