{"ast": null, "code": "import{createSlice}from\"@reduxjs/toolkit\";const subscriptionSlice=createSlice({name:\"subscription\",initialState:{subscriptionData:null},reducers:{SetSubscription:(state,action)=>{state.subscriptionData=action.payload;}}});export const{SetSubscription}=subscriptionSlice.actions;export default subscriptionSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "subscriptionSlice", "name", "initialState", "subscriptionData", "reducers", "SetSubscription", "state", "action", "payload", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/redux/subscriptionSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\nconst subscriptionSlice = createSlice({\r\n  name: \"subscription\",\r\n  initialState: {\r\n    subscriptionData: null,\r\n  },\r\n  reducers: {\r\n    SetSubscription: (state, action) => {\r\n      state.subscriptionData = action.payload;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { SetSubscription } = subscriptionSlice.actions;\r\nexport default subscriptionSlice.reducer;\r\n"], "mappings": "AAAA,OAASA,WAAW,KAAQ,kBAAkB,CAE9C,KAAM,CAAAC,iBAAiB,CAAGD,WAAW,CAAC,CACpCE,IAAI,CAAE,cAAc,CACpBC,YAAY,CAAE,CACZC,gBAAgB,CAAE,IACpB,CAAC,CACDC,QAAQ,CAAE,CACRC,eAAe,CAAEA,CAACC,KAAK,CAAEC,MAAM,GAAK,CAClCD,KAAK,CAACH,gBAAgB,CAAGI,MAAM,CAACC,OAAO,CACzC,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEH,eAAgB,CAAC,CAAGL,iBAAiB,CAACS,OAAO,CAC5D,cAAe,CAAAT,iBAAiB,CAACU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}