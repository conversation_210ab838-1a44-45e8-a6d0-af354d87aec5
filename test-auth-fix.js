const axios = require('axios');
const qs = require('querystring');

const testAuthFix = async () => {
  console.log('🔐 Testing Updated ZenoPay Authentication...\n');

  const API_KEY = '-YIkdkUWpqEyy9DOaKPTDeaEZ5O97_DkSxmZdBLwYrE';
  const ACCOUNT_ID = 'zp38236';
  const ENDPOINT = 'https://api.zeno.africa/api/payments/mobile_money_tanzania';

  console.log('🔑 API Key:', API_KEY.substring(0, 15) + '...');
  console.log('🆔 Account ID:', ACCOUNT_ID);
  console.log('🌐 Endpoint:', ENDPOINT);

  const testData = {
    account_id: ACCOUNT_ID,
    amount: 1000,
    buyer_email: '<EMAIL>',
    buyer_name: 'Test User',
    buyer_phone: '**********',
    webhook_url: 'https://server-fmff.onrender.com/api/payment/webhook',
    success_url: 'http://localhost:3000/payment/success',
    cancel_url: 'http://localhost:3000/payment/cancel',
    item_name: 'BrainWave Subscription Test'
  };

  console.log('\n🧪 Testing with updated authentication method...');
  
  try {
    const formattedData = qs.stringify(testData);
    console.log('📤 Sending payment request with Authorization: API-Key header...');

    const response = await axios.post(ENDPOINT, formattedData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `API-Key ${API_KEY}`  // Updated authentication
      },
      timeout: 15000,
      validateStatus: () => true
    });

    console.log('📥 Response Status:', response.status);
    console.log('📥 Response Data:', response.data);

    if (response.status === 200 || response.status === 201) {
      console.log('✅ SUCCESS: Authentication method is working!');
      if (response.data.payment_url) {
        console.log('🔗 Payment URL received:', response.data.payment_url);
      }
      if (response.data.order_id) {
        console.log('🆔 Order ID:', response.data.order_id);
      }
    } else if (response.status === 403) {
      console.log('❌ 403 Forbidden: Check if server IP needs whitelisting');
      if (response.data.includes && response.data.includes('Imunify360')) {
        console.log('🛡️ IP Protection detected - contact ZenoPay to whitelist your server IP');
      }
    } else {
      console.log('⚠️ Unexpected response - check API documentation');
    }

  } catch (error) {
    console.log('❌ Request failed:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    }
  }

  console.log('\n📋 Summary of Changes Made:');
  console.log('✅ Updated authentication from "x-api-key" to "Authorization: API-Key"');
  console.log('✅ Added proper logging for debugging');
  console.log('✅ Added timeout handling');

  console.log('\n🚨 IMPORTANT NEXT STEPS:');
  console.log('1. Contact ZenoPay support to whitelist your server IP:');
  console.log('   - Server IP: Your Render.com server IP');
  console.log('   - Phone: +255 793 166 166');
  console.log('   - Email: <EMAIL>');
  console.log('');
  console.log('2. Tell them:');
  console.log('   "Please whitelist my server IP for API access."');
  console.log('   "Account ID: zp38236"');
  console.log('   "Server URL: https://server-fmff.onrender.com"');
  console.log('');
  console.log('3. Test payment after IP whitelisting');

  console.log('\n💡 Why this happened:');
  console.log('- ZenoPay updated their API authentication method');
  console.log('- They added IP protection (Imunify360)');
  console.log('- Your server IP needs to be whitelisted for API access');
  console.log('- This is a security improvement on their end');
};

testAuthFix().catch(console.error);
