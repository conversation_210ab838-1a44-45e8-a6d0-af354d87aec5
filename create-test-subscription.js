const mongoose = require('mongoose');
require('dotenv').config({ path: './server/.env' });

// Import models
const User = require('./server/models/userModel');
const Subscription = require('./server/models/subscriptionModel');
const Plan = require('./server/models/planModel');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

const createTestSubscription = async () => {
  console.log('🔄 Creating test subscription for current user...\n');

  try {
    await connectDB();

    // Find a user to test with (preferably one that exists)
    const testUser = await User.findOne({ 
      $or: [
        { name: '<PERSON> Mosha' },
        { email: { $exists: true } }
      ]
    });

    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`👤 Found test user: ${testUser.name}`);
    console.log(`   Email: ${testUser.email}`);
    console.log(`   Phone: ${testUser.phoneNumber}`);

    // Find or create a test plan
    let testPlan = await Plan.findOne({ title: /basic/i });
    
    if (!testPlan) {
      console.log('📋 Creating test plan...');
      testPlan = new Plan({
        title: 'Basic Membership',
        features: ['2-month access', 'Basic quizzes', 'Study materials', 'Progress tracking'],
        actualPrice: 15000,
        discountedPrice: 12000,
        discountPercentage: 20,
        duration: 2,
        status: true
      });
      await testPlan.save();
      console.log('✅ Created test plan');
    }

    console.log(`📋 Using plan: ${testPlan.title}`);

    // Remove any existing subscriptions for this user
    await Subscription.deleteMany({ user: testUser._id });
    console.log('🗑️ Removed existing subscriptions');

    // Create new active subscription
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + testPlan.duration);

    const newSubscription = new Subscription({
      user: testUser._id,
      activePlan: testPlan._id,
      paymentStatus: 'paid',
      status: 'active',
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      paymentHistory: [{
        orderId: `TEST_${Date.now()}`,
        plan: testPlan._id,
        amount: testPlan.discountedPrice,
        paymentStatus: 'paid',
        paymentDate: startDate.toISOString().split('T')[0]
      }]
    });

    await newSubscription.save();
    console.log('✅ Created new subscription');

    // Update user status
    testUser.subscriptionStatus = 'active';
    testUser.paymentRequired = false;
    testUser.subscriptionStartDate = startDate;
    testUser.subscriptionEndDate = endDate;
    testUser.subscriptionPlan = 'basic';
    await testUser.save();
    console.log('✅ Updated user status');

    // Verify the subscription
    const verifySubscription = await Subscription.findOne({
      user: testUser._id,
      paymentStatus: 'paid',
      status: 'active'
    }).populate('activePlan');

    console.log('\n🔍 Verification:');
    console.log(`   User: ${testUser.name}`);
    console.log(`   Plan: ${verifySubscription.activePlan.title}`);
    console.log(`   Payment Status: ${verifySubscription.paymentStatus}`);
    console.log(`   Status: ${verifySubscription.status}`);
    console.log(`   Start Date: ${verifySubscription.startDate}`);
    console.log(`   End Date: ${verifySubscription.endDate}`);

    // Calculate days remaining
    const now = new Date();
    const end = new Date(verifySubscription.endDate);
    const diffTime = end - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    console.log(`   Days Remaining: ${Math.max(0, diffDays)}`);

    console.log('\n🎯 Test Instructions:');
    console.log('1. Login as this user in the application');
    console.log('2. Navigate to /user/plans');
    console.log('3. You should see the current subscription details');
    console.log('4. Check the browser console for debug information');

    console.log('\n📋 Expected Display:');
    console.log(`- Plan Title: ${verifySubscription.activePlan.title}`);
    console.log(`- Start Date: ${new Date(verifySubscription.startDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long', 
      day: 'numeric'
    })}`);
    console.log(`- End Date: ${new Date(verifySubscription.endDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })}`);
    console.log(`- Days Remaining: ${Math.max(0, diffDays)} days`);

    process.exit(0);

  } catch (error) {
    console.error('❌ Error creating test subscription:', error);
    process.exit(1);
  }
};

createTestSubscription();
