{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Register\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input, Select } from \"antd\";\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\nimport Logo from \"../../../assets/logo.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction Register() {\n  _s();\n  const [verification, setVerification] = useState(false);\n  const [data, setData] = useState(\"\");\n  const [otp, setOTP] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\");\n  const navigate = useNavigate();\n  const onFinish = async values => {\n    try {\n      const response = await registerUser(values);\n      if (response.success) {\n        message.success({\n          content: response.message,\n          duration: 6,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        // Add a small delay to let user see the success message\n        setTimeout(() => {\n          navigate(\"/login\");\n        }, 1500);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Registration failed\");\n        setVerification(false);\n      }\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\n      setVerification(false);\n    }\n  };\n  const verifyUser = async values => {\n    var _values$otp;\n    if (!((_values$otp = values.otp) !== null && _values$otp !== void 0 && _values$otp.trim())) {\n      message.error(\"🔢 Please enter the verification code\");\n      return;\n    }\n    if (values.otp.length !== 6) {\n      message.error(\"🔢 Verification code must be 6 digits\");\n      return;\n    }\n    if (values.otp === otp) {\n      message.loading(\"✅ Verifying your code...\", 1);\n      setTimeout(() => {\n        onFinish(data);\n      }, 1000);\n    } else {\n      message.error({\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\n        duration: 5,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n\n  // Enhanced error handling function\n  const showUserFriendlyError = (error, defaultMessage) => {\n    var _error$response, _error$response$data, _error$response2, _error$response2$data;\n    const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || defaultMessage;\n    const errorType = (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.errorType;\n\n    // Show different message styles based on error type\n    if (errorType === \"EMAIL_EXISTS\" || errorType === \"PHONE_EXISTS\") {\n      message.warning({\n        content: errorMessage,\n        duration: 6,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else if (errorType === \"INVALID_EMAIL\" || errorType === \"INVALID_PHONE\") {\n      message.error({\n        content: errorMessage,\n        duration: 5,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else {\n      message.error({\n        content: errorMessage,\n        duration: 4,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n  const generateOTP = async formData => {\n    var _formData$name, _formData$email, _formData$phoneNumber, _formData$password, _formData$confirmPass;\n    // Enhanced validation with user-friendly messages\n    if (!((_formData$name = formData.name) !== null && _formData$name !== void 0 && _formData$name.trim())) {\n      message.error(\"👤 Please enter your full name\");\n      return;\n    }\n    if (!((_formData$email = formData.email) !== null && _formData$email !== void 0 && _formData$email.trim())) {\n      message.error(\"📧 Please enter your email address\");\n      return;\n    }\n    if (!((_formData$phoneNumber = formData.phoneNumber) !== null && _formData$phoneNumber !== void 0 && _formData$phoneNumber.trim())) {\n      message.error(\"📱 Please enter your phone number\");\n      return;\n    }\n    if (!((_formData$password = formData.password) !== null && _formData$password !== void 0 && _formData$password.trim())) {\n      message.error(\"🔒 Please create a password\");\n      return;\n    }\n    if (!((_formData$confirmPass = formData.confirmPassword) !== null && _formData$confirmPass !== void 0 && _formData$confirmPass.trim())) {\n      message.error(\"🔒 Please confirm your password\");\n      return;\n    }\n    if (formData.password !== formData.confirmPassword) {\n      message.error(\"🔒 Passwords do not match. Please check and try again.\");\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await sendOTP(formData);\n      if (response.success) {\n        message.success({\n          content: response.message,\n          duration: 5,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        setData(formData);\n        setOTP(response.data);\n        setVerification(true);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Failed to send verification code\");\n      }\n    } catch (error) {\n      console.error(\"OTP generation error:\", error);\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"register-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register-card\",\n      children: verification ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Verify Your Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"We've sent a verification code to your email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"otp-instructions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"otp-info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"otp-info-title\",\n              children: \"\\uD83D\\uDCE7 Check Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"otp-info-text\",\n              children: [\"We've sent a \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"6-digit verification code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 32\n              }, this), \" to your email address.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-steps\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Open your email app or website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Look for an email from BrainWave\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Copy the 6-digit code from the email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Enter the code in the box below\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-help\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"help-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCA1 Can't find the email?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), \" Check your spam/junk folder or wait a few minutes for delivery.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: verifyUser,\n          className: \"register-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"otp\",\n            label: \"Verification Code\",\n            rules: [{\n              required: true,\n              message: \"Please enter the OTP!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"number\",\n              className: \"form-input otp-input\",\n              placeholder: \"Enter 6-digit code (e.g., 123456)\",\n              maxLength: 6\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter the verification code from your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"register-btn\",\n            disabled: loading,\n            children: loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resend-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"resend-text\",\n              children: \"Didn't receive the code?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"resend-btn\",\n              onClick: () => generateOTP(data),\n              disabled: loading,\n              children: \"\\uD83D\\uDCE7 Resend Verification Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"Join thousands of students learning with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: generateOTP,\n          className: \"register-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Full Name\",\n            rules: [{\n              required: true,\n              message: \"👤 Please enter your full name\"\n            }, {\n              min: 2,\n              message: \"👤 Name must be at least 2 characters long\"\n            }, {\n              max: 50,\n              message: \"👤 Name must be less than 50 characters\"\n            }, {\n              pattern: /^[a-zA-Z\\s]+$/,\n              message: \"👤 Name should only contain letters and spaces\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your full name (e.g., John Doe)\",\n              autoComplete: \"name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter your first and last name as you'd like it to appear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"school\",\n            label: \"School Name\",\n            rules: [{\n              required: true,\n              message: \"🏫 Please enter your school name\"\n            }, {\n              min: 3,\n              message: \"🏫 School name must be at least 3 characters long\"\n            }, {\n              max: 100,\n              message: \"🏫 School name must be less than 100 characters\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your school name (e.g., Dar es Salaam Secondary School)\",\n              autoComplete: \"organization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter the full name of your current school\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Education Level\",\n            rules: [{\n              required: true,\n              message: \"🎓 Please select your education level\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              onChange: value => setSchoolType(value),\n              className: \"form-input\",\n              placeholder: \"Choose your current education level\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"Primary\",\n                children: \"\\uD83C\\uDF92 Primary Education (Classes 1-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Secondary\",\n                children: \"\\uD83D\\uDCDA Secondary Education (Forms 1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Advance\",\n                children: \"\\uD83C\\uDF93 Advanced Level (Forms 5-6)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Select the education level you are currently studying\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class\",\n            label: \"Class/Form\",\n            rules: [{\n              required: true,\n              message: \"📝 Please select your class or form\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              className: \"form-input\",\n              placeholder: schoolType ? \"Select your class/form\" : \"Please select education level first\",\n              disabled: !schoolType,\n              children: [schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: i,\n                children: `📚 Class ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this)), schoolType === \"Secondary\" && [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `📖 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 21\n              }, this)), schoolType === \"Advance\" && [5, 6].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `🎓 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: [!schoolType && \"Please select your education level first\", schoolType === \"Primary\" && \"Select your current class (1-7)\", schoolType === \"Secondary\" && \"Select your current form (1-4)\", schoolType === \"Advance\" && \"Select your current form (5-6)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email Address\",\n            rules: [{\n              required: true,\n              message: \"Please enter your email!\"\n            }, {\n              type: \"email\",\n              message: \"Please enter a valid email address!\"\n            }, {\n              pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\n              message: \"Please enter a properly formatted email address!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              className: \"form-input\",\n              placeholder: \"Enter your email address (e.g., <EMAIL>)\",\n              autoComplete: \"email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"We'll send important updates to this email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"phoneNumber\",\n            label: \"Phone Number\",\n            rules: [{\n              required: true,\n              message: \"📱 Please enter your phone number\"\n            }, {\n              pattern: /^(\\+255[67]\\d{8}|0[67]\\d{8})$/,\n              message: \"📱 Please enter a valid Tanzanian mobile number starting with 06 or 07\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"tel\",\n              className: \"form-input\",\n              placeholder: \"Enter mobile number (e.g., **********, **********, +************)\",\n              autoComplete: \"tel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-help-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"Enter a valid Tanzanian mobile number for account verification\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-examples\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"phone-example-title\",\n                  children: \"\\u2705 Valid formats:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-examples-grid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"phone-example\",\n                    children: \"**********\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"phone-example\",\n                    children: \"**********\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"phone-example\",\n                    children: \"+************\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"phone-example\",\n                    children: \"+************\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"phone-note\",\n                  children: [\"\\uD83D\\uDCDE Must start with \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"06\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 66\n                  }, this), \" or \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"07\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 89\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            rules: [{\n              required: true,\n              message: \"Please enter your password!\"\n            }, {\n              min: 8,\n              message: \"Password must be at least 8 characters long!\"\n            }, {\n              pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n              message: \"Password must contain uppercase, lowercase, number and special character!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Create a strong password (min 8 characters)\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Must include: uppercase, lowercase, number, and special character\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"confirmPassword\",\n            label: \"Retype Password\",\n            dependencies: ['password'],\n            rules: [{\n              required: true,\n              message: \"Please retype your password!\"\n            }, ({\n              getFieldValue\n            }) => ({\n              validator(_, value) {\n                if (!value || getFieldValue('password') === value) {\n                  return Promise.resolve();\n                }\n                return Promise.reject(new Error('The two passwords do not match!'));\n              }\n            })],\n            children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Retype your password to confirm\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Must match the password above\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"register-btn\",\n              disabled: loading,\n              children: loading ? \"Creating Account...\" : \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account? \", \" \", /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"register-link\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"ZCOX0z1U6pPkSU7THaoXima+72A=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["Form", "message", "Input", "Select", "React", "useState", "Link", "useNavigate", "registerUser", "sendOTP", "Logo", "jsxDEV", "_jsxDEV", "Option", "Register", "_s", "verification", "setVerification", "data", "setData", "otp", "setOTP", "loading", "setLoading", "schoolType", "setSchoolType", "navigate", "onFinish", "values", "response", "success", "content", "duration", "style", "marginTop", "setTimeout", "showUserFriendlyError", "error", "console", "verifyUser", "_values$otp", "trim", "length", "defaultMessage", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errorMessage", "errorType", "warning", "generateOTP", "formData", "_formData$name", "_formData$email", "_formData$phoneNumber", "_formData$password", "_formData$confirmPass", "name", "email", "phoneNumber", "password", "confirmPassword", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "label", "rules", "required", "type", "placeholder", "max<PERSON><PERSON><PERSON>", "disabled", "onClick", "min", "max", "pattern", "autoComplete", "onChange", "value", "map", "i", "Password", "dependencies", "getFieldValue", "validator", "_", "Promise", "resolve", "reject", "Error", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Register/index.js"], "sourcesContent": ["import { Form, message, Input, Select } from \"antd\";\r\nimport React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\r\nimport Logo from \"../../../assets/logo.png\";\r\n\r\nconst { Option } = Select;\r\n\r\nfunction Register() {\r\n  const [verification, setVerification] = useState(false);\r\n  const [data, setData] = useState(\"\");\r\n  const [otp, setOTP] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [schoolType, setSchoolType] = useState(\"\");\r\n  const navigate = useNavigate();\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      const response = await registerUser(values);\r\n      if (response.success) {\r\n        message.success({\r\n          content: response.message,\r\n          duration: 6,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        // Add a small delay to let user see the success message\r\n        setTimeout(() => {\r\n          navigate(\"/login\");\r\n        }, 1500);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Registration failed\");\r\n        setVerification(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Registration error:\", error);\r\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\r\n      setVerification(false);\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (!values.otp?.trim()) {\r\n      message.error(\"🔢 Please enter the verification code\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp.length !== 6) {\r\n      message.error(\"🔢 Verification code must be 6 digits\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp === otp) {\r\n      message.loading(\"✅ Verifying your code...\", 1);\r\n      setTimeout(() => {\r\n        onFinish(data);\r\n      }, 1000);\r\n    } else {\r\n      message.error({\r\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\r\n        duration: 5,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  // Enhanced error handling function\r\n  const showUserFriendlyError = (error, defaultMessage) => {\r\n    const errorMessage = error.response?.data?.message || error.message || defaultMessage;\r\n    const errorType = error.response?.data?.errorType;\r\n\r\n    // Show different message styles based on error type\r\n    if (errorType === \"EMAIL_EXISTS\" || errorType === \"PHONE_EXISTS\") {\r\n      message.warning({\r\n        content: errorMessage,\r\n        duration: 6,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else if (errorType === \"INVALID_EMAIL\" || errorType === \"INVALID_PHONE\") {\r\n      message.error({\r\n        content: errorMessage,\r\n        duration: 5,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else {\r\n      message.error({\r\n        content: errorMessage,\r\n        duration: 4,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  const generateOTP = async (formData) => {\r\n    // Enhanced validation with user-friendly messages\r\n    if (!formData.name?.trim()) {\r\n      message.error(\"👤 Please enter your full name\");\r\n      return;\r\n    }\r\n    if (!formData.email?.trim()) {\r\n      message.error(\"📧 Please enter your email address\");\r\n      return;\r\n    }\r\n    if (!formData.phoneNumber?.trim()) {\r\n      message.error(\"📱 Please enter your phone number\");\r\n      return;\r\n    }\r\n    if (!formData.password?.trim()) {\r\n      message.error(\"🔒 Please create a password\");\r\n      return;\r\n    }\r\n    if (!formData.confirmPassword?.trim()) {\r\n      message.error(\"🔒 Please confirm your password\");\r\n      return;\r\n    }\r\n    if (formData.password !== formData.confirmPassword) {\r\n      message.error(\"🔒 Passwords do not match. Please check and try again.\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await sendOTP(formData);\r\n      if (response.success) {\r\n        message.success({\r\n          content: response.message,\r\n          duration: 5,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        setData(formData);\r\n        setOTP(response.data);\r\n        setVerification(true);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Failed to send verification code\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"OTP generation error:\", error);\r\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"register-container\">\r\n      <div className=\"register-card\">\r\n        {verification ? (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Verify Your Email</h1>\r\n              <p className=\"register-subtitle\">We've sent a verification code to your email</p>\r\n            </div>\r\n\r\n            {/* OTP Instructions */}\r\n            <div className=\"otp-instructions\">\r\n              <div className=\"otp-info-card\">\r\n                <h3 className=\"otp-info-title\">📧 Check Your Email</h3>\r\n                <p className=\"otp-info-text\">\r\n                  We've sent a <strong>6-digit verification code</strong> to your email address.\r\n                </p>\r\n                <div className=\"otp-steps\">\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">1</span>\r\n                    <span className=\"step-text\">Open your email app or website</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">2</span>\r\n                    <span className=\"step-text\">Look for an email from BrainWave</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">3</span>\r\n                    <span className=\"step-text\">Copy the 6-digit code from the email</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">4</span>\r\n                    <span className=\"step-text\">Enter the code in the box below</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"otp-help\">\r\n                  <p className=\"help-text\">\r\n                    <strong>💡 Can't find the email?</strong> Check your spam/junk folder or wait a few minutes for delivery.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Form layout=\"vertical\" onFinish={verifyUser} className=\"register-form\">\r\n              <Form.Item name=\"otp\" label=\"Verification Code\" rules={[{ required: true, message: \"Please enter the OTP!\" }]}>\r\n                <Input\r\n                  type=\"number\"\r\n                  className=\"form-input otp-input\"\r\n                  placeholder=\"Enter 6-digit code (e.g., 123456)\"\r\n                  maxLength={6}\r\n                />\r\n                <p className=\"form-help-text\">Enter the verification code from your email</p>\r\n              </Form.Item>\r\n\r\n              <button type=\"submit\" className=\"register-btn\" disabled={loading}>\r\n                {loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"}\r\n              </button>\r\n\r\n              <div className=\"resend-section\">\r\n                <p className=\"resend-text\">Didn't receive the code?</p>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"resend-btn\"\r\n                  onClick={() => generateOTP(data)}\r\n                  disabled={loading}\r\n                >\r\n                  📧 Resend Verification Code\r\n                </button>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Create Account</h1>\r\n              <p className=\"register-subtitle\">Join thousands of students learning with BrainWave</p>\r\n            </div>\r\n\r\n\r\n\r\n            <Form layout=\"vertical\" onFinish={generateOTP} className=\"register-form\">\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"Full Name\"\r\n                rules={[\r\n                  { required: true, message: \"👤 Please enter your full name\" },\r\n                  { min: 2, message: \"👤 Name must be at least 2 characters long\" },\r\n                  { max: 50, message: \"👤 Name must be less than 50 characters\" },\r\n                  {\r\n                    pattern: /^[a-zA-Z\\s]+$/,\r\n                    message: \"👤 Name should only contain letters and spaces\"\r\n                  }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your full name (e.g., John Doe)\"\r\n                  autoComplete=\"name\"\r\n                />\r\n                <p className=\"form-help-text\">Enter your first and last name as you'd like it to appear</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"school\"\r\n                label=\"School Name\"\r\n                rules={[\r\n                  { required: true, message: \"🏫 Please enter your school name\" },\r\n                  { min: 3, message: \"🏫 School name must be at least 3 characters long\" },\r\n                  { max: 100, message: \"🏫 School name must be less than 100 characters\" }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your school name (e.g., Dar es Salaam Secondary School)\"\r\n                  autoComplete=\"organization\"\r\n                />\r\n                <p className=\"form-help-text\">Enter the full name of your current school</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"level\"\r\n                label=\"Education Level\"\r\n                rules={[{ required: true, message: \"🎓 Please select your education level\" }]}\r\n              >\r\n                <Select\r\n                  onChange={(value) => setSchoolType(value)}\r\n                  className=\"form-input\"\r\n                  placeholder=\"Choose your current education level\"\r\n                >\r\n                  <Option value=\"Primary\">🎒 Primary Education (Classes 1-7)</Option>\r\n                  <Option value=\"Secondary\">📚 Secondary Education (Forms 1-4)</Option>\r\n                  <Option value=\"Advance\">🎓 Advanced Level (Forms 5-6)</Option>\r\n                </Select>\r\n                <p className=\"form-help-text\">Select the education level you are currently studying</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"class\"\r\n                label=\"Class/Form\"\r\n                rules={[{ required: true, message: \"📝 Please select your class or form\" }]}\r\n              >\r\n                <Select\r\n                  className=\"form-input\"\r\n                  placeholder={schoolType ? \"Select your class/form\" : \"Please select education level first\"}\r\n                  disabled={!schoolType}\r\n                >\r\n                  {schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map((i) => (\r\n                    <Option key={i} value={i}>{`📚 Class ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Secondary\" && [1, 2, 3, 4].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`📖 Form ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Advance\" && [5, 6].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`🎓 Form ${i}`}</Option>\r\n                  ))}\r\n                </Select>\r\n                <p className=\"form-help-text\">\r\n                  {!schoolType && \"Please select your education level first\"}\r\n                  {schoolType === \"Primary\" && \"Select your current class (1-7)\"}\r\n                  {schoolType === \"Secondary\" && \"Select your current form (1-4)\"}\r\n                  {schoolType === \"Advance\" && \"Select your current form (5-6)\"}\r\n                </p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"Email Address\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your email!\" },\r\n                  { type: \"email\", message: \"Please enter a valid email address!\" },\r\n                  {\r\n                    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\r\n                    message: \"Please enter a properly formatted email address!\"\r\n                  }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"email\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your email address (e.g., <EMAIL>)\"\r\n                  autoComplete=\"email\"\r\n                />\r\n                <p className=\"form-help-text\">We'll send important updates to this email</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phoneNumber\"\r\n                label=\"Phone Number\"\r\n                rules={[\r\n                  { required: true, message: \"📱 Please enter your phone number\" },\r\n                  {\r\n                    pattern: /^(\\+255[67]\\d{8}|0[67]\\d{8})$/,\r\n                    message: \"📱 Please enter a valid Tanzanian mobile number starting with 06 or 07\"\r\n                  },\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"tel\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter mobile number (e.g., **********, **********, +************)\"\r\n                  autoComplete=\"tel\"\r\n                />\r\n                <div className=\"phone-help-section\">\r\n                  <p className=\"form-help-text\">Enter a valid Tanzanian mobile number for account verification</p>\r\n                  <div className=\"phone-examples\">\r\n                    <p className=\"phone-example-title\">✅ Valid formats:</p>\r\n                    <div className=\"phone-examples-grid\">\r\n                      <span className=\"phone-example\">**********</span>\r\n                      <span className=\"phone-example\">**********</span>\r\n                      <span className=\"phone-example\">+************</span>\r\n                      <span className=\"phone-example\">+************</span>\r\n                    </div>\r\n                    <p className=\"phone-note\">📞 Must start with <strong>06</strong> or <strong>07</strong></p>\r\n                  </div>\r\n                </div>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                label=\"Password\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your password!\" },\r\n                  { min: 8, message: \"Password must be at least 8 characters long!\" },\r\n                  {\r\n                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\r\n                    message: \"Password must contain uppercase, lowercase, number and special character!\"\r\n                  }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Create a strong password (min 8 characters)\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must include: uppercase, lowercase, number, and special character</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                label=\"Retype Password\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: \"Please retype your password!\" },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('The two passwords do not match!'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Retype your password to confirm\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must match the password above</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <button type=\"submit\" className=\"register-btn\" disabled={loading}>\r\n                  {loading ? \"Creating Account...\" : \"Create Account\"}\r\n                </button>\r\n              </Form.Item>\r\n            </Form>\r\n\r\n            <div className=\"register-footer\">\r\n              <p>\r\n                Already have an account? {\" \"}\r\n                <Link to=\"/login\" className=\"register-link\">\r\n                  Sign In\r\n                </Link>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACnD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAC/D,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAM;EAAEC;AAAO,CAAC,GAAGV,MAAM;AAEzB,SAASW,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACa,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACe,GAAG,EAAEC,MAAM,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMqB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,YAAY,CAACoB,MAAM,CAAC;MAC3C,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB7B,OAAO,CAAC6B,OAAO,CAAC;UACdC,OAAO,EAAEF,QAAQ,CAAC5B,OAAO;UACzB+B,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACF;QACAC,UAAU,CAAC,MAAM;UACfT,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLU,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,qBAAqB,CAAC;QAC1DZ,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CD,qBAAqB,CAACC,KAAK,EAAE,wCAAwC,CAAC;MACtEpB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsB,UAAU,GAAG,MAAOX,MAAM,IAAK;IAAA,IAAAY,WAAA;IACnC,IAAI,GAAAA,WAAA,GAACZ,MAAM,CAACR,GAAG,cAAAoB,WAAA,eAAVA,WAAA,CAAYC,IAAI,CAAC,CAAC,GAAE;MACvBxC,OAAO,CAACoC,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIT,MAAM,CAACR,GAAG,CAACsB,MAAM,KAAK,CAAC,EAAE;MAC3BzC,OAAO,CAACoC,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIT,MAAM,CAACR,GAAG,KAAKA,GAAG,EAAE;MACtBnB,OAAO,CAACqB,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;MAC9Ca,UAAU,CAAC,MAAM;QACfR,QAAQ,CAACT,IAAI,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLjB,OAAO,CAACoC,KAAK,CAAC;QACZN,OAAO,EAAE,8EAA8E;QACvFC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAACC,KAAK,EAAEM,cAAc,KAAK;IAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACvD,MAAMC,YAAY,GAAG,EAAAJ,eAAA,GAAAP,KAAK,CAACR,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB1B,IAAI,cAAA2B,oBAAA,uBAApBA,oBAAA,CAAsB5C,OAAO,KAAIoC,KAAK,CAACpC,OAAO,IAAI0C,cAAc;IACrF,MAAMM,SAAS,IAAAH,gBAAA,GAAGT,KAAK,CAACR,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBE,SAAS;;IAEjD;IACA,IAAIA,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,cAAc,EAAE;MAChEhD,OAAO,CAACiD,OAAO,CAAC;QACdnB,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIe,SAAS,KAAK,eAAe,IAAIA,SAAS,KAAK,eAAe,EAAE;MACzEhD,OAAO,CAACoC,KAAK,CAAC;QACZN,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLjC,OAAO,CAACoC,KAAK,CAAC;QACZN,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMiB,WAAW,GAAG,MAAOC,QAAQ,IAAK;IAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA;IACtC;IACA,IAAI,GAAAJ,cAAA,GAACD,QAAQ,CAACM,IAAI,cAAAL,cAAA,eAAbA,cAAA,CAAeZ,IAAI,CAAC,CAAC,GAAE;MAC1BxC,OAAO,CAACoC,KAAK,CAAC,gCAAgC,CAAC;MAC/C;IACF;IACA,IAAI,GAAAiB,eAAA,GAACF,QAAQ,CAACO,KAAK,cAAAL,eAAA,eAAdA,eAAA,CAAgBb,IAAI,CAAC,CAAC,GAAE;MAC3BxC,OAAO,CAACoC,KAAK,CAAC,oCAAoC,CAAC;MACnD;IACF;IACA,IAAI,GAAAkB,qBAAA,GAACH,QAAQ,CAACQ,WAAW,cAAAL,qBAAA,eAApBA,qBAAA,CAAsBd,IAAI,CAAC,CAAC,GAAE;MACjCxC,OAAO,CAACoC,KAAK,CAAC,mCAAmC,CAAC;MAClD;IACF;IACA,IAAI,GAAAmB,kBAAA,GAACJ,QAAQ,CAACS,QAAQ,cAAAL,kBAAA,eAAjBA,kBAAA,CAAmBf,IAAI,CAAC,CAAC,GAAE;MAC9BxC,OAAO,CAACoC,KAAK,CAAC,6BAA6B,CAAC;MAC5C;IACF;IACA,IAAI,GAAAoB,qBAAA,GAACL,QAAQ,CAACU,eAAe,cAAAL,qBAAA,eAAxBA,qBAAA,CAA0BhB,IAAI,CAAC,CAAC,GAAE;MACrCxC,OAAO,CAACoC,KAAK,CAAC,iCAAiC,CAAC;MAChD;IACF;IACA,IAAIe,QAAQ,CAACS,QAAQ,KAAKT,QAAQ,CAACU,eAAe,EAAE;MAClD7D,OAAO,CAACoC,KAAK,CAAC,wDAAwD,CAAC;MACvE;IACF;IAEAd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMpB,OAAO,CAAC2C,QAAQ,CAAC;MACxC,IAAIvB,QAAQ,CAACC,OAAO,EAAE;QACpB7B,OAAO,CAAC6B,OAAO,CAAC;UACdC,OAAO,EAAEF,QAAQ,CAAC5B,OAAO;UACzB+B,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACFf,OAAO,CAACiC,QAAQ,CAAC;QACjB/B,MAAM,CAACQ,QAAQ,CAACX,IAAI,CAAC;QACrBD,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACLmB,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,kCAAkC,CAAC;MACzE;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,qBAAqB,CAACC,KAAK,EAAE,oEAAoE,CAAC;IACpG;IACAd,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAID,oBACEX,OAAA;IAAKmD,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjCpD,OAAA;MAAKmD,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BhD,YAAY,gBACXJ,OAAA;QAAAoD,QAAA,gBACEpD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpD,OAAA;YAAKqD,GAAG,EAAEvD,IAAK;YAACwD,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjE1D,OAAA;YAAImD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD1D,OAAA;YAAGmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAA4C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAGN1D,OAAA;UAAKmD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BpD,OAAA;YAAKmD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpD,OAAA;cAAImD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD1D,OAAA;cAAGmD,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,eACd,eAAApD,OAAA;gBAAAoD,QAAA,EAAQ;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,2BACzD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ1D,OAAA;cAAKmD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBpD,OAAA;gBAAKmD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpD,OAAA;kBAAMmD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC1D,OAAA;kBAAMmD,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACN1D,OAAA;gBAAKmD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpD,OAAA;kBAAMmD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC1D,OAAA;kBAAMmD,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAgC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN1D,OAAA;gBAAKmD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpD,OAAA;kBAAMmD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC1D,OAAA;kBAAMmD,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAoC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACN1D,OAAA;gBAAKmD,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBpD,OAAA;kBAAMmD,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtC1D,OAAA;kBAAMmD,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1D,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpD,OAAA;gBAAGmD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtBpD,OAAA;kBAAAoD,QAAA,EAAQ;gBAAwB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oEAC3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1D,OAAA,CAACZ,IAAI;UAACuE,MAAM,EAAC,UAAU;UAAC5C,QAAQ,EAAEY,UAAW;UAACwB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACrEpD,OAAA,CAACZ,IAAI,CAACwE,IAAI;YAACd,IAAI,EAAC,KAAK;YAACe,KAAK,EAAC,mBAAmB;YAACC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAA+D,QAAA,gBAC5GpD,OAAA,CAACV,KAAK;cACJ0E,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,sBAAsB;cAChCc,WAAW,EAAC,mCAAmC;cAC/CC,SAAS,EAAE;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACF1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA2C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEZ1D,OAAA;YAAQgE,IAAI,EAAC,QAAQ;YAACb,SAAS,EAAC,cAAc;YAACgB,QAAQ,EAAEzD,OAAQ;YAAA0C,QAAA,EAC9D1C,OAAO,GAAG,gBAAgB,GAAG;UAAkC;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAET1D,OAAA;YAAKmD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpD,OAAA;cAAGmD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvD1D,OAAA;cACEgE,IAAI,EAAC,QAAQ;cACbb,SAAS,EAAC,YAAY;cACtBiB,OAAO,EAAEA,CAAA,KAAM7B,WAAW,CAACjC,IAAI,CAAE;cACjC6D,QAAQ,EAAEzD,OAAQ;cAAA0C,QAAA,EACnB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,gBAEN1D,OAAA;QAAAoD,QAAA,gBACEpD,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpD,OAAA;YAAKqD,GAAG,EAAEvD,IAAK;YAACwD,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjE1D,OAAA;YAAImD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClD1D,OAAA;YAAGmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAIN1D,OAAA,CAACZ,IAAI;UAACuE,MAAM,EAAC,UAAU;UAAC5C,QAAQ,EAAEwB,WAAY;UAACY,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACtEpD,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,MAAM;YACXe,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAAiC,CAAC,EAC7D;cAAEgF,GAAG,EAAE,CAAC;cAAEhF,OAAO,EAAE;YAA6C,CAAC,EACjE;cAAEiF,GAAG,EAAE,EAAE;cAAEjF,OAAO,EAAE;YAA0C,CAAC,EAC/D;cACEkF,OAAO,EAAE,eAAe;cACxBlF,OAAO,EAAE;YACX,CAAC,CACD;YAAA+D,QAAA,gBAEFpD,OAAA,CAACV,KAAK;cACJ0E,IAAI,EAAC,MAAM;cACXb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,uCAAuC;cACnDO,YAAY,EAAC;YAAM;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACF1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAyD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,QAAQ;YACbe,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAAmC,CAAC,EAC/D;cAAEgF,GAAG,EAAE,CAAC;cAAEhF,OAAO,EAAE;YAAoD,CAAC,EACxE;cAAEiF,GAAG,EAAE,GAAG;cAAEjF,OAAO,EAAE;YAAkD,CAAC,CACxE;YAAA+D,QAAA,gBAEFpD,OAAA,CAACV,KAAK;cACJ0E,IAAI,EAAC,MAAM;cACXb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,+DAA+D;cAC3EO,YAAY,EAAC;YAAc;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACF1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA0C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,OAAO;YACZe,KAAK,EAAC,iBAAiB;YACvBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAAwC,CAAC,CAAE;YAAA+D,QAAA,gBAE9EpD,OAAA,CAACT,MAAM;cACLkF,QAAQ,EAAGC,KAAK,IAAK7D,aAAa,CAAC6D,KAAK,CAAE;cAC1CvB,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,qCAAqC;cAAAb,QAAA,gBAEjDpD,OAAA,CAACC,MAAM;gBAACyE,KAAK,EAAC,SAAS;gBAAAtB,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnE1D,OAAA,CAACC,MAAM;gBAACyE,KAAK,EAAC,WAAW;gBAAAtB,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrE1D,OAAA,CAACC,MAAM;gBAACyE,KAAK,EAAC,SAAS;gBAAAtB,QAAA,EAAC;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACT1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAqD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,OAAO;YACZe,KAAK,EAAC,YAAY;YAClBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAAsC,CAAC,CAAE;YAAA+D,QAAA,gBAE5EpD,OAAA,CAACT,MAAM;cACL4D,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAErD,UAAU,GAAG,wBAAwB,GAAG,qCAAsC;cAC3FuD,QAAQ,EAAE,CAACvD,UAAW;cAAAwC,QAAA,GAErBxC,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC+D,GAAG,CAAEC,CAAC,iBACvD5E,OAAA,CAACC,MAAM;gBAASyE,KAAK,EAAEE,CAAE;gBAAAxB,QAAA,EAAG,YAAWwB,CAAE;cAAC,GAA7BA,CAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CACpD,CAAC,EACD9C,UAAU,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC+D,GAAG,CAAEC,CAAC,iBAChD5E,OAAA,CAACC,MAAM;gBAASyE,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAAxB,QAAA,EAAG,WAAUwB,CAAE;cAAC,GAAtCA,CAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC,EACD9C,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC+D,GAAG,CAAEC,CAAC,iBACxC5E,OAAA,CAACC,MAAM;gBAASyE,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAAxB,QAAA,EAAG,WAAUwB,CAAE;cAAC,GAAtCA,CAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACT1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAC1B,CAACxC,UAAU,IAAI,0CAA0C,EACzDA,UAAU,KAAK,SAAS,IAAI,iCAAiC,EAC7DA,UAAU,KAAK,WAAW,IAAI,gCAAgC,EAC9DA,UAAU,KAAK,SAAS,IAAI,gCAAgC;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,OAAO;YACZe,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAA2B,CAAC,EACvD;cAAE2E,IAAI,EAAE,OAAO;cAAE3E,OAAO,EAAE;YAAsC,CAAC,EACjE;cACEkF,OAAO,EAAE,kDAAkD;cAC3DlF,OAAO,EAAE;YACX,CAAC,CACD;YAAA+D,QAAA,gBAEFpD,OAAA,CAACV,KAAK;cACJ0E,IAAI,EAAC,OAAO;cACZb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,sDAAsD;cAClEO,YAAY,EAAC;YAAO;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACF1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA0C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,aAAa;YAClBe,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAAoC,CAAC,EAChE;cACEkF,OAAO,EAAE,+BAA+B;cACxClF,OAAO,EAAE;YACX,CAAC,CACD;YAAA+D,QAAA,gBAEFpD,OAAA,CAACV,KAAK;cACJ0E,IAAI,EAAC,KAAK;cACVb,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,mEAAmE;cAC/EO,YAAY,EAAC;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF1D,OAAA;cAAKmD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCpD,OAAA;gBAAGmD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAA8D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChG1D,OAAA;gBAAKmD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpD,OAAA;kBAAGmD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACvD1D,OAAA;kBAAKmD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCpD,OAAA;oBAAMmD,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjD1D,OAAA;oBAAMmD,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjD1D,OAAA;oBAAMmD,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpD1D,OAAA;oBAAMmD,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN1D,OAAA;kBAAGmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,+BAAmB,eAAApD,OAAA;oBAAAoD,QAAA,EAAQ;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,QAAI,eAAA1D,OAAA;oBAAAoD,QAAA,EAAQ;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,UAAU;YACfe,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAA8B,CAAC,EAC1D;cAAEgF,GAAG,EAAE,CAAC;cAAEhF,OAAO,EAAE;YAA+C,CAAC,EACnE;cACEkF,OAAO,EAAE,iEAAiE;cAC1ElF,OAAO,EAAE;YACX,CAAC,CACD;YAAA+D,QAAA,gBAEFpD,OAAA,CAACV,KAAK,CAACuF,QAAQ;cACb1B,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,6CAA6C;cACzDO,YAAY,EAAC;YAAc;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACF1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAiE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YACRd,IAAI,EAAC,iBAAiB;YACtBe,KAAK,EAAC,iBAAiB;YACvBiB,YAAY,EAAE,CAAC,UAAU,CAAE;YAC3BhB,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAE1E,OAAO,EAAE;YAA+B,CAAC,EAC3D,CAAC;cAAE0F;YAAc,CAAC,MAAM;cACtBC,SAASA,CAACC,CAAC,EAAEP,KAAK,EAAE;gBAClB,IAAI,CAACA,KAAK,IAAIK,aAAa,CAAC,UAAU,CAAC,KAAKL,KAAK,EAAE;kBACjD,OAAOQ,OAAO,CAACC,OAAO,CAAC,CAAC;gBAC1B;gBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,iCAAiC,CAAC,CAAC;cACrE;YACF,CAAC,CAAC,CACF;YAAAjC,QAAA,gBAEFpD,OAAA,CAACV,KAAK,CAACuF,QAAQ;cACb1B,SAAS,EAAC,YAAY;cACtBc,WAAW,EAAC,iCAAiC;cAC7CO,YAAY,EAAC;YAAc;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACF1D,OAAA;cAAGmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEZ1D,OAAA,CAACZ,IAAI,CAACwE,IAAI;YAAAR,QAAA,eACRpD,OAAA;cAAQgE,IAAI,EAAC,QAAQ;cAACb,SAAS,EAAC,cAAc;cAACgB,QAAQ,EAAEzD,OAAQ;cAAA0C,QAAA,EAC9D1C,OAAO,GAAG,qBAAqB,GAAG;YAAgB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEP1D,OAAA;UAAKmD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BpD,OAAA;YAAAoD,QAAA,GAAG,2BACwB,EAAC,GAAG,eAC7BpD,OAAA,CAACN,IAAI;cAAC4F,EAAE,EAAC,QAAQ;cAACnC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE5C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvD,EAAA,CApaQD,QAAQ;EAAA,QAMEP,WAAW;AAAA;AAAA4F,EAAA,GANrBrF,QAAQ;AAsajB,eAAeA,QAAQ;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}