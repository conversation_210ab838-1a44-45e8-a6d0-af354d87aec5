const axios = require('axios');

async function testWebhook() {
  console.log('🔔 Testing Webhook Endpoint...\n');

  try {
    // Test GET webhook-test endpoint
    console.log('📡 Testing GET /api/payment/webhook-test...');
    const getResponse = await axios.get('http://localhost:5000/api/payment/webhook-test');
    console.log('✅ GET Response:', getResponse.data);
    console.log('');

    // Test POST webhook-test endpoint
    console.log('📡 Testing POST /api/payment/webhook-test...');
    const postResponse = await axios.post('http://localhost:5000/api/payment/webhook-test', {
      test: 'webhook test data',
      timestamp: new Date().toISOString()
    });
    console.log('✅ POST Response:', postResponse.data);
    console.log('');

    // Simulate a real webhook from ZenoPay
    console.log('📡 Simulating ZenoPay webhook notification...');
    const webhookData = {
      order_id: "TEST_ORDER_123456",
      payment_status: "COMPLETED",
      reference: "ZP123456789",
      metadata: {
        test: true,
        timestamp: new Date().toISOString()
      }
    };

    console.log('📤 Webhook data:', JSON.stringify(webhookData, null, 2));

    // Note: This will fail because we need a valid API key and existing order
    try {
      const webhookResponse = await axios.post('http://localhost:5000/api/payment/webhook', webhookData, {
        headers: {
          'x-api-key': process.env.ZENOPAY_API_KEY || 'test-key',
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Webhook Response:', webhookResponse.data);
    } catch (webhookError) {
      console.log('⚠️ Webhook simulation failed (expected for test data):');
      if (webhookError.response) {
        console.log('Status:', webhookError.response.status);
        console.log('Response:', webhookError.response.data);
      } else {
        console.log('Error:', webhookError.message);
      }
    }

    console.log('\n📋 Webhook Test Summary:');
    console.log('✅ Webhook endpoint is accessible');
    console.log('✅ GET test endpoint working');
    console.log('✅ POST test endpoint working');
    console.log('✅ Ready to receive real ZenoPay notifications');
    console.log('');
    console.log('🎯 Next Steps:');
    console.log('1. Make a real payment to test the full flow');
    console.log('2. ZenoPay will send webhook notification automatically');
    console.log('3. Success modal will appear immediately');
    console.log('4. All pages will be unlocked for the user');

  } catch (error) {
    console.error('❌ Webhook test failed:', error.message);
  }
}

testWebhook();
