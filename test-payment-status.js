const axios = require('axios');

const testPaymentStatus = async () => {
  console.log('🧪 Testing Payment Status API...\n');

  try {
    // Test the payment status endpoint
    const response = await axios.get('http://localhost:5000/api/payment/check-payment-status', {
      headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE', // You'll need a valid token
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ API Response Status:', response.status);
    console.log('📋 API Response Data:');
    console.log(JSON.stringify(response.data, null, 2));

    // Check the structure
    const data = response.data;
    console.log('\n🔍 Data Structure Analysis:');
    console.log('- Has paymentStatus:', !!data.paymentStatus);
    console.log('- Has status:', !!data.status);
    console.log('- Has plan:', !!data.plan);
    console.log('- Has error:', !!data.error);

    if (data.paymentStatus && data.status) {
      console.log('\n📊 Payment Details:');
      console.log('- Payment Status:', data.paymentStatus);
      console.log('- Subscription Status:', data.status);
      console.log('- Plan Title:', data.plan?.title || 'No title');
      console.log('- Amount:', data.amount);
      console.log('- Start Date:', data.startDate);
      console.log('- End Date:', data.endDate);

      const isConfirmed = data.paymentStatus === 'paid' && data.status === 'active';
      console.log('\n🎯 Payment Confirmed:', isConfirmed ? '✅ YES' : '❌ NO');
    }

  } catch (error) {
    console.error('❌ API Error:', error.response?.status || error.message);
    console.error('📋 Error Data:', error.response?.data || 'No error data');
  }

  console.log('\n🔧 Debugging Tips:');
  console.log('1. Make sure you have a valid JWT token');
  console.log('2. Ensure user has an active subscription');
  console.log('3. Check if webhook has been called');
  console.log('4. Verify subscription status in database');
};

testPaymentStatus();
