{"ast": null, "code": "import{createSlice}from\"@reduxjs/toolkit\";export const loaderSlice=createSlice({name:\"loader\",initialState:{loading:false},reducers:{ShowLoading(state){state.loading=true;},HideLoading(state){state.loading=false;}}});export const{ShowLoading,HideLoading}=loaderSlice.actions;export default loaderSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "loaderSlice", "name", "initialState", "loading", "reducers", "ShowLoading", "state", "HideLoading", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/redux/loaderSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\nexport const loaderSlice = createSlice({\r\n  name: \"loader\",\r\n  initialState: {\r\n    loading: false,\r\n  },\r\n  reducers: {\r\n    ShowLoading(state) {\r\n      state.loading = true;\r\n    },\r\n    HideLoading(state) {\r\n      state.loading = false;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { ShowLoading, HideLoading } = loaderSlice.actions;\r\n\r\nexport default loaderSlice.reducer;"], "mappings": "AAAA,OAASA,WAAW,KAAQ,kBAAkB,CAE9C,MAAO,MAAM,CAAAC,WAAW,CAAGD,WAAW,CAAC,CACrCE,IAAI,CAAE,QAAQ,CACdC,YAAY,CAAE,CACZC,OAAO,CAAE,KACX,CAAC,CACDC,QAAQ,CAAE,CACRC,WAAWA,CAACC,KAAK,CAAE,CACjBA,KAAK,CAACH,OAAO,CAAG,IAAI,CACtB,CAAC,CACDI,WAAWA,CAACD,KAAK,CAAE,CACjBA,KAAK,CAACH,OAAO,CAAG,KAAK,CACvB,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEE,WAAW,CAAEE,WAAY,CAAC,CAAGP,WAAW,CAACQ,OAAO,CAE/D,cAAe,CAAAR,WAAW,CAACS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}