{"ast": null, "code": "import React from'react';import{Tb<PERSON><PERSON>tTriangle,TbRefresh,TbHome}from'react-icons/tb';import{useNavigate}from'react-router-dom';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";class RankingErrorBoundary extends React.Component{constructor(props){super(props);this.state={hasError:false,error:null,errorInfo:null};}static getDerivedStateFromError(error){return{hasError:true};}componentDidCatch(error,errorInfo){this.setState({error:error,errorInfo:errorInfo});// Log error to console for debugging\nconsole.error('Ranking Error:',error,errorInfo);}render(){if(this.state.hasError){return/*#__PURE__*/_jsx(RankingErrorFallback,{error:this.state.error,resetError:()=>this.setState({hasError:false,error:null,errorInfo:null})});}return this.props.children;}}const RankingErrorFallback=_ref=>{let{error,resetError}=_ref;const navigate=useNavigate();const handleRetry=()=>{resetError();window.location.reload();};const handleGoHome=()=>{navigate('/user/hub');};return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",children:/*#__PURE__*/_jsx(TbAlertTriangle,{className:\"w-10 h-10 text-red-600\"})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900 mb-4\",children:\"Ranking System Error\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"We encountered an error while loading the rankings. This might be a temporary issue with the server connection.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleRetry,className:\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\",children:[/*#__PURE__*/_jsx(TbRefresh,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Refresh Rankings\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleGoHome,className:\"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\",children:[/*#__PURE__*/_jsx(TbHome,{className:\"w-5 h-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Go to Hub\"})]})]}),process.env.NODE_ENV==='development'&&error&&/*#__PURE__*/_jsxs(\"details\",{className:\"mt-6 text-left\",children:[/*#__PURE__*/_jsx(\"summary\",{className:\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",children:\"Error Details (Development)\"}),/*#__PURE__*/_jsx(\"pre\",{className:\"mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-32\",children:error.toString()})]})]})});};export default RankingErrorBoundary;", "map": {"version": 3, "names": ["React", "TbAlertTriangle", "TbRefresh", "TbHome", "useNavigate", "jsx", "_jsx", "jsxs", "_jsxs", "RankingError<PERSON><PERSON><PERSON>ry", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "setState", "console", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetError", "children", "_ref", "navigate", "handleRetry", "window", "location", "reload", "handleGoHome", "className", "onClick", "process", "env", "NODE_ENV", "toString"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/RankingErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport { TbAlertTriangle, TbRefresh, TbHome } from 'react-icons/tb';\nimport { useNavigate } from 'react-router-dom';\n\nclass RankingErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n    \n    // Log error to console for debugging\n    console.error('Ranking Error:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return <RankingErrorFallback \n        error={this.state.error} \n        resetError={() => this.setState({ hasError: false, error: null, errorInfo: null })}\n      />;\n    }\n\n    return this.props.children;\n  }\n}\n\nconst RankingErrorFallback = ({ error, resetError }) => {\n  const navigate = useNavigate();\n\n  const handleRetry = () => {\n    resetError();\n    window.location.reload();\n  };\n\n  const handleGoHome = () => {\n    navigate('/user/hub');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\">\n        <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <TbAlertTriangle className=\"w-10 h-10 text-red-600\" />\n        </div>\n        \n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n          Ranking System Error\n        </h1>\n        \n        <p className=\"text-gray-600 mb-6\">\n          We encountered an error while loading the rankings. This might be a temporary issue with the server connection.\n        </p>\n        \n        <div className=\"space-y-3\">\n          <button\n            onClick={handleRetry}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\"\n          >\n            <TbRefresh className=\"w-5 h-5\" />\n            <span>Refresh Rankings</span>\n          </button>\n          \n          <button\n            onClick={handleGoHome}\n            className=\"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            <span>Go to Hub</span>\n          </button>\n        </div>\n        \n        {process.env.NODE_ENV === 'development' && error && (\n          <details className=\"mt-6 text-left\">\n            <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n              Error Details (Development)\n            </summary>\n            <pre className=\"mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-32\">\n              {error.toString()}\n            </pre>\n          </details>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default RankingErrorBoundary;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,eAAe,CAAEC,SAAS,CAAEC,MAAM,KAAQ,gBAAgB,CACnE,OAASC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,oBAAoB,QAAS,CAAAT,KAAK,CAACU,SAAU,CACjDC,WAAWA,CAACC,KAAK,CAAE,CACjB,KAAK,CAACA,KAAK,CAAC,CACZ,IAAI,CAACC,KAAK,CAAG,CAAEC,QAAQ,CAAE,KAAK,CAAEC,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,IAAK,CAAC,CAChE,CAEA,MAAO,CAAAC,wBAAwBA,CAACF,KAAK,CAAE,CACrC,MAAO,CAAED,QAAQ,CAAE,IAAK,CAAC,CAC3B,CAEAI,iBAAiBA,CAACH,KAAK,CAAEC,SAAS,CAAE,CAClC,IAAI,CAACG,QAAQ,CAAC,CACZJ,KAAK,CAAEA,KAAK,CACZC,SAAS,CAAEA,SACb,CAAC,CAAC,CAEF;AACAI,OAAO,CAACL,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAEC,SAAS,CAAC,CACnD,CAEAK,MAAMA,CAAA,CAAG,CACP,GAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,CAAE,CACvB,mBAAOR,IAAA,CAACgB,oBAAoB,EAC1BP,KAAK,CAAE,IAAI,CAACF,KAAK,CAACE,KAAM,CACxBQ,UAAU,CAAEA,CAAA,GAAM,IAAI,CAACJ,QAAQ,CAAC,CAAEL,QAAQ,CAAE,KAAK,CAAEC,KAAK,CAAE,IAAI,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAE,CACpF,CAAC,CACJ,CAEA,MAAO,KAAI,CAACJ,KAAK,CAACY,QAAQ,CAC5B,CACF,CAEA,KAAM,CAAAF,oBAAoB,CAAGG,IAAA,EAA2B,IAA1B,CAAEV,KAAK,CAAEQ,UAAW,CAAC,CAAAE,IAAA,CACjD,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAuB,WAAW,CAAGA,CAAA,GAAM,CACxBJ,UAAU,CAAC,CAAC,CACZK,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBL,QAAQ,CAAC,WAAW,CAAC,CACvB,CAAC,CAED,mBACEpB,IAAA,QAAK0B,SAAS,CAAC,+FAA+F,CAAAR,QAAA,cAC5GhB,KAAA,QAAKwB,SAAS,CAAC,gEAAgE,CAAAR,QAAA,eAC7ElB,IAAA,QAAK0B,SAAS,CAAC,iFAAiF,CAAAR,QAAA,cAC9FlB,IAAA,CAACL,eAAe,EAAC+B,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACnD,CAAC,cAEN1B,IAAA,OAAI0B,SAAS,CAAC,uCAAuC,CAAAR,QAAA,CAAC,sBAEtD,CAAI,CAAC,cAELlB,IAAA,MAAG0B,SAAS,CAAC,oBAAoB,CAAAR,QAAA,CAAC,iHAElC,CAAG,CAAC,cAEJhB,KAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAR,QAAA,eACxBhB,KAAA,WACEyB,OAAO,CAAEN,WAAY,CACrBK,SAAS,CAAC,8JAA8J,CAAAR,QAAA,eAExKlB,IAAA,CAACJ,SAAS,EAAC8B,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC1B,IAAA,SAAAkB,QAAA,CAAM,kBAAgB,CAAM,CAAC,EACvB,CAAC,cAEThB,KAAA,WACEyB,OAAO,CAAEF,YAAa,CACtBC,SAAS,CAAC,iKAAiK,CAAAR,QAAA,eAE3KlB,IAAA,CAACH,MAAM,EAAC6B,SAAS,CAAC,SAAS,CAAE,CAAC,cAC9B1B,IAAA,SAAAkB,QAAA,CAAM,WAAS,CAAM,CAAC,EAChB,CAAC,EACN,CAAC,CAELU,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EAAIrB,KAAK,eAC9CP,KAAA,YAASwB,SAAS,CAAC,gBAAgB,CAAAR,QAAA,eACjClB,IAAA,YAAS0B,SAAS,CAAC,0DAA0D,CAAAR,QAAA,CAAC,6BAE9E,CAAS,CAAC,cACVlB,IAAA,QAAK0B,SAAS,CAAC,6DAA6D,CAAAR,QAAA,CACzET,KAAK,CAACsB,QAAQ,CAAC,CAAC,CACd,CAAC,EACC,CACV,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5B,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}