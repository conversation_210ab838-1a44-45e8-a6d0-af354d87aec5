// Test Real Payment Flow - Simulate Frontend Request
const axios = require('axios');

async function testRealPaymentFlow() {
  console.log('🧪 Testing Real Payment Flow...\n');

  // Step 1: Try to login with a test user
  console.log('📍 Step 1: Attempting login...');
  
  // You'll need to replace these with real credentials or create a test user
  const loginCredentials = [
    { username: 'testuser', password: 'testpass' },
    { username: 'admin', password: 'admin' },
    { username: 'test', password: 'test123' },
    { email: '<EMAIL>', password: 'password' }
  ];

  let authToken = null;
  let user = null;

  for (const creds of loginCredentials) {
    try {
      console.log(`🔐 Trying login with:`, Object.keys(creds));
      
      const loginResponse = await axios.post('http://localhost:5000/api/users/login', creds, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      });

      if (loginResponse.data.success) {
        authToken = loginResponse.data.data;
        user = loginResponse.data.response;
        console.log('✅ Login successful!');
        console.log('👤 User:', {
          id: user._id,
          name: user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber
        });
        break;
      }
    } catch (error) {
      console.log(`❌ Login failed:`, error.response?.data?.message || error.message);
    }
  }

  if (!authToken) {
    console.log('\n❌ Could not login with any test credentials');
    console.log('💡 Please create a test user or use existing credentials');
    console.log('💡 You can still test the validation logic below...\n');
    
    // Test validation without real user
    await testValidationLogic();
    return;
  }

  // Step 2: Test payment with real authentication
  console.log('\n📍 Step 2: Testing payment with real authentication...');
  
  const paymentData = {
    plan: {
      _id: 'test_plan_id_12345',
      title: 'Premium Plan',
      discountedPrice: 5000,
      duration: 1,
      features: ['Feature 1', 'Feature 2']
    },
    userId: user._id,
    userPhone: user.phoneNumber,
    userEmail: user.email
  };

  console.log('📤 Payment data being sent:');
  console.log(JSON.stringify(paymentData, null, 2));

  try {
    const paymentResponse = await axios.post('http://localhost:5000/api/payment/create-invoice', paymentData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      timeout: 30000
    });

    console.log('✅ Payment request successful!');
    console.log('📥 Response:', JSON.stringify(paymentResponse.data, null, 2));

  } catch (error) {
    console.log('❌ Payment request failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 400) {
        console.log('\n🔍 This is the validation error!');
        console.log('Error Type:', error.response.data.errorType);
        console.log('Message:', error.response.data.message);
        
        // Analyze the user data
        console.log('\n📊 User Data Analysis:');
        analyzeUserForPayment(user);
      }
    } else {
      console.log('Network error:', error.message);
    }
  }
}

function analyzeUserForPayment(user) {
  console.log('🔍 Analyzing user data for payment validation...');
  
  // Compute name (same logic as backend)
  let userName = user.name;
  if (!userName && user.firstName && user.lastName) {
    userName = `${user.firstName} ${user.lastName}`;
  } else if (!userName && user.firstName) {
    userName = user.firstName;
  }

  // Check phone format
  const phoneRegex = /^0[67]\d{8}$/;
  const phoneValid = user.phoneNumber && phoneRegex.test(user.phoneNumber);

  // Check email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const emailValid = user.email && emailRegex.test(user.email);

  console.log('📋 Validation Results:');
  console.log(`  👤 Name: "${userName}" ${userName && userName.trim().length >= 2 ? '✅' : '❌'}`);
  console.log(`  📱 Phone: "${user.phoneNumber}" ${phoneValid ? '✅' : '❌'}`);
  console.log(`  📧 Email: "${user.email}" ${emailValid ? '✅' : '⚠️'}`);

  if (!userName || userName.trim().length < 2) {
    console.log('\n❌ NAME ISSUE:');
    console.log('  - user.name:', user.name);
    console.log('  - user.firstName:', user.firstName);
    console.log('  - user.lastName:', user.lastName);
    console.log('  💡 Fix: Update profile with first and last name');
  }

  if (!phoneValid) {
    console.log('\n❌ PHONE ISSUE:');
    console.log('  - Current phone:', user.phoneNumber);
    console.log('  - Required format: 06xxxxxxxx or 07xxxxxxxx (10 digits)');
    console.log('  💡 Fix: Update phone number in profile');
  }

  if (!emailValid && user.email) {
    console.log('\n⚠️ EMAIL ISSUE:');
    console.log('  - Current email:', user.email);
    console.log('  - Will auto-generate email for payment');
  }
}

async function testValidationLogic() {
  console.log('🧪 Testing Validation Logic with Sample Data...\n');
  
  const testUsers = [
    {
      name: 'Valid User',
      data: {
        _id: 'user1',
        name: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '0712345678',
        email: '<EMAIL>'
      }
    },
    {
      name: 'User with missing name',
      data: {
        _id: 'user2',
        name: null,
        firstName: null,
        lastName: null,
        phoneNumber: '0712345678',
        email: '<EMAIL>'
      }
    },
    {
      name: 'User with invalid phone',
      data: {
        _id: 'user3',
        name: 'Jane Smith',
        firstName: 'Jane',
        lastName: 'Smith',
        phoneNumber: '0812345678', // Invalid - starts with 08
        email: '<EMAIL>'
      }
    },
    {
      name: 'User with missing phone',
      data: {
        _id: 'user4',
        name: 'Bob Wilson',
        firstName: 'Bob',
        lastName: 'Wilson',
        phoneNumber: null,
        email: '<EMAIL>'
      }
    }
  ];

  testUsers.forEach(testUser => {
    console.log(`📋 Testing: ${testUser.name}`);
    analyzeUserForPayment(testUser.data);
    console.log('─'.repeat(50));
  });
}

// Main function
async function runTest() {
  console.log('🚀 Starting Real Payment Flow Test...\n');
  
  await testRealPaymentFlow();
  
  console.log('\n✅ Test completed!');
  console.log('\n💡 Next Steps:');
  console.log('1. If login failed: Create a test user or use existing credentials');
  console.log('2. If payment failed: Check the validation errors above');
  console.log('3. Update user profile with valid phone number and name');
  console.log('4. Try payment again in the frontend');
}

runTest().catch(console.error);
