{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{message}from\"antd\";import React,{useEffect,useState,useRef}from\"react\";import{motion}from\"framer-motion\";import{getUserInfo}from\"../apicalls/users\";import{useDispatch,useSelector}from\"react-redux\";import{SetUser}from\"../redux/usersSlice.js\";import{useNavigate,useLocation}from\"react-router-dom\";import{HideLoading,ShowLoading}from\"../redux/loaderSlice\";import{checkPaymentStatus}from\"../apicalls/payment.js\";import\"./ProtectedRoute.css\";import{SetSubscription}from\"../redux/subscriptionSlice.js\";import{setPaymentVerificationNeeded}from\"../redux/paymentSlice.js\";import AdminNavigation from\"./AdminNavigation\";import ModernSidebar from\"./ModernSidebar\";import{TbHome,TbBrandTanzania,TbMenu2,TbX,TbChevronDown,TbLogout,TbUser,TbSettings,TbBell,TbStar}from\"react-icons/tb\";import OnlineStatusIndicator from'./common/OnlineStatusIndicator';import NotificationBell from'./common/NotificationBell';import ProfilePicture from'./common/ProfilePicture';import FloatingBrainwaveAI from'./FloatingBrainwaveAI';import{setUserOnline,setUserOffline,sendHeartbeat}from'../apicalls/notifications';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";function ProtectedRoute(_ref){let{children}=_ref;const{user}=useSelector(state=>state.user);const[isPaymentPending,setIsPaymentPending]=useState(false);const intervalRef=useRef(null);const heartbeatRef=useRef(null);const{subscriptionData}=useSelector(state=>state.subscription);const{paymentVerificationNeeded}=useSelector(state=>state.payment);const dispatch=useDispatch();const navigate=useNavigate();const location=useLocation();// Check if current page should show floating AI (exclude quiz, results, plans, and profile pages)\nconst shouldShowFloatingAI=()=>{const currentPath=location.pathname;const excludedPaths=['/user/quiz','/user/quiz/','/quiz','/quiz/','/results','/results/','/user/results','/user/results/','/user/plans','/user/plans/','/plans','/plans/','/profile','/profile/','/user/profile','/user/profile/'];// Check if current path starts with any excluded path or contains quiz/result keywords\nreturn!excludedPaths.some(path=>currentPath.includes(path))&&!currentPath.includes('quiz')&&!currentPath.includes('result')&&!currentPath.includes('plans')&&!currentPath.includes('profile');};const activeRoute=location.pathname;const getUserData=async()=>{try{const response=await getUserInfo();if(response.success){dispatch(SetUser(response.data));// Store user data in localStorage for consistency\nlocalStorage.setItem(\"user\",JSON.stringify(response.data));// Debug log to help identify admin login issues\nconsole.log(\"User data loaded:\",{name:response.data.name,isAdmin:response.data.isAdmin,email:response.data.email});}else{message.error(response.message);navigate(\"/login\");}}catch(error){navigate(\"/login\");message.error(error.message);}};useEffect(()=>{const token=localStorage.getItem(\"token\");if(token){// Check if user data already exists in Redux (from login)\nif(!user){// Try to load user from localStorage first\nconst storedUser=localStorage.getItem(\"user\");if(storedUser){try{const userData=JSON.parse(storedUser);console.log(\"ProtectedRoute: Loading user from localStorage\",{name:userData.name,isAdmin:userData.isAdmin});dispatch(SetUser(userData));}catch(error){console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");getUserData();}}else{console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");getUserData();}}else{console.log(\"ProtectedRoute: User already in Redux\",{name:user.name,isAdmin:user.isAdmin});}}else{navigate(\"/login\");}},[]);useEffect(()=>{if(isPaymentPending&&!['/plans','/profile'].includes(activeRoute)){navigate('/user/plans');}},[isPaymentPending,activeRoute,navigate]);const verifyPaymentStatus=async()=>{try{const data=await checkPaymentStatus();console.log(\"Payment Status:\",data);if(data!==null&&data!==void 0&&data.error||(data===null||data===void 0?void 0:data.paymentStatus)!=='paid'){if(subscriptionData!==null){dispatch(SetSubscription(null));}setIsPaymentPending(true);}else{setIsPaymentPending(false);dispatch(SetSubscription(data));if(intervalRef.current){clearInterval(intervalRef.current);}}}catch(error){console.log(\"Error checking payment status:\",error);dispatch(SetSubscription(null));setIsPaymentPending(true);}};useEffect(()=>{if(user!==null&&user!==void 0&&user.paymentRequired&&!(user!==null&&user!==void 0&&user.isAdmin)){console.log(\"Effect Runing 2222222...\");if(paymentVerificationNeeded){console.log('Inside timer in effect 2....');intervalRef.current=setInterval(()=>{console.log('Timer in action...');verifyPaymentStatus();},15000);dispatch(setPaymentVerificationNeeded(false));}}},[paymentVerificationNeeded]);useEffect(()=>{if(user!==null&&user!==void 0&&user.paymentRequired&&!(user!==null&&user!==void 0&&user.isAdmin)){console.log(\"Effect Runing...\");verifyPaymentStatus();}},[user,activeRoute]);// Online status management\nuseEffect(()=>{if(user&&!user.isAdmin){// Set user as online when component mounts\nsetUserOnline().catch(console.error);// Send heartbeat every 2 minutes\nheartbeatRef.current=setInterval(()=>{sendHeartbeat().catch(console.error);},120000);// 2 minutes\n// Set user as offline when component unmounts or page unloads\nconst handleBeforeUnload=()=>{setUserOffline().catch(console.error);};window.addEventListener('beforeunload',handleBeforeUnload);return()=>{if(heartbeatRef.current){clearInterval(heartbeatRef.current);}window.removeEventListener('beforeunload',handleBeforeUnload);setUserOffline().catch(console.error);};}},[user]);const getButtonClass=title=>{// Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\nif(!user.paymentRequired||title===\"Plans\"||title===\"Profile\"||title===\"Logout\"){return\"\";// No class applied\n}return(subscriptionData===null||subscriptionData===void 0?void 0:subscriptionData.paymentStatus)!==\"paid\"&&user!==null&&user!==void 0&&user.paymentRequired?\"button-disabled\":\"\";};return/*#__PURE__*/_jsxs(\"div\",{className:\"layout-modern min-h-screen flex flex-col\",children:[!(user!==null&&user!==void 0&&user.isAdmin)&&/*#__PURE__*/_jsx(ModernSidebar,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col min-h-screen\",children:[/*#__PURE__*/_jsx(motion.header,{initial:{y:-20,opacity:0},animate:{y:0,opacity:1},className:\"nav-modern \".concat(location.pathname.includes('/quiz')||location.pathname.includes('/write-exam')?'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98':'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98',\" backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"),children:/*#__PURE__*/_jsx(\"div\",{className:\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-2\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex justify-center\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{duration:0.6,delay:0.2},className:\"relative group flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",style:{width:'32px',height:'24px'},children:/*#__PURE__*/_jsx(\"img\",{src:\"https://flagcdn.com/w40/tz.png\",alt:\"Tanzania Flag\",className:\"w-full h-full object-cover\",style:{objectFit:'cover'},onError:e=>{// Fallback to another flag source if first fails\ne.target.src=\"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";e.target.onerror=()=>{// Final fallback - hide image and show text\ne.target.style.display='none';e.target.parentElement.innerHTML='<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';};}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative brainwave-container\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",style:{fontFamily:\"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",letterSpacing:'-0.02em'},children:[/*#__PURE__*/_jsxs(motion.span,{className:\"relative inline-block\",initial:{opacity:0,x:-30,scale:0.8},animate:{opacity:1,x:0,scale:1,textShadow:[\"0 0 10px rgba(59, 130, 246, 0.5)\",\"0 0 20px rgba(59, 130, 246, 0.8)\",\"0 0 10px rgba(59, 130, 246, 0.5)\"]},transition:{duration:1,delay:0.3,textShadow:{duration:2,repeat:Infinity,ease:\"easeInOut\"}},whileHover:{scale:1.1,rotate:[0,-2,2,0],transition:{duration:0.3}},style:{color:'#1f2937',fontWeight:'900',textShadow:'0 0 10px rgba(59, 130, 246, 0.5)'},children:[\"Brain\",/*#__PURE__*/_jsx(motion.div,{className:\"absolute -top-1 -right-1 w-2 h-2 rounded-full\",animate:{opacity:[0,1,0],scale:[0.5,1.2,0.5],backgroundColor:['#3b82f6','#60a5fa','#3b82f6']},transition:{duration:1.5,repeat:Infinity,delay:2},style:{backgroundColor:'#3b82f6',boxShadow:'0 0 10px #3b82f6'}})]}),/*#__PURE__*/_jsxs(motion.span,{className:\"relative inline-block\",initial:{opacity:0,x:30,scale:0.8},animate:{opacity:1,x:0,scale:1,y:[0,-2,0,2,0],textShadow:[\"0 0 10px rgba(16, 185, 129, 0.5)\",\"0 0 20px rgba(16, 185, 129, 0.8)\",\"0 0 10px rgba(16, 185, 129, 0.5)\"]},transition:{duration:1,delay:0.5,y:{duration:3,repeat:Infinity,ease:\"easeInOut\"},textShadow:{duration:2.5,repeat:Infinity,ease:\"easeInOut\"}},whileHover:{scale:1.1,rotate:[0,2,-2,0],transition:{duration:0.3}},style:{color:'#059669',fontWeight:'900',textShadow:'0 0 10px rgba(16, 185, 129, 0.5)'},children:[\"wave\",/*#__PURE__*/_jsx(motion.div,{className:\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",animate:{opacity:[0,1,0],x:[0,40,80],y:[0,-5,0,5,0],backgroundColor:['#10b981','#34d399','#10b981']},transition:{duration:3,repeat:Infinity,delay:1},style:{backgroundColor:'#10b981',boxShadow:'0 0 8px #10b981'}})]})]}),/*#__PURE__*/_jsx(motion.div,{className:\"absolute -bottom-1 left-0 h-1 rounded-full\",initial:{width:0,opacity:0},animate:{width:'100%',opacity:1,boxShadow:['0 0 10px rgba(16, 185, 129, 0.5)','0 0 20px rgba(59, 130, 246, 0.8)','0 0 10px rgba(16, 185, 129, 0.5)']},transition:{duration:1.5,delay:1.2,boxShadow:{duration:2,repeat:Infinity,ease:\"easeInOut\"}},style:{background:'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',boxShadow:'0 0 15px rgba(16, 185, 129, 0.6)'}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-full overflow-hidden border-2 border-white/20 relative\",style:{background:'#f0f0f0',boxShadow:'0 2px 8px rgba(0,0,0,0.15)',width:'32px',height:'32px'},children:[/*#__PURE__*/_jsx(\"img\",{src:\"/favicon.png\",alt:\"Brainwave Logo\",className:\"w-full h-full object-cover\",style:{objectFit:'cover'},onError:e=>{e.target.style.display='none';e.target.nextSibling.style.display='flex';}}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",style:{display:'none',fontSize:'12px'},children:\"\\uD83E\\uDDE0\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end space-x-2 sm:space-x-3\",children:[!(user!==null&&user!==void 0&&user.isAdmin)&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.8},animate:{opacity:1,scale:1},transition:{duration:0.5,delay:0.2},children:/*#__PURE__*/_jsx(NotificationBell,{unreadCount:2})}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.8},animate:{opacity:1,scale:1},transition:{duration:0.5,delay:0.3},className:\"flex items-center space-x-2 group\",children:[/*#__PURE__*/_jsx(ProfilePicture,{user:_objectSpread(_objectSpread({},user),{},{isOnline:true,lastActivity:new Date().toISOString()}),size:\"sm\",showOnlineStatus:true,style:{width:'32px',height:'32px'}}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden sm:block text-right\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",children:(user===null||user===void 0?void 0:user.name)||'User'}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",children:[\"Class \",user===null||user===void 0?void 0:user.class]})]})]})]})]})})}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1 overflow-auto \".concat(user!==null&&user!==void 0&&user.isAdmin?'bg-gray-100':'bg-gradient-to-br from-gray-50 to-blue-50',\" \").concat(user!==null&&user!==void 0&&user.isAdmin?'p-6':'pb-20 sm:pb-0'),children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.3,delay:0.1},className:\"h-full\",children:children})}),shouldShowFloatingAI()&&/*#__PURE__*/_jsx(FloatingBrainwaveAI,{})]})]});}export default ProtectedRoute;", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "AdminNavigation", "ModernSidebar", "TbHome", "TbBrandTanzania", "TbMenu2", "TbX", "TbChevronDown", "TbLogout", "TbUser", "TbSettings", "TbBell", "TbStar", "OnlineStatusIndicator", "NotificationBell", "ProfilePicture", "FloatingBrainwaveAI", "setUserOnline", "setUserOffline", "sendHeartbeat", "jsx", "_jsx", "jsxs", "_jsxs", "ProtectedRoute", "_ref", "children", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "heartbeatRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "shouldShowFloatingAI", "currentPath", "pathname", "excludedPaths", "some", "path", "includes", "activeRoute", "getUserData", "response", "success", "data", "localStorage", "setItem", "JSON", "stringify", "console", "log", "name", "isAdmin", "email", "error", "token", "getItem", "storedUser", "userData", "parse", "verifyPaymentStatus", "paymentStatus", "current", "clearInterval", "paymentRequired", "setInterval", "catch", "handleBeforeUnload", "window", "addEventListener", "removeEventListener", "getButtonClass", "title", "className", "header", "initial", "y", "opacity", "animate", "concat", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "e", "target", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "unreadCount", "_objectSpread", "isOnline", "lastActivity", "Date", "toISOString", "size", "showOnlineStatus", "class"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport AdminNavigation from \"./AdminNavigation\";\r\nimport ModernSidebar from \"./ModernSidebar\";\r\nimport { TbHome, TbBrandTanzania, TbMenu2, TbX, TbChevronDown, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ell, TbStar } from \"react-icons/tb\";\r\nimport OnlineStatusIndicator from './common/OnlineStatusIndicator';\r\nimport NotificationBell from './common/NotificationBell';\r\nimport ProfilePicture from './common/ProfilePicture';\r\nimport FloatingBrainwaveAI from './FloatingBrainwaveAI';\r\nimport { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const heartbeatRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n\r\n  // Check if current page should show floating AI (exclude quiz, results, plans, and profile pages)\r\n  const shouldShowFloatingAI = () => {\r\n    const currentPath = location.pathname;\r\n    const excludedPaths = [\r\n      '/user/quiz',\r\n      '/user/quiz/',\r\n      '/quiz',\r\n      '/quiz/',\r\n      '/results',\r\n      '/results/',\r\n      '/user/results',\r\n      '/user/results/',\r\n      '/user/plans',\r\n      '/user/plans/',\r\n      '/plans',\r\n      '/plans/',\r\n      '/profile',\r\n      '/profile/',\r\n      '/user/profile',\r\n      '/user/profile/'\r\n    ];\r\n\r\n    // Check if current path starts with any excluded path or contains quiz/result keywords\r\n    return !excludedPaths.some(path => currentPath.includes(path)) &&\r\n           !currentPath.includes('quiz') &&\r\n           !currentPath.includes('result') &&\r\n           !currentPath.includes('plans') &&\r\n           !currentPath.includes('profile');\r\n  };\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n\r\n        // Store user data in localStorage for consistency\r\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\r\n\r\n        // Debug log to help identify admin login issues\r\n        console.log(\"User data loaded:\", {\r\n          name: response.data.name,\r\n          isAdmin: response.data.isAdmin,\r\n          email: response.data.email\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      // Check if user data already exists in Redux (from login)\r\n      if (!user) {\r\n        // Try to load user from localStorage first\r\n        const storedUser = localStorage.getItem(\"user\");\r\n        if (storedUser) {\r\n          try {\r\n            const userData = JSON.parse(storedUser);\r\n            console.log(\"ProtectedRoute: Loading user from localStorage\", { name: userData.name, isAdmin: userData.isAdmin });\r\n            dispatch(SetUser(userData));\r\n          } catch (error) {\r\n            console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");\r\n            getUserData();\r\n          }\r\n        } else {\r\n          console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");\r\n          getUserData();\r\n        }\r\n      } else {\r\n        console.log(\"ProtectedRoute: User already in Redux\", { name: user.name, isAdmin: user.isAdmin });\r\n      }\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n  // Online status management\r\n  useEffect(() => {\r\n    if (user && !user.isAdmin) {\r\n      // Set user as online when component mounts\r\n      setUserOnline().catch(console.error);\r\n\r\n      // Send heartbeat every 2 minutes\r\n      heartbeatRef.current = setInterval(() => {\r\n        sendHeartbeat().catch(console.error);\r\n      }, 120000); // 2 minutes\r\n\r\n      // Set user as offline when component unmounts or page unloads\r\n      const handleBeforeUnload = () => {\r\n        setUserOffline().catch(console.error);\r\n      };\r\n\r\n      window.addEventListener('beforeunload', handleBeforeUnload);\r\n\r\n      return () => {\r\n        if (heartbeatRef.current) {\r\n          clearInterval(heartbeatRef.current);\r\n        }\r\n        window.removeEventListener('beforeunload', handleBeforeUnload);\r\n        setUserOffline().catch(console.error);\r\n      };\r\n    }\r\n  }, [user]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* Modern Sidebar for regular users */}\r\n      {!user?.isAdmin && <ModernSidebar />}\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Responsive Header - Show for all users */}\r\n        {(\r\n          <motion.header\r\n            initial={{ y: -20, opacity: 0 }}\r\n            animate={{ y: 0, opacity: 1 }}\r\n            className={`nav-modern ${\r\n              location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')\r\n                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'\r\n                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'\r\n            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}\r\n          >\r\n          <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n            <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n              {/* Left section - Modern Sidebar */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                {/* Modern Sidebar Toggle Button */}\r\n              </div>\r\n\r\n              {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n              <div className=\"flex-1 flex justify-center\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.9 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"relative group flex items-center space-x-3\"\r\n                >\r\n                  {/* Tanzania Flag - Using actual flag image */}\r\n                  <div\r\n                    className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '24px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"https://flagcdn.com/w40/tz.png\"\r\n                      alt=\"Tanzania Flag\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        // Fallback to another flag source if first fails\r\n                        e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                        e.target.onerror = () => {\r\n                          // Final fallback - hide image and show text\r\n                          e.target.style.display = 'none';\r\n                          e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                        };\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Amazing Animated Brainwave Text */}\r\n                  <div className=\"relative brainwave-container\">\r\n                    <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                        style={{\r\n                          fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                          letterSpacing: '-0.02em'\r\n                        }}>\r\n                      {/* Brain - with amazing effects */}\r\n                      <motion.span\r\n                        className=\"relative inline-block\"\r\n                        initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                        animate={{\r\n                          opacity: 1,\r\n                          x: 0,\r\n                          scale: 1,\r\n                          textShadow: [\r\n                            \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                            \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                            \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                          ]\r\n                        }}\r\n                        transition={{\r\n                          duration: 1,\r\n                          delay: 0.3,\r\n                          textShadow: {\r\n                            duration: 2,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          }\r\n                        }}\r\n                        whileHover={{\r\n                          scale: 1.1,\r\n                          rotate: [0, -2, 2, 0],\r\n                          transition: { duration: 0.3 }\r\n                        }}\r\n                        style={{\r\n                          color: '#1f2937',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                        }}\r\n                      >\r\n                        Brain\r\n\r\n                        {/* Electric spark */}\r\n                        <motion.div\r\n                          className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                          animate={{\r\n                            opacity: [0, 1, 0],\r\n                            scale: [0.5, 1.2, 0.5],\r\n                            backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                          }}\r\n                          transition={{\r\n                            duration: 1.5,\r\n                            repeat: Infinity,\r\n                            delay: 2\r\n                          }}\r\n                          style={{\r\n                            backgroundColor: '#3b82f6',\r\n                            boxShadow: '0 0 10px #3b82f6'\r\n                          }}\r\n                        />\r\n                      </motion.span>\r\n\r\n                      {/* Wave - with flowing effects (no space) */}\r\n                      <motion.span\r\n                        className=\"relative inline-block\"\r\n                        initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                        animate={{\r\n                          opacity: 1,\r\n                          x: 0,\r\n                          scale: 1,\r\n                          y: [0, -2, 0, 2, 0],\r\n                          textShadow: [\r\n                            \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                            \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                            \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                          ]\r\n                        }}\r\n                        transition={{\r\n                          duration: 1,\r\n                          delay: 0.5,\r\n                          y: {\r\n                            duration: 3,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          },\r\n                          textShadow: {\r\n                            duration: 2.5,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          }\r\n                        }}\r\n                        whileHover={{\r\n                          scale: 1.1,\r\n                          rotate: [0, 2, -2, 0],\r\n                          transition: { duration: 0.3 }\r\n                        }}\r\n                        style={{\r\n                          color: '#059669',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                        }}\r\n                      >\r\n                        wave\r\n\r\n                        {/* Wave particle */}\r\n                        <motion.div\r\n                          className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                          animate={{\r\n                            opacity: [0, 1, 0],\r\n                            x: [0, 40, 80],\r\n                            y: [0, -5, 0, 5, 0],\r\n                            backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                          }}\r\n                          transition={{\r\n                            duration: 3,\r\n                            repeat: Infinity,\r\n                            delay: 1\r\n                          }}\r\n                          style={{\r\n                            backgroundColor: '#10b981',\r\n                            boxShadow: '0 0 8px #10b981'\r\n                          }}\r\n                        />\r\n                      </motion.span>\r\n                    </h1>\r\n\r\n                    {/* Glowing underline effect */}\r\n                    <motion.div\r\n                      className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                      initial={{ width: 0, opacity: 0 }}\r\n                      animate={{\r\n                        width: '100%',\r\n                        opacity: 1,\r\n                        boxShadow: [\r\n                          '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                          '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                          '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1.5,\r\n                        delay: 1.2,\r\n                        boxShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Official Logo - Small like profile */}\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"/favicon.png\"\r\n                      alt=\"Brainwave Logo\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        e.target.style.display = 'none';\r\n                        e.target.nextSibling.style.display = 'flex';\r\n                      }}\r\n                    />\r\n                    <div\r\n                      className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                      style={{\r\n                        display: 'none',\r\n                        fontSize: '12px'\r\n                      }}\r\n                    >\r\n                      🧠\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Modern Glow Effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Right Section - Notifications + User Profile */}\r\n              <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n                {/* Notification Bell */}\r\n                {!user?.isAdmin && (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, scale: 0.8 }}\r\n                    animate={{ opacity: 1, scale: 1 }}\r\n                    transition={{ duration: 0.5, delay: 0.2 }}\r\n                  >\r\n                    <NotificationBell unreadCount={2} />\r\n                  </motion.div>\r\n                )}\r\n\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={{\r\n                      ...user,\r\n                      isOnline: true,\r\n                      lastActivity: new Date().toISOString()\r\n                    }}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n        )}\r\n\r\n        {/* Page Content */}\r\n        <main className={`flex-1 overflow-auto ${\r\n          user?.isAdmin\r\n            ? 'bg-gray-100'\r\n            : 'bg-gradient-to-br from-gray-50 to-blue-50'\r\n        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n\r\n        {/* Floating Brainwave AI - Show on all pages except quiz and results */}\r\n        {shouldShowFloatingAI() && <FloatingBrainwaveAI />}\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": "+HAAA,OAASA,OAAO,KAAQ,MAAM,CAC9B,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,KAAQ,mBAAmB,CAC/C,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,OAAO,KAAQ,wBAAwB,CAChD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,WAAW,CAAEC,WAAW,KAAQ,sBAAsB,CAC/D,OAASC,kBAAkB,KAAQ,wBAAwB,CAC3D,MAAO,sBAAsB,CAC7B,OAASC,eAAe,KAAQ,+BAA+B,CAC/D,OAASC,4BAA4B,KAAQ,0BAA0B,CACvE,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,OAASC,MAAM,CAAEC,eAAe,CAAEC,OAAO,CAAEC,GAAG,CAAEC,aAAa,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,UAAU,CAAEC,MAAM,CAAEC,MAAM,KAAQ,gBAAgB,CACnI,MAAO,CAAAC,qBAAqB,KAAM,gCAAgC,CAClE,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,mBAAmB,KAAM,uBAAuB,CACvD,OAASC,aAAa,CAAEC,cAAc,CAAEC,aAAa,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAGzF,QAAS,CAAAC,cAAcA,CAAAC,IAAA,CAAe,IAAd,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAClC,KAAM,CAAEE,IAAK,CAAC,CAAGnC,WAAW,CAAEoC,KAAK,EAAKA,KAAK,CAACD,IAAI,CAAC,CACnD,KAAM,CAACE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAA4C,WAAW,CAAG3C,MAAM,CAAC,IAAI,CAAC,CAChC,KAAM,CAAA4C,YAAY,CAAG5C,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAE6C,gBAAiB,CAAC,CAAGzC,WAAW,CAAEoC,KAAK,EAAKA,KAAK,CAACM,YAAY,CAAC,CACvE,KAAM,CAAEC,yBAA0B,CAAC,CAAG3C,WAAW,CAAEoC,KAAK,EAAKA,KAAK,CAACQ,OAAO,CAAC,CAC3E,KAAM,CAAAC,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+C,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6C,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAA6C,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,WAAW,CAAGF,QAAQ,CAACG,QAAQ,CACrC,KAAM,CAAAC,aAAa,CAAG,CACpB,YAAY,CACZ,aAAa,CACb,OAAO,CACP,QAAQ,CACR,UAAU,CACV,WAAW,CACX,eAAe,CACf,gBAAgB,CAChB,aAAa,CACb,cAAc,CACd,QAAQ,CACR,SAAS,CACT,UAAU,CACV,WAAW,CACX,eAAe,CACf,gBAAgB,CACjB,CAED;AACA,MAAO,CAACA,aAAa,CAACC,IAAI,CAACC,IAAI,EAAIJ,WAAW,CAACK,QAAQ,CAACD,IAAI,CAAC,CAAC,EACvD,CAACJ,WAAW,CAACK,QAAQ,CAAC,MAAM,CAAC,EAC7B,CAACL,WAAW,CAACK,QAAQ,CAAC,QAAQ,CAAC,EAC/B,CAACL,WAAW,CAACK,QAAQ,CAAC,OAAO,CAAC,EAC9B,CAACL,WAAW,CAACK,QAAQ,CAAC,SAAS,CAAC,CACzC,CAAC,CACD,KAAM,CAAAC,WAAW,CAAGR,QAAQ,CAACG,QAAQ,CAMrC,KAAM,CAAAM,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA3D,WAAW,CAAC,CAAC,CACpC,GAAI2D,QAAQ,CAACC,OAAO,CAAE,CACpBb,QAAQ,CAAC5C,OAAO,CAACwD,QAAQ,CAACE,IAAI,CAAC,CAAC,CAEhC;AACAC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAACE,IAAI,CAAC,CAAC,CAE3D;AACAK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE,CAC/BC,IAAI,CAAET,QAAQ,CAACE,IAAI,CAACO,IAAI,CACxBC,OAAO,CAAEV,QAAQ,CAACE,IAAI,CAACQ,OAAO,CAC9BC,KAAK,CAAEX,QAAQ,CAACE,IAAI,CAACS,KACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL5E,OAAO,CAAC6E,KAAK,CAACZ,QAAQ,CAACjE,OAAO,CAAC,CAC/BsD,QAAQ,CAAC,QAAQ,CAAC,CACpB,CACF,CAAE,MAAOuB,KAAK,CAAE,CACdvB,QAAQ,CAAC,QAAQ,CAAC,CAClBtD,OAAO,CAAC6E,KAAK,CAACA,KAAK,CAAC7E,OAAO,CAAC,CAC9B,CACF,CAAC,CAEDE,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4E,KAAK,CAAGV,YAAY,CAACW,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAID,KAAK,CAAE,CACT;AACA,GAAI,CAACnC,IAAI,CAAE,CACT;AACA,KAAM,CAAAqC,UAAU,CAAGZ,YAAY,CAACW,OAAO,CAAC,MAAM,CAAC,CAC/C,GAAIC,UAAU,CAAE,CACd,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAGX,IAAI,CAACY,KAAK,CAACF,UAAU,CAAC,CACvCR,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAE,CAAEC,IAAI,CAAEO,QAAQ,CAACP,IAAI,CAAEC,OAAO,CAAEM,QAAQ,CAACN,OAAQ,CAAC,CAAC,CACjHtB,QAAQ,CAAC5C,OAAO,CAACwE,QAAQ,CAAC,CAAC,CAC7B,CAAE,MAAOJ,KAAK,CAAE,CACdL,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC,CACnFT,WAAW,CAAC,CAAC,CACf,CACF,CAAC,IAAM,CACLQ,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC,CACrFT,WAAW,CAAC,CAAC,CACf,CACF,CAAC,IAAM,CACLQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAE,CAAEC,IAAI,CAAE/B,IAAI,CAAC+B,IAAI,CAAEC,OAAO,CAAEhC,IAAI,CAACgC,OAAQ,CAAC,CAAC,CAClG,CACF,CAAC,IAAM,CACLrB,QAAQ,CAAC,QAAQ,CAAC,CACpB,CACF,CAAC,CAAE,EAAE,CAAC,CAINpD,SAAS,CAAC,IAAM,CACd,GAAI2C,gBAAgB,EAAI,CAAC,CAAC,QAAQ,CAAE,UAAU,CAAC,CAACiB,QAAQ,CAACC,WAAW,CAAC,CAAE,CACrET,QAAQ,CAAC,aAAa,CAAC,CACzB,CACF,CAAC,CAAE,CAACT,gBAAgB,CAAEkB,WAAW,CAAET,QAAQ,CAAC,CAAC,CAE7C,KAAM,CAAA6B,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAAhB,IAAI,CAAG,KAAM,CAAArD,kBAAkB,CAAC,CAAC,CACvC0D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEN,IAAI,CAAC,CACpC,GAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEU,KAAK,EAAI,CAAAV,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiB,aAAa,IAAK,MAAM,CAAE,CACjD,GAAInC,gBAAgB,GAAK,IAAI,CAAE,CAC7BI,QAAQ,CAACtC,eAAe,CAAC,IAAI,CAAC,CAAC,CACjC,CACA+B,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,IACI,CACHA,mBAAmB,CAAC,KAAK,CAAC,CAC1BO,QAAQ,CAACtC,eAAe,CAACoD,IAAI,CAAC,CAAC,CAC/B,GAAIpB,WAAW,CAACsC,OAAO,CAAE,CACvBC,aAAa,CAACvC,WAAW,CAACsC,OAAO,CAAC,CACpC,CACF,CACF,CAAE,MAAOR,KAAK,CAAE,CACdL,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEI,KAAK,CAAC,CACpDxB,QAAQ,CAACtC,eAAe,CAAC,IAAI,CAAC,CAAC,CAC/B+B,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAED5C,SAAS,CAAC,IAAM,CACd,GAAIyC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAE4C,eAAe,EAAI,EAAC5C,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgC,OAAO,EAAE,CAC3CH,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CAEvC,GAAItB,yBAAyB,CAAE,CAC7BqB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC3C1B,WAAW,CAACsC,OAAO,CAAGG,WAAW,CAAC,IAAM,CACtChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjCU,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,KAAK,CAAC,CACT9B,QAAQ,CAACrC,4BAA4B,CAAC,KAAK,CAAC,CAAC,CAC/C,CACF,CACF,CAAC,CAAE,CAACmC,yBAAyB,CAAC,CAAC,CAE/BjD,SAAS,CAAC,IAAM,CACd,GAAIyC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAE4C,eAAe,EAAI,EAAC5C,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgC,OAAO,EAAE,CAC3CH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CAC/BU,mBAAmB,CAAC,CAAC,CACvB,CACF,CAAC,CAAE,CAACxC,IAAI,CAAEoB,WAAW,CAAC,CAAC,CAEvB;AACA7D,SAAS,CAAC,IAAM,CACd,GAAIyC,IAAI,EAAI,CAACA,IAAI,CAACgC,OAAO,CAAE,CACzB;AACA1C,aAAa,CAAC,CAAC,CAACwD,KAAK,CAACjB,OAAO,CAACK,KAAK,CAAC,CAEpC;AACA7B,YAAY,CAACqC,OAAO,CAAGG,WAAW,CAAC,IAAM,CACvCrD,aAAa,CAAC,CAAC,CAACsD,KAAK,CAACjB,OAAO,CAACK,KAAK,CAAC,CACtC,CAAC,CAAE,MAAM,CAAC,CAAE;AAEZ;AACA,KAAM,CAAAa,kBAAkB,CAAGA,CAAA,GAAM,CAC/BxD,cAAc,CAAC,CAAC,CAACuD,KAAK,CAACjB,OAAO,CAACK,KAAK,CAAC,CACvC,CAAC,CAEDc,MAAM,CAACC,gBAAgB,CAAC,cAAc,CAAEF,kBAAkB,CAAC,CAE3D,MAAO,IAAM,CACX,GAAI1C,YAAY,CAACqC,OAAO,CAAE,CACxBC,aAAa,CAACtC,YAAY,CAACqC,OAAO,CAAC,CACrC,CACAM,MAAM,CAACE,mBAAmB,CAAC,cAAc,CAAEH,kBAAkB,CAAC,CAC9DxD,cAAc,CAAC,CAAC,CAACuD,KAAK,CAACjB,OAAO,CAACK,KAAK,CAAC,CACvC,CAAC,CACH,CACF,CAAC,CAAE,CAAClC,IAAI,CAAC,CAAC,CAGV,KAAM,CAAAmD,cAAc,CAAIC,KAAK,EAAK,CAChC;AACA,GAAI,CAACpD,IAAI,CAAC4C,eAAe,EAAIQ,KAAK,GAAK,OAAO,EAAIA,KAAK,GAAK,SAAS,EAAIA,KAAK,GAAK,QAAQ,CAAE,CAC3F,MAAO,EAAE,CAAE;AACb,CAEA,MAAO,CAAA9C,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEmC,aAAa,IAAK,MAAM,EAAIzC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAE4C,eAAe,CACtE,iBAAiB,CACjB,EAAE,CACR,CAAC,CAKD,mBACEhD,KAAA,QAAKyD,SAAS,CAAC,0CAA0C,CAAAtD,QAAA,EAEtD,EAACC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgC,OAAO,gBAAItC,IAAA,CAACnB,aAAa,GAAE,CAAC,cAGpCqB,KAAA,QAAKyD,SAAS,CAAC,mCAAmC,CAAAtD,QAAA,eAG9CL,IAAA,CAAChC,MAAM,CAAC4F,MAAM,EACZC,OAAO,CAAE,CAAEC,CAAC,CAAE,CAAC,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAE,CAChCC,OAAO,CAAE,CAAEF,CAAC,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAE,CAAE,CAC9BJ,SAAS,eAAAM,MAAA,CACP/C,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,OAAO,CAAC,EAAIP,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,aAAa,CAAC,CAC5E,8EAA8E,CAC9E,2DAA2D,gGAC8B,CAAApB,QAAA,cAEjGL,IAAA,QAAK2D,SAAS,CAAC,+CAA+C,CAAAtD,QAAA,cAC5DH,KAAA,QAAKyD,SAAS,CAAC,wEAAwE,CAAAtD,QAAA,eAErFL,IAAA,QAAK2D,SAAS,CAAC,6BAA6B,CAEvC,CAAC,cAGN3D,IAAA,QAAK2D,SAAS,CAAC,4BAA4B,CAAAtD,QAAA,cACzCH,KAAA,CAAClC,MAAM,CAACkG,GAAG,EACTL,OAAO,CAAE,CAAEE,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,CAAE,CAAE,CAClCC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC1CX,SAAS,CAAC,4CAA4C,CAAAtD,QAAA,eAGtDL,IAAA,QACE2D,SAAS,CAAC,wEAAwE,CAClFY,KAAK,CAAE,CACLC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAE,CAAApE,QAAA,cAEFL,IAAA,QACE0E,GAAG,CAAC,gCAAgC,CACpCC,GAAG,CAAC,eAAe,CACnBhB,SAAS,CAAC,4BAA4B,CACtCY,KAAK,CAAE,CAAEK,SAAS,CAAE,OAAQ,CAAE,CAC9BC,OAAO,CAAGC,CAAC,EAAK,CACd;AACAA,CAAC,CAACC,MAAM,CAACL,GAAG,CAAG,8GAA8G,CAC7HI,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAM,CACvB;AACAF,CAAC,CAACC,MAAM,CAACR,KAAK,CAACU,OAAO,CAAG,MAAM,CAC/BH,CAAC,CAACC,MAAM,CAACG,aAAa,CAACC,SAAS,CAAG,+GAA+G,CACpJ,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,cAGNjF,KAAA,QAAKyD,SAAS,CAAC,8BAA8B,CAAAtD,QAAA,eAC3CH,KAAA,OAAIyD,SAAS,CAAC,qFAAqF,CAC/FY,KAAK,CAAE,CACLa,UAAU,CAAE,yDAAyD,CACrEC,aAAa,CAAE,SACjB,CAAE,CAAAhF,QAAA,eAEJH,KAAA,CAAClC,MAAM,CAACsH,IAAI,EACV3B,SAAS,CAAC,uBAAuB,CACjCE,OAAO,CAAE,CAAEE,OAAO,CAAE,CAAC,CAAEwB,CAAC,CAAE,CAAC,EAAE,CAAEpB,KAAK,CAAE,GAAI,CAAE,CAC5CH,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVwB,CAAC,CAAE,CAAC,CACJpB,KAAK,CAAE,CAAC,CACRqB,UAAU,CAAE,CACV,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAEtC,CAAE,CACFpB,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,GAAG,CACVkB,UAAU,CAAE,CACVnB,QAAQ,CAAE,CAAC,CACXoB,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CACF,CAAE,CACFC,UAAU,CAAE,CACVzB,KAAK,CAAE,GAAG,CACV0B,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACrBzB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CAAE,CACFE,KAAK,CAAE,CACLuB,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,KAAK,CACjBP,UAAU,CAAE,kCACd,CAAE,CAAAnF,QAAA,EACH,OAGC,cACAL,IAAA,CAAChC,MAAM,CAACkG,GAAG,EACTP,SAAS,CAAC,+CAA+C,CACzDK,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAClBI,KAAK,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACtB6B,eAAe,CAAE,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CACnD,CAAE,CACF5B,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACboB,MAAM,CAAEC,QAAQ,CAChBpB,KAAK,CAAE,CACT,CAAE,CACFC,KAAK,CAAE,CACLyB,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,kBACb,CAAE,CACH,CAAC,EACS,CAAC,cAGd/F,KAAA,CAAClC,MAAM,CAACsH,IAAI,EACV3B,SAAS,CAAC,uBAAuB,CACjCE,OAAO,CAAE,CAAEE,OAAO,CAAE,CAAC,CAAEwB,CAAC,CAAE,EAAE,CAAEpB,KAAK,CAAE,GAAI,CAAE,CAC3CH,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVwB,CAAC,CAAE,CAAC,CACJpB,KAAK,CAAE,CAAC,CACRL,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACnB0B,UAAU,CAAE,CACV,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAEtC,CAAE,CACFpB,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,GAAG,CACVR,CAAC,CAAE,CACDO,QAAQ,CAAE,CAAC,CACXoB,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CAAC,CACDH,UAAU,CAAE,CACVnB,QAAQ,CAAE,GAAG,CACboB,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CACF,CAAE,CACFC,UAAU,CAAE,CACVzB,KAAK,CAAE,GAAG,CACV0B,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CACrBzB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CAAE,CACFE,KAAK,CAAE,CACLuB,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,KAAK,CACjBP,UAAU,CAAE,kCACd,CAAE,CAAAnF,QAAA,EACH,MAGC,cACAL,IAAA,CAAChC,MAAM,CAACkG,GAAG,EACTP,SAAS,CAAC,gDAAgD,CAC1DK,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAClBwB,CAAC,CAAE,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACdzB,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACnBkC,eAAe,CAAE,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CACnD,CAAE,CACF5B,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXoB,MAAM,CAAEC,QAAQ,CAChBpB,KAAK,CAAE,CACT,CAAE,CACFC,KAAK,CAAE,CACLyB,eAAe,CAAE,SAAS,CAC1BC,SAAS,CAAE,iBACb,CAAE,CACH,CAAC,EACS,CAAC,EACZ,CAAC,cAGLjG,IAAA,CAAChC,MAAM,CAACkG,GAAG,EACTP,SAAS,CAAC,4CAA4C,CACtDE,OAAO,CAAE,CAAEW,KAAK,CAAE,CAAC,CAAET,OAAO,CAAE,CAAE,CAAE,CAClCC,OAAO,CAAE,CACPQ,KAAK,CAAE,MAAM,CACbT,OAAO,CAAE,CAAC,CACVkC,SAAS,CAAE,CACT,kCAAkC,CAClC,kCAAkC,CAClC,kCAAkC,CAEtC,CAAE,CACF7B,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbC,KAAK,CAAE,GAAG,CACV2B,SAAS,CAAE,CACT5B,QAAQ,CAAE,CAAC,CACXoB,MAAM,CAAEC,QAAQ,CAChBC,IAAI,CAAE,WACR,CACF,CAAE,CACFpB,KAAK,CAAE,CACL2B,UAAU,CAAE,mDAAmD,CAC/DD,SAAS,CAAE,kCACb,CAAE,CACH,CAAC,EACC,CAAC,cAGN/F,KAAA,QACEyD,SAAS,CAAC,gEAAgE,CAC1EY,KAAK,CAAE,CACL2B,UAAU,CAAE,SAAS,CACrBD,SAAS,CAAE,4BAA4B,CACvCzB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAE,CAAApE,QAAA,eAEFL,IAAA,QACE0E,GAAG,CAAC,cAAc,CAClBC,GAAG,CAAC,gBAAgB,CACpBhB,SAAS,CAAC,4BAA4B,CACtCY,KAAK,CAAE,CAAEK,SAAS,CAAE,OAAQ,CAAE,CAC9BC,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACR,KAAK,CAACU,OAAO,CAAG,MAAM,CAC/BH,CAAC,CAACC,MAAM,CAACoB,WAAW,CAAC5B,KAAK,CAACU,OAAO,CAAG,MAAM,CAC7C,CAAE,CACH,CAAC,cACFjF,IAAA,QACE2D,SAAS,CAAC,gHAAgH,CAC1HY,KAAK,CAAE,CACLU,OAAO,CAAE,MAAM,CACfmB,QAAQ,CAAE,MACZ,CAAE,CAAA/F,QAAA,CACH,cAED,CAAK,CAAC,EACH,CAAC,cAGNL,IAAA,QAAK2D,SAAS,CAAC,yKAAyK,CAAM,CAAC,EACrL,CAAC,CACV,CAAC,cAGNzD,KAAA,QAAKyD,SAAS,CAAC,sDAAsD,CAAAtD,QAAA,EAElE,EAACC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgC,OAAO,gBACbtC,IAAA,CAAChC,MAAM,CAACkG,GAAG,EACTL,OAAO,CAAE,CAAEE,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,CAAE,CAAE,CAClCC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAI,CAAE,CAAAjE,QAAA,cAE1CL,IAAA,CAACP,gBAAgB,EAAC4G,WAAW,CAAE,CAAE,CAAE,CAAC,CAC1B,CACb,cAEDnG,KAAA,CAAClC,MAAM,CAACkG,GAAG,EACTL,OAAO,CAAE,CAAEE,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEI,KAAK,CAAE,CAAE,CAAE,CAClCC,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC1CX,SAAS,CAAC,mCAAmC,CAAAtD,QAAA,eAG7CL,IAAA,CAACN,cAAc,EACbY,IAAI,CAAAgG,aAAA,CAAAA,aAAA,IACChG,IAAI,MACPiG,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EACtC,CACFC,IAAI,CAAC,IAAI,CACTC,gBAAgB,CAAE,IAAK,CACvBrC,KAAK,CAAE,CACLC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MACV,CAAE,CACH,CAAC,cAGFvE,KAAA,QAAKyD,SAAS,CAAC,4BAA4B,CAAAtD,QAAA,eACzCL,IAAA,QAAK2D,SAAS,CAAC,wGAAwG,CAAAtD,QAAA,CACpH,CAAAC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE+B,IAAI,GAAI,MAAM,CAClB,CAAC,cACNnC,KAAA,QAAKyD,SAAS,CAAC,iFAAiF,CAAAtD,QAAA,EAAC,QACzF,CAACC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEuG,KAAK,EACf,CAAC,EACH,CAAC,EACI,CAAC,EACV,CAAC,EACH,CAAC,CACH,CAAC,CACO,CAAC,cAIhB7G,IAAA,SAAM2D,SAAS,yBAAAM,MAAA,CACb3D,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgC,OAAO,CACT,aAAa,CACb,2CAA2C,MAAA2B,MAAA,CAC7C3D,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEgC,OAAO,CAAG,KAAK,CAAG,eAAe,CAAG,CAAAjC,QAAA,cAC5CL,IAAA,CAAChC,MAAM,CAACkG,GAAG,EACTL,OAAO,CAAE,CAAEE,OAAO,CAAE,CAAC,CAAED,CAAC,CAAE,EAAG,CAAE,CAC/BE,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAED,CAAC,CAAE,CAAE,CAAE,CAC9BM,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC1CX,SAAS,CAAC,QAAQ,CAAAtD,QAAA,CAEjBA,QAAQ,CACC,CAAC,CACT,CAAC,CAGNc,oBAAoB,CAAC,CAAC,eAAInB,IAAA,CAACL,mBAAmB,GAAE,CAAC,EAE/C,CAAC,EACH,CAAC,CAEV,CAEA,cAAe,CAAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}