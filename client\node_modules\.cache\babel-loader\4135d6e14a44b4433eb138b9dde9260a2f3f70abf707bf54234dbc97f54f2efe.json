{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"glimp-plan-sample\",\n    title: \"Glimp Plan\",\n    features: [\"1-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 15000,\n    discountedPrice: 13000,\n    discountPercentage: 13,\n    duration: 1,\n    status: true\n  }, {\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n  const handlePlanSelect = async plan => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      setSelectedPlan(plan);\n      setPaymentLoading(true);\n      setShowProcessingModal(true);\n      setPaymentStatus('Initiating payment...');\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('Sending payment request...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        // Start checking payment status\n        setTimeout(() => {\n          checkPaymentConfirmation(response.order_id || 'demo_order');\n        }, 3000);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let isPolling = true;\n    let handleVisibilityChange;\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 1 second for up to 1 minute (very responsive)\n      let attempts = 0;\n      const maxAttempts = 60; // 60 attempts * 1 second = 1 minute\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n\n            // Show success immediately\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setShowSuccessModal(true);\n              setPaymentLoading(false);\n\n              // Refresh subscription data\n              checkCurrentSubscription();\n\n              // Show immediate success message\n              message.success({\n                content: '🎉 Payment confirmed! All features are now unlocked!',\n                duration: 5,\n                style: {\n                  marginTop: '20vh',\n                  fontSize: '16px'\n                }\n              });\n            }, 500); // Reduced delay for immediate feedback\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(false);\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n          } else {\n            // Continue polling with very frequent checks\n            const remainingSeconds = maxAttempts - attempts;\n            if (remainingSeconds > 30) {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${Math.ceil(remainingSeconds / 60)} minutes remaining)`);\n            } else {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${remainingSeconds} seconds remaining)`);\n            }\n            setTimeout(pollPaymentStatus, 1000); // Check every 1 second\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setShowProcessingModal(false);\n            setPaymentLoading(false);\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n    } catch (error) {\n      isPolling = false; // Stop polling\n      document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"current-price\",\n                    children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading,\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this), paymentLoading ? 'Processing...' : subscriptionStatus === 'active' ? 'Upgrade to This Plan' : subscriptionStatus === 'expired' ? 'Renew with This Plan' : 'Select This Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-processing-animation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"payment-status\",\n              children: paymentStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-details\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-plan-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-instructions\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\uD83D\\uDCF1 Check your phone for SMS confirmation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\uD83D\\uDCB3 Follow the instructions to complete payment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal success-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-animation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-checkmark\",\n                children: \"\\uD83C\\uDF89\\u2728\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-confetti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                color: '#52c41a',\n                fontSize: '32px',\n                fontWeight: 'bold',\n                marginBottom: '16px',\n                textAlign: 'center'\n              },\n              children: \"\\uD83C\\uDF8A Payment Successful! \\uD83C\\uDF8A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"success-message\",\n              style: {\n                fontSize: '18px',\n                color: '#1890ff',\n                fontWeight: '500',\n                textAlign: 'center',\n                marginBottom: '24px'\n              },\n              children: [\"\\uD83C\\uDF89 Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"! Your subscription is now ACTIVE and all features are unlocked! \\uD83C\\uDF89\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-details\",\n              style: {\n                background: 'linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%)',\n                border: '2px solid #52c41a',\n                borderRadius: '12px',\n                padding: '20px',\n                marginBottom: '20px',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-plan-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    color: '#389e0d',\n                    marginBottom: '12px'\n                  },\n                  children: \"\\uD83C\\uDFAF Your Premium Plan Details:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'grid',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\uD83D\\uDCCB Plan:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#1890ff'\n                      },\n                      children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 51\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u23F0 Duration:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#1890ff'\n                      },\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 54\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\uD83D\\uDCB0 Amount:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#1890ff'\n                      },\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\uD83D\\uDC8E Status:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 602,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#52c41a',\n                        fontWeight: 'bold'\n                      },\n                      children: \"ACTIVE & PREMIUM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 602,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-features\",\n              style: {\n                background: '#fff7e6',\n                border: '1px solid #ffd666',\n                borderRadius: '12px',\n                padding: '20px',\n                marginBottom: '24px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  color: '#d48806',\n                  marginBottom: '16px',\n                  textAlign: 'center'\n                },\n                children: \"\\uD83D\\uDE80 Everything is now unlocked for you:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '8px',\n                  textAlign: 'left'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Unlimited Quizzes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 74\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83E\\uDD16 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"AI Chat Assistant\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83D\\uDCDA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Study Materials\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83D\\uDCCA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Progress Tracking\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83C\\uDFA5 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Learning Videos\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83D\\uDCAC \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Forum Access\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-actions\",\n              style: {\n                display: 'flex',\n                gap: '16px',\n                justifyContent: 'center',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"success-btn primary\",\n                onClick: () => {\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                },\n                style: {\n                  background: 'linear-gradient(45deg, #1890ff, #52c41a)',\n                  border: 'none',\n                  color: 'white',\n                  padding: '12px 24px',\n                  borderRadius: '8px',\n                  fontSize: '16px',\n                  fontWeight: 'bold',\n                  cursor: 'pointer',\n                  minWidth: '160px'\n                },\n                children: \"\\uD83C\\uDFE0 Go to Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"success-btn secondary\",\n                onClick: () => setShowSuccessModal(false),\n                style: {\n                  background: 'white',\n                  border: '2px solid #1890ff',\n                  color: '#1890ff',\n                  padding: '12px 24px',\n                  borderRadius: '8px',\n                  fontSize: '16px',\n                  fontWeight: 'bold',\n                  cursor: 'pointer',\n                  minWidth: '160px'\n                },\n                children: \"Continue Here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginTop: '20px',\n                fontSize: '14px',\n                color: '#8c8c8c',\n                fontStyle: 'italic',\n                textAlign: 'center'\n              },\n              children: \"\\uD83C\\uDF89 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 325,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"7rikL3tWJsP/hz3mgn9dXHf5mpw=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "jsxDEV", "_jsxDEV", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "console", "log", "response", "success", "data", "length", "Array", "isArray", "warn", "info", "error", "warning", "handlePlanSelect", "plan", "phoneNumber", "test", "_user$name", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "setTimeout", "checkPaymentConfirmation", "order_id", "Error", "orderId", "isPolling", "handleVisibilityChange", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "demo", "document", "removeEventListener", "content", "style", "marginTop", "fontSize", "remainingSeconds", "Math", "ceil", "hidden", "addEventListener", "getSubscriptionStatus", "endDate", "Date", "now", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "includes", "toLocaleString", "slice", "feature", "index", "disabled", "window", "location", "href", "color", "fontWeight", "marginBottom", "textAlign", "background", "border", "borderRadius", "padding", "display", "gap", "margin", "gridTemplateColumns", "justifyContent", "flexWrap", "cursor", "min<PERSON><PERSON><PERSON>", "fontStyle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"glimp-plan-sample\",\n      title: \"Glimp Plan\",\n      features: [\n        \"1-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 15000,\n      discountedPrice: 13000,\n      discountPercentage: 13,\n      duration: 1,\n      status: true\n    },\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  const handlePlanSelect = async (plan) => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n      setSelectedPlan(plan);\n      setPaymentLoading(true);\n      setShowProcessingModal(true);\n      setPaymentStatus('Initiating payment...');\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('Sending payment request...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        // Start checking payment status\n        setTimeout(() => {\n          checkPaymentConfirmation(response.order_id || 'demo_order');\n        }, 3000);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let isPolling = true;\n    let handleVisibilityChange;\n\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 1 second for up to 1 minute (very responsive)\n      let attempts = 0;\n      const maxAttempts = 60; // 60 attempts * 1 second = 1 minute\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n\n          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n\n            // Show success immediately\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setShowSuccessModal(true);\n              setPaymentLoading(false);\n\n              // Refresh subscription data\n              checkCurrentSubscription();\n\n              // Show immediate success message\n              message.success({\n                content: '🎉 Payment confirmed! All features are now unlocked!',\n                duration: 5,\n                style: {\n                  marginTop: '20vh',\n                  fontSize: '16px'\n                }\n              });\n\n            }, 500); // Reduced delay for immediate feedback\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(false);\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n\n          } else {\n            // Continue polling with very frequent checks\n            const remainingSeconds = (maxAttempts - attempts);\n            if (remainingSeconds > 30) {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${Math.ceil(remainingSeconds / 60)} minutes remaining)`);\n            } else {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${remainingSeconds} seconds remaining)`);\n            }\n            setTimeout(pollPaymentStatus, 1000); // Check every 1 second\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(false);\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      isPolling = false; // Stop polling\n      document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('glimp') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <span className=\"current-price\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Upgrade to This Plan'\n                        : subscriptionStatus === 'expired'\n                          ? 'Renew with This Plan'\n                          : 'Select This Plan'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Payment Processing Modal */}\n        {showProcessingModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal\">\n              <div className=\"payment-modal-content\">\n                <div className=\"payment-processing-animation\">\n                  <div className=\"payment-spinner\"></div>\n                  <div className=\"payment-pulse\"></div>\n                </div>\n                <h3>Processing Payment</h3>\n                <p className=\"payment-status\">{paymentStatus}</p>\n                <div className=\"payment-details\">\n                  <div className=\"payment-plan-info\">\n                    <h4>{selectedPlan?.title}</h4>\n                    <p>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</p>\n                    <p>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</p>\n                  </div>\n                </div>\n                <div className=\"payment-instructions\">\n                  <p>📱 Check your phone for SMS confirmation</p>\n                  <p>💳 Follow the instructions to complete payment</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced Payment Success Modal */}\n        {showSuccessModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal success-modal\">\n              <div className=\"payment-modal-content\">\n                <div className=\"success-animation\">\n                  <div className=\"success-checkmark\">🎉✨🚀</div>\n                  <div className=\"success-confetti\"></div>\n                </div>\n                <h2 style={{\n                  color: '#52c41a',\n                  fontSize: '32px',\n                  fontWeight: 'bold',\n                  marginBottom: '16px',\n                  textAlign: 'center'\n                }}>\n                  🎊 Payment Successful! 🎊\n                </h2>\n                <p className=\"success-message\" style={{\n                  fontSize: '18px',\n                  color: '#1890ff',\n                  fontWeight: '500',\n                  textAlign: 'center',\n                  marginBottom: '24px'\n                }}>\n                  🎉 Welcome to {selectedPlan?.title}! Your subscription is now ACTIVE and all features are unlocked! 🎉\n                </p>\n\n                <div className=\"success-details\" style={{\n                  background: 'linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%)',\n                  border: '2px solid #52c41a',\n                  borderRadius: '12px',\n                  padding: '20px',\n                  marginBottom: '20px',\n                  textAlign: 'center'\n                }}>\n                  <div className=\"success-plan-info\">\n                    <h3 style={{ color: '#389e0d', marginBottom: '12px' }}>\n                      🎯 Your Premium Plan Details:\n                    </h3>\n                    <div style={{ display: 'grid', gap: '8px' }}>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>📋 Plan:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.title}</span>\n                      </p>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>⏰ Duration:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</span>\n                      </p>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>💰 Amount:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</span>\n                      </p>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>💎 Status:</strong> <span style={{ color: '#52c41a', fontWeight: 'bold' }}>ACTIVE & PREMIUM</span>\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"success-features\" style={{\n                  background: '#fff7e6',\n                  border: '1px solid #ffd666',\n                  borderRadius: '12px',\n                  padding: '20px',\n                  marginBottom: '24px'\n                }}>\n                  <h3 style={{ color: '#d48806', marginBottom: '16px', textAlign: 'center' }}>\n                    🚀 Everything is now unlocked for you:\n                  </h3>\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '8px',\n                    textAlign: 'left'\n                  }}>\n                    <div>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>✅ <strong>Unlimited Quizzes</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>🤖 <strong>AI Chat Assistant</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>📚 <strong>Study Materials</strong></p>\n                    </div>\n                    <div>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>📊 <strong>Progress Tracking</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>🎥 <strong>Learning Videos</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>💬 <strong>Forum Access</strong></p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"success-actions\" style={{\n                  display: 'flex',\n                  gap: '16px',\n                  justifyContent: 'center',\n                  flexWrap: 'wrap'\n                }}>\n                  <button\n                    className=\"success-btn primary\"\n                    onClick={() => {\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                    style={{\n                      background: 'linear-gradient(45deg, #1890ff, #52c41a)',\n                      border: 'none',\n                      color: 'white',\n                      padding: '12px 24px',\n                      borderRadius: '8px',\n                      fontSize: '16px',\n                      fontWeight: 'bold',\n                      cursor: 'pointer',\n                      minWidth: '160px'\n                    }}\n                  >\n                    🏠 Go to Dashboard\n                  </button>\n                  <button\n                    className=\"success-btn secondary\"\n                    onClick={() => setShowSuccessModal(false)}\n                    style={{\n                      background: 'white',\n                      border: '2px solid #1890ff',\n                      color: '#1890ff',\n                      padding: '12px 24px',\n                      borderRadius: '8px',\n                      fontSize: '16px',\n                      fontWeight: 'bold',\n                      cursor: 'pointer',\n                      minWidth: '160px'\n                    }}\n                  >\n                    Continue Here\n                  </button>\n                </div>\n\n                <p style={{\n                  marginTop: '20px',\n                  fontSize: '14px',\n                  color: '#8c8c8c',\n                  fontStyle: 'italic',\n                  textAlign: 'center'\n                }}>\n                  🎉 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies!\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEsC;EAAK,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGtC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwC,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDlD,SAAS,CAAC,MAAM;IACdmD,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChB2B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMC,QAAQ,GAAG,MAAM5C,QAAQ,CAAC,CAAC;MACjC0C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjElC,QAAQ,CAAC+B,QAAQ,CAACE,IAAI,CAAC;QACvBJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACnE,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QACzD;QACAlC,QAAQ,CAAC+B,QAAQ,CAAC;QAClBF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;MAC1D,CAAC,MAAM;QACLF,OAAO,CAACQ,IAAI,CAAC,uCAAuC,CAAC;QACrDrC,QAAQ,CAACkB,WAAW,CAAC;QACrBtC,OAAO,CAAC0D,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDV,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C9B,QAAQ,CAACkB,WAAW,CAAC;MACrBtC,OAAO,CAAC4D,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM1C,kBAAkB,CAAC,CAAC;MAC3CwC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdV,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACvC,IAAI,CAAC7B,IAAI,CAAC8B,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC/B,IAAI,CAAC8B,WAAW,CAAC,EAAE;MACjE/D,OAAO,CAAC2D,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAM,UAAA;MACFnC,eAAe,CAACgC,IAAI,CAAC;MACrBtC,iBAAiB,CAAC,IAAI,CAAC;MACvBE,sBAAsB,CAAC,IAAI,CAAC;MAC5BM,gBAAgB,CAAC,uBAAuB,CAAC;MAEzC,MAAMkC,WAAW,GAAG;QAClBJ,IAAI,EAAEA,IAAI;QACVK,MAAM,EAAElC,IAAI,CAACM,GAAG;QAChB6B,SAAS,EAAEnC,IAAI,CAAC8B,WAAW;QAC3BM,SAAS,EAAEpC,IAAI,CAACqC,KAAK,IAAK,IAAAL,UAAA,GAAEhC,IAAI,CAACsC,IAAI,cAAAN,UAAA,uBAATA,UAAA,CAAWO,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAEDzC,gBAAgB,CAAC,4BAA4B,CAAC;MAC9C,MAAMmB,QAAQ,GAAG,MAAM3C,UAAU,CAAC0D,WAAW,CAAC;MAE9C,IAAIf,QAAQ,CAACC,OAAO,EAAE;QACpBpB,gBAAgB,CAAC,wDAAwD,CAAC;;QAE1E;QACA0C,UAAU,CAAC,MAAM;UACfC,wBAAwB,CAACxB,QAAQ,CAACyB,QAAQ,IAAI,YAAY,CAAC;QAC7D,CAAC,EAAE,IAAI,CAAC;MAEV,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC1B,QAAQ,CAACnD,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACdjC,sBAAsB,CAAC,KAAK,CAAC;MAC7B1B,OAAO,CAAC2D,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAAC3D,OAAO,CAAC;MACjDwB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMmD,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,sBAAsB;IAE1B,IAAI;MACFhD,gBAAgB,CAAC,0EAA0E,CAAC;;MAE5F;MACA,IAAIiD,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;;MAExB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QAEV,IAAI;UACF,MAAMG,cAAc,GAAG,MAAM3E,kBAAkB,CAAC;YAAEqE;UAAQ,CAAC,CAAC;UAE5D,IAAIM,cAAc,CAAChC,OAAO,KAAKgC,cAAc,CAACtC,MAAM,KAAK,WAAW,IAAIsC,cAAc,CAACC,IAAI,CAAC,EAAE;YAC5F;YACAN,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BM,QAAQ,CAACC,mBAAmB,CAAC,kBAAkB,EAAEP,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEAhD,gBAAgB,CAAC,uDAAuD,CAAC;;YAEzE;YACA0C,UAAU,CAAC,MAAM;cACfhD,sBAAsB,CAAC,KAAK,CAAC;cAC7BE,mBAAmB,CAAC,IAAI,CAAC;cACzBJ,iBAAiB,CAAC,KAAK,CAAC;;cAExB;cACAwB,wBAAwB,CAAC,CAAC;;cAE1B;cACAhD,OAAO,CAACoD,OAAO,CAAC;gBACdoC,OAAO,EAAE,sDAAsD;gBAC/D3C,QAAQ,EAAE,CAAC;gBACX4C,KAAK,EAAE;kBACLC,SAAS,EAAE,MAAM;kBACjBC,QAAQ,EAAE;gBACZ;cACF,CAAC,CAAC;YAEJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;UAEX,CAAC,MAAM,IAAIV,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BM,QAAQ,CAACC,mBAAmB,CAAC,kBAAkB,EAAEP,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEAhD,gBAAgB,CAAC,8EAA8E,CAAC;YAEhG0C,UAAU,CAAC,MAAM;cACfhD,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,KAAK,CAAC;cACxBxB,OAAO,CAAC4D,OAAO,CAAC,0GAA0G,CAAC;YAC7H,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACA,MAAMgC,gBAAgB,GAAIV,WAAW,GAAGD,QAAS;YACjD,IAAIW,gBAAgB,GAAG,EAAE,EAAE;cACzB5D,gBAAgB,CAAE,4CAA2C6D,IAAI,CAACC,IAAI,CAACF,gBAAgB,GAAG,EAAE,CAAE,qBAAoB,CAAC;YACrH,CAAC,MAAM;cACL5D,gBAAgB,CAAE,4CAA2C4D,gBAAiB,qBAAoB,CAAC;YACrG;YACAlB,UAAU,CAACS,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;UACvC;QAEF,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdV,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,IAAIsB,QAAQ,IAAIC,WAAW,EAAE;YAC3BH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BM,QAAQ,CAACC,mBAAmB,CAAC,kBAAkB,EAAEP,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YACAtD,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,KAAK,CAAC;YACxBxB,OAAO,CAAC2D,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAe,UAAU,CAACS,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAH,sBAAsB,GAAGA,CAAA,KAAM;QAC7B,IAAI,CAACM,QAAQ,CAACS,MAAM,IAAIhB,SAAS,EAAE;UACjC9B,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3ElB,gBAAgB,CAAC,+BAA+B,CAAC;UACjD;UACA0C,UAAU,CAAC,MAAMS,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C;MACF,CAAC;MAEDG,QAAQ,CAACU,gBAAgB,CAAC,kBAAkB,EAAEhB,sBAAsB,CAAC;;MAErE;MACAN,UAAU,CAACS,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdoB,SAAS,GAAG,KAAK,CAAC,CAAC;MACnBO,QAAQ,CAACC,mBAAmB,CAAC,kBAAkB,EAAEP,sBAAsB,CAAC,CAAC,CAAC;MAC1EtD,sBAAsB,CAAC,KAAK,CAAC;MAC7B1B,OAAO,CAAC2D,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAAC3D,OAAO,CAAC;MAC9DwB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMyE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI9D,gBAAgB,IAAIA,gBAAgB,CAACJ,aAAa,KAAK,MAAM,IAAII,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMoD,OAAO,GAAG,IAAIC,IAAI,CAAChE,gBAAgB,CAAC+D,OAAO,CAAC;MAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGE,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAAnE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,kBAAkB,MAAK,SAAS,IAAKlE,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAMwD,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIJ,IAAI,CAACI,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACzE,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE+D,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAAChE,gBAAgB,CAAC+D,OAAO,CAAC;IAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMU,QAAQ,GAAGX,OAAO,GAAGE,GAAG;IAC9B,MAAMU,QAAQ,GAAGjB,IAAI,CAACC,IAAI,CAACe,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOhB,IAAI,CAACkB,GAAG,CAAC,CAAC,EAAED,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGJ,qBAAqB,CAAC,CAAC;EAElD,oBACEpF,OAAA;IAAKmG,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChCpG,OAAA;MAAKmG,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCpG,OAAA,CAACd,MAAM,CAACmH,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE1E,QAAQ,EAAE;QAAI,CAAE;QAC9BmE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAE/BpG,OAAA;UAAImG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxBpG,OAAA,CAACZ,OAAO;YAAC+G,SAAS,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9G,OAAA;UAAGmG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGb9G,OAAA,CAACd,MAAM,CAACmH,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE1E,QAAQ,EAAE,GAAG;UAAE+E,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhCpG,OAAA;UAAImG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDtB,kBAAkB,KAAK,QAAQ,iBAC9BxF,OAAA;UAAKmG,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCpG,OAAA;YAAKmG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCpG,OAAA,CAACV,aAAa;cAAC6G,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD9G,OAAA;cAAMmG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN9G,OAAA;YAAKmG,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCpG,OAAA;cAAKmG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpG,OAAA,CAACZ,OAAO;gBAAC+G,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnC9G,OAAA;gBAAAoG,QAAA,GAAM,QAAM,EAAC,CAAA9E,gBAAgB,aAAhBA,gBAAgB,wBAAAnB,qBAAA,GAAhBmB,gBAAgB,CAAE0F,UAAU,cAAA7G,qBAAA,uBAA5BA,qBAAA,CAA8BwB,KAAK,KAAI,cAAc;cAAA;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACN9G,OAAA;cAAKmG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpG,OAAA,CAACX,aAAa;gBAAC8G,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC9G,OAAA;gBAAAoG,QAAA,GAAM,WAAS,EAACX,UAAU,CAACnE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE+D,OAAO,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN9G,OAAA;cAAKmG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpG,OAAA,CAACV,aAAa;gBAAC6G,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC9G,OAAA;gBAAAoG,QAAA,GAAM,kBAAgB,EAACL,gBAAgB,CAAC,CAAC;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAtB,kBAAkB,KAAK,SAAS,iBAC/BxF,OAAA;UAAKmG,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCpG,OAAA;YAAKmG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCpG,OAAA,CAACT,aAAa;cAAC4G,SAAS,EAAC;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD9G,OAAA;cAAMmG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN9G,OAAA;YAAKmG,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCpG,OAAA;cAAKmG,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpG,OAAA,CAACX,aAAa;gBAAC8G,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC9G,OAAA;gBAAAoG,QAAA,GAAM,WAAS,EAACX,UAAU,CAACnE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE+D,OAAO,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN9G,OAAA;cAAGmG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAtB,kBAAkB,KAAK,MAAM,iBAC5BxF,OAAA;UAAKmG,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCpG,OAAA;YAAKmG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCpG,OAAA,CAACP,MAAM;cAAC0G,SAAS,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC9G,OAAA;cAAMmG,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN9G,OAAA;YAAKmG,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCpG,OAAA;cAAGmG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGb9G,OAAA,CAACd,MAAM,CAACmH,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE1E,QAAQ,EAAE,GAAG;UAAE+E,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3BpG,OAAA;UAAImG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1BZ,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eACL9G,OAAA;UAAGmG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5BZ,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEHtG,OAAO,gBACNR,OAAA;UAAKmG,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpG,OAAA;YAAKmG,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/B9G,OAAA;YAAAoG,QAAA,EAAG;UAAgB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJxG,KAAK,CAACmC,MAAM,KAAK,CAAC,gBACpBzC,OAAA;UAAKmG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpG,OAAA;YAAKmG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC9G,OAAA;YAAAoG,QAAA,EAAI;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B9G,OAAA;YAAAoG,QAAA,EAAG;UAA6E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpF9G,OAAA;YAAQmG,SAAS,EAAC,aAAa;YAACc,OAAO,EAAE/E,UAAW;YAAAkE,QAAA,EAAC;UAErD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN9G,OAAA;UAAKmG,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB9F,KAAK,CAAC4G,GAAG,CAAEjE,IAAI;YAAA,IAAAkE,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACdtH,OAAA,CAACd,MAAM,CAACmH,GAAG;cAETkB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BrB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErBpG,OAAA;gBAAKmG,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BpG,OAAA;kBAAImG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEnD,IAAI,CAACtB;gBAAK;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAK,WAAA,GAAAlE,IAAI,CAACtB,KAAK,cAAAwF,WAAA,uBAAVA,WAAA,CAAYvD,WAAW,CAAC,CAAC,CAAC8D,QAAQ,CAAC,OAAO,CAAC,kBAC1C1H,OAAA;kBAAMmG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9G,OAAA;gBAAKmG,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BpG,OAAA;kBAAKmG,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BpG,OAAA;oBAAMmG,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAAgB,qBAAA,GAAEnE,IAAI,CAACnB,eAAe,cAAAsF,qBAAA,uBAApBA,qBAAA,CAAsBO,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClF7D,IAAI,CAACpB,WAAW,GAAGoB,IAAI,CAACnB,eAAe,iBACtC9B,OAAA;oBAAMmG,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,IAAAiB,iBAAA,GAAEpE,IAAI,CAACpB,WAAW,cAAAwF,iBAAA,uBAAhBA,iBAAA,CAAkBM,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAChF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN9G,OAAA;kBAAKmG,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAEnD,IAAI,CAACjB,QAAQ,EAAC,QAAM,EAACiB,IAAI,CAACjB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC,eAEN9G,OAAA;gBAAKmG,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAkB,cAAA,GAC3BrE,IAAI,CAACrB,QAAQ,cAAA0F,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7C9H,OAAA;kBAAiBmG,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvCpG,OAAA,CAACV,aAAa;oBAAC6G,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1C9G,OAAA;oBAAAoG,QAAA,EAAOyB;kBAAO;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdgB,KAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9G,OAAA;gBACEmG,SAAS,EAAC,iBAAiB;gBAC3Bc,OAAO,EAAEA,CAAA,KAAMjE,gBAAgB,CAACC,IAAI,CAAE;gBACtC8E,QAAQ,EAAErH,cAAe;gBAAA0F,QAAA,gBAEzBpG,OAAA,CAACR,YAAY;kBAAC2G,SAAS,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCpG,cAAc,GACX,eAAe,GACf8E,kBAAkB,KAAK,QAAQ,GAC7B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,sBAAsB,GACtB,kBAAkB;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEpB,CAAC;YAAA,GA7CJ7D,IAAI,CAACvB,GAAG;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAAC1F,IAAI,CAAC8B,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC/B,IAAI,CAAC8B,WAAW,CAAC,kBAC7DlD,OAAA,CAACd,MAAM,CAACmH,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE1E,QAAQ,EAAE,GAAG;UAAE+E,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzBpG,OAAA;UAAKmG,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpG,OAAA,CAACT,aAAa;YAAC4G,SAAS,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C9G,OAAA;YAAAoG,QAAA,gBACEpG,OAAA;cAAAoG,QAAA,EAAI;YAAqB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B9G,OAAA;cAAAoG,QAAA,EAAG;YAAuE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9E9G,OAAA;cACEmG,SAAS,EAAC,kBAAkB;cAC5Bc,OAAO,EAAEA,CAAA,KAAMe,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAA9B,QAAA,EAClD;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGAlG,mBAAmB,iBAClBZ,OAAA;QAAKmG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCpG,OAAA;UAAKmG,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BpG,OAAA;YAAKmG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCpG,OAAA;cAAKmG,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3CpG,OAAA;gBAAKmG,SAAS,EAAC;cAAiB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvC9G,OAAA;gBAAKmG,SAAS,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN9G,OAAA;cAAAoG,QAAA,EAAI;YAAkB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3B9G,OAAA;cAAGmG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAElF;YAAa;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD9G,OAAA;cAAKmG,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BpG,OAAA;gBAAKmG,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCpG,OAAA;kBAAAoG,QAAA,EAAKpF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW;gBAAK;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9B9G,OAAA;kBAAAoG,QAAA,GAAIpF,YAAY,aAAZA,YAAY,wBAAAZ,qBAAA,GAAZY,YAAY,CAAEc,eAAe,cAAA1B,qBAAA,uBAA7BA,qBAAA,CAA+BuH,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5D9G,OAAA;kBAAAoG,QAAA,GAAIpF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,EAAC,QAAM,EAAC,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9G,OAAA;cAAKmG,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCpG,OAAA;gBAAAoG,QAAA,EAAG;cAAwC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/C9G,OAAA;gBAAAoG,QAAA,EAAG;cAA8C;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAhG,gBAAgB,iBACfd,OAAA;QAAKmG,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCpG,OAAA;UAAKmG,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CpG,OAAA;YAAKmG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCpG,OAAA;cAAKmG,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCpG,OAAA;gBAAKmG,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C9G,OAAA;gBAAKmG,SAAS,EAAC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACN9G,OAAA;cAAI4E,KAAK,EAAE;gBACTuD,KAAK,EAAE,SAAS;gBAChBrD,QAAQ,EAAE,MAAM;gBAChBsD,UAAU,EAAE,MAAM;gBAClBC,YAAY,EAAE,MAAM;gBACpBC,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAAC;YAEH;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9G,OAAA;cAAGmG,SAAS,EAAC,iBAAiB;cAACvB,KAAK,EAAE;gBACpCE,QAAQ,EAAE,MAAM;gBAChBqD,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBE,SAAS,EAAE,QAAQ;gBACnBD,YAAY,EAAE;cAChB,CAAE;cAAAjC,QAAA,GAAC,0BACa,EAACpF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW,KAAK,EAAC,+EACrC;YAAA;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ9G,OAAA;cAAKmG,SAAS,EAAC,iBAAiB;cAACvB,KAAK,EAAE;gBACtC2D,UAAU,EAAE,mDAAmD;gBAC/DC,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfL,YAAY,EAAE,MAAM;gBACpBC,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,eACApG,OAAA;gBAAKmG,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCpG,OAAA;kBAAI4E,KAAK,EAAE;oBAAEuD,KAAK,EAAE,SAAS;oBAAEE,YAAY,EAAE;kBAAO,CAAE;kBAAAjC,QAAA,EAAC;gBAEvD;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9G,OAAA;kBAAK4E,KAAK,EAAE;oBAAE+D,OAAO,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAM,CAAE;kBAAAxC,QAAA,gBAC1CpG,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,GAAG;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,gBAC1CpG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAQ;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAA9G,OAAA;sBAAM4E,KAAK,EAAE;wBAAEuD,KAAK,EAAE;sBAAU,CAAE;sBAAA/B,QAAA,EAAEpF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW;oBAAK;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CAAC,eACJ9G,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,GAAG;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,gBAC1CpG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAW;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAA9G,OAAA;sBAAM4E,KAAK,EAAE;wBAAEuD,KAAK,EAAE;sBAAU,CAAE;sBAAA/B,QAAA,GAAEpF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,EAAC,QAAM,EAAC,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;oBAAA;sBAAA2E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3I,CAAC,eACJ9G,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,GAAG;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,gBAC1CpG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAU;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAA9G,OAAA;sBAAM4E,KAAK,EAAE;wBAAEuD,KAAK,EAAE;sBAAU,CAAE;sBAAA/B,QAAA,GAAEpF,YAAY,aAAZA,YAAY,wBAAAX,sBAAA,GAAZW,YAAY,CAAEc,eAAe,cAAAzB,sBAAA,uBAA7BA,sBAAA,CAA+BsH,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H,CAAC,eACJ9G,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,GAAG;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,gBAC1CpG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAU;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAA9G,OAAA;sBAAM4E,KAAK,EAAE;wBAAEuD,KAAK,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAO,CAAE;sBAAAhC,QAAA,EAAC;oBAAgB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAKmG,SAAS,EAAC,kBAAkB;cAACvB,KAAK,EAAE;gBACvC2D,UAAU,EAAE,SAAS;gBACrBC,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfL,YAAY,EAAE;cAChB,CAAE;cAAAjC,QAAA,gBACApG,OAAA;gBAAI4E,KAAK,EAAE;kBAAEuD,KAAK,EAAE,SAAS;kBAAEE,YAAY,EAAE,MAAM;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAlC,QAAA,EAAC;cAE5E;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9G,OAAA;gBAAK4E,KAAK,EAAE;kBACV+D,OAAO,EAAE,MAAM;kBACfG,mBAAmB,EAAE,sCAAsC;kBAC3DF,GAAG,EAAE,KAAK;kBACVN,SAAS,EAAE;gBACb,CAAE;gBAAAlC,QAAA,gBACApG,OAAA;kBAAAoG,QAAA,gBACEpG,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,OAAO;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,GAAC,SAAE,eAAApG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzF9G,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,OAAO;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,GAAC,eAAG,eAAApG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1F9G,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,OAAO;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,GAAC,eAAG,eAAApG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACN9G,OAAA;kBAAAoG,QAAA,gBACEpG,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,OAAO;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,GAAC,eAAG,eAAApG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1F9G,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,OAAO;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,GAAC,eAAG,eAAApG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACxF9G,OAAA;oBAAG4E,KAAK,EAAE;sBAAEiE,MAAM,EAAE,OAAO;sBAAE/D,QAAQ,EAAE;oBAAO,CAAE;oBAAAsB,QAAA,GAAC,eAAG,eAAApG,OAAA;sBAAAoG,QAAA,EAAQ;oBAAY;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9G,OAAA;cAAKmG,SAAS,EAAC,iBAAiB;cAACvB,KAAK,EAAE;gBACtC+D,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,MAAM;gBACXG,cAAc,EAAE,QAAQ;gBACxBC,QAAQ,EAAE;cACZ,CAAE;cAAA5C,QAAA,gBACApG,OAAA;gBACEmG,SAAS,EAAC,qBAAqB;gBAC/Bc,OAAO,EAAEA,CAAA,KAAM;kBACblG,mBAAmB,CAAC,KAAK,CAAC;kBAC1BiH,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;gBACpC,CAAE;gBACFtD,KAAK,EAAE;kBACL2D,UAAU,EAAE,0CAA0C;kBACtDC,MAAM,EAAE,MAAM;kBACdL,KAAK,EAAE,OAAO;kBACdO,OAAO,EAAE,WAAW;kBACpBD,YAAY,EAAE,KAAK;kBACnB3D,QAAQ,EAAE,MAAM;kBAChBsD,UAAU,EAAE,MAAM;kBAClBa,MAAM,EAAE,SAAS;kBACjBC,QAAQ,EAAE;gBACZ,CAAE;gBAAA9C,QAAA,EACH;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9G,OAAA;gBACEmG,SAAS,EAAC,uBAAuB;gBACjCc,OAAO,EAAEA,CAAA,KAAMlG,mBAAmB,CAAC,KAAK,CAAE;gBAC1C6D,KAAK,EAAE;kBACL2D,UAAU,EAAE,OAAO;kBACnBC,MAAM,EAAE,mBAAmB;kBAC3BL,KAAK,EAAE,SAAS;kBAChBO,OAAO,EAAE,WAAW;kBACpBD,YAAY,EAAE,KAAK;kBACnB3D,QAAQ,EAAE,MAAM;kBAChBsD,UAAU,EAAE,MAAM;kBAClBa,MAAM,EAAE,SAAS;kBACjBC,QAAQ,EAAE;gBACZ,CAAE;gBAAA9C,QAAA,EACH;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN9G,OAAA;cAAG4E,KAAK,EAAE;gBACRC,SAAS,EAAE,MAAM;gBACjBC,QAAQ,EAAE,MAAM;gBAChBqD,KAAK,EAAE,SAAS;gBAChBgB,SAAS,EAAE,QAAQ;gBACnBb,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAAC;YAEH;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5G,EAAA,CA/qBID,YAAY;EAAA,QAQCjB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAAmK,EAAA,GAVxBnJ,YAAY;AAirBlB,eAAeA,YAAY;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}