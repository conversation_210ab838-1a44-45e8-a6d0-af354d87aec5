{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionModal\\\\SubscriptionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionModal = ({\n  isOpen,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  var _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = phone => {\n    const isValid = phone && /^(06|07)\\d{8}$/.test(phone);\n    console.log('🔍 Phone validation:', {\n      phone,\n      isValid\n    });\n    return isValid;\n  };\n\n  // Update user's phone number in profile (simplified approach)\n  const updateUserPhoneNumber = async newPhone => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n      console.log('👤 Current user data:', user);\n\n      // Ensure we have all required fields\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Try direct API call first\n      console.log('🔄 Attempting direct API call...');\n      const directPayload = {\n        userId: user._id,\n        name: user.name || 'Unknown',\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone\n      };\n      console.log('📤 Direct API payload:', directPayload);\n      const directResponse = await axiosInstance.post('/api/users/update-user-info', directPayload);\n      console.log('📥 Direct API response:', directResponse.data);\n      if (directResponse.data.success) {\n        // Update Redux store with new user data\n        const updatedUser = directResponse.data.data;\n        dispatch(SetUser(updatedUser));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(updatedUser));\n\n        // Force update the payment phone state\n        setPaymentPhone(updatedUser.phoneNumber);\n        console.log('✅ User phone number updated successfully (direct API)');\n        console.log('📱 New user data:', updatedUser);\n        console.log('📱 Updated phone number:', updatedUser.phoneNumber);\n        return true;\n      } else {\n        console.error('❌ Direct API failed, trying updateUserInfo...');\n\n        // Fallback to original method\n        const response = await updateUserInfo(directPayload);\n        console.log('📥 Fallback response:', response);\n        if (response.success) {\n          // Update Redux store with new user data\n          const updatedUser = response.data;\n          dispatch(SetUser(updatedUser));\n\n          // Update localStorage\n          localStorage.setItem('user', JSON.stringify(updatedUser));\n\n          // Force update the payment phone state\n          setPaymentPhone(updatedUser.phoneNumber);\n          console.log('✅ User phone number updated successfully (fallback)');\n          console.log('📱 New user data:', updatedUser);\n          console.log('📱 Updated phone number:', updatedUser.phoneNumber);\n          return true;\n        } else {\n          console.error('❌ Both methods failed');\n          console.error('❌ Direct response:', directResponse.data);\n          console.error('❌ Fallback response:', response);\n          return false;\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response2$data;\n      console.error('❌ Error updating user phone number:', error);\n      console.error('❌ Error details:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n\n      // Show specific error message\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.message) {\n        message.error(`Update failed: ${error.response.data.message}`);\n      }\n      return false;\n    }\n  };\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    }\n  }, [isOpen, user === null || user === void 0 ? void 0 : user.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.phoneNumber && !isEditingPhone) {\n      console.log('🔄 User phone changed, updating payment phone:', user.phoneNumber);\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user === null || user === void 0 ? void 0 : user.phoneNumber, isEditingPhone]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = plan => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone,\n        // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n          return true;\n        }\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: [step === 'plans' && '🚀 Choose Your Learning Plan', step === 'payment' && '💳 Complete Your Payment', step === 'success' && '⏳ Processing Payment...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [step === 'plans' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading plans...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this) : plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$features, _plan$features2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-card\",\n              onClick: () => handlePlanSelect(plan),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 23\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-amount\",\n                  children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-original\",\n                  children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-period\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: [(_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 25\n                }, this)), ((_plan$features2 = plan.features) === null || _plan$features2 === void 0 ? void 0 : _plan$features2.length) > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: [plan.features.length - 4, \" more features\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                children: [\"Choose \", plan.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), step === 'payment' && selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-plan-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Selected Plan: \", selectedPlan.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"plan-price-summary\",\n              children: [(_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS for \", selectedPlan.duration, \" month\", selectedPlan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-label\",\n                  children: \"Phone Number for Payment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this), !isEditingPhone ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `info-value ${phoneUpdated ? 'updated' : ''}`,\n                    children: [paymentPhone || 'Not provided', phoneUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"updated-indicator\",\n                      children: \"\\u2705 Updated\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 44\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"edit-phone-btn\",\n                    onClick: () => setIsEditingPhone(true),\n                    type: \"button\",\n                    children: \"\\u270F\\uFE0F Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-edit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    value: paymentPhone,\n                    onChange: e => setPaymentPhone(e.target.value),\n                    placeholder: \"Enter phone number (e.g., 0744963858)\",\n                    className: `phone-input ${paymentPhone ? isValidPhone(paymentPhone) ? 'valid' : 'invalid' : ''}`,\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 25\n                  }, this), paymentPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`,\n                    children: isValidPhone(paymentPhone) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message valid\",\n                      children: \"\\u2705 Valid phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message invalid\",\n                      children: \"\\u274C Must start with 06 or 07 and be 10 digits\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"phone-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"save-phone-btn\",\n                      onClick: async e => {\n                        e.preventDefault();\n                        console.log('🔥 SAVE BUTTON CLICKED!');\n                        console.log('📱 Payment phone:', paymentPhone);\n                        console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n                        if (!isValidPhone(paymentPhone)) {\n                          console.log('❌ Invalid phone number');\n                          message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                          return;\n                        }\n                        try {\n                          console.log('💾 Starting phone number save process...');\n\n                          // Simple approach - just close editing and update\n                          setIsEditingPhone(false);\n                          setPhoneUpdated(true);\n\n                          // Force update the displayed phone number immediately\n                          console.log('🔄 Force updating displayed phone number to:', paymentPhone);\n\n                          // Show immediate success\n                          message.success({\n                            content: '📱 Phone number saved for payment!',\n                            duration: 3,\n                            style: {\n                              marginTop: '20vh',\n                              fontSize: '15px',\n                              fontWeight: '600'\n                            }\n                          });\n\n                          // Try to update user profile in background\n                          try {\n                            console.log('🔄 Updating user profile in background...');\n                            const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n                            if (updateSuccess) {\n                              console.log('✅ Profile updated successfully');\n                              setTimeout(() => {\n                                message.info({\n                                  content: '💡 Your profile has been updated permanently.',\n                                  duration: 3,\n                                  style: {\n                                    marginTop: '20vh',\n                                    fontSize: '14px'\n                                  }\n                                });\n                              }, 1000);\n                            } else {\n                              console.log('⚠️ Profile update failed, but phone saved for payment');\n                            }\n                          } catch (profileError) {\n                            console.error('⚠️ Profile update error (non-critical):', profileError);\n                          }\n\n                          // Reset the updated indicator after 5 seconds\n                          setTimeout(() => {\n                            setPhoneUpdated(false);\n                          }, 5000);\n                        } catch (error) {\n                          console.error('❌ Error saving phone number:', error);\n                          message.error('Failed to save phone number. Please try again.');\n                        }\n                      },\n                      disabled: !isValidPhone(paymentPhone),\n                      type: \"button\",\n                      children: \"\\u2705 Save\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"cancel-phone-btn\",\n                      onClick: () => {\n                        setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n                        setIsEditingPhone(false);\n                      },\n                      type: \"button\",\n                      children: \"\\u274C Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-note\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"\\uD83D\\uDCA1 This number will receive the payment SMS. You can use a different number than your profile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Payment Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: \"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"back-btn\",\n              onClick: () => setStep('plans'),\n              children: \"\\u2190 Back to Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pay-btn\",\n              onClick: e => {\n                e.preventDefault();\n                console.log('💳 PAY BUTTON CLICKED!');\n                console.log('📱 Payment phone:', paymentPhone);\n                console.log('✏️ Is editing phone:', isEditingPhone);\n                console.log('⏳ Payment loading:', paymentLoading);\n                if (isEditingPhone) {\n                  message.warning('Please save your phone number first');\n                  return;\n                }\n                if (!paymentPhone) {\n                  message.error('Please enter a phone number');\n                  return;\n                }\n                if (!isValidPhone(paymentPhone)) {\n                  message.error('Please enter a valid phone number');\n                  return;\n                }\n                handlePayment();\n              },\n              disabled: paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone),\n              children: paymentLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 23\n                }, this), \"Processing...\"]\n              }, void 0, true) : isEditingPhone ? 'Save phone number first' : !paymentPhone ? 'Enter phone number' : !isValidPhone(paymentPhone) ? 'Invalid phone number' : `Pay ${(_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString()} TZS`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), step === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-animation\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Request Sent!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation and complete the payment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Check your phone for SMS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Follow the payment instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Your subscription will activate automatically\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"check-status-btn\",\n              onClick: async () => {\n                console.log('🔍 Manual payment check triggered');\n                try {\n                  const response = await checkPaymentStatus();\n                  console.log('📥 Manual check response:', response);\n                  if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                    console.log('✅ Payment confirmed manually!');\n                    dispatch(SetSubscription(response));\n                    message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                    onSuccess && onSuccess();\n                    setTimeout(() => onClose(), 1000);\n                  } else {\n                    message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                  }\n                } catch (error) {\n                  console.error('❌ Manual check error:', error);\n                  message.error('Error checking payment status');\n                }\n              },\n              children: \"\\u2705 Check Payment Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"done-btn\",\n              onClick: handleClose,\n              children: \"I'll complete the payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 282,\n    columnNumber: 5\n  }, this);\n};\n_s(SubscriptionModal, \"aZxc1xREfgbzjCdSZ68Mez4hUuk=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = SubscriptionModal;\nexport default SubscriptionModal;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "message", "getPlans", "addPayment", "checkPaymentStatus", "updateUserInfo", "axiosInstance", "SetSubscription", "SetUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionModal", "isOpen", "onClose", "onSuccess", "_s", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "paymentPhone", "setPaymentPhone", "isEditingPhone", "setIsEditingPhone", "phoneUpdated", "setPhoneUpdated", "isValidPhone", "phone", "<PERSON><PERSON><PERSON><PERSON>", "test", "console", "log", "updateUserPhoneNumber", "newPhone", "user", "_id", "error", "directPayload", "userId", "name", "email", "school", "class_", "class", "className", "level", "phoneNumber", "directResponse", "post", "data", "success", "updatedUser", "dispatch", "localStorage", "setItem", "JSON", "stringify", "response", "_error$response", "_error$response2", "_error$response2$data", "state", "fetchPlans", "Array", "isArray", "handlePlanSelect", "plan", "handlePayment", "length", "_user$name", "paymentData", "userPhone", "userEmail", "replace", "toLowerCase", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "checkStatus", "paymentStatus", "status", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "warning", "handleClose", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "title", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "type", "value", "onChange", "e", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "preventDefault", "updateSuccess", "info", "profileError", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionModal/SubscriptionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = (phone) => {\n    const isValid = phone && /^(06|07)\\d{8}$/.test(phone);\n    console.log('🔍 Phone validation:', { phone, isValid });\n    return isValid;\n  };\n\n  // Update user's phone number in profile (simplified approach)\n  const updateUserPhoneNumber = async (newPhone) => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n      console.log('👤 Current user data:', user);\n\n      // Ensure we have all required fields\n      if (!user._id) {\n        console.error('❌ User ID is missing');\n        return false;\n      }\n\n      // Try direct API call first\n      console.log('🔄 Attempting direct API call...');\n\n      const directPayload = {\n        userId: user._id,\n        name: user.name || 'Unknown',\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || user.className || '',\n        level: user.level || 'Primary',\n        phoneNumber: newPhone\n      };\n\n      console.log('📤 Direct API payload:', directPayload);\n\n      const directResponse = await axiosInstance.post('/api/users/update-user-info', directPayload);\n\n      console.log('📥 Direct API response:', directResponse.data);\n\n      if (directResponse.data.success) {\n        // Update Redux store with new user data\n        const updatedUser = directResponse.data.data;\n        dispatch(SetUser(updatedUser));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(updatedUser));\n\n        // Force update the payment phone state\n        setPaymentPhone(updatedUser.phoneNumber);\n\n        console.log('✅ User phone number updated successfully (direct API)');\n        console.log('📱 New user data:', updatedUser);\n        console.log('📱 Updated phone number:', updatedUser.phoneNumber);\n        return true;\n      } else {\n        console.error('❌ Direct API failed, trying updateUserInfo...');\n\n        // Fallback to original method\n        const response = await updateUserInfo(directPayload);\n\n        console.log('📥 Fallback response:', response);\n\n        if (response.success) {\n          // Update Redux store with new user data\n          const updatedUser = response.data;\n          dispatch(SetUser(updatedUser));\n\n          // Update localStorage\n          localStorage.setItem('user', JSON.stringify(updatedUser));\n\n          // Force update the payment phone state\n          setPaymentPhone(updatedUser.phoneNumber);\n\n          console.log('✅ User phone number updated successfully (fallback)');\n          console.log('📱 New user data:', updatedUser);\n          console.log('📱 Updated phone number:', updatedUser.phoneNumber);\n          return true;\n        } else {\n          console.error('❌ Both methods failed');\n          console.error('❌ Direct response:', directResponse.data);\n          console.error('❌ Fallback response:', response);\n          return false;\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error updating user phone number:', error);\n      console.error('❌ Error details:', error.response?.data);\n\n      // Show specific error message\n      if (error.response?.data?.message) {\n        message.error(`Update failed: ${error.response.data.message}`);\n      }\n\n      return false;\n    }\n  };\n  \n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone(user?.phoneNumber || '');\n    }\n  }, [isOpen, user?.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user?.phoneNumber && !isEditingPhone) {\n      console.log('🔄 User phone changed, updating payment phone:', user.phoneNumber);\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user?.phoneNumber, isEditingPhone]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone, // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n        \n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone(user?.phoneNumber || '');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n            {step === 'success' && '⏳ Processing Payment...'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('glimp') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"phone-section\">\n                  <div className=\"info-item\">\n                    <span className=\"info-label\">Phone Number for Payment:</span>\n                    {!isEditingPhone ? (\n                      <div className=\"phone-display\">\n                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>\n                          {paymentPhone || 'Not provided'}\n                          {phoneUpdated && <span className=\"updated-indicator\">✅ Updated</span>}\n                        </span>\n                        <button\n                          className=\"edit-phone-btn\"\n                          onClick={() => setIsEditingPhone(true)}\n                          type=\"button\"\n                        >\n                          ✏️ Change\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"phone-edit\">\n                        <input\n                          type=\"tel\"\n                          value={paymentPhone}\n                          onChange={(e) => setPaymentPhone(e.target.value)}\n                          placeholder=\"Enter phone number (e.g., 0744963858)\"\n                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}\n                          maxLength=\"10\"\n                        />\n                        {paymentPhone && (\n                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>\n                            {isValidPhone(paymentPhone) ? (\n                              <span className=\"validation-message valid\">✅ Valid phone number</span>\n                            ) : (\n                              <span className=\"validation-message invalid\">❌ Must start with 06 or 07 and be 10 digits</span>\n                            )}\n                          </div>\n                        )}\n                        <div className=\"phone-actions\">\n                          <button\n                            className=\"save-phone-btn\"\n                            onClick={async (e) => {\n                              e.preventDefault();\n                              console.log('🔥 SAVE BUTTON CLICKED!');\n                              console.log('📱 Payment phone:', paymentPhone);\n                              console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n\n                              if (!isValidPhone(paymentPhone)) {\n                                console.log('❌ Invalid phone number');\n                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                                return;\n                              }\n\n                              try {\n                                console.log('💾 Starting phone number save process...');\n\n                                // Simple approach - just close editing and update\n                                setIsEditingPhone(false);\n                                setPhoneUpdated(true);\n\n                                // Force update the displayed phone number immediately\n                                console.log('🔄 Force updating displayed phone number to:', paymentPhone);\n\n                                // Show immediate success\n                                message.success({\n                                  content: '📱 Phone number saved for payment!',\n                                  duration: 3,\n                                  style: {\n                                    marginTop: '20vh',\n                                    fontSize: '15px',\n                                    fontWeight: '600'\n                                  }\n                                });\n\n                                // Try to update user profile in background\n                                try {\n                                  console.log('🔄 Updating user profile in background...');\n                                  const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n\n                                  if (updateSuccess) {\n                                    console.log('✅ Profile updated successfully');\n                                    setTimeout(() => {\n                                      message.info({\n                                        content: '💡 Your profile has been updated permanently.',\n                                        duration: 3,\n                                        style: {\n                                          marginTop: '20vh',\n                                          fontSize: '14px'\n                                        }\n                                      });\n                                    }, 1000);\n                                  } else {\n                                    console.log('⚠️ Profile update failed, but phone saved for payment');\n                                  }\n                                } catch (profileError) {\n                                  console.error('⚠️ Profile update error (non-critical):', profileError);\n                                }\n\n                                // Reset the updated indicator after 5 seconds\n                                setTimeout(() => {\n                                  setPhoneUpdated(false);\n                                }, 5000);\n\n                              } catch (error) {\n                                console.error('❌ Error saving phone number:', error);\n                                message.error('Failed to save phone number. Please try again.');\n                              }\n                            }}\n                            disabled={!isValidPhone(paymentPhone)}\n                            type=\"button\"\n                          >\n                            ✅ Save\n                          </button>\n                          <button\n                            className=\"cancel-phone-btn\"\n                            onClick={() => {\n                              setPaymentPhone(user?.phoneNumber || '');\n                              setIsEditingPhone(false);\n                            }}\n                            type=\"button\"\n                          >\n                            ❌ Cancel\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"phone-note\">\n                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    console.log('💳 PAY BUTTON CLICKED!');\n                    console.log('📱 Payment phone:', paymentPhone);\n                    console.log('✏️ Is editing phone:', isEditingPhone);\n                    console.log('⏳ Payment loading:', paymentLoading);\n\n                    if (isEditingPhone) {\n                      message.warning('Please save your phone number first');\n                      return;\n                    }\n\n                    if (!paymentPhone) {\n                      message.error('Please enter a phone number');\n                      return;\n                    }\n\n                    if (!isValidPhone(paymentPhone)) {\n                      message.error('Please enter a valid phone number');\n                      return;\n                    }\n\n                    handlePayment();\n                  }}\n                  disabled={paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone)}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : isEditingPhone ? (\n                    'Save phone number first'\n                  ) : !paymentPhone ? (\n                    'Enter phone number'\n                  ) : !isValidPhone(paymentPhone) ? (\n                    'Invalid phone number'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {step === 'success' && (\n            <div className=\"success-step\">\n              <div className=\"success-animation\">\n                <div className=\"pulse-circle\">\n                  <div className=\"phone-icon\">📱</div>\n                </div>\n              </div>\n              \n              <h3>Payment Request Sent!</h3>\n              <p>Please check your phone for SMS confirmation and complete the payment.</p>\n              \n              <div className=\"payment-steps\">\n                <div className=\"step\">\n                  <span className=\"step-number\">1</span>\n                  <span className=\"step-text\">Check your phone for SMS</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">2</span>\n                  <span className=\"step-text\">Follow the payment instructions</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">3</span>\n                  <span className=\"step-text\">Your subscription will activate automatically</span>\n                </div>\n              </div>\n\n              <div className=\"success-actions\">\n                <button\n                  className=\"check-status-btn\"\n                  onClick={async () => {\n                    console.log('🔍 Manual payment check triggered');\n                    try {\n                      const response = await checkPaymentStatus();\n                      console.log('📥 Manual check response:', response);\n\n                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                        console.log('✅ Payment confirmed manually!');\n                        dispatch(SetSubscription(response));\n                        message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                        onSuccess && onSuccess();\n                        setTimeout(() => onClose(), 1000);\n                      } else {\n                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                      }\n                    } catch (error) {\n                      console.error('❌ Manual check error:', error);\n                      message.error('Error checking payment status');\n                    }\n                  }}\n                >\n                  ✅ Check Payment Status\n                </button>\n\n                <button className=\"done-btn\" onClick={handleClose}>\n                  I'll complete the payment\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,wBAAwB;AACvE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAClE,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMyC,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,OAAO,GAAGD,KAAK,IAAI,gBAAgB,CAACE,IAAI,CAACF,KAAK,CAAC;IACrDG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAAEJ,KAAK;MAAEC;IAAQ,CAAC,CAAC;IACvD,OAAOA,OAAO;EAChB,CAAC;;EAED;EACA,MAAMI,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACFH,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEE,QAAQ,CAAC;MAClEH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,IAAI,CAAC;;MAE1C;MACA,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE;QACbL,OAAO,CAACM,KAAK,CAAC,sBAAsB,CAAC;QACrC,OAAO,KAAK;MACd;;MAEA;MACAN,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAE/C,MAAMM,aAAa,GAAG;QACpBC,MAAM,EAAEJ,IAAI,CAACC,GAAG;QAChBI,IAAI,EAAEL,IAAI,CAACK,IAAI,IAAI,SAAS;QAC5BC,KAAK,EAAEN,IAAI,CAACM,KAAK,IAAI,EAAE;QACvBC,MAAM,EAAEP,IAAI,CAACO,MAAM,IAAI,EAAE;QACzBC,MAAM,EAAER,IAAI,CAACS,KAAK,IAAIT,IAAI,CAACU,SAAS,IAAI,EAAE;QAC1CC,KAAK,EAAEX,IAAI,CAACW,KAAK,IAAI,SAAS;QAC9BC,WAAW,EAAEb;MACf,CAAC;MAEDH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEM,aAAa,CAAC;MAEpD,MAAMU,cAAc,GAAG,MAAMrD,aAAa,CAACsD,IAAI,CAAC,6BAA6B,EAAEX,aAAa,CAAC;MAE7FP,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,cAAc,CAACE,IAAI,CAAC;MAE3D,IAAIF,cAAc,CAACE,IAAI,CAACC,OAAO,EAAE;QAC/B;QACA,MAAMC,WAAW,GAAGJ,cAAc,CAACE,IAAI,CAACA,IAAI;QAC5CG,QAAQ,CAACxD,OAAO,CAACuD,WAAW,CAAC,CAAC;;QAE9B;QACAE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,WAAW,CAAC,CAAC;;QAEzD;QACA9B,eAAe,CAAC8B,WAAW,CAACL,WAAW,CAAC;QAExChB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoB,WAAW,CAAC;QAC7CrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAACL,WAAW,CAAC;QAChE,OAAO,IAAI;MACb,CAAC,MAAM;QACLhB,OAAO,CAACM,KAAK,CAAC,+CAA+C,CAAC;;QAE9D;QACA,MAAMqB,QAAQ,GAAG,MAAMhE,cAAc,CAAC4C,aAAa,CAAC;QAEpDP,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0B,QAAQ,CAAC;QAE9C,IAAIA,QAAQ,CAACP,OAAO,EAAE;UACpB;UACA,MAAMC,WAAW,GAAGM,QAAQ,CAACR,IAAI;UACjCG,QAAQ,CAACxD,OAAO,CAACuD,WAAW,CAAC,CAAC;;UAE9B;UACAE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,WAAW,CAAC,CAAC;;UAEzD;UACA9B,eAAe,CAAC8B,WAAW,CAACL,WAAW,CAAC;UAExChB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClED,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoB,WAAW,CAAC;UAC7CrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAACL,WAAW,CAAC;UAChE,OAAO,IAAI;QACb,CAAC,MAAM;UACLhB,OAAO,CAACM,KAAK,CAAC,uBAAuB,CAAC;UACtCN,OAAO,CAACM,KAAK,CAAC,oBAAoB,EAAEW,cAAc,CAACE,IAAI,CAAC;UACxDnB,OAAO,CAACM,KAAK,CAAC,sBAAsB,EAAEqB,QAAQ,CAAC;UAC/C,OAAO,KAAK;QACd;MACF;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MAAA,IAAAsB,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACd9B,OAAO,CAACM,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DN,OAAO,CAACM,KAAK,CAAC,kBAAkB,GAAAsB,eAAA,GAAEtB,KAAK,CAACqB,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBT,IAAI,CAAC;;MAEvD;MACA,KAAAU,gBAAA,GAAIvB,KAAK,CAACqB,QAAQ,cAAAE,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,eAApBA,qBAAA,CAAsBvE,OAAO,EAAE;QACjCA,OAAO,CAAC+C,KAAK,CAAE,kBAAiBA,KAAK,CAACqB,QAAQ,CAACR,IAAI,CAAC5D,OAAQ,EAAC,CAAC;MAChE;MAEA,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM;IAAE6C;EAAK,CAAC,GAAG/C,WAAW,CAAE0E,KAAK,IAAKA,KAAK,CAAC3B,IAAI,CAAC;EACnD,MAAMkB,QAAQ,GAAGhE,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,EAAE;MACV0D,UAAU,CAAC,CAAC;MACZ;MACAzC,eAAe,CAAC,CAAAa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;IAC1C;EACF,CAAC,EAAE,CAAC1C,MAAM,EAAE8B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,CAAC,CAAC;;EAE/B;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIgD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEY,WAAW,IAAI,CAACxB,cAAc,EAAE;MACxCQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEG,IAAI,CAACY,WAAW,CAAC;MAC/EzB,eAAe,CAACa,IAAI,CAACY,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,EAAExB,cAAc,CAAC,CAAC;EAEvC,MAAMwC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0C,QAAQ,GAAG,MAAMnE,QAAQ,CAAC,CAAC;MACjCqB,QAAQ,CAACoD,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C/C,OAAO,CAAC+C,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkD,gBAAgB,GAAIC,IAAI,IAAK;IACjCrD,eAAe,CAACqD,IAAI,CAAC;IACrB/C,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAMgD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACvD,YAAY,EAAE;MACjBvB,OAAO,CAAC+C,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF;IAEA,IAAI,CAAChB,YAAY,IAAIA,YAAY,CAACgD,MAAM,GAAG,EAAE,EAAE;MAC7C/E,OAAO,CAAC+C,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,IAAI,CAAC,gBAAgB,CAACP,IAAI,CAACT,YAAY,CAAC,EAAE;MACxC/B,OAAO,CAAC+C,KAAK,CAAC,wEAAwE,CAAC;MACvF;IACF;IAEA,IAAI;MAAA,IAAAiC,UAAA;MACFpD,iBAAiB,CAAC,IAAI,CAAC;MACvBmC,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMwE,WAAW,GAAG;QAClBJ,IAAI,EAAEtD,YAAY;QAClB0B,MAAM,EAAEJ,IAAI,CAACC,GAAG;QAChBoC,SAAS,EAAEnD,YAAY;QAAE;QACzBoD,SAAS,EAAEtC,IAAI,CAACM,KAAK,IAAK,IAAA6B,UAAA,GAAEnC,IAAI,CAACK,IAAI,cAAA8B,UAAA,uBAATA,UAAA,CAAWI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAMjB,QAAQ,GAAG,MAAMlE,UAAU,CAAC+E,WAAW,CAAC;MAE9C,IAAIb,QAAQ,CAACP,OAAO,EAAE;QACpB7D,OAAO,CAAC6D,OAAO,CAAC,kEAAkE,CAAC;QACnF/B,OAAO,CAAC,SAAS,CAAC;;QAElB;QACAwD,wBAAwB,CAAClB,QAAQ,CAACmB,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACpB,QAAQ,CAACpE,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC/C,OAAO,CAAC+C,KAAK,CAACA,KAAK,CAAC/C,OAAO,IAAI,mCAAmC,CAAC;IACrE,CAAC,SAAS;MACR4B,iBAAiB,CAAC,KAAK,CAAC;MACxBmC,QAAQ,CAACvD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM8E,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFF,QAAQ,EAAE;QACVjD,OAAO,CAACC,GAAG,CAAE,yCAAwCgD,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAMvB,QAAQ,GAAG,MAAMjE,kBAAkB,CAAC,CAAC;QAC3CsC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0B,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACrB,KAAK,IAAIqB,QAAQ,CAACyB,aAAa,KAAK,MAAM,IAAIzB,QAAQ,CAAC0B,MAAM,KAAK,QAAQ,EAAE;UACpGrD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACAqB,QAAQ,CAACzD,eAAe,CAAC8D,QAAQ,CAAC,CAAC;;UAEnC;UACApE,OAAO,CAAC6D,OAAO,CAAC;YACdkC,OAAO,EAAE,2CAA2C;YACpDC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;;UAEF;UACAnF,SAAS,IAAIA,SAAS,CAAC,CAAC;;UAExB;UACAoF,UAAU,CAAC,MAAM;YACfrF,OAAO,CAAC,CAAC;UACX,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,IAAI;QACb;QAEA,IAAI0E,QAAQ,IAAIC,WAAW,EAAE;UAC3BlD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C1C,OAAO,CAACsG,OAAO,CAAC;YACdP,OAAO,EAAE,sGAAsG;YAC/GC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,OAAO,KAAK;QACd;;QAEA;QACAK,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAO7C,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI2C,QAAQ,IAAIC,WAAW,EAAE;UAC3B3F,OAAO,CAAC+C,KAAK,CAAC,4EAA4E,CAAC;QAC7F,CAAC,MAAM;UACLsD,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACAA,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBzE,OAAO,CAAC,OAAO,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,KAAK,CAAC;IACxBM,iBAAiB,CAAC,KAAK,CAAC;IACxBE,eAAe,CAAC,KAAK,CAAC;IACtBJ,eAAe,CAAC,CAAAa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;IACxCzC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAK4C,SAAS,EAAC,4BAA4B;IAAAiD,QAAA,eACzC7F,OAAA;MAAK4C,SAAS,EAAC,oBAAoB;MAAAiD,QAAA,gBACjC7F,OAAA;QAAK4C,SAAS,EAAC,cAAc;QAAAiD,QAAA,gBAC3B7F,OAAA;UAAI4C,SAAS,EAAC,aAAa;UAAAiD,QAAA,GACxB3E,IAAI,KAAK,OAAO,IAAI,8BAA8B,EAClDA,IAAI,KAAK,SAAS,IAAI,0BAA0B,EAChDA,IAAI,KAAK,SAAS,IAAI,yBAAyB;QAAA;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACLjG,OAAA;UAAQ4C,SAAS,EAAC,cAAc;UAACsD,OAAO,EAAEN,WAAY;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAENjG,OAAA;QAAK4C,SAAS,EAAC,eAAe;QAAAiD,QAAA,GAC3B3E,IAAI,KAAK,OAAO,iBACflB,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAiD,QAAA,EACxB/E,OAAO,gBACNd,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAiD,QAAA,gBAC5B7F,OAAA;cAAK4C,SAAS,EAAC;YAAS;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/BjG,OAAA;cAAA6F,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAENvF,KAAK,CAACyF,GAAG,CAAEjC,IAAI;YAAA,IAAAkC,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA;YAAA,oBACbvG,OAAA;cAAoB4C,SAAS,EAAC,WAAW;cAACsD,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACC,IAAI,CAAE;cAAA2B,QAAA,gBAC9E7F,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAiD,QAAA,gBAC1B7F,OAAA;kBAAI4C,SAAS,EAAC,YAAY;kBAAAiD,QAAA,EAAE3B,IAAI,CAACsC;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAG,WAAA,GAAAlC,IAAI,CAACsC,KAAK,cAAAJ,WAAA,uBAAVA,WAAA,CAAY1B,WAAW,CAAC,CAAC,CAAC+B,QAAQ,CAAC,OAAO,CAAC,kBAC1CzG,OAAA;kBAAM4C,SAAS,EAAC,YAAY;kBAAAiD,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENjG,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAiD,QAAA,gBACzB7F,OAAA;kBAAM4C,SAAS,EAAC,cAAc;kBAAAiD,QAAA,IAAAQ,qBAAA,GAAEnC,IAAI,CAACwC,eAAe,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACjF/B,IAAI,CAAC0C,WAAW,IAAI1C,IAAI,CAAC0C,WAAW,KAAK1C,IAAI,CAACwC,eAAe,iBAC5D1G,OAAA;kBAAM4C,SAAS,EAAC,gBAAgB;kBAAAiD,QAAA,GAAE3B,IAAI,CAAC0C,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/E,eACDjG,OAAA;kBAAM4C,SAAS,EAAC,cAAc;kBAAAiD,QAAA,GAAE3B,IAAI,CAACmB,QAAQ,EAAC,QAAM,EAACnB,IAAI,CAACmB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eAENjG,OAAA;gBAAK4C,SAAS,EAAC,eAAe;gBAAAiD,QAAA,IAAAS,cAAA,GAC3BpC,IAAI,CAAC2C,QAAQ,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,KAAK,kBAC7ChH,OAAA;kBAAiB4C,SAAS,EAAC,SAAS;kBAAAiD,QAAA,gBAClC7F,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAiD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvCjG,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAiD,QAAA,EAAEkB;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFvCe,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC,EACD,EAAAM,eAAA,GAAArC,IAAI,CAAC2C,QAAQ,cAAAN,eAAA,uBAAbA,eAAA,CAAenC,MAAM,IAAG,CAAC,iBACxBpE,OAAA;kBAAK4C,SAAS,EAAC,SAAS;kBAAAiD,QAAA,gBACtB7F,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAiD,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvCjG,OAAA;oBAAM4C,SAAS,EAAC,cAAc;oBAAAiD,QAAA,GAAE3B,IAAI,CAAC2C,QAAQ,CAACzC,MAAM,GAAG,CAAC,EAAC,gBAAc;kBAAA;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENjG,OAAA;gBAAQ4C,SAAS,EAAC,iBAAiB;gBAAAiD,QAAA,GAAC,SAC3B,EAAC3B,IAAI,CAACsC,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjCD/B,IAAI,CAAC/B,GAAG;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCb,CAAC;UAAA,CACP;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEA/E,IAAI,KAAK,SAAS,IAAIN,YAAY,iBACjCZ,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAiD,QAAA,gBAC3B7F,OAAA;YAAK4C,SAAS,EAAC,uBAAuB;YAAAiD,QAAA,gBACpC7F,OAAA;cAAA6F,QAAA,GAAI,iBAAe,EAACjF,YAAY,CAAC4F,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5CjG,OAAA;cAAG4C,SAAS,EAAC,oBAAoB;cAAAiD,QAAA,IAAArF,qBAAA,GAC9BI,YAAY,CAAC8F,eAAe,cAAAlG,qBAAA,uBAA5BA,qBAAA,CAA8BmG,cAAc,CAAC,CAAC,EAAC,WAAS,EAAC/F,YAAY,CAACyE,QAAQ,EAAC,QAAM,EAACzE,YAAY,CAACyE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENjG,OAAA;YAAK4C,SAAS,EAAC,cAAc;YAAAiD,QAAA,gBAC3B7F,OAAA;cAAK4C,SAAS,EAAC,eAAe;cAAAiD,QAAA,gBAC5B7F,OAAA;gBAAK4C,SAAS,EAAC,WAAW;gBAAAiD,QAAA,gBACxB7F,OAAA;kBAAM4C,SAAS,EAAC,YAAY;kBAAAiD,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC5D,CAAC3E,cAAc,gBACdtB,OAAA;kBAAK4C,SAAS,EAAC,eAAe;kBAAAiD,QAAA,gBAC5B7F,OAAA;oBAAM4C,SAAS,EAAG,cAAapB,YAAY,GAAG,SAAS,GAAG,EAAG,EAAE;oBAAAqE,QAAA,GAC5DzE,YAAY,IAAI,cAAc,EAC9BI,YAAY,iBAAIxB,OAAA;sBAAM4C,SAAS,EAAC,mBAAmB;sBAAAiD,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACPjG,OAAA;oBACE4C,SAAS,EAAC,gBAAgB;oBAC1BsD,OAAO,EAAEA,CAAA,KAAM3E,iBAAiB,CAAC,IAAI,CAAE;oBACvC0F,IAAI,EAAC,QAAQ;oBAAApB,QAAA,EACd;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAENjG,OAAA;kBAAK4C,SAAS,EAAC,YAAY;kBAAAiD,QAAA,gBACzB7F,OAAA;oBACEiH,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAE9F,YAAa;oBACpB+F,QAAQ,EAAGC,CAAC,IAAK/F,eAAe,CAAC+F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,WAAW,EAAC,uCAAuC;oBACnD1E,SAAS,EAAG,eAAcxB,YAAY,GAAIM,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAS,GAAI,EAAG,EAAE;oBACnGmG,SAAS,EAAC;kBAAI;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACD7E,YAAY,iBACXpB,OAAA;oBAAK4C,SAAS,EAAG,oBAAmBlB,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAU,EAAE;oBAAAyE,QAAA,EACpFnE,YAAY,CAACN,YAAY,CAAC,gBACzBpB,OAAA;sBAAM4C,SAAS,EAAC,0BAA0B;sBAAAiD,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEtEjG,OAAA;sBAAM4C,SAAS,EAAC,4BAA4B;sBAAAiD,QAAA,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC/F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eACDjG,OAAA;oBAAK4C,SAAS,EAAC,eAAe;oBAAAiD,QAAA,gBAC5B7F,OAAA;sBACE4C,SAAS,EAAC,gBAAgB;sBAC1BsD,OAAO,EAAE,MAAOkB,CAAC,IAAK;wBACpBA,CAAC,CAACI,cAAc,CAAC,CAAC;wBAClB1F,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;wBACtCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEX,YAAY,CAAC;wBAC9CU,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEL,YAAY,CAACN,YAAY,CAAC,CAAC;wBAE5D,IAAI,CAACM,YAAY,CAACN,YAAY,CAAC,EAAE;0BAC/BU,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;0BACrC1C,OAAO,CAAC+C,KAAK,CAAC,wEAAwE,CAAC;0BACvF;wBACF;wBAEA,IAAI;0BACFN,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;0BAEvD;0BACAR,iBAAiB,CAAC,KAAK,CAAC;0BACxBE,eAAe,CAAC,IAAI,CAAC;;0BAErB;0BACAK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEX,YAAY,CAAC;;0BAEzE;0BACA/B,OAAO,CAAC6D,OAAO,CAAC;4BACdkC,OAAO,EAAE,oCAAoC;4BAC7CC,QAAQ,EAAE,CAAC;4BACXC,KAAK,EAAE;8BACLC,SAAS,EAAE,MAAM;8BACjBC,QAAQ,EAAE,MAAM;8BAChBC,UAAU,EAAE;4BACd;0BACF,CAAC,CAAC;;0BAEF;0BACA,IAAI;4BACF3D,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;4BACxD,MAAM0F,aAAa,GAAG,MAAMzF,qBAAqB,CAACZ,YAAY,CAAC;4BAE/D,IAAIqG,aAAa,EAAE;8BACjB3F,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;8BAC7C2D,UAAU,CAAC,MAAM;gCACfrG,OAAO,CAACqI,IAAI,CAAC;kCACXtC,OAAO,EAAE,+CAA+C;kCACxDC,QAAQ,EAAE,CAAC;kCACXC,KAAK,EAAE;oCACLC,SAAS,EAAE,MAAM;oCACjBC,QAAQ,EAAE;kCACZ;gCACF,CAAC,CAAC;8BACJ,CAAC,EAAE,IAAI,CAAC;4BACV,CAAC,MAAM;8BACL1D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;4BACtE;0BACF,CAAC,CAAC,OAAO4F,YAAY,EAAE;4BACrB7F,OAAO,CAACM,KAAK,CAAC,yCAAyC,EAAEuF,YAAY,CAAC;0BACxE;;0BAEA;0BACAjC,UAAU,CAAC,MAAM;4BACfjE,eAAe,CAAC,KAAK,CAAC;0BACxB,CAAC,EAAE,IAAI,CAAC;wBAEV,CAAC,CAAC,OAAOW,KAAK,EAAE;0BACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;0BACpD/C,OAAO,CAAC+C,KAAK,CAAC,gDAAgD,CAAC;wBACjE;sBACF,CAAE;sBACFwF,QAAQ,EAAE,CAAClG,YAAY,CAACN,YAAY,CAAE;sBACtC6F,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTjG,OAAA;sBACE4C,SAAS,EAAC,kBAAkB;sBAC5BsD,OAAO,EAAEA,CAAA,KAAM;wBACb7E,eAAe,CAAC,CAAAa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,WAAW,KAAI,EAAE,CAAC;wBACxCvB,iBAAiB,CAAC,KAAK,CAAC;sBAC1B,CAAE;sBACF0F,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNjG,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAiD,QAAA,eACzB7F,OAAA;kBAAA6F,QAAA,EAAO;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjG,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAiD,QAAA,gBACxB7F,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAiD,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDjG,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAiD,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjG,OAAA;YAAK4C,SAAS,EAAC,iBAAiB;YAAAiD,QAAA,gBAC9B7F,OAAA;cAAQ4C,SAAS,EAAC,UAAU;cAACsD,OAAO,EAAEA,CAAA,KAAM/E,OAAO,CAAC,OAAO,CAAE;cAAA0E,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjG,OAAA;cACE4C,SAAS,EAAC,SAAS;cACnBsD,OAAO,EAAGkB,CAAC,IAAK;gBACdA,CAAC,CAACI,cAAc,CAAC,CAAC;gBAClB1F,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;gBACrCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEX,YAAY,CAAC;gBAC9CU,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAET,cAAc,CAAC;gBACnDQ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEf,cAAc,CAAC;gBAEjD,IAAIM,cAAc,EAAE;kBAClBjC,OAAO,CAACsG,OAAO,CAAC,qCAAqC,CAAC;kBACtD;gBACF;gBAEA,IAAI,CAACvE,YAAY,EAAE;kBACjB/B,OAAO,CAAC+C,KAAK,CAAC,6BAA6B,CAAC;kBAC5C;gBACF;gBAEA,IAAI,CAACV,YAAY,CAACN,YAAY,CAAC,EAAE;kBAC/B/B,OAAO,CAAC+C,KAAK,CAAC,mCAAmC,CAAC;kBAClD;gBACF;gBAEA+B,aAAa,CAAC,CAAC;cACjB,CAAE;cACFyD,QAAQ,EAAE5G,cAAc,IAAI,CAACI,YAAY,IAAIE,cAAc,IAAI,CAACI,YAAY,CAACN,YAAY,CAAE;cAAAyE,QAAA,EAE1F7E,cAAc,gBACbhB,OAAA,CAAAE,SAAA;gBAAA2F,QAAA,gBACE7F,OAAA;kBAAM4C,SAAS,EAAC;gBAAa;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEvC;cAAA,eAAE,CAAC,GACD3E,cAAc,GAChB,yBAAyB,GACvB,CAACF,YAAY,GACf,oBAAoB,GAClB,CAACM,YAAY,CAACN,YAAY,CAAC,GAC7B,sBAAsB,GAErB,OAAI,CAAAX,sBAAA,GAAEG,YAAY,CAAC8F,eAAe,cAAAjG,sBAAA,uBAA5BA,sBAAA,CAA8BkG,cAAc,CAAC,CAAE;YACvD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA/E,IAAI,KAAK,SAAS,iBACjBlB,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAiD,QAAA,gBAC3B7F,OAAA;YAAK4C,SAAS,EAAC,mBAAmB;YAAAiD,QAAA,eAChC7F,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAiD,QAAA,eAC3B7F,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAiD,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjG,OAAA;YAAA6F,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BjG,OAAA;YAAA6F,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE7EjG,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAiD,QAAA,gBAC5B7F,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAiD,QAAA,gBACnB7F,OAAA;gBAAM4C,SAAS,EAAC,aAAa;gBAAAiD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCjG,OAAA;gBAAM4C,SAAS,EAAC,WAAW;gBAAAiD,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNjG,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAiD,QAAA,gBACnB7F,OAAA;gBAAM4C,SAAS,EAAC,aAAa;gBAAAiD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCjG,OAAA;gBAAM4C,SAAS,EAAC,WAAW;gBAAAiD,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNjG,OAAA;cAAK4C,SAAS,EAAC,MAAM;cAAAiD,QAAA,gBACnB7F,OAAA;gBAAM4C,SAAS,EAAC,aAAa;gBAAAiD,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCjG,OAAA;gBAAM4C,SAAS,EAAC,WAAW;gBAAAiD,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjG,OAAA;YAAK4C,SAAS,EAAC,iBAAiB;YAAAiD,QAAA,gBAC9B7F,OAAA;cACE4C,SAAS,EAAC,kBAAkB;cAC5BsD,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnBpE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;gBAChD,IAAI;kBACF,MAAM0B,QAAQ,GAAG,MAAMjE,kBAAkB,CAAC,CAAC;kBAC3CsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0B,QAAQ,CAAC;kBAElD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACrB,KAAK,IAAIqB,QAAQ,CAACyB,aAAa,KAAK,MAAM,IAAIzB,QAAQ,CAAC0B,MAAM,KAAK,QAAQ,EAAE;oBACpGrD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;oBAC5CqB,QAAQ,CAACzD,eAAe,CAAC8D,QAAQ,CAAC,CAAC;oBACnCpE,OAAO,CAAC6D,OAAO,CAAC,2CAA2C,CAAC;oBAC5D5C,SAAS,IAAIA,SAAS,CAAC,CAAC;oBACxBoF,UAAU,CAAC,MAAMrF,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;kBACnC,CAAC,MAAM;oBACLhB,OAAO,CAACqI,IAAI,CAAC,0EAA0E,CAAC;kBAC1F;gBACF,CAAC,CAAC,OAAOtF,KAAK,EAAE;kBACdN,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;kBAC7C/C,OAAO,CAAC+C,KAAK,CAAC,+BAA+B,CAAC;gBAChD;cACF,CAAE;cAAAyD,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETjG,OAAA;cAAQ4C,SAAS,EAAC,UAAU;cAACsD,OAAO,EAAEN,WAAY;cAAAC,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1F,EAAA,CA9kBIJ,iBAAiB;EAAA,QA0GJhB,WAAW,EACXC,WAAW;AAAA;AAAAyI,EAAA,GA3GxB1H,iBAAiB;AAglBvB,eAAeA,iBAAiB;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}