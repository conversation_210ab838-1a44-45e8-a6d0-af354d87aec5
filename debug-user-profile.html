<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #007BFF;
        }
        .field {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .field.valid {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .field.invalid {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .field.warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .btn {
            background-color: #007BFF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.danger {
            background-color: #dc3545;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 User Profile Debug Tool</h1>
            <p>Check if your profile is ready for payments</p>
        </div>

        <div class="section">
            <h3>📋 Profile Status</h3>
            <div id="profile-status">
                <p>Click "Check Profile" to analyze your profile data</p>
            </div>
        </div>

        <div class="section">
            <h3>🔧 Actions</h3>
            <button class="btn" onclick="checkProfile()">Check Profile</button>
            <button class="btn" onclick="testPayment()">Test Payment Validation</button>
            <button class="btn success" onclick="goToProfile()">Go to Profile Page</button>
        </div>

        <div class="section">
            <h3>📊 Validation Results</h3>
            <div id="validation-results">
                <p>No validation performed yet</p>
            </div>
        </div>

        <div class="log" id="debug-log">
            <strong>Debug Log:</strong><br>
            Ready to check profile...
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<br>[${timestamp}] ${message}`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('profile-status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showValidationResults(results) {
            const resultsDiv = document.getElementById('validation-results');
            resultsDiv.innerHTML = results;
        }

        async function checkProfile() {
            log('🔍 Checking user profile...');
            showStatus('Checking profile...', 'warning');

            try {
                // Get user data from localStorage (assuming it's stored there)
                const userStr = localStorage.getItem('user');
                if (!userStr) {
                    throw new Error('No user data found in localStorage. Please login first.');
                }

                const user = JSON.parse(userStr);
                log('👤 User data found in localStorage');
                
                analyzeUserData(user);

            } catch (error) {
                log(`❌ Error: ${error.message}`);
                showStatus(`Error: ${error.message}`, 'error');
                
                // Try to get user data from API
                log('🔄 Trying to fetch user data from API...');
                await fetchUserFromAPI();
            }
        }

        function analyzeUserData(user) {
            log('📊 Analyzing user data...');
            
            // Compute name (same logic as backend)
            let userName = user.name;
            if (!userName && user.firstName && user.lastName) {
                userName = `${user.firstName} ${user.lastName}`;
            } else if (!userName && user.firstName) {
                userName = user.firstName;
            }

            // Validate phone number (Tanzania format)
            const phoneRegex = /^0[67]\d{8}$/;
            const phoneValid = user.phoneNumber && phoneRegex.test(user.phoneNumber);

            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const emailValid = user.email && emailRegex.test(user.email);

            // Generate results
            let results = '<h4>Profile Analysis:</h4>';
            
            // Name check
            if (userName && userName.trim().length >= 2) {
                results += `<div class="field valid">✅ Name: "${userName}" (Valid)</div>`;
                log(`✅ Name is valid: "${userName}"`);
            } else {
                results += `<div class="field invalid">❌ Name: Missing or too short</div>`;
                log(`❌ Name issue: "${userName}"`);
            }

            // Phone check
            if (phoneValid) {
                results += `<div class="field valid">✅ Phone: ${user.phoneNumber} (Valid Tanzania format)</div>`;
                log(`✅ Phone is valid: ${user.phoneNumber}`);
            } else if (user.phoneNumber) {
                results += `<div class="field invalid">❌ Phone: ${user.phoneNumber} (Invalid format - must be 06xxxxxxxx or 07xxxxxxxx)</div>`;
                log(`❌ Phone format invalid: ${user.phoneNumber}`);
            } else {
                results += `<div class="field invalid">❌ Phone: Missing</div>`;
                log(`❌ Phone is missing`);
            }

            // Email check
            if (emailValid) {
                results += `<div class="field valid">✅ Email: ${user.email} (Valid)</div>`;
                log(`✅ Email is valid: ${user.email}`);
            } else if (user.email) {
                results += `<div class="field warning">⚠️ Email: ${user.email} (Invalid format, but auto-generated email will be used)</div>`;
                log(`⚠️ Email format invalid: ${user.email}`);
            } else {
                results += `<div class="field warning">⚠️ Email: Missing (Auto-generated email will be used)</div>`;
                log(`⚠️ Email is missing - will auto-generate`);
            }

            // Overall status
            const nameOk = userName && userName.trim().length >= 2;
            const phoneOk = phoneValid;
            
            if (nameOk && phoneOk) {
                showStatus('✅ Profile is ready for payments!', 'success');
                results += '<div class="field valid"><strong>✅ Overall: Ready for payments</strong></div>';
                log('🎉 Profile validation passed!');
            } else {
                const issues = [];
                if (!nameOk) issues.push('name');
                if (!phoneOk) issues.push('phone number');
                
                showStatus(`❌ Profile needs attention: ${issues.join(', ')}`, 'error');
                results += `<div class="field invalid"><strong>❌ Overall: Fix ${issues.join(' and ')} before payment</strong></div>`;
                log(`❌ Profile validation failed: ${issues.join(', ')}`);
            }

            showValidationResults(results);
        }

        async function fetchUserFromAPI() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    throw new Error('No authentication token found. Please login first.');
                }

                const response = await fetch('/api/users/get-user-info', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status}`);
                }

                const data = await response.json();
                if (data.success) {
                    log('✅ User data fetched from API');
                    analyzeUserData(data.data);
                } else {
                    throw new Error(data.message || 'Failed to fetch user data');
                }

            } catch (error) {
                log(`❌ API fetch error: ${error.message}`);
                showStatus(`Cannot fetch user data: ${error.message}`, 'error');
                showValidationResults('<p>❌ Unable to analyze profile. Please ensure you are logged in.</p>');
            }
        }

        async function testPayment() {
            log('🧪 Testing payment validation...');
            showStatus('Testing payment validation...', 'warning');

            try {
                const userStr = localStorage.getItem('user');
                if (!userStr) {
                    throw new Error('No user data found. Please login first.');
                }

                const user = JSON.parse(userStr);
                const token = localStorage.getItem('token');

                const testPaymentData = {
                    plan: {
                        _id: "test_plan_id",
                        title: "Test Plan",
                        discountedPrice: 1000,
                        duration: 1
                    },
                    userId: user._id,
                    userPhone: user.phoneNumber,
                    userEmail: user.email
                };

                log('📤 Sending test payment request...');

                const response = await fetch('/api/payment/create-invoice', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testPaymentData)
                });

                const data = await response.json();

                if (response.ok) {
                    log('✅ Payment validation passed!');
                    showStatus('✅ Payment validation successful!', 'success');
                    showValidationResults('<div class="field valid"><strong>✅ Payment system is ready to use!</strong></div>');
                } else {
                    log(`❌ Payment validation failed: ${data.message}`);
                    showStatus(`❌ Payment validation failed: ${data.message}`, 'error');
                    showValidationResults(`<div class="field invalid"><strong>❌ ${data.message}</strong><br>Error Type: ${data.errorType || 'Unknown'}</div>`);
                }

            } catch (error) {
                log(`❌ Test payment error: ${error.message}`);
                showStatus(`Test failed: ${error.message}`, 'error');
            }
        }

        function goToProfile() {
            log('🔄 Redirecting to profile page...');
            window.location.href = '/profile';
        }

        // Auto-check profile on page load
        window.addEventListener('load', () => {
            log('🚀 Page loaded, ready to check profile');
        });
    </script>
</body>
</html>
