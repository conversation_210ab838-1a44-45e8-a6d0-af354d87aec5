{"ast": null, "code": "import React,{useState,useEffect,useCallback,startTransition}from'react';import{useNavigate}from'react-router-dom';import{useDispatch,useSelector}from'react-redux';import{message}from'antd';import{Tb<PERSON>earch,Tb<PERSON><PERSON><PERSON>,TbClock,TbQuestionMark,TbTrophy,TbPlayerPlay,TbBrain,TbTarget,TbCheck,TbX,TbStar,TbHome,TbBolt,TbRefresh}from'react-icons/tb';import{getAllExams}from'../../../apicalls/exams';import{getAllReportsByUser}from'../../../apicalls/reports';import{HideLoading,ShowLoading}from'../../../redux/loaderSlice';import'./animations.css';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const Quiz=()=>{const[exams,setExams]=useState([]);const[filteredExams,setFilteredExams]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[selectedClass,setSelectedClass]=useState('');const[userResults,setUserResults]=useState({});const[loading,setLoading]=useState(true);const[refreshing,setRefreshing]=useState(false);const[lastRefresh,setLastRefresh]=useState(null);const navigate=useNavigate();const dispatch=useDispatch();const{user}=useSelector(state=>state.user);const getUserResults=useCallback(async()=>{try{if(!(user!==null&&user!==void 0&&user._id))return;const response=await getAllReportsByUser({userId:user._id});if(response.success){const resultsMap={};response.data.forEach(report=>{var _report$exam;const examId=(_report$exam=report.exam)===null||_report$exam===void 0?void 0:_report$exam._id;if(!examId||!report.result)return;// Extract data from the result object\nconst result=report.result;if(!resultsMap[examId]||new Date(report.createdAt)>new Date(resultsMap[examId].createdAt)){resultsMap[examId]={verdict:result.verdict,percentage:result.percentage,correctAnswers:result.correctAnswers,wrongAnswers:result.wrongAnswers,totalQuestions:result.totalQuestions,obtainedMarks:result.obtainedMarks,totalMarks:result.totalMarks,score:result.score,points:result.points,xpEarned:result.xpEarned||result.points||result.xpGained||0,timeTaken:report.timeTaken,completedAt:report.createdAt};}});setUserResults(resultsMap);}}catch(error){console.error('Error fetching user results:',error);}},[user===null||user===void 0?void 0:user._id]);// Define getExams function outside useEffect so it can be called from other functions\nconst getExams=useCallback(async function(){let isRefresh=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{// Safety check: ensure user exists before proceeding\nif(!user){console.log(\"User not loaded yet, skipping exam fetch\");return;}// Check cache first (unless refreshing)\nif(!isRefresh){const cachedExams=localStorage.getItem('user_exams_cache');const cacheTime=localStorage.getItem('user_exams_cache_time');const now=Date.now();// Use cache if less than 3 minutes old\nif(cachedExams&&cacheTime&&now-parseInt(cacheTime)<180000){const cached=JSON.parse(cachedExams);setExams(cached);setLastRefresh(new Date(parseInt(cacheTime)));if(user!==null&&user!==void 0&&user.class){setSelectedClass(String(user.class));}setLoading(false);return;}}if(isRefresh){setRefreshing(true);}else{dispatch(ShowLoading());}const response=await getAllExams();if(isRefresh){setRefreshing(false);}else{dispatch(HideLoading());}if(response.success){console.log('Raw exams from API:',response.data.length);console.log('User level:',user===null||user===void 0?void 0:user.level);// Filter exams by user's level with proper null checks\nconst userLevelExams=response.data.filter(exam=>{if(!exam.level||!user||!user.level)return false;return exam.level.toLowerCase()===user.level.toLowerCase();});console.log('User level exams after filtering:',userLevelExams.length);const sortedExams=userLevelExams.sort((a,b)=>new Date(b.createdAt)-new Date(a.createdAt));setExams(sortedExams);setLastRefresh(new Date());// Cache the exams data\nlocalStorage.setItem('user_exams_cache',JSON.stringify(sortedExams));localStorage.setItem('user_exams_cache_time',Date.now().toString());// Set default class filter to user's class\nif(user!==null&&user!==void 0&&user.class){setSelectedClass(String(user.class));}}else{message.error(response.message);}}catch(error){if(isRefresh){setRefreshing(false);}else{dispatch(HideLoading());}message.error(error.message);}finally{setLoading(false);}},[dispatch,user]);useEffect(()=>{getExams(false);// Initial load\ngetUserResults();},[getExams,getUserResults]);// Real-time updates for quiz completion and new exams\nuseEffect(()=>{// Listen for real-time updates from quiz completion\nconst handleRankingUpdate=()=>{console.log('🔄 Quiz listing - refreshing data after quiz completion...');getUserResults();// Refresh user results to show updated XP\n};// Listen for new exam creation events\nconst handleNewExam=()=>{console.log('🆕 New exam created - refreshing exam list...');if(user){getExams(true);// Use refresh mode\ngetUserResults();}};// Listen for window focus to refresh data when returning from quiz\nconst handleWindowFocus=()=>{console.log('🎯 Quiz listing - window focused, refreshing data...');getUserResults();// Also refresh exams list to show newly generated exams\nif(user){console.log('🔄 Refreshing exams list for new exams...');getExams(true);// Use refresh mode\n}};window.addEventListener('rankingUpdate',handleRankingUpdate);window.addEventListener('focus',handleWindowFocus);window.addEventListener('newExamCreated',handleNewExam);return()=>{window.removeEventListener('rankingUpdate',handleRankingUpdate);window.removeEventListener('focus',handleWindowFocus);window.removeEventListener('newExamCreated',handleNewExam);};},[]);// Periodic refresh to ensure quiz list stays up to date\nuseEffect(()=>{const refreshInterval=setInterval(()=>{if(user&&!loading&&!refreshing){console.log('🔄 Periodic refresh of quiz list...');getExams(true);// Use refresh mode\n}},5*60*1000);// Refresh every 5 minutes\nreturn()=>clearInterval(refreshInterval);},[user,loading,refreshing]);useEffect(()=>{console.log('Filtering exams:',{exams:exams.length,searchTerm,selectedClass});let filtered=exams;if(searchTerm){filtered=filtered.filter(exam=>{var _exam$name,_exam$subject;return((_exam$name=exam.name)===null||_exam$name===void 0?void 0:_exam$name.toLowerCase().includes(searchTerm.toLowerCase()))||((_exam$subject=exam.subject)===null||_exam$subject===void 0?void 0:_exam$subject.toLowerCase().includes(searchTerm.toLowerCase()));});}if(selectedClass){filtered=filtered.filter(exam=>String(exam.class)===String(selectedClass));}filtered.sort((a,b)=>new Date(b.createdAt)-new Date(a.createdAt));console.log('Filtered exams result:',filtered.length);setFilteredExams(filtered);},[exams,searchTerm,selectedClass]);const availableClasses=[...new Set(exams.map(e=>e.class).filter(Boolean))].sort();const handleQuizStart=quiz=>{if(!quiz||!quiz._id){message.error('Invalid quiz selected. Please try again.');return;}// Validate MongoDB ObjectId format (24 character hex string)\nconst objectIdRegex=/^[0-9a-fA-F]{24}$/;if(!objectIdRegex.test(quiz._id)){message.error('Invalid quiz ID format. Please try again.');return;}startTransition(()=>{navigate(\"/quiz/\".concat(quiz._id,\"/play\"));});};// Manual refresh function\nconst handleRefresh=async()=>{console.log('🔄 Manual refresh triggered...');if(refreshing||loading)return;// Prevent multiple simultaneous refreshes\ntry{if(user){await getExams(true);// Use refresh mode\nawait getUserResults();message.success('Quiz list refreshed successfully!');}}catch(error){message.error('Failed to refresh quiz list');}};const handleQuizView=quiz=>{if(!quiz||!quiz._id){message.error('Invalid quiz selected. Please try again.');return;}// Check if user has attempted this quiz\nconst userResult=userResults[quiz._id];if(!userResult){message.info('You need to attempt this quiz first to view results.');return;}startTransition(()=>{navigate(\"/quiz/\".concat(quiz._id,\"/result\"));});};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Loading quizzes...\"})]})});}return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center mb-8 sm:mb-12 opacity-100\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\",children:/*#__PURE__*/_jsx(TbBrain,{className:\"w-8 h-8 sm:w-10 sm:h-10 text-white\"})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",children:\"Challenge Your Brain, Beat the Rest\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\",children:\"Test your knowledge with our comprehensive quizzes designed for You!\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-green-500 rounded-full\"}),/*#__PURE__*/_jsxs(\"span\",{children:[filteredExams.length,\" Available Quizzes\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-blue-500 rounded-full\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Level: \",(user===null||user===void 0?void 0:user.level)||'All Levels']})]}),lastRefresh&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 text-xs text-gray-400\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-gray-400 rounded-full\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Updated: \",lastRefresh.toLocaleTimeString()]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"max-w-4xl mx-auto mb-8 sm:mb-12 opacity-100\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-4 sm:p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-3 sm:gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(TbSearch,{className:\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search quizzes by name or subject...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"sm:w-48 md:w-64\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(TbFilter,{className:\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"})}),/*#__PURE__*/_jsxs(\"select\",{value:selectedClass,onChange:e=>setSelectedClass(e.target.value),className:\"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Classes\"}),availableClasses.map(className=>/*#__PURE__*/_jsxs(\"option\",{value:className,children:[\"Class \",className]},className))]})]})}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleRefresh,disabled:loading||refreshing,className:\"flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95\",title:\"Refresh quiz list\",children:[/*#__PURE__*/_jsx(TbRefresh,{className:\"h-4 w-4 sm:h-5 sm:w-5 \".concat(loading||refreshing?'animate-spin':'')}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 hidden sm:inline text-sm sm:text-base\",children:refreshing?'Refreshing...':'Refresh'})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"opacity-100\",children:filteredExams.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-12 sm:py-16\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\",children:[/*#__PURE__*/_jsx(TbTarget,{className:\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\",children:\"No Quizzes Found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm sm:text-base\",children:searchTerm||selectedClass?\"Try adjusting your search or filter criteria.\":\"No quizzes are available for your level at the moment.\"})]})}):/*#__PURE__*/_jsx(\"div\",{className:\"quiz-grid\",children:filteredExams.map((quiz,index)=>/*#__PURE__*/_jsx(QuizCard,{quiz:quiz,userResult:userResults[quiz._id],showResults:true,onStart:handleQuizStart,onView:()=>handleQuizView(quiz),index:index},quiz._id))})})]})});};// Simple QuizCard component without Framer Motion\nconst QuizCard=_ref=>{var _quiz$questions;let{quiz,userResult,onStart,onView,index}=_ref;const formatTime=seconds=>{if(!seconds)return'N/A';const minutes=Math.floor(seconds/60);const remainingSeconds=seconds%60;return\"\".concat(minutes,\":\").concat(remainingSeconds.toString().padStart(2,'0'));};const formatCompletionTime=timeInSeconds=>{// Handle different possible time formats\nif(!timeInSeconds&&timeInSeconds!==0)return'0s';let totalSeconds=timeInSeconds;// If it's a string, try to parse it\nif(typeof timeInSeconds==='string'){totalSeconds=parseInt(timeInSeconds,10);}// If it's still not a valid number, return 0s\nif(isNaN(totalSeconds)||totalSeconds<0)return'0s';const minutes=Math.floor(totalSeconds/60);const seconds=totalSeconds%60;if(minutes>0){return\"\".concat(minutes,\"m \").concat(seconds,\"s\");}return\"\".concat(timeInSeconds,\"s\");};// Safety checks for quiz object\nif(!quiz||typeof quiz!=='object'){return/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"Invalid quiz data\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 transform hover:scale-105 opacity-100 relative flex flex-col\",style:{background:'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',border:userResult?userResult.verdict==='Pass'?'2px solid #10b981':'2px solid #ef4444':'2px solid #3b82f6',boxShadow:userResult?userResult.verdict==='Pass'?'0 8px 20px rgba(16, 185, 129, 0.3)':'0 8px 20px rgba(239, 68, 68, 0.3)':'0 8px 20px rgba(59, 130, 246, 0.3)',minHeight:window.innerWidth<=768?'240px':'320px',height:'auto'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-2 text-center\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mb-1 line-clamp-2\",style:{color:'#1f2937',textShadow:'0 1px 2px rgba(0,0,0,0.1)',lineHeight:'1.1',fontSize:window.innerWidth<=768?'14px':'16px'},children:typeof quiz.name==='string'?quiz.name:'Untitled Quiz'})}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-2 text-center\",children:userResult?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center gap-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\",style:{backgroundColor:userResult.verdict==='Pass'?'#10b981':'#ef4444',fontSize:window.innerWidth<=768?'9px':'10px'},children:userResult.verdict==='Pass'?'✅ PASSED':'❌ FAILED'}),/*#__PURE__*/_jsxs(\"div\",{className:\"px-2 py-1 rounded-full text-xs font-bold text-center shadow-md\",style:{backgroundColor:'#ffffff',color:'#1f2937',fontSize:window.innerWidth<=768?'9px':'10px'},children:[typeof userResult.percentage==='number'?userResult.percentage:0,\"%\"]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\",style:{backgroundColor:'#3b82f6',fontSize:window.innerWidth<=768?'9px':'10px'},children:\"\\uD83C\\uDD95 NOT ATTEMPTED\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-1 mb-2 justify-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\",style:{background:'linear-gradient(to right, #eff6ff, #e0e7ff)',borderColor:'#bfdbfe'},children:[/*#__PURE__*/_jsx(TbQuestionMark,{className:\"w-3 h-3\",style:{color:'#2563eb'}}),/*#__PURE__*/_jsx(\"span\",{className:\"font-bold\",style:{color:'#1e40af',fontSize:window.innerWidth<=768?'11px':'12px'},children:Array.isArray(quiz.questions)?quiz.questions.length:0})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\",style:{background:'linear-gradient(to right, #fdf4ff, #fce7f3)',borderColor:'#e9d5ff'},children:[/*#__PURE__*/_jsx(TbClock,{className:\"w-3 h-3\",style:{color:'#9333ea'}}),/*#__PURE__*/_jsx(\"span\",{className:\"font-bold\",style:{color:'#7c3aed',fontSize:window.innerWidth<=768?'11px':'12px'},children:\"3m\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center gap-1 flex-wrap mb-2\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",style:{background:'linear-gradient(to right, #4ade80, #3b82f6)',fontSize:window.innerWidth<=768?'8px':'10px'},children:[\"\\uD83D\\uDCD6\",typeof quiz.class==='string'||typeof quiz.class==='number'?quiz.class:'N/A']}),/*#__PURE__*/_jsxs(\"span\",{className:\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",style:{background:'linear-gradient(to right, #667eea, #764ba2)',fontSize:window.innerWidth<=768?'8px':'10px'},children:[\"\\uD83D\\uDCDA\",quiz.subject]}),quiz.category&&quiz.category!=='General'&&/*#__PURE__*/_jsxs(\"span\",{className:\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",style:{background:'linear-gradient(to right, #f97316, #ea580c)',fontSize:window.innerWidth<=768?'8px':'10px'},children:[\"\\uD83D\\uDCC2\",quiz.category]}),quiz.topic&&quiz.topic!=='General'&&/*#__PURE__*/_jsxs(\"span\",{className:\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",style:{background:'linear-gradient(to right, #10b981, #059669)',fontSize:window.innerWidth<=768?'8px':'10px'},children:[\"\\uD83D\\uDCD6\",quiz.topic]})]})]})}),userResult&&typeof userResult==='object'&&/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2 p-2 rounded-lg border shadow-md\",style:{background:userResult.verdict==='Pass'?'linear-gradient(to bottom right, #f0fdf4, #ecfdf5)':'linear-gradient(to bottom right, #fef2f2, #fdf2f8)',borderColor:userResult.verdict==='Pass'?'#86efac':'#fca5a5'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[userResult.verdict==='Pass'?/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\",style:{background:'linear-gradient(to right, #10b981, #059669)',borderColor:'#86efac'},children:/*#__PURE__*/_jsx(TbCheck,{className:\"w-6 h-6 font-bold\",style:{color:'#ffffff'}})}):/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\",style:{background:'linear-gradient(to right, #ef4444, #dc2626)',borderColor:'#fca5a5'},children:/*#__PURE__*/_jsx(TbX,{className:\"w-6 h-6 font-bold\",style:{color:'#ffffff'}})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-lg font-bold\",style:{color:'#1f2937'},children:\"\\uD83C\\uDFC6 Last Result\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",style:{color:'#6b7280'},children:new Date(userResult.completedAt||userResult.createdAt||Date.now()).toLocaleDateString()})]})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-3xl font-bold shadow-lg\",style:{color:userResult.verdict==='Pass'?'#059669':'#dc2626'},children:[typeof userResult.percentage==='number'?userResult.percentage:0,\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-1 justify-center flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\",style:{background:'linear-gradient(to right, #dcfce7, #fecaca)',borderColor:'#86efac'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1\",children:[/*#__PURE__*/_jsx(TbCheck,{className:\"w-3 h-3\",style:{color:'#16a34a'}}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-bold\",style:{color:'#15803d'},children:typeof userResult.correctAnswers==='number'?userResult.correctAnswers:0})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1\",children:[/*#__PURE__*/_jsx(TbX,{className:\"w-3 h-3\",style:{color:'#dc2626'}}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-bold\",style:{color:'#b91c1c'},children:(((_quiz$questions=quiz.questions)===null||_quiz$questions===void 0?void 0:_quiz$questions.length)||0)-(typeof userResult.correctAnswers==='number'?userResult.correctAnswers:0)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\",style:{background:'linear-gradient(to bottom right, #fef3c7, #fed7aa)',borderColor:'#fde047'},children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"\\u2B50\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-bold\",style:{color:'#92400e'},children:userResult.xpEarned||userResult.points||0})]}),userResult.timeTaken&&userResult.timeTaken>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\",style:{background:'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',borderColor:'#c4b5fd'},children:[/*#__PURE__*/_jsx(TbClock,{className:\"w-3 h-3\",style:{color:'#9333ea'}}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-bold\",style:{color:'#7c3aed'},children:formatCompletionTime(userResult.timeTaken)})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 mt-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onStart(quiz),className:\"flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95 text-white\",style:{background:userResult?'linear-gradient(to right, #f97316, #ef4444)':'linear-gradient(to right, #3b82f6, #8b5cf6)',fontSize:'13px',minHeight:'36px'},children:[/*#__PURE__*/_jsx(TbPlayerPlay,{className:\"w-3 h-3\"}),userResult?'🔄 Retake Quiz':'🚀 Start Quiz']}),userResult&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>onView(quiz),className:\"px-3 py-2 rounded-lg transition-all duration-200 font-bold transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg text-white\",style:{background:'linear-gradient(to right, #fbbf24, #f97316)',fontSize:'13px',minHeight:'36px'},title:\"View Results\",children:/*#__PURE__*/_jsx(TbTrophy,{className:\"w-3 h-3\"})})]})]});};export default Quiz;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useNavigate", "useDispatch", "useSelector", "message", "TbSearch", "Tb<PERSON><PERSON>er", "TbClock", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "TbTarget", "TbCheck", "TbX", "TbStar", "TbHome", "TbBolt", "TbRefresh", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "jsx", "_jsx", "jsxs", "_jsxs", "Quiz", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "refreshing", "setRefreshing", "lastRefresh", "setLastRefresh", "navigate", "dispatch", "user", "state", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "for<PERSON>ach", "report", "_report$exam", "examId", "exam", "result", "Date", "createdAt", "verdict", "percentage", "correctAnswers", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "score", "points", "xpEarned", "xpGained", "timeTaken", "completedAt", "error", "console", "getExams", "isRefresh", "arguments", "length", "undefined", "log", "cachedExams", "localStorage", "getItem", "cacheTime", "now", "parseInt", "cached", "JSON", "parse", "class", "String", "level", "userLevelExams", "filter", "toLowerCase", "sortedExams", "sort", "a", "b", "setItem", "stringify", "toString", "handleRankingUpdate", "handleNewExam", "handleWindowFocus", "window", "addEventListener", "removeEventListener", "refreshInterval", "setInterval", "clearInterval", "filtered", "_exam$name", "_exam$subject", "name", "includes", "subject", "availableClasses", "Set", "map", "e", "Boolean", "handleQuizStart", "quiz", "objectIdRegex", "test", "concat", "handleRefresh", "handleQuizView", "userResult", "info", "className", "children", "toLocaleTimeString", "type", "placeholder", "value", "onChange", "target", "onClick", "disabled", "title", "index", "QuizCard", "showResults", "onStart", "onView", "_ref", "_quiz$questions", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "padStart", "formatCompletionTime", "timeInSeconds", "totalSeconds", "isNaN", "style", "background", "border", "boxShadow", "minHeight", "innerWidth", "height", "color", "textShadow", "lineHeight", "fontSize", "backgroundColor", "borderColor", "Array", "isArray", "questions", "category", "topic", "toLocaleDateString"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { message } from 'antd';\r\nimport {\r\n  Tb<PERSON>earch,\r\n  Tb<PERSON><PERSON><PERSON>,\r\n  TbClock,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX,\r\n  TbStar,\r\n  TbHome,\r\n  TbBolt,\r\n  TbRefresh\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport './animations.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedClass, setSelectedClass] = useState('');\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [lastRefresh, setLastRefresh] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const getUserResults = useCallback(async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId || !report.result) return;\r\n\r\n          // Extract data from the result object\r\n          const result = report.result;\r\n\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: result.verdict,\r\n              percentage: result.percentage,\r\n              correctAnswers: result.correctAnswers,\r\n              wrongAnswers: result.wrongAnswers,\r\n              totalQuestions: result.totalQuestions,\r\n              obtainedMarks: result.obtainedMarks,\r\n              totalMarks: result.totalMarks,\r\n              score: result.score,\r\n              points: result.points,\r\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  }, [user?._id]);\r\n\r\n  // Define getExams function outside useEffect so it can be called from other functions\r\n  const getExams = useCallback(async (isRefresh = false) => {\r\n      try {\r\n        // Safety check: ensure user exists before proceeding\r\n        if (!user) {\r\n          console.log(\"User not loaded yet, skipping exam fetch\");\r\n          return;\r\n        }\r\n\r\n        // Check cache first (unless refreshing)\r\n        if (!isRefresh) {\r\n          const cachedExams = localStorage.getItem('user_exams_cache');\r\n          const cacheTime = localStorage.getItem('user_exams_cache_time');\r\n          const now = Date.now();\r\n\r\n          // Use cache if less than 3 minutes old\r\n          if (cachedExams && cacheTime && (now - parseInt(cacheTime)) < 180000) {\r\n            const cached = JSON.parse(cachedExams);\r\n            setExams(cached);\r\n            setLastRefresh(new Date(parseInt(cacheTime)));\r\n            if (user?.class) {\r\n              setSelectedClass(String(user.class));\r\n            }\r\n            setLoading(false);\r\n            return;\r\n          }\r\n        }\r\n\r\n        if (isRefresh) {\r\n          setRefreshing(true);\r\n        } else {\r\n          dispatch(ShowLoading());\r\n        }\r\n\r\n        const response = await getAllExams();\r\n\r\n        if (isRefresh) {\r\n          setRefreshing(false);\r\n        } else {\r\n          dispatch(HideLoading());\r\n        }\r\n\r\n        if (response.success) {\r\n          console.log('Raw exams from API:', response.data.length);\r\n          console.log('User level:', user?.level);\r\n\r\n          // Filter exams by user's level with proper null checks\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          console.log('User level exams after filtering:', userLevelExams.length);\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n          setLastRefresh(new Date());\r\n\r\n          // Cache the exams data\r\n          localStorage.setItem('user_exams_cache', JSON.stringify(sortedExams));\r\n          localStorage.setItem('user_exams_cache_time', Date.now().toString());\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        if (isRefresh) {\r\n          setRefreshing(false);\r\n        } else {\r\n          dispatch(HideLoading());\r\n        }\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n  }, [dispatch, user]);\r\n\r\n  useEffect(() => {\r\n    getExams(false); // Initial load\r\n    getUserResults();\r\n  }, [getExams, getUserResults]);\r\n\r\n  // Real-time updates for quiz completion and new exams\r\n  useEffect(() => {\r\n    // Listen for real-time updates from quiz completion\r\n    const handleRankingUpdate = () => {\r\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\r\n      getUserResults(); // Refresh user results to show updated XP\r\n    };\r\n\r\n    // Listen for new exam creation events\r\n    const handleNewExam = () => {\r\n      console.log('🆕 New exam created - refreshing exam list...');\r\n      if (user) {\r\n        getExams(true); // Use refresh mode\r\n        getUserResults();\r\n      }\r\n    };\r\n\r\n    // Listen for window focus to refresh data when returning from quiz\r\n    const handleWindowFocus = () => {\r\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\r\n      getUserResults();\r\n      // Also refresh exams list to show newly generated exams\r\n      if (user) {\r\n        console.log('🔄 Refreshing exams list for new exams...');\r\n        getExams(true); // Use refresh mode\r\n      }\r\n    };\r\n\r\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\r\n    window.addEventListener('focus', handleWindowFocus);\r\n    window.addEventListener('newExamCreated', handleNewExam);\r\n\r\n    return () => {\r\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\r\n      window.removeEventListener('focus', handleWindowFocus);\r\n      window.removeEventListener('newExamCreated', handleNewExam);\r\n    };\r\n  }, []);\r\n\r\n  // Periodic refresh to ensure quiz list stays up to date\r\n  useEffect(() => {\r\n    const refreshInterval = setInterval(() => {\r\n      if (user && !loading && !refreshing) {\r\n        console.log('🔄 Periodic refresh of quiz list...');\r\n        getExams(true); // Use refresh mode\r\n      }\r\n    }, 5 * 60 * 1000); // Refresh every 5 minutes\r\n\r\n    return () => clearInterval(refreshInterval);\r\n  }, [user, loading, refreshing]);\r\n\r\n  useEffect(() => {\r\n    console.log('Filtering exams:', { exams: exams.length, searchTerm, selectedClass });\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    console.log('Filtered exams result:', filtered.length);\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n\r\n    // Validate MongoDB ObjectId format (24 character hex string)\r\n    const objectIdRegex = /^[0-9a-fA-F]{24}$/;\r\n    if (!objectIdRegex.test(quiz._id)) {\r\n      message.error('Invalid quiz ID format. Please try again.');\r\n      return;\r\n    }\r\n\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/play`);\r\n    });\r\n  };\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = async () => {\r\n    console.log('🔄 Manual refresh triggered...');\r\n    if (refreshing || loading) return; // Prevent multiple simultaneous refreshes\r\n\r\n    try {\r\n      if (user) {\r\n        await getExams(true); // Use refresh mode\r\n        await getUserResults();\r\n        message.success('Quiz list refreshed successfully!');\r\n      }\r\n    } catch (error) {\r\n      message.error('Failed to refresh quiz list');\r\n    }\r\n  };\r\n\r\n  const handleQuizView = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    // Check if user has attempted this quiz\r\n    const userResult = userResults[quiz._id];\r\n    if (!userResult) {\r\n      message.info('You need to attempt this quiz first to view results.');\r\n      return;\r\n    }\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/result`);\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading quizzes...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Hero Section */}\r\n        <div className=\"text-center mb-8 sm:mb-12 opacity-100\">\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\">\r\n            <TbBrain className=\"w-8 h-8 sm:w-10 sm:h-10 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Challenge Your Brain, Beat the Rest\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\">\r\n            Test your knowledge with our comprehensive quizzes designed for You!\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              <span>{filteredExams.length} Available Quizzes</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>Level: {user?.level || 'All Levels'}</span>\r\n            </div>\r\n            {lastRefresh && (\r\n              <div className=\"flex items-center gap-2 text-xs text-gray-400\">\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full\"></div>\r\n                <span>Updated: {lastRefresh.toLocaleTimeString()}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter */}\r\n        <div className=\"max-w-4xl mx-auto mb-8 sm:mb-12 opacity-100\">\r\n          <div className=\"bg-white rounded-2xl shadow-lg p-4 sm:p-6\">\r\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by name or subject...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\r\n                />\r\n              </div>\r\n              <div className=\"sm:w-48 md:w-64\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => setSelectedClass(e.target.value)}\r\n                    className=\"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>Class {className}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Refresh Button */}\r\n              <button\r\n                onClick={handleRefresh}\r\n                disabled={loading || refreshing}\r\n                className=\"flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95\"\r\n                title=\"Refresh quiz list\"\r\n              >\r\n                <TbRefresh className={`h-4 w-4 sm:h-5 sm:w-5 ${(loading || refreshing) ? 'animate-spin' : ''}`} />\r\n                <span className=\"ml-2 hidden sm:inline text-sm sm:text-base\">\r\n                  {refreshing ? 'Refreshing...' : 'Refresh'}\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quiz Grid */}\r\n        <div className=\"opacity-100\">\r\n\r\n\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-12 sm:py-16\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 text-sm sm:text-base\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"quiz-grid\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <QuizCard\r\n                  key={quiz._id}\r\n                  quiz={quiz}\r\n                  userResult={userResults[quiz._id]}\r\n                  showResults={true}\r\n                  onStart={handleQuizStart}\r\n                  onView={() => handleQuizView(quiz)}\r\n                  index={index}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Simple QuizCard component without Framer Motion\r\nconst QuizCard = ({ quiz, userResult, onStart, onView, index }) => {\r\n  const formatTime = (seconds) => {\r\n    if (!seconds) return 'N/A';\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const formatCompletionTime = (timeInSeconds) => {\r\n    // Handle different possible time formats\r\n    if (!timeInSeconds && timeInSeconds !== 0) return '0s';\r\n\r\n    let totalSeconds = timeInSeconds;\r\n\r\n    // If it's a string, try to parse it\r\n    if (typeof timeInSeconds === 'string') {\r\n      totalSeconds = parseInt(timeInSeconds, 10);\r\n    }\r\n\r\n    // If it's still not a valid number, return 0s\r\n    if (isNaN(totalSeconds) || totalSeconds < 0) return '0s';\r\n\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n\r\n    if (minutes > 0) {\r\n      return `${minutes}m ${seconds}s`;\r\n    }\r\n    return `${timeInSeconds}s`;\r\n  };\r\n\r\n  // Safety checks for quiz object\r\n  if (!quiz || typeof quiz !== 'object') {\r\n    return (\r\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\r\n        <p className=\"text-gray-500\">Invalid quiz data</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 transform hover:scale-105 opacity-100 relative flex flex-col\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',\r\n        border: userResult\r\n          ? (userResult.verdict === 'Pass' ? '2px solid #10b981' : '2px solid #ef4444')\r\n          : '2px solid #3b82f6',\r\n        boxShadow: userResult\r\n          ? (userResult.verdict === 'Pass'\r\n              ? '0 8px 20px rgba(16, 185, 129, 0.3)'\r\n              : '0 8px 20px rgba(239, 68, 68, 0.3)')\r\n          : '0 8px 20px rgba(59, 130, 246, 0.3)',\r\n        minHeight: window.innerWidth <= 768 ? '240px' : '320px',\r\n        height: 'auto'\r\n      }}\r\n    >\r\n      {/* Quiz Title - At Top */}\r\n      <div className=\"mb-2 text-center\">\r\n        <h3\r\n          className=\"font-bold mb-1 line-clamp-2\"\r\n          style={{\r\n            color: '#1f2937',\r\n            textShadow: '0 1px 2px rgba(0,0,0,0.1)',\r\n            lineHeight: '1.1',\r\n            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\r\n          }}\r\n        >\r\n          {typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'}\r\n        </h3>\r\n      </div>\r\n\r\n      {/* Status Tags - Centered */}\r\n      <div className=\"mb-2 text-center\">\r\n        {userResult ? (\r\n          <div className=\"flex items-center justify-center gap-1\">\r\n            <div\r\n              className=\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\"\r\n              style={{\r\n                backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',\r\n                fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n              }}\r\n            >\r\n              {userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'}\r\n            </div>\r\n            <div\r\n              className=\"px-2 py-1 rounded-full text-xs font-bold text-center shadow-md\"\r\n              style={{\r\n                backgroundColor: '#ffffff',\r\n                color: '#1f2937',\r\n                fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div\r\n            className=\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\"\r\n            style={{\r\n              backgroundColor: '#3b82f6',\r\n              fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n            }}\r\n          >\r\n            🆕 NOT ATTEMPTED\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"text-center mb-6\">\r\n        <div className=\"flex-1\">\r\n\r\n          {/* Questions and Duration - Horizontal */}\r\n          <div className=\"flex gap-1 mb-2 justify-center\">\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #eff6ff, #e0e7ff)',\r\n                borderColor: '#bfdbfe'\r\n              }}\r\n            >\r\n              <TbQuestionMark className=\"w-3 h-3\" style={{ color: '#2563eb' }} />\r\n              <span\r\n                className=\"font-bold\"\r\n                style={{\r\n                  color: '#1e40af',\r\n                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'\r\n                }}\r\n              >\r\n                {Array.isArray(quiz.questions) ? quiz.questions.length : 0}\r\n              </span>\r\n            </div>\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #fdf4ff, #fce7f3)',\r\n                borderColor: '#e9d5ff'\r\n              }}\r\n            >\r\n              <TbClock className=\"w-3 h-3\" style={{ color: '#9333ea' }} />\r\n              <span\r\n                className=\"font-bold\"\r\n                style={{\r\n                  color: '#7c3aed',\r\n                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'\r\n                }}\r\n              >\r\n                3m\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n\r\n\r\n          <div className=\"flex items-center justify-center gap-1 flex-wrap mb-2\">\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #4ade80, #3b82f6)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📖{typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.class : 'N/A'}\r\n            </span>\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #667eea, #764ba2)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📚{quiz.subject}\r\n            </span>\r\n            {/* Category Tag - Only show if not General */}\r\n            {quiz.category && quiz.category !== 'General' && (\r\n              <span\r\n                className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n                style={{\r\n                  background: 'linear-gradient(to right, #f97316, #ea580c)',\r\n                  fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n                }}\r\n              >\r\n                📂{quiz.category}\r\n              </span>\r\n            )}\r\n            {/* Topic Tag - Only show if not General */}\r\n            {quiz.topic && quiz.topic !== 'General' && (\r\n              <span\r\n                className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n                style={{\r\n                  background: 'linear-gradient(to right, #10b981, #059669)',\r\n                  fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n                }}\r\n              >\r\n                📖{quiz.topic}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      {userResult && typeof userResult === 'object' && (\r\n        <div\r\n          className=\"mb-2 p-2 rounded-lg border shadow-md\"\r\n          style={{\r\n            background: userResult.verdict === 'Pass'\r\n              ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5)'\r\n              : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8)',\r\n            borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'\r\n          }}\r\n        >\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              {userResult.verdict === 'Pass' ? (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #10b981, #059669)',\r\n                    borderColor: '#86efac'\r\n                  }}\r\n                >\r\n                  <TbCheck className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #ef4444, #dc2626)',\r\n                    borderColor: '#fca5a5'\r\n                  }}\r\n                >\r\n                  <TbX className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              )}\r\n              <div>\r\n                <span className=\"text-lg font-bold\" style={{ color: '#1f2937' }}>🏆 Last Result</span>\r\n                <div className=\"text-sm\" style={{ color: '#6b7280' }}>\r\n                  {new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span\r\n              className=\"text-3xl font-bold shadow-lg\"\r\n              style={{\r\n                color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </span>\r\n          </div>\r\n\r\n          {/* Horizontal Layout for Results */}\r\n          <div className=\"flex gap-1 justify-center flex-wrap\">\r\n            {/* Correct/Wrong - Horizontal */}\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #dcfce7, #fecaca)',\r\n                borderColor: '#86efac'\r\n              }}\r\n            >\r\n              <div className=\"flex items-center gap-1\">\r\n                <TbCheck className=\"w-3 h-3\" style={{ color: '#16a34a' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#15803d' }}>\r\n                  {typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-center gap-1\">\r\n                <TbX className=\"w-3 h-3\" style={{ color: '#dc2626' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#b91c1c' }}>\r\n                  {(quiz.questions?.length || 0) - (typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0)}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* XP */}\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',\r\n                borderColor: '#fde047'\r\n              }}\r\n            >\r\n              <span className=\"text-sm\">⭐</span>\r\n              <span className=\"text-sm font-bold\" style={{ color: '#92400e' }}>\r\n                {userResult.xpEarned || userResult.points || 0}\r\n              </span>\r\n            </div>\r\n\r\n            {/* Time - Horizontal if available */}\r\n            {userResult.timeTaken && userResult.timeTaken > 0 && (\r\n              <div\r\n                className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n                style={{\r\n                  background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',\r\n                  borderColor: '#c4b5fd'\r\n                }}\r\n              >\r\n                <TbClock className=\"w-3 h-3\" style={{ color: '#9333ea' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#7c3aed' }}>\r\n                  {formatCompletionTime(userResult.timeTaken)}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Spacer to push buttons to bottom */}\r\n      <div className=\"flex-1\"></div>\r\n\r\n      <div className=\"flex gap-2 mt-3\">\r\n        {/* Main Action Button - Bigger for retake */}\r\n        <button\r\n          onClick={() => onStart(quiz)}\r\n          className=\"flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95 text-white\"\r\n          style={{\r\n            background: userResult\r\n              ? 'linear-gradient(to right, #f97316, #ef4444)'\r\n              : 'linear-gradient(to right, #3b82f6, #8b5cf6)',\r\n            fontSize: '13px',\r\n            minHeight: '36px'\r\n          }}\r\n        >\r\n          <TbPlayerPlay className=\"w-3 h-3\" />\r\n          {userResult ? '🔄 Retake Quiz' : '🚀 Start Quiz'}\r\n        </button>\r\n\r\n        {/* Small Trophy Button - Only show when there are results */}\r\n        {userResult && (\r\n          <button\r\n            onClick={() => onView(quiz)}\r\n            className=\"px-3 py-2 rounded-lg transition-all duration-200 font-bold transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg text-white\"\r\n            style={{\r\n              background: 'linear-gradient(to right, #fbbf24, #f97316)',\r\n              fontSize: '13px',\r\n              minHeight: '36px'\r\n            }}\r\n            title=\"View Results\"\r\n          >\r\n            <TbTrophy className=\"w-3 h-3\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,eAAe,KAAQ,OAAO,CAChF,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,OAAO,KAAQ,MAAM,CAC9B,OACEC,QAAQ,CACRC,QAAQ,CACRC,OAAO,CACPC,cAAc,CACdC,QAAQ,CACRC,YAAY,CACZC,OAAO,CACPC,QAAQ,CACRC,OAAO,CACPC,GAAG,CACHC,MAAM,CACNC,MAAM,CACNC,MAAM,CACNC,SAAS,KACJ,gBAAgB,CACvB,OAASC,WAAW,KAAQ,yBAAyB,CACrD,OAASC,mBAAmB,KAAQ,2BAA2B,CAC/D,OAASC,WAAW,CAAEC,WAAW,KAAQ,4BAA4B,CACrE,MAAO,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CACjB,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACiC,aAAa,CAAEC,gBAAgB,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmC,UAAU,CAAEC,aAAa,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACuC,WAAW,CAAEC,cAAc,CAAC,CAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAClD,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC2C,UAAU,CAAEC,aAAa,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC6C,WAAW,CAAEC,cAAc,CAAC,CAAG9C,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAA+C,QAAQ,CAAG3C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4C,QAAQ,CAAG3C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE4C,IAAK,CAAC,CAAG3C,WAAW,CAAE4C,KAAK,EAAKA,KAAK,CAACD,IAAI,CAAC,CAEnD,KAAM,CAAAE,cAAc,CAAGjD,WAAW,CAAC,SAAY,CAC7C,GAAI,CACF,GAAI,EAAC+C,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,GAAG,EAAE,OAEhB,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA9B,mBAAmB,CAAC,CAAE+B,MAAM,CAAEL,IAAI,CAACG,GAAI,CAAC,CAAC,CAEhE,GAAIC,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAC,UAAU,CAAG,CAAC,CAAC,CACrBH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAACC,MAAM,EAAI,KAAAC,YAAA,CAC9B,KAAM,CAAAC,MAAM,EAAAD,YAAA,CAAGD,MAAM,CAACG,IAAI,UAAAF,YAAA,iBAAXA,YAAA,CAAaR,GAAG,CAC/B,GAAI,CAACS,MAAM,EAAI,CAACF,MAAM,CAACI,MAAM,CAAE,OAE/B;AACA,KAAM,CAAAA,MAAM,CAAGJ,MAAM,CAACI,MAAM,CAE5B,GAAI,CAACP,UAAU,CAACK,MAAM,CAAC,EAAI,GAAI,CAAAG,IAAI,CAACL,MAAM,CAACM,SAAS,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACR,UAAU,CAACK,MAAM,CAAC,CAACI,SAAS,CAAC,CAAE,CAC9FT,UAAU,CAACK,MAAM,CAAC,CAAG,CACnBK,OAAO,CAAEH,MAAM,CAACG,OAAO,CACvBC,UAAU,CAAEJ,MAAM,CAACI,UAAU,CAC7BC,cAAc,CAAEL,MAAM,CAACK,cAAc,CACrCC,YAAY,CAAEN,MAAM,CAACM,YAAY,CACjCC,cAAc,CAAEP,MAAM,CAACO,cAAc,CACrCC,aAAa,CAAER,MAAM,CAACQ,aAAa,CACnCC,UAAU,CAAET,MAAM,CAACS,UAAU,CAC7BC,KAAK,CAAEV,MAAM,CAACU,KAAK,CACnBC,MAAM,CAAEX,MAAM,CAACW,MAAM,CACrBC,QAAQ,CAAEZ,MAAM,CAACY,QAAQ,EAAIZ,MAAM,CAACW,MAAM,EAAIX,MAAM,CAACa,QAAQ,EAAI,CAAC,CAClEC,SAAS,CAAElB,MAAM,CAACkB,SAAS,CAC3BC,WAAW,CAAEnB,MAAM,CAACM,SACtB,CAAC,CACH,CACF,CAAC,CAAC,CACFzB,cAAc,CAACgB,UAAU,CAAC,CAC5B,CACF,CAAE,MAAOuB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAAE,CAAC9B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEG,GAAG,CAAC,CAAC,CAEf;AACA,KAAM,CAAA6B,QAAQ,CAAG/E,WAAW,CAAC,gBAA6B,IAAtB,CAAAgF,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACjD,GAAI,CACF;AACA,GAAI,CAAClC,IAAI,CAAE,CACT+B,OAAO,CAACM,GAAG,CAAC,0CAA0C,CAAC,CACvD,OACF,CAEA;AACA,GAAI,CAACJ,SAAS,CAAE,CACd,KAAM,CAAAK,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,CAC5D,KAAM,CAAAC,SAAS,CAAGF,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAC/D,KAAM,CAAAE,GAAG,CAAG3B,IAAI,CAAC2B,GAAG,CAAC,CAAC,CAEtB;AACA,GAAIJ,WAAW,EAAIG,SAAS,EAAKC,GAAG,CAAGC,QAAQ,CAACF,SAAS,CAAC,CAAI,MAAM,CAAE,CACpE,KAAM,CAAAG,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACR,WAAW,CAAC,CACtCvD,QAAQ,CAAC6D,MAAM,CAAC,CAChB/C,cAAc,CAAC,GAAI,CAAAkB,IAAI,CAAC4B,QAAQ,CAACF,SAAS,CAAC,CAAC,CAAC,CAC7C,GAAIzC,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAE+C,KAAK,CAAE,CACf1D,gBAAgB,CAAC2D,MAAM,CAAChD,IAAI,CAAC+C,KAAK,CAAC,CAAC,CACtC,CACAtD,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACF,CAEA,GAAIwC,SAAS,CAAE,CACbtC,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACLI,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC,CACzB,CAEA,KAAM,CAAA4B,QAAQ,CAAG,KAAM,CAAA/B,WAAW,CAAC,CAAC,CAEpC,GAAI4D,SAAS,CAAE,CACbtC,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,IAAM,CACLI,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC,CACzB,CAEA,GAAI6B,QAAQ,CAACE,OAAO,CAAE,CACpByB,OAAO,CAACM,GAAG,CAAC,qBAAqB,CAAEjC,QAAQ,CAACI,IAAI,CAAC2B,MAAM,CAAC,CACxDJ,OAAO,CAACM,GAAG,CAAC,aAAa,CAAErC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiD,KAAK,CAAC,CAEvC;AACA,KAAM,CAAAC,cAAc,CAAG9C,QAAQ,CAACI,IAAI,CAAC2C,MAAM,CAACtC,IAAI,EAAI,CAClD,GAAI,CAACA,IAAI,CAACoC,KAAK,EAAI,CAACjD,IAAI,EAAI,CAACA,IAAI,CAACiD,KAAK,CAAE,MAAO,MAAK,CACrD,MAAO,CAAApC,IAAI,CAACoC,KAAK,CAACG,WAAW,CAAC,CAAC,GAAKpD,IAAI,CAACiD,KAAK,CAACG,WAAW,CAAC,CAAC,CAC9D,CAAC,CAAC,CAEFrB,OAAO,CAACM,GAAG,CAAC,mCAAmC,CAAEa,cAAc,CAACf,MAAM,CAAC,CACvE,KAAM,CAAAkB,WAAW,CAAGH,cAAc,CAACI,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAAzC,IAAI,CAACyC,CAAC,CAACxC,SAAS,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACwC,CAAC,CAACvC,SAAS,CAAC,CAAC,CAChGjC,QAAQ,CAACsE,WAAW,CAAC,CACrBxD,cAAc,CAAC,GAAI,CAAAkB,IAAI,CAAC,CAAC,CAAC,CAE1B;AACAwB,YAAY,CAACkB,OAAO,CAAC,kBAAkB,CAAEZ,IAAI,CAACa,SAAS,CAACL,WAAW,CAAC,CAAC,CACrEd,YAAY,CAACkB,OAAO,CAAC,uBAAuB,CAAE1C,IAAI,CAAC2B,GAAG,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC,CAAC,CAEpE;AACA,GAAI3D,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAE+C,KAAK,CAAE,CACf1D,gBAAgB,CAAC2D,MAAM,CAAChD,IAAI,CAAC+C,KAAK,CAAC,CAAC,CACtC,CACF,CAAC,IAAM,CACLzF,OAAO,CAACwE,KAAK,CAAC1B,QAAQ,CAAC9C,OAAO,CAAC,CACjC,CACF,CAAE,MAAOwE,KAAK,CAAE,CACd,GAAIG,SAAS,CAAE,CACbtC,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,IAAM,CACLI,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC,CACzB,CACAjB,OAAO,CAACwE,KAAK,CAACA,KAAK,CAACxE,OAAO,CAAC,CAC9B,CAAC,OAAS,CACRmC,UAAU,CAAC,KAAK,CAAC,CACnB,CACJ,CAAC,CAAE,CAACM,QAAQ,CAAEC,IAAI,CAAC,CAAC,CAEpBhD,SAAS,CAAC,IAAM,CACdgF,QAAQ,CAAC,KAAK,CAAC,CAAE;AACjB9B,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAAC8B,QAAQ,CAAE9B,cAAc,CAAC,CAAC,CAE9B;AACAlD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA4G,mBAAmB,CAAGA,CAAA,GAAM,CAChC7B,OAAO,CAACM,GAAG,CAAC,4DAA4D,CAAC,CACzEnC,cAAc,CAAC,CAAC,CAAE;AACpB,CAAC,CAED;AACA,KAAM,CAAA2D,aAAa,CAAGA,CAAA,GAAM,CAC1B9B,OAAO,CAACM,GAAG,CAAC,+CAA+C,CAAC,CAC5D,GAAIrC,IAAI,CAAE,CACRgC,QAAQ,CAAC,IAAI,CAAC,CAAE;AAChB9B,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAED;AACA,KAAM,CAAA4D,iBAAiB,CAAGA,CAAA,GAAM,CAC9B/B,OAAO,CAACM,GAAG,CAAC,sDAAsD,CAAC,CACnEnC,cAAc,CAAC,CAAC,CAChB;AACA,GAAIF,IAAI,CAAE,CACR+B,OAAO,CAACM,GAAG,CAAC,2CAA2C,CAAC,CACxDL,QAAQ,CAAC,IAAI,CAAC,CAAE;AAClB,CACF,CAAC,CAED+B,MAAM,CAACC,gBAAgB,CAAC,eAAe,CAAEJ,mBAAmB,CAAC,CAC7DG,MAAM,CAACC,gBAAgB,CAAC,OAAO,CAAEF,iBAAiB,CAAC,CACnDC,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,CAAEH,aAAa,CAAC,CAExD,MAAO,IAAM,CACXE,MAAM,CAACE,mBAAmB,CAAC,eAAe,CAAEL,mBAAmB,CAAC,CAChEG,MAAM,CAACE,mBAAmB,CAAC,OAAO,CAAEH,iBAAiB,CAAC,CACtDC,MAAM,CAACE,mBAAmB,CAAC,gBAAgB,CAAEJ,aAAa,CAAC,CAC7D,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA7G,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkH,eAAe,CAAGC,WAAW,CAAC,IAAM,CACxC,GAAInE,IAAI,EAAI,CAACR,OAAO,EAAI,CAACE,UAAU,CAAE,CACnCqC,OAAO,CAACM,GAAG,CAAC,qCAAqC,CAAC,CAClDL,QAAQ,CAAC,IAAI,CAAC,CAAE;AAClB,CACF,CAAC,CAAE,CAAC,CAAG,EAAE,CAAG,IAAI,CAAC,CAAE;AAEnB,MAAO,IAAMoC,aAAa,CAACF,eAAe,CAAC,CAC7C,CAAC,CAAE,CAAClE,IAAI,CAAER,OAAO,CAAEE,UAAU,CAAC,CAAC,CAE/B1C,SAAS,CAAC,IAAM,CACd+E,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAE,CAAEvD,KAAK,CAAEA,KAAK,CAACqD,MAAM,CAAEjD,UAAU,CAAEE,aAAc,CAAC,CAAC,CACnF,GAAI,CAAAiF,QAAQ,CAAGvF,KAAK,CACpB,GAAII,UAAU,CAAE,CACdmF,QAAQ,CAAGA,QAAQ,CAAClB,MAAM,CAACtC,IAAI,OAAAyD,UAAA,CAAAC,aAAA,OAC7B,EAAAD,UAAA,CAAAzD,IAAI,CAAC2D,IAAI,UAAAF,UAAA,iBAATA,UAAA,CAAWlB,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAACvF,UAAU,CAACkE,WAAW,CAAC,CAAC,CAAC,KAAAmB,aAAA,CAC3D1D,IAAI,CAAC6D,OAAO,UAAAH,aAAA,iBAAZA,aAAA,CAAcnB,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAACvF,UAAU,CAACkE,WAAW,CAAC,CAAC,CAAC,GAChE,CAAC,CACH,CACA,GAAIhE,aAAa,CAAE,CACjBiF,QAAQ,CAAGA,QAAQ,CAAClB,MAAM,CAACtC,IAAI,EAAImC,MAAM,CAACnC,IAAI,CAACkC,KAAK,CAAC,GAAKC,MAAM,CAAC5D,aAAa,CAAC,CAAC,CAClF,CACAiF,QAAQ,CAACf,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAAzC,IAAI,CAACyC,CAAC,CAACxC,SAAS,CAAC,CAAG,GAAI,CAAAD,IAAI,CAACwC,CAAC,CAACvC,SAAS,CAAC,CAAC,CACtEe,OAAO,CAACM,GAAG,CAAC,wBAAwB,CAAEgC,QAAQ,CAAClC,MAAM,CAAC,CACtDlD,gBAAgB,CAACoF,QAAQ,CAAC,CAC5B,CAAC,CAAE,CAACvF,KAAK,CAAEI,UAAU,CAAEE,aAAa,CAAC,CAAC,CAEtC,KAAM,CAAAuF,gBAAgB,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAAC9F,KAAK,CAAC+F,GAAG,CAACC,CAAC,EAAIA,CAAC,CAAC/B,KAAK,CAAC,CAACI,MAAM,CAAC4B,OAAO,CAAC,CAAC,CAAC,CAACzB,IAAI,CAAC,CAAC,CAErF,KAAM,CAAA0B,eAAe,CAAIC,IAAI,EAAK,CAChC,GAAI,CAACA,IAAI,EAAI,CAACA,IAAI,CAAC9E,GAAG,CAAE,CACtB7C,OAAO,CAACwE,KAAK,CAAC,0CAA0C,CAAC,CACzD,OACF,CAEA;AACA,KAAM,CAAAoD,aAAa,CAAG,mBAAmB,CACzC,GAAI,CAACA,aAAa,CAACC,IAAI,CAACF,IAAI,CAAC9E,GAAG,CAAC,CAAE,CACjC7C,OAAO,CAACwE,KAAK,CAAC,2CAA2C,CAAC,CAC1D,OACF,CAEA5E,eAAe,CAAC,IAAM,CACpB4C,QAAQ,UAAAsF,MAAA,CAAUH,IAAI,CAAC9E,GAAG,SAAO,CAAC,CACpC,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAkF,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChCtD,OAAO,CAACM,GAAG,CAAC,gCAAgC,CAAC,CAC7C,GAAI3C,UAAU,EAAIF,OAAO,CAAE,OAAQ;AAEnC,GAAI,CACF,GAAIQ,IAAI,CAAE,CACR,KAAM,CAAAgC,QAAQ,CAAC,IAAI,CAAC,CAAE;AACtB,KAAM,CAAA9B,cAAc,CAAC,CAAC,CACtB5C,OAAO,CAACgD,OAAO,CAAC,mCAAmC,CAAC,CACtD,CACF,CAAE,MAAOwB,KAAK,CAAE,CACdxE,OAAO,CAACwE,KAAK,CAAC,6BAA6B,CAAC,CAC9C,CACF,CAAC,CAED,KAAM,CAAAwD,cAAc,CAAIL,IAAI,EAAK,CAC/B,GAAI,CAACA,IAAI,EAAI,CAACA,IAAI,CAAC9E,GAAG,CAAE,CACtB7C,OAAO,CAACwE,KAAK,CAAC,0CAA0C,CAAC,CACzD,OACF,CACA;AACA,KAAM,CAAAyD,UAAU,CAAGjG,WAAW,CAAC2F,IAAI,CAAC9E,GAAG,CAAC,CACxC,GAAI,CAACoF,UAAU,CAAE,CACfjI,OAAO,CAACkI,IAAI,CAAC,sDAAsD,CAAC,CACpE,OACF,CACAtI,eAAe,CAAC,IAAM,CACpB4C,QAAQ,UAAAsF,MAAA,CAAUH,IAAI,CAAC9E,GAAG,WAAS,CAAC,CACtC,CAAC,CAAC,CACJ,CAAC,CAID,GAAIX,OAAO,CAAE,CACX,mBACEd,IAAA,QAAK+G,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACzG9G,KAAA,QAAK6G,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BhH,IAAA,QAAK+G,SAAS,CAAC,6EAA6E,CAAM,CAAC,cACnG/G,IAAA,MAAG+G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,EAChD,CAAC,CACH,CAAC,CAEV,CAEA,mBACEhH,IAAA,QAAK+G,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxE9G,KAAA,QAAK6G,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAE1E9G,KAAA,QAAK6G,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDhH,IAAA,QAAK+G,SAAS,CAAC,qJAAqJ,CAAAC,QAAA,cAClKhH,IAAA,CAACb,OAAO,EAAC4H,SAAS,CAAC,oCAAoC,CAAE,CAAC,CACvD,CAAC,cACN/G,IAAA,OAAI+G,SAAS,CAAC,wFAAwF,CAAAC,QAAA,CAAC,qCAEvG,CAAI,CAAC,cACLhH,IAAA,MAAG+G,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAAC,sEAEjG,CAAG,CAAC,cACJ9G,KAAA,QAAK6G,SAAS,CAAC,iGAAiG,CAAAC,QAAA,eAC9G9G,KAAA,QAAK6G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtChH,IAAA,QAAK+G,SAAS,CAAC,mCAAmC,CAAM,CAAC,cACzD7G,KAAA,SAAA8G,QAAA,EAAO1G,aAAa,CAACmD,MAAM,CAAC,oBAAkB,EAAM,CAAC,EAClD,CAAC,cACNvD,KAAA,QAAK6G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtChH,IAAA,QAAK+G,SAAS,CAAC,kCAAkC,CAAM,CAAC,cACxD7G,KAAA,SAAA8G,QAAA,EAAM,SAAO,CAAC,CAAA1F,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEiD,KAAK,GAAI,YAAY,EAAO,CAAC,EAC9C,CAAC,CACLrD,WAAW,eACVhB,KAAA,QAAK6G,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DhH,IAAA,QAAK+G,SAAS,CAAC,kCAAkC,CAAM,CAAC,cACxD7G,KAAA,SAAA8G,QAAA,EAAM,WAAS,CAAC9F,WAAW,CAAC+F,kBAAkB,CAAC,CAAC,EAAO,CAAC,EACrD,CACN,EACE,CAAC,EACH,CAAC,cAGNjH,IAAA,QAAK+G,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1DhH,IAAA,QAAK+G,SAAS,CAAC,2CAA2C,CAAAC,QAAA,cACxD9G,KAAA,QAAK6G,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvD9G,KAAA,QAAK6G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhH,IAAA,QAAK+G,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAC3FhH,IAAA,CAACnB,QAAQ,EAACkI,SAAS,CAAC,qCAAqC,CAAE,CAAC,CACzD,CAAC,cACN/G,IAAA,UACEkH,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,sCAAsC,CAClDC,KAAK,CAAE5G,UAAW,CAClB6G,QAAQ,CAAGjB,CAAC,EAAK3F,aAAa,CAAC2F,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE,CAC/CL,SAAS,CAAC,mOAAmO,CAC9O,CAAC,EACC,CAAC,cACN/G,IAAA,QAAK+G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9G,KAAA,QAAK6G,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBhH,IAAA,QAAK+G,SAAS,CAAC,8EAA8E,CAAAC,QAAA,cAC3FhH,IAAA,CAAClB,QAAQ,EAACiI,SAAS,CAAC,qCAAqC,CAAE,CAAC,CACzD,CAAC,cACN7G,KAAA,WACEkH,KAAK,CAAE1G,aAAc,CACrB2G,QAAQ,CAAGjB,CAAC,EAAKzF,gBAAgB,CAACyF,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE,CAClDL,SAAS,CAAC,2OAA2O,CAAAC,QAAA,eAErPhH,IAAA,WAAQoH,KAAK,CAAC,EAAE,CAAAJ,QAAA,CAAC,aAAW,CAAQ,CAAC,CACpCf,gBAAgB,CAACE,GAAG,CAAEY,SAAS,eAC9B7G,KAAA,WAAwBkH,KAAK,CAAEL,SAAU,CAAAC,QAAA,EAAC,QAAM,CAACD,SAAS,GAA7CA,SAAsD,CACpE,CAAC,EACI,CAAC,EACN,CAAC,CACH,CAAC,cAGN7G,KAAA,WACEqH,OAAO,CAAEZ,aAAc,CACvBa,QAAQ,CAAE1G,OAAO,EAAIE,UAAW,CAChC+F,SAAS,CAAC,4VAA4V,CACtWU,KAAK,CAAC,mBAAmB,CAAAT,QAAA,eAEzBhH,IAAA,CAACN,SAAS,EAACqH,SAAS,0BAAAL,MAAA,CAA4B5F,OAAO,EAAIE,UAAU,CAAI,cAAc,CAAG,EAAE,CAAG,CAAE,CAAC,cAClGhB,IAAA,SAAM+G,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACzDhG,UAAU,CAAG,eAAe,CAAG,SAAS,CACrC,CAAC,EACD,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,cAGNhB,IAAA,QAAK+G,SAAS,CAAC,aAAa,CAAAC,QAAA,CAGzB1G,aAAa,CAACmD,MAAM,GAAK,CAAC,cACzBzD,IAAA,QAAK+G,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzC9G,KAAA,QAAK6G,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAC1EhH,IAAA,CAACZ,QAAQ,EAAC2H,SAAS,CAAC,sDAAsD,CAAE,CAAC,cAC7E/G,IAAA,OAAI+G,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACzFhH,IAAA,MAAG+G,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAC9CxG,UAAU,EAAIE,aAAa,CACxB,+CAA+C,CAC/C,wDAAwD,CAE3D,CAAC,EACD,CAAC,CACH,CAAC,cAENV,IAAA,QAAK+G,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB1G,aAAa,CAAC6F,GAAG,CAAC,CAACI,IAAI,CAAEmB,KAAK,gBAC7B1H,IAAA,CAAC2H,QAAQ,EAEPpB,IAAI,CAAEA,IAAK,CACXM,UAAU,CAAEjG,WAAW,CAAC2F,IAAI,CAAC9E,GAAG,CAAE,CAClCmG,WAAW,CAAE,IAAK,CAClBC,OAAO,CAAEvB,eAAgB,CACzBwB,MAAM,CAAEA,CAAA,GAAMlB,cAAc,CAACL,IAAI,CAAE,CACnCmB,KAAK,CAAEA,KAAM,EANRnB,IAAI,CAAC9E,GAOX,CACF,CAAC,CACC,CACN,CACE,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAkG,QAAQ,CAAGI,IAAA,EAAkD,KAAAC,eAAA,IAAjD,CAAEzB,IAAI,CAAEM,UAAU,CAAEgB,OAAO,CAAEC,MAAM,CAAEJ,KAAM,CAAC,CAAAK,IAAA,CAC5D,KAAM,CAAAE,UAAU,CAAIC,OAAO,EAAK,CAC9B,GAAI,CAACA,OAAO,CAAE,MAAO,KAAK,CAC1B,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACxC,KAAM,CAAAI,gBAAgB,CAAGJ,OAAO,CAAG,EAAE,CACrC,SAAAxB,MAAA,CAAUyB,OAAO,MAAAzB,MAAA,CAAI4B,gBAAgB,CAACrD,QAAQ,CAAC,CAAC,CAACsD,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EACnE,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIC,aAAa,EAAK,CAC9C;AACA,GAAI,CAACA,aAAa,EAAIA,aAAa,GAAK,CAAC,CAAE,MAAO,IAAI,CAEtD,GAAI,CAAAC,YAAY,CAAGD,aAAa,CAEhC;AACA,GAAI,MAAO,CAAAA,aAAa,GAAK,QAAQ,CAAE,CACrCC,YAAY,CAAGzE,QAAQ,CAACwE,aAAa,CAAE,EAAE,CAAC,CAC5C,CAEA;AACA,GAAIE,KAAK,CAACD,YAAY,CAAC,EAAIA,YAAY,CAAG,CAAC,CAAE,MAAO,IAAI,CAExD,KAAM,CAAAP,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACK,YAAY,CAAG,EAAE,CAAC,CAC7C,KAAM,CAAAR,OAAO,CAAGQ,YAAY,CAAG,EAAE,CAEjC,GAAIP,OAAO,CAAG,CAAC,CAAE,CACf,SAAAzB,MAAA,CAAUyB,OAAO,OAAAzB,MAAA,CAAKwB,OAAO,MAC/B,CACA,SAAAxB,MAAA,CAAU+B,aAAa,MACzB,CAAC,CAED;AACA,GAAI,CAAClC,IAAI,EAAI,MAAO,CAAAA,IAAI,GAAK,QAAQ,CAAE,CACrC,mBACEvG,IAAA,QAAK+G,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxEhH,IAAA,MAAG+G,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,CAC/C,CAAC,CAEV,CAEA,mBACE9G,KAAA,QACE6G,SAAS,CAAC,mIAAmI,CAC7I6B,KAAK,CAAE,CACLC,UAAU,CAAE,gEAAgE,CAC5EC,MAAM,CAAEjC,UAAU,CACbA,UAAU,CAACtE,OAAO,GAAK,MAAM,CAAG,mBAAmB,CAAG,mBAAmB,CAC1E,mBAAmB,CACvBwG,SAAS,CAAElC,UAAU,CAChBA,UAAU,CAACtE,OAAO,GAAK,MAAM,CAC1B,oCAAoC,CACpC,mCAAmC,CACvC,oCAAoC,CACxCyG,SAAS,CAAE3D,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,OAAO,CAAG,OAAO,CACvDC,MAAM,CAAE,MACV,CAAE,CAAAlC,QAAA,eAGFhH,IAAA,QAAK+G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BhH,IAAA,OACE+G,SAAS,CAAC,6BAA6B,CACvC6B,KAAK,CAAE,CACLO,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,2BAA2B,CACvCC,UAAU,CAAE,KAAK,CACjBC,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CAAAjC,QAAA,CAED,MAAO,CAAAT,IAAI,CAACT,IAAI,GAAK,QAAQ,CAAGS,IAAI,CAACT,IAAI,CAAG,eAAe,CAC1D,CAAC,CACF,CAAC,cAGN9F,IAAA,QAAK+G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BH,UAAU,cACT3G,KAAA,QAAK6G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDhH,IAAA,QACE+G,SAAS,CAAC,+DAA+D,CACzE6B,KAAK,CAAE,CACLW,eAAe,CAAE1C,UAAU,CAACtE,OAAO,GAAK,MAAM,CAAG,SAAS,CAAG,SAAS,CACtE+G,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC/C,CAAE,CAAAjC,QAAA,CAEDH,UAAU,CAACtE,OAAO,GAAK,MAAM,CAAG,UAAU,CAAG,UAAU,CACrD,CAAC,cACNrC,KAAA,QACE6G,SAAS,CAAC,gEAAgE,CAC1E6B,KAAK,CAAE,CACLW,eAAe,CAAE,SAAS,CAC1BJ,KAAK,CAAE,SAAS,CAChBG,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC/C,CAAE,CAAAjC,QAAA,EAED,MAAO,CAAAH,UAAU,CAACrE,UAAU,GAAK,QAAQ,CAAGqE,UAAU,CAACrE,UAAU,CAAG,CAAC,CAAC,GACzE,EAAK,CAAC,EACH,CAAC,cAENxC,IAAA,QACE+G,SAAS,CAAC,+DAA+D,CACzE6B,KAAK,CAAE,CACLW,eAAe,CAAE,SAAS,CAC1BD,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC/C,CAAE,CAAAjC,QAAA,CACH,4BAED,CAAK,CACN,CACE,CAAC,cAENhH,IAAA,QAAK+G,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/B9G,KAAA,QAAK6G,SAAS,CAAC,QAAQ,CAAAC,QAAA,eAGrB9G,KAAA,QAAK6G,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C9G,KAAA,QACE6G,SAAS,CAAC,+DAA+D,CACzE6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDW,WAAW,CAAE,SACf,CAAE,CAAAxC,QAAA,eAEFhH,IAAA,CAAChB,cAAc,EAAC+H,SAAS,CAAC,SAAS,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,cACnEnJ,IAAA,SACE+G,SAAS,CAAC,WAAW,CACrB6B,KAAK,CAAE,CACLO,KAAK,CAAE,SAAS,CAChBG,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CAAAjC,QAAA,CAEDyC,KAAK,CAACC,OAAO,CAACnD,IAAI,CAACoD,SAAS,CAAC,CAAGpD,IAAI,CAACoD,SAAS,CAAClG,MAAM,CAAG,CAAC,CACtD,CAAC,EACJ,CAAC,cACNvD,KAAA,QACE6G,SAAS,CAAC,+DAA+D,CACzE6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDW,WAAW,CAAE,SACf,CAAE,CAAAxC,QAAA,eAEFhH,IAAA,CAACjB,OAAO,EAACgI,SAAS,CAAC,SAAS,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,cAC5DnJ,IAAA,SACE+G,SAAS,CAAC,WAAW,CACrB6B,KAAK,CAAE,CACLO,KAAK,CAAE,SAAS,CAChBG,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CAAAjC,QAAA,CACH,IAED,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,cAIN9G,KAAA,QAAK6G,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpE9G,KAAA,SACE6G,SAAS,CAAC,oEAAoE,CAC9E6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDS,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC/C,CAAE,CAAAjC,QAAA,EACH,cACG,CAAC,MAAO,CAAAT,IAAI,CAAClC,KAAK,GAAK,QAAQ,EAAI,MAAO,CAAAkC,IAAI,CAAClC,KAAK,GAAK,QAAQ,CAAGkC,IAAI,CAAClC,KAAK,CAAG,KAAK,EACpF,CAAC,cACPnE,KAAA,SACE6G,SAAS,CAAC,oEAAoE,CAC9E6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDS,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC/C,CAAE,CAAAjC,QAAA,EACH,cACG,CAACT,IAAI,CAACP,OAAO,EACX,CAAC,CAENO,IAAI,CAACqD,QAAQ,EAAIrD,IAAI,CAACqD,QAAQ,GAAK,SAAS,eAC3C1J,KAAA,SACE6G,SAAS,CAAC,oEAAoE,CAC9E6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDS,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC/C,CAAE,CAAAjC,QAAA,EACH,cACG,CAACT,IAAI,CAACqD,QAAQ,EACZ,CACP,CAEArD,IAAI,CAACsD,KAAK,EAAItD,IAAI,CAACsD,KAAK,GAAK,SAAS,eACrC3J,KAAA,SACE6G,SAAS,CAAC,oEAAoE,CAC9E6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDS,QAAQ,CAAEjE,MAAM,CAAC4D,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC/C,CAAE,CAAAjC,QAAA,EACH,cACG,CAACT,IAAI,CAACsD,KAAK,EACT,CACP,EACE,CAAC,EACH,CAAC,CACH,CAAC,CAILhD,UAAU,EAAI,MAAO,CAAAA,UAAU,GAAK,QAAQ,eAC3C3G,KAAA,QACE6G,SAAS,CAAC,sCAAsC,CAChD6B,KAAK,CAAE,CACLC,UAAU,CAAEhC,UAAU,CAACtE,OAAO,GAAK,MAAM,CACrC,oDAAoD,CACpD,oDAAoD,CACxDiH,WAAW,CAAE3C,UAAU,CAACtE,OAAO,GAAK,MAAM,CAAG,SAAS,CAAG,SAC3D,CAAE,CAAAyE,QAAA,eAEF9G,KAAA,QAAK6G,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD9G,KAAA,QAAK6G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACrCH,UAAU,CAACtE,OAAO,GAAK,MAAM,cAC5BvC,IAAA,QACE+G,SAAS,CAAC,0FAA0F,CACpG6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDW,WAAW,CAAE,SACf,CAAE,CAAAxC,QAAA,cAEFhH,IAAA,CAACX,OAAO,EAAC0H,SAAS,CAAC,mBAAmB,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACnE,CAAC,cAENnJ,IAAA,QACE+G,SAAS,CAAC,0FAA0F,CACpG6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDW,WAAW,CAAE,SACf,CAAE,CAAAxC,QAAA,cAEFhH,IAAA,CAACV,GAAG,EAACyH,SAAS,CAAC,mBAAmB,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC/D,CACN,cACDjJ,KAAA,QAAA8G,QAAA,eACEhH,IAAA,SAAM+G,SAAS,CAAC,mBAAmB,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAAnC,QAAA,CAAC,0BAAc,CAAM,CAAC,cACtFhH,IAAA,QAAK+G,SAAS,CAAC,SAAS,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAAnC,QAAA,CAClD,GAAI,CAAA3E,IAAI,CAACwE,UAAU,CAAC1D,WAAW,EAAI0D,UAAU,CAACvE,SAAS,EAAID,IAAI,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC8F,kBAAkB,CAAC,CAAC,CACzF,CAAC,EACH,CAAC,EACH,CAAC,cACN5J,KAAA,SACE6G,SAAS,CAAC,8BAA8B,CACxC6B,KAAK,CAAE,CACLO,KAAK,CAAEtC,UAAU,CAACtE,OAAO,GAAK,MAAM,CAAG,SAAS,CAAG,SACrD,CAAE,CAAAyE,QAAA,EAED,MAAO,CAAAH,UAAU,CAACrE,UAAU,GAAK,QAAQ,CAAGqE,UAAU,CAACrE,UAAU,CAAG,CAAC,CAAC,GACzE,EAAM,CAAC,EACJ,CAAC,cAGNtC,KAAA,QAAK6G,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAElD9G,KAAA,QACE6G,SAAS,CAAC,+DAA+D,CACzE6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDW,WAAW,CAAE,SACf,CAAE,CAAAxC,QAAA,eAEF9G,KAAA,QAAK6G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtChH,IAAA,CAACX,OAAO,EAAC0H,SAAS,CAAC,SAAS,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,cAC5DnJ,IAAA,SAAM+G,SAAS,CAAC,mBAAmB,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAAnC,QAAA,CAC7D,MAAO,CAAAH,UAAU,CAACpE,cAAc,GAAK,QAAQ,CAAGoE,UAAU,CAACpE,cAAc,CAAG,CAAC,CAC1E,CAAC,EACJ,CAAC,cACNvC,KAAA,QAAK6G,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtChH,IAAA,CAACV,GAAG,EAACyH,SAAS,CAAC,SAAS,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,cACxDnJ,IAAA,SAAM+G,SAAS,CAAC,mBAAmB,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAAnC,QAAA,CAC7D,CAAC,EAAAgB,eAAA,CAAAzB,IAAI,CAACoD,SAAS,UAAA3B,eAAA,iBAAdA,eAAA,CAAgBvE,MAAM,GAAI,CAAC,GAAK,MAAO,CAAAoD,UAAU,CAACpE,cAAc,GAAK,QAAQ,CAAGoE,UAAU,CAACpE,cAAc,CAAG,CAAC,CAAC,CAC5G,CAAC,EACJ,CAAC,EACH,CAAC,cAGNvC,KAAA,QACE6G,SAAS,CAAC,+DAA+D,CACzE6B,KAAK,CAAE,CACLC,UAAU,CAAE,oDAAoD,CAChEW,WAAW,CAAE,SACf,CAAE,CAAAxC,QAAA,eAEFhH,IAAA,SAAM+G,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,cAClChH,IAAA,SAAM+G,SAAS,CAAC,mBAAmB,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAAnC,QAAA,CAC7DH,UAAU,CAAC7D,QAAQ,EAAI6D,UAAU,CAAC9D,MAAM,EAAI,CAAC,CAC1C,CAAC,EACJ,CAAC,CAGL8D,UAAU,CAAC3D,SAAS,EAAI2D,UAAU,CAAC3D,SAAS,CAAG,CAAC,eAC/ChD,KAAA,QACE6G,SAAS,CAAC,+DAA+D,CACzE6B,KAAK,CAAE,CACLC,UAAU,CAAE,oDAAoD,CAChEW,WAAW,CAAE,SACf,CAAE,CAAAxC,QAAA,eAEFhH,IAAA,CAACjB,OAAO,EAACgI,SAAS,CAAC,SAAS,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,cAC5DnJ,IAAA,SAAM+G,SAAS,CAAC,mBAAmB,CAAC6B,KAAK,CAAE,CAAEO,KAAK,CAAE,SAAU,CAAE,CAAAnC,QAAA,CAC7DwB,oBAAoB,CAAC3B,UAAU,CAAC3D,SAAS,CAAC,CACvC,CAAC,EACJ,CACN,EACE,CAAC,EACH,CACN,cAGDlD,IAAA,QAAK+G,SAAS,CAAC,QAAQ,CAAM,CAAC,cAE9B7G,KAAA,QAAK6G,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAE9B9G,KAAA,WACEqH,OAAO,CAAEA,CAAA,GAAMM,OAAO,CAACtB,IAAI,CAAE,CAC7BQ,SAAS,CAAC,yLAAyL,CACnM6B,KAAK,CAAE,CACLC,UAAU,CAAEhC,UAAU,CAClB,6CAA6C,CAC7C,6CAA6C,CACjDyC,QAAQ,CAAE,MAAM,CAChBN,SAAS,CAAE,MACb,CAAE,CAAAhC,QAAA,eAEFhH,IAAA,CAACd,YAAY,EAAC6H,SAAS,CAAC,SAAS,CAAE,CAAC,CACnCF,UAAU,CAAG,gBAAgB,CAAG,eAAe,EAC1C,CAAC,CAGRA,UAAU,eACT7G,IAAA,WACEuH,OAAO,CAAEA,CAAA,GAAMO,MAAM,CAACvB,IAAI,CAAE,CAC5BQ,SAAS,CAAC,2IAA2I,CACrJ6B,KAAK,CAAE,CACLC,UAAU,CAAE,6CAA6C,CACzDS,QAAQ,CAAE,MAAM,CAChBN,SAAS,CAAE,MACb,CAAE,CACFvB,KAAK,CAAC,cAAc,CAAAT,QAAA,cAEpBhH,IAAA,CAACf,QAAQ,EAAC8H,SAAS,CAAC,SAAS,CAAE,CAAC,CAC1B,CACT,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5G,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}