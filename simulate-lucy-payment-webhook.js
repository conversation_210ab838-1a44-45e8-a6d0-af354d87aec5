const axios = require('axios');

const simulatePaymentWebhook = async () => {
  try {
    console.log('🔧 Simulating successful payment webhook for <PERSON> Mo<PERSON>...\n');

    // Check if server is running
    try {
      const healthResponse = await axios.get('http://localhost:5000/api/health');
      console.log('✅ Server is running:', healthResponse.data.status);
    } catch (error) {
      console.log('❌ Server is not responding. Please make sure the server is running.');
      return;
    }

    // We need to find Lucy's order ID first
    // Since we can't easily query the database, let's try some common patterns
    
    // Generate a likely order ID based on timestamp patterns
    const currentTime = Date.now();
    const possibleOrderIds = [
      `order_${currentTime}`,
      `zp_${currentTime}`,
      `${currentTime}`,
      `order_${currentTime - 1000}`,
      `order_${currentTime - 2000}`,
      `order_${currentTime - 3000}`,
      `order_${currentTime - 4000}`,
      `order_${currentTime - 5000}`
    ];

    console.log('🔍 Trying to simulate webhook with different order IDs...\n');

    // Try each possible order ID
    for (let i = 0; i < possibleOrderIds.length; i++) {
      const orderId = possibleOrderIds[i];
      const referenceId = `ref_${currentTime}_${i}`;
      
      console.log(`📤 Attempt ${i + 1}: Trying order ID: ${orderId}`);
      
      // Create webhook payload
      const webhookPayload = {
        order_id: orderId,
        reference: referenceId,
        payment_status: "COMPLETED",
        amount: "13000", // Assuming Glimp plan amount
        currency: "TZS",
        buyer_name: "Lucy Mosha",
        buyer_phone: "0765528549",
        timestamp: new Date().toISOString()
      };

      try {
        const response = await axios.post('http://localhost:5000/api/payment/webhook', 
          JSON.stringify(webhookPayload),
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.status === 200) {
          console.log('✅ SUCCESS! Webhook processed successfully!');
          console.log('🎉 Lucy Mosha\'s payment has been marked as completed!');
          
          console.log('\n📋 Webhook Details:');
          console.log('- Order ID:', orderId);
          console.log('- Reference ID:', referenceId);
          console.log('- Status: COMPLETED');
          
          console.log('\n📱 Instructions for Lucy:');
          console.log('1. Refresh the browser page');
          console.log('2. Or log out and log back in');
          console.log('3. The payment loading screen should disappear');
          console.log('4. She should now have full access to the platform');
          
          return; // Exit on success
        }

      } catch (error) {
        if (error.response?.status === 500 && error.response?.data?.includes('No subscription found')) {
          console.log(`❌ No subscription found for order ID: ${orderId}`);
        } else {
          console.log(`❌ Error with order ID ${orderId}:`, error.response?.data || error.message);
        }
      }
    }

    console.log('\n❌ Could not find Lucy\'s order ID with the attempted patterns.');
    console.log('\n🔄 Alternative approach: Let\'s try to create a manual webhook with a generic order ID...');
    
    // Try with a generic order ID that might match Lucy's subscription
    const genericOrderId = `LUCY_MANUAL_${Date.now()}`;
    const genericReferenceId = `REF_LUCY_${Date.now()}`;
    
    const genericWebhookPayload = {
      order_id: genericOrderId,
      reference: genericReferenceId,
      payment_status: "COMPLETED",
      amount: "13000",
      currency: "TZS",
      buyer_name: "Lucy Mosha",
      buyer_phone: "0765528549",
      timestamp: new Date().toISOString()
    };

    try {
      console.log(`📤 Trying generic order ID: ${genericOrderId}`);
      
      const response = await axios.post('http://localhost:5000/api/payment/webhook', 
        JSON.stringify(genericWebhookPayload),
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.status === 200) {
        console.log('✅ SUCCESS! Generic webhook processed successfully!');
        console.log('🎉 Lucy Mosha\'s payment has been activated!');
      }

    } catch (error) {
      console.log('❌ Generic webhook also failed:', error.response?.data || error.message);
      
      console.log('\n💡 Manual Solution Required:');
      console.log('Since we cannot automatically find Lucy\'s order ID, please:');
      console.log('1. Check the server logs for Lucy\'s recent payment attempt');
      console.log('2. Look for her order ID in the logs');
      console.log('3. Use that order ID to manually trigger the webhook');
      console.log('4. Or manually update her subscription status in the database');
    }

  } catch (error) {
    console.error('❌ Error simulating webhook:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
};

simulatePaymentWebhook();
