{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Hub\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport { FaHome, FaQuestionCircle, FaBook, FaChartLine, FaUser, FaComments, FaCreditCard, FaInfoCircle, FaGraduationCap, FaTrophy, FaStar, FaRocket, FaRobot, FaSignOutAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hub = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Check if user profile is complete\n  const isProfileComplete = () => {\n    if (!user) return false;\n    const requiredFields = ['firstName', 'lastName', 'phoneNumber', 'level', 'class'];\n    return requiredFields.every(field => user[field] && user[field].toString().trim() !== '');\n  };\n  const profileCompletionPercentage = () => {\n    if (!user) return 0;\n    const fields = ['firstName', 'lastName', 'email', 'phoneNumber', 'level', 'class'];\n    const completedFields = fields.filter(field => user[field] && user[field].toString().trim() !== '');\n    return Math.round(completedFields.length / fields.length * 100);\n  };\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n  const inspiringQuotes = [\"Education is the most powerful weapon which you can use to change the world. - Nelson Mandela\", \"The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt\", \"Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill\", \"Your limitation—it's only your imagination.\", \"Great things never come from comfort zones.\", \"Dream it. Wish it. Do it.\"];\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote(prev => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n  const navigationItems = [{\n    title: 'Take Quiz',\n    description: 'Test your knowledge',\n    icon: FaQuestionCircle,\n    path: '/user/quiz',\n    color: 'from-blue-500 to-blue-600',\n    hoverColor: 'from-blue-600 to-blue-700'\n  }, {\n    title: 'Study Materials',\n    description: 'Books, videos & notes',\n    icon: FaBook,\n    path: '/user/study-material',\n    color: 'from-purple-500 to-purple-600',\n    hoverColor: 'from-purple-600 to-purple-700'\n  }, {\n    title: 'Reports',\n    description: 'Track your progress',\n    icon: FaChartLine,\n    path: '/user/reports',\n    color: 'from-green-500 to-green-600',\n    hoverColor: 'from-green-600 to-green-700'\n  }, {\n    title: 'Ranking',\n    description: 'See your position',\n    icon: FaTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600',\n    hoverColor: 'from-yellow-600 to-yellow-700'\n  }, {\n    title: 'Profile',\n    description: 'Manage your account',\n    icon: FaUser,\n    path: '/profile',\n    color: 'from-indigo-500 to-indigo-600',\n    hoverColor: 'from-indigo-600 to-indigo-700'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: FaComments,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600',\n    hoverColor: 'from-pink-600 to-pink-700'\n  }, {\n    title: 'About Us',\n    description: 'Learn about our mission',\n    icon: FaInfoCircle,\n    path: '/user/about-us',\n    color: 'from-cyan-500 to-cyan-600',\n    hoverColor: 'from-cyan-600 to-cyan-700'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hub-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hub-content\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"hub-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-welcome relative overflow-hidden min-h-[200px]\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute inset-0 rounded-3xl\",\n            style: {\n              background: 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',\n              filter: 'blur(30px)'\n            },\n            animate: {\n              scale: [1, 1.1, 1],\n              opacity: [0.3, 0.6, 0.3],\n              rotate: [0, 2, -2, 0]\n            },\n            transition: {\n              duration: 8,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"relative z-10 text-center\",\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative inline-block mr-6\",\n              initial: {\n                x: -200,\n                opacity: 0,\n                rotateY: -90\n              },\n              animate: {\n                x: 0,\n                opacity: 1,\n                rotateY: 0\n              },\n              transition: {\n                duration: 1.8,\n                delay: 0.5,\n                ease: \"easeOut\"\n              },\n              children: /*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\",\n                style: {\n                  background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  backgroundSize: '200% 200%',\n                  fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                  letterSpacing: '-0.06em',\n                  textShadow: '0 0 50px rgba(59, 130, 246, 0.4)'\n                },\n                animate: {\n                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                  y: [0, -5, 0],\n                  textShadow: ['0 0 50px rgba(59, 130, 246, 0.4)', '0 0 80px rgba(59, 130, 246, 0.7)', '0 0 50px rgba(59, 130, 246, 0.4)']\n                },\n                transition: {\n                  backgroundPosition: {\n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  y: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  textShadow: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                },\n                whileHover: {\n                  scale: 1.05,\n                  rotateZ: [0, 2, -2, 0],\n                  transition: {\n                    duration: 0.6\n                  }\n                },\n                children: \"Study\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"relative inline-block\",\n              initial: {\n                x: 200,\n                opacity: 0,\n                rotateY: 90\n              },\n              animate: {\n                x: 0,\n                opacity: 1,\n                rotateY: 0\n              },\n              transition: {\n                duration: 1.8,\n                delay: 1,\n                ease: \"easeOut\"\n              },\n              children: /*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\",\n                style: {\n                  background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  backgroundSize: '200% 200%',\n                  fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                  letterSpacing: '-0.06em',\n                  textShadow: '0 0 50px rgba(16, 185, 129, 0.4)'\n                },\n                animate: {\n                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                  y: [0, 5, 0],\n                  textShadow: ['0 0 50px rgba(16, 185, 129, 0.4)', '0 0 80px rgba(16, 185, 129, 0.7)', '0 0 50px rgba(16, 185, 129, 0.4)']\n                },\n                transition: {\n                  backgroundPosition: {\n                    duration: 5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                  },\n                  y: {\n                    duration: 3.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 0.5\n                  },\n                  textShadow: {\n                    duration: 4.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                  }\n                },\n                whileHover: {\n                  scale: 1.05,\n                  rotateZ: [0, -2, 2, 0],\n                  transition: {\n                    duration: 0.6\n                  }\n                },\n                children: \"Smarter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-8 relative\",\n              initial: {\n                opacity: 0,\n                y: 50,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                y: 0,\n                scale: 1\n              },\n              transition: {\n                duration: 1.5,\n                delay: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"text-3xl sm:text-4xl font-bold block\",\n                style: {\n                  background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  backgroundClip: 'text',\n                  backgroundSize: '200% 200%',\n                  textShadow: '0 0 30px rgba(245, 158, 11, 0.4)'\n                },\n                animate: {\n                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                  scale: [1, 1.02, 1],\n                  textShadow: ['0 0 30px rgba(245, 158, 11, 0.4)', '0 0 50px rgba(245, 158, 11, 0.7)', '0 0 30px rgba(245, 158, 11, 0.4)']\n                },\n                transition: {\n                  backgroundPosition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  scale: {\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  },\n                  textShadow: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                },\n                whileHover: {\n                  scale: 1.1,\n                  rotate: [0, 3, -3, 0],\n                  transition: {\n                    duration: 0.4\n                  }\n                },\n                children: [user === null || user === void 0 ? void 0 : user.name, \"!\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-6 relative\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                duration: 1,\n                delay: 2.5\n              },\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"h-2 mx-auto rounded-full relative overflow-hidden\",\n                style: {\n                  width: '90%',\n                  background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',\n                  boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)'\n                },\n                animate: {\n                  boxShadow: ['0 0 30px rgba(59, 130, 246, 0.5)', '0 0 50px rgba(16, 185, 129, 0.7)', '0 0 40px rgba(245, 158, 11, 0.6)', '0 0 30px rgba(59, 130, 246, 0.5)']\n                },\n                transition: {\n                  duration: 6,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute inset-0 rounded-full\",\n                  style: {\n                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',\n                    width: '40%'\n                  },\n                  animate: {\n                    x: ['-100%', '250%']\n                  },\n                  transition: {\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hub-subtitle\",\n          children: \"Ready to shine today? \\u2728 Choose your learning path below.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-quote\",\n          children: [/*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginRight: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), \"\\\"\", inspiringQuotes[currentQuote], \"\\\"\", /*#__PURE__*/_jsxDEV(FaStar, {\n            style: {\n              color: '#f59e0b',\n              marginLeft: '0.5rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              marginTop: '0.5rem'\n            },\n            children: \"- BrainWave Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), !isProfileComplete() && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.3\n        },\n        className: \"mb-8 mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 border border-blue-200 rounded-2xl p-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row items-center justify-between gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-full\",\n                children: /*#__PURE__*/_jsxDEV(FaUser, {\n                  className: \"text-white text-xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-800 mb-1\",\n                  children: [\"\\uD83D\\uDC4B Welcome to BrainWave, \", (user === null || user === void 0 ? void 0 : user.firstName) || (user === null || user === void 0 ? void 0 : user.name) || 'Student', \"!\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 text-sm mb-2\",\n                  children: \"Complete your profile to unlock personalized features and track your progress\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(motion.div, {\n                      initial: {\n                        width: 0\n                      },\n                      animate: {\n                        width: `${profileCompletionPercentage()}%`\n                      },\n                      transition: {\n                        duration: 1,\n                        delay: 0.5\n                      },\n                      className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [profileCompletionPercentage(), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => navigate('/profile'),\n              className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(FaUser, {\n                className: \"text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this), \"Complete Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hub-grid-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hub-grid\",\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              className: `hub-card hover:${item.hoverColor} ${item.color} ${item.title === 'Profile' ? 'relative ring-2 ring-blue-300 ring-opacity-50' : ''}`,\n              onClick: () => navigate(item.path),\n              tabIndex: 0,\n              role: \"button\",\n              onKeyDown: e => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  navigate(item.path);\n                }\n              },\n              style: {\n                cursor: 'pointer',\n                touchAction: 'manipulation' // Improves touch responsiveness\n              },\n              children: [item.title === 'Profile' && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  scale: 0\n                },\n                animate: {\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 1\n                },\n                className: \"absolute -top-2 -right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg\",\n                children: \"\\u2B50 Start Here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hub-card-icon\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"hub-card-title\",\n                children: [item.title, item.title === 'Profile' && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-blue-500\",\n                  children: \"\\uD83D\\uDC64\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"hub-card-description\",\n                children: item.title === 'Profile' ? 'Complete your profile & manage subscription' : item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this)]\n            }, item.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.5\n          },\n          className: \"hub-bottom-decoration\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"decoration-content\",\n            children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Your learning journey starts here!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FaRocket, {\n              className: \"decoration-icon animate-bounce-gentle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this), !isProfileComplete() && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.8,\n          delay: 2\n        },\n        className: \"fixed bottom-6 right-6 z-50\",\n        children: /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.1\n          },\n          whileTap: {\n            scale: 0.9\n          },\n          animate: {\n            y: [0, -10, 0],\n            boxShadow: ['0 10px 30px rgba(59, 130, 246, 0.3)', '0 20px 40px rgba(59, 130, 246, 0.5)', '0 10px 30px rgba(59, 130, 246, 0.3)']\n          },\n          transition: {\n            y: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            boxShadow: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }\n          },\n          onClick: () => navigate('/profile'),\n          className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 group\",\n          title: \"Complete Your Profile\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"text-xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline font-medium\",\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute inset-0 rounded-full border-2 border-blue-400\",\n            animate: {\n              scale: [1, 1.5, 1],\n              opacity: [1, 0, 1]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n};\n_s(Hub, \"yUKD1BuiW8zY+bONKPa0/Mswuw0=\", false, function () {\n  return [useNavigate, useSelector];\n});\n_c = Hub;\nexport default Hub;\nvar _c;\n$RefreshReg$(_c, \"Hub\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSelector", "motion", "message", "FaHome", "FaQuestionCircle", "FaBook", "FaChartLine", "FaUser", "FaComments", "FaCreditCard", "FaInfoCircle", "FaGraduationCap", "FaTrophy", "FaStar", "FaRocket", "FaRobot", "FaSignOutAlt", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "user", "state", "currentQuote", "setCurrentQuote", "isProfileComplete", "requiredFields", "every", "field", "toString", "trim", "profileCompletionPercentage", "fields", "completedFields", "filter", "Math", "round", "length", "handleLogout", "localStorage", "removeItem", "success", "inspiringQuotes", "interval", "setInterval", "prev", "clearInterval", "navigationItems", "title", "description", "icon", "path", "color", "hoverColor", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "style", "background", "scale", "rotate", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "rotateY", "delay", "span", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "backgroundSize", "fontFamily", "letterSpacing", "textShadow", "backgroundPosition", "whileHover", "rotateZ", "name", "width", "boxShadow", "marginRight", "marginLeft", "fontSize", "marginTop", "firstName", "button", "whileTap", "onClick", "map", "item", "index", "IconComponent", "tabIndex", "role", "onKeyDown", "e", "key", "cursor", "touchAction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Hub/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport './Hub.css';\nimport {\n  FaHome,\n  FaQuestionCircle,\n  FaBook,\n  FaChartLine,\n  FaUser,\n  FaComments,\n  FaCreditCard,\n  FaInfoCircle,\n  FaGraduationCap,\n  FaTrophy,\n  FaStar,\n  FaRocket,\n  FaRobot,\n  FaSignOutAlt\n} from 'react-icons/fa';\n\nconst Hub = () => {\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [currentQuote, setCurrentQuote] = useState(0);\n\n  // Check if user profile is complete\n  const isProfileComplete = () => {\n    if (!user) return false;\n    const requiredFields = ['firstName', 'lastName', 'phoneNumber', 'level', 'class'];\n    return requiredFields.every(field => user[field] && user[field].toString().trim() !== '');\n  };\n\n  const profileCompletionPercentage = () => {\n    if (!user) return 0;\n    const fields = ['firstName', 'lastName', 'email', 'phoneNumber', 'level', 'class'];\n    const completedFields = fields.filter(field => user[field] && user[field].toString().trim() !== '');\n    return Math.round((completedFields.length / fields.length) * 100);\n  };\n\n  // Logout function\n  const handleLogout = () => {\n    // Clear authentication data\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n\n    // Show success message\n    message.success('Logged out successfully!');\n\n    // Navigate to home page\n    navigate('/');\n  };\n\n  const inspiringQuotes = [\n    \"Education is the most powerful weapon which you can use to change the world. - Nelson Mandela\",\n    \"The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill\",\n    \"Your limitation—it's only your imagination.\",\n    \"Great things never come from comfort zones.\",\n    \"Dream it. Wish it. Do it.\"\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);\n    }, 4000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const navigationItems = [\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: FaQuestionCircle,\n      path: '/user/quiz',\n      color: 'from-blue-500 to-blue-600',\n      hoverColor: 'from-blue-600 to-blue-700'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: FaBook,\n      path: '/user/study-material',\n      color: 'from-purple-500 to-purple-600',\n      hoverColor: 'from-purple-600 to-purple-700'\n    },\n    {\n      title: 'Reports',\n      description: 'Track your progress',\n      icon: FaChartLine,\n      path: '/user/reports',\n      color: 'from-green-500 to-green-600',\n      hoverColor: 'from-green-600 to-green-700'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: FaTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600',\n      hoverColor: 'from-yellow-600 to-yellow-700'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage your account',\n      icon: FaUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600',\n      hoverColor: 'from-indigo-600 to-indigo-700'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: FaComments,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600',\n      hoverColor: 'from-pink-600 to-pink-700'\n    },\n\n    {\n      title: 'About Us',\n      description: 'Learn about our mission',\n      icon: FaInfoCircle,\n      path: '/user/about-us',\n      color: 'from-cyan-500 to-cyan-600',\n      hoverColor: 'from-cyan-600 to-cyan-700'\n    }\n  ];\n\n  return (\n    <div className=\"hub-container\">\n      <div className=\"hub-content\">\n\n\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"hub-header\"\n        >\n\n\n          {/* Dynamic Inspiring Study Smarter Animation */}\n          <div className=\"hub-welcome relative overflow-hidden min-h-[200px]\">\n\n\n            {/* Dynamic Background Waves */}\n            <motion.div\n              className=\"absolute inset-0 rounded-3xl\"\n              style={{\n                background: 'radial-gradient(ellipse 120% 80% at 50% 50%, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.06), transparent)',\n                filter: 'blur(30px)'\n              }}\n              animate={{\n                scale: [1, 1.1, 1],\n                opacity: [0.3, 0.6, 0.3],\n                rotate: [0, 2, -2, 0]\n              }}\n              transition={{\n                duration: 8,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            />\n\n            {/* Main Content */}\n            <motion.div\n              className=\"relative z-10 text-center\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 1.5 }}\n            >\n              {/* Study Text with Motion */}\n              <motion.div\n                className=\"relative inline-block mr-6\"\n                initial={{ x: -200, opacity: 0, rotateY: -90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 0.5,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 30%, #60a5fa 60%, #93c5fd 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(59, 130, 246, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, -5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(59, 130, 246, 0.4)',\n                      '0 0 80px rgba(59, 130, 246, 0.7)',\n                      '0 0 50px rgba(59, 130, 246, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 6, repeat: Infinity, ease: \"easeInOut\" },\n                    y: { duration: 3, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 4, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, 2, -2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Study\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* Smarter Text with Motion */}\n              <motion.div\n                className=\"relative inline-block\"\n                initial={{ x: 200, opacity: 0, rotateY: 90 }}\n                animate={{\n                  x: 0,\n                  opacity: 1,\n                  rotateY: 0\n                }}\n                transition={{\n                  duration: 1.8,\n                  delay: 1,\n                  ease: \"easeOut\"\n                }}\n              >\n                <motion.span\n                  className=\"block text-5xl sm:text-6xl md:text-7xl font-black tracking-tight\"\n                  style={{\n                    background: 'linear-gradient(135deg, #065f46 0%, #059669 30%, #10b981 60%, #34d399 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    fontFamily: \"'Inter', 'SF Pro Display', system-ui, sans-serif\",\n                    letterSpacing: '-0.06em',\n                    textShadow: '0 0 50px rgba(16, 185, 129, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    y: [0, 5, 0],\n                    textShadow: [\n                      '0 0 50px rgba(16, 185, 129, 0.4)',\n                      '0 0 80px rgba(16, 185, 129, 0.7)',\n                      '0 0 50px rgba(16, 185, 129, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 5, repeat: Infinity, ease: \"easeInOut\", delay: 1 },\n                    y: { duration: 3.5, repeat: Infinity, ease: \"easeInOut\", delay: 0.5 },\n                    textShadow: { duration: 4.5, repeat: Infinity, ease: \"easeInOut\", delay: 1 }\n                  }}\n                  whileHover={{\n                    scale: 1.05,\n                    rotateZ: [0, -2, 2, 0],\n                    transition: { duration: 0.6 }\n                  }}\n                >\n                  Smarter\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* User Name with Inspiring Animation */}\n              <motion.div\n                className=\"mt-8 relative\"\n                initial={{ opacity: 0, y: 50, scale: 0.8 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                transition={{ duration: 1.5, delay: 2 }}\n              >\n                <motion.span\n                  className=\"text-3xl sm:text-4xl font-bold block\"\n                  style={{\n                    background: 'linear-gradient(45deg, #f59e0b, #f97316, #ef4444, #ec4899)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    backgroundClip: 'text',\n                    backgroundSize: '200% 200%',\n                    textShadow: '0 0 30px rgba(245, 158, 11, 0.4)'\n                  }}\n                  animate={{\n                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],\n                    scale: [1, 1.02, 1],\n                    textShadow: [\n                      '0 0 30px rgba(245, 158, 11, 0.4)',\n                      '0 0 50px rgba(245, 158, 11, 0.7)',\n                      '0 0 30px rgba(245, 158, 11, 0.4)'\n                    ]\n                  }}\n                  transition={{\n                    backgroundPosition: { duration: 4, repeat: Infinity, ease: \"easeInOut\" },\n                    scale: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n                    textShadow: { duration: 3, repeat: Infinity, ease: \"easeInOut\" }\n                  }}\n                  whileHover={{\n                    scale: 1.1,\n                    rotate: [0, 3, -3, 0],\n                    transition: { duration: 0.4 }\n                  }}\n                >\n                  {user?.name}!\n                </motion.span>\n\n\n              </motion.div>\n\n              {/* Dynamic Inspiring Underline */}\n              <motion.div\n                className=\"mt-6 relative\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 1, delay: 2.5 }}\n              >\n                <motion.div\n                  className=\"h-2 mx-auto rounded-full relative overflow-hidden\"\n                  style={{\n                    width: '90%',\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6)',\n                    boxShadow: '0 0 30px rgba(59, 130, 246, 0.5)'\n                  }}\n                  animate={{\n                    boxShadow: [\n                      '0 0 30px rgba(59, 130, 246, 0.5)',\n                      '0 0 50px rgba(16, 185, 129, 0.7)',\n                      '0 0 40px rgba(245, 158, 11, 0.6)',\n                      '0 0 30px rgba(59, 130, 246, 0.5)'\n                    ]\n                  }}\n                  transition={{\n                    duration: 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                >\n                  {/* Moving Light Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 rounded-full\"\n                    style={{\n                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.9), transparent)',\n                      width: '40%'\n                    }}\n                    animate={{\n                      x: ['-100%', '250%']\n                    }}\n                    transition={{\n                      duration: 4,\n                      repeat: Infinity,\n                      ease: \"easeInOut\",\n                      delay: 3\n                    }}\n                  />\n                </motion.div>\n              </motion.div>\n            </motion.div>\n          </div>\n\n          <p className=\"hub-subtitle\">\n            Ready to shine today? ✨ Choose your learning path below.\n          </p>\n\n          <div className=\"hub-quote\">\n            <FaStar style={{ color: '#f59e0b', marginRight: '0.5rem' }} />\n            \"{inspiringQuotes[currentQuote]}\"\n            <FaStar style={{ color: '#f59e0b', marginLeft: '0.5rem' }} />\n            <div style={{ fontSize: '0.875rem', color: '#6b7280', marginTop: '0.5rem' }}>\n              - BrainWave Team\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Welcome Banner for Users with Incomplete Profiles */}\n        {!isProfileComplete() && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            className=\"mb-8 mx-4\"\n          >\n            <div className=\"bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 border border-blue-200 rounded-2xl p-6 shadow-lg\">\n              <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-full\">\n                    <FaUser className=\"text-white text-xl\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-1\">\n                      👋 Welcome to BrainWave, {user?.firstName || user?.name || 'Student'}!\n                    </h3>\n                    <p className=\"text-gray-600 text-sm mb-2\">\n                      Complete your profile to unlock personalized features and track your progress\n                    </p>\n\n                    {/* Progress Bar */}\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                        <motion.div\n                          initial={{ width: 0 }}\n                          animate={{ width: `${profileCompletionPercentage()}%` }}\n                          transition={{ duration: 1, delay: 0.5 }}\n                          className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\"\n                        />\n                      </div>\n                      <span className=\"text-sm font-medium text-gray-700\">\n                        {profileCompletionPercentage()}%\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/profile')}\n                  className=\"bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2\"\n                >\n                  <FaUser className=\"text-sm\" />\n                  Complete Profile\n                </motion.button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        <div className=\"hub-grid-container\">\n          <div className=\"hub-grid\">\n            {navigationItems.map((item, index) => {\n              const IconComponent = item.icon;\n              return (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className={`hub-card hover:${item.hoverColor} ${item.color} ${\n                    item.title === 'Profile' ? 'relative ring-2 ring-blue-300 ring-opacity-50' : ''\n                  }`}\n                  onClick={() => navigate(item.path)}\n                  tabIndex={0}\n                  role=\"button\"\n                  onKeyDown={(e) => {\n                    if (e.key === 'Enter' || e.key === ' ') {\n                      navigate(item.path);\n                    }\n                  }}\n                  style={{\n                    cursor: 'pointer',\n                    touchAction: 'manipulation', // Improves touch responsiveness\n                  }}\n                >\n                  {/* Special badge for Profile */}\n                  {item.title === 'Profile' && (\n                    <motion.div\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ duration: 0.5, delay: 1 }}\n                      className=\"absolute -top-2 -right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg\"\n                    >\n                      ⭐ Start Here\n                    </motion.div>\n                  )}\n\n                  <div className=\"hub-card-icon\">\n                    <IconComponent />\n                  </div>\n\n                  <h3 className=\"hub-card-title\">\n                    {item.title}\n                    {item.title === 'Profile' && (\n                      <span className=\"ml-2 text-blue-500\">👤</span>\n                    )}\n                  </h3>\n\n                  <p className=\"hub-card-description\">\n                    {item.title === 'Profile'\n                      ? 'Complete your profile & manage subscription'\n                      : item.description\n                    }\n                  </p>\n                </motion.div>\n              );\n            })}\n          </div>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            className=\"hub-bottom-decoration\"\n          >\n            <div className=\"decoration-content\">\n              <FaGraduationCap className=\"decoration-icon animate-bounce-gentle\" />\n              <span>Your learning journey starts here!</span>\n              <FaRocket className=\"decoration-icon animate-bounce-gentle\" />\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Floating Profile Access Button for Users with Incomplete Profiles */}\n        {!isProfileComplete() && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 2 }}\n            className=\"fixed bottom-6 right-6 z-50\"\n          >\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            animate={{\n              y: [0, -10, 0],\n              boxShadow: [\n                '0 10px 30px rgba(59, 130, 246, 0.3)',\n                '0 20px 40px rgba(59, 130, 246, 0.5)',\n                '0 10px 30px rgba(59, 130, 246, 0.3)'\n              ]\n            }}\n            transition={{\n              y: { duration: 2, repeat: Infinity, ease: \"easeInOut\" },\n              boxShadow: { duration: 2, repeat: Infinity, ease: \"easeInOut\" }\n            }}\n            onClick={() => navigate('/profile')}\n            className=\"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 group\"\n            title=\"Complete Your Profile\"\n          >\n            <div className=\"flex items-center gap-2\">\n              <FaUser className=\"text-xl\" />\n              <span className=\"hidden sm:inline font-medium\">Profile</span>\n            </div>\n\n            {/* Pulsing ring effect */}\n            <motion.div\n              className=\"absolute inset-0 rounded-full border-2 border-blue-400\"\n              animate={{\n                scale: [1, 1.5, 1],\n                opacity: [1, 0, 1]\n              }}\n              transition={{\n                duration: 2,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n            />\n          </motion.button>\n        </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Hub;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAO,WAAW;AAClB,SACEC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,eAAe,EACfC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,YAAY,QACP,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACJ,IAAI,EAAE,OAAO,KAAK;IACvB,MAAMK,cAAc,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC;IACjF,OAAOA,cAAc,CAACC,KAAK,CAACC,KAAK,IAAIP,IAAI,CAACO,KAAK,CAAC,IAAIP,IAAI,CAACO,KAAK,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;EAC3F,CAAC;EAED,MAAMC,2BAA2B,GAAGA,CAAA,KAAM;IACxC,IAAI,CAACV,IAAI,EAAE,OAAO,CAAC;IACnB,MAAMW,MAAM,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC;IAClF,MAAMC,eAAe,GAAGD,MAAM,CAACE,MAAM,CAACN,KAAK,IAAIP,IAAI,CAACO,KAAK,CAAC,IAAIP,IAAI,CAACO,KAAK,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IACnG,OAAOK,IAAI,CAACC,KAAK,CAAEH,eAAe,CAACI,MAAM,GAAGL,MAAM,CAACK,MAAM,GAAI,GAAG,CAAC;EACnE,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAvC,OAAO,CAACwC,OAAO,CAAC,0BAA0B,CAAC;;IAE3C;IACArB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,MAAMsB,eAAe,GAAG,CACtB,+FAA+F,EAC/F,4FAA4F,EAC5F,4GAA4G,EAC5G,6CAA6C,EAC7C,6CAA6C,EAC7C,2BAA2B,CAC5B;EAED7C,SAAS,CAAC,MAAM;IACd,MAAM8C,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCpB,eAAe,CAAEqB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,eAAe,CAACL,MAAM,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMS,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE/C,gBAAgB;IACtBgD,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE9C,MAAM;IACZ+C,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE7C,WAAW;IACjB8C,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,6BAA6B;IACpCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAEvC,QAAQ;IACdwC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE5C,MAAM;IACZ6C,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,+BAA+B;IACtCC,UAAU,EAAE;EACd,CAAC,EACD;IACEL,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE3C,UAAU;IAChB4C,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,EAED;IACEL,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,EAAEzC,YAAY;IAClB0C,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,2BAA2B;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACEpC,OAAA;IAAKqC,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BtC,OAAA;MAAKqC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAG1BtC,OAAA,CAACjB,MAAM,CAACwD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAKtBtC,OAAA;UAAKqC,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBAIjEtC,OAAA,CAACjB,MAAM,CAACwD,GAAG;YACTF,SAAS,EAAC,8BAA8B;YACxCS,KAAK,EAAE;cACLC,UAAU,EAAE,+GAA+G;cAC3H9B,MAAM,EAAE;YACV,CAAE;YACF0B,OAAO,EAAE;cACPK,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBP,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;cACxBQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YACtB,CAAE;YACFL,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXK,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGFxD,OAAA,CAACjB,MAAM,CAACwD,GAAG;YACTF,SAAS,EAAC,2BAA2B;YACrCG,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,gBAG9BtC,OAAA,CAACjB,MAAM,CAACwD,GAAG;cACTF,SAAS,EAAC,4BAA4B;cACtCG,OAAO,EAAE;gBAAEiB,CAAC,EAAE,CAAC,GAAG;gBAAEhB,OAAO,EAAE,CAAC;gBAAEiB,OAAO,EAAE,CAAC;cAAG,CAAE;cAC/Cf,OAAO,EAAE;gBACPc,CAAC,EAAE,CAAC;gBACJhB,OAAO,EAAE,CAAC;gBACViB,OAAO,EAAE;cACX,CAAE;cACFd,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbc,KAAK,EAAE,GAAG;gBACVP,IAAI,EAAE;cACR,CAAE;cAAAd,QAAA,eAEFtC,OAAA,CAACjB,MAAM,CAAC6E,IAAI;gBACVvB,SAAS,EAAC,kEAAkE;gBAC5ES,KAAK,EAAE;kBACLC,UAAU,EAAE,6EAA6E;kBACzFc,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE,WAAW;kBAC3BC,UAAU,EAAE,kDAAkD;kBAC9DC,aAAa,EAAE,SAAS;kBACxBC,UAAU,EAAE;gBACd,CAAE;gBACFxB,OAAO,EAAE;kBACPyB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;kBACpD1B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACbyB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACFvB,UAAU,EAAE;kBACVwB,kBAAkB,EAAE;oBAAEvB,QAAQ,EAAE,CAAC;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBACxEV,CAAC,EAAE;oBAAEG,QAAQ,EAAE,CAAC;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBACvDe,UAAU,EAAE;oBAAEtB,QAAQ,EAAE,CAAC;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY;gBACjE,CAAE;gBACFiB,UAAU,EAAE;kBACVrB,KAAK,EAAE,IAAI;kBACXsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACtB1B,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI;gBAC9B,CAAE;gBAAAP,QAAA,EACH;cAED;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGJ,CAAC,eAGbxD,OAAA,CAACjB,MAAM,CAACwD,GAAG;cACTF,SAAS,EAAC,uBAAuB;cACjCG,OAAO,EAAE;gBAAEiB,CAAC,EAAE,GAAG;gBAAEhB,OAAO,EAAE,CAAC;gBAAEiB,OAAO,EAAE;cAAG,CAAE;cAC7Cf,OAAO,EAAE;gBACPc,CAAC,EAAE,CAAC;gBACJhB,OAAO,EAAE,CAAC;gBACViB,OAAO,EAAE;cACX,CAAE;cACFd,UAAU,EAAE;gBACVC,QAAQ,EAAE,GAAG;gBACbc,KAAK,EAAE,CAAC;gBACRP,IAAI,EAAE;cACR,CAAE;cAAAd,QAAA,eAEFtC,OAAA,CAACjB,MAAM,CAAC6E,IAAI;gBACVvB,SAAS,EAAC,kEAAkE;gBAC5ES,KAAK,EAAE;kBACLC,UAAU,EAAE,6EAA6E;kBACzFc,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE,WAAW;kBAC3BC,UAAU,EAAE,kDAAkD;kBAC9DC,aAAa,EAAE,SAAS;kBACxBC,UAAU,EAAE;gBACd,CAAE;gBACFxB,OAAO,EAAE;kBACPyB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;kBACpD1B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBACZyB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACFvB,UAAU,EAAE;kBACVwB,kBAAkB,EAAE;oBAAEvB,QAAQ,EAAE,CAAC;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE,WAAW;oBAAEO,KAAK,EAAE;kBAAE,CAAC;kBAClFjB,CAAC,EAAE;oBAAEG,QAAQ,EAAE,GAAG;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE,WAAW;oBAAEO,KAAK,EAAE;kBAAI,CAAC;kBACrEQ,UAAU,EAAE;oBAAEtB,QAAQ,EAAE,GAAG;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE,WAAW;oBAAEO,KAAK,EAAE;kBAAE;gBAC7E,CAAE;gBACFU,UAAU,EAAE;kBACVrB,KAAK,EAAE,IAAI;kBACXsB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBACtB1B,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI;gBAC9B,CAAE;gBAAAP,QAAA,EACH;cAED;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGJ,CAAC,eAGbxD,OAAA,CAACjB,MAAM,CAACwD,GAAG;cACTF,SAAS,EAAC,eAAe;cACzBG,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAI,CAAE;cAC3CL,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;gBAAEM,KAAK,EAAE;cAAE,CAAE;cACxCJ,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEc,KAAK,EAAE;cAAE,CAAE;cAAArB,QAAA,eAExCtC,OAAA,CAACjB,MAAM,CAAC6E,IAAI;gBACVvB,SAAS,EAAC,sCAAsC;gBAChDS,KAAK,EAAE;kBACLC,UAAU,EAAE,4DAA4D;kBACxEc,oBAAoB,EAAE,MAAM;kBAC5BC,mBAAmB,EAAE,aAAa;kBAClCC,cAAc,EAAE,MAAM;kBACtBC,cAAc,EAAE,WAAW;kBAC3BG,UAAU,EAAE;gBACd,CAAE;gBACFxB,OAAO,EAAE;kBACPyB,kBAAkB,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;kBACpDpB,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;kBACnBmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACFvB,UAAU,EAAE;kBACVwB,kBAAkB,EAAE;oBAAEvB,QAAQ,EAAE,CAAC;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBACxEJ,KAAK,EAAE;oBAAEH,QAAQ,EAAE,CAAC;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY,CAAC;kBAC3De,UAAU,EAAE;oBAAEtB,QAAQ,EAAE,CAAC;oBAAEK,MAAM,EAAEC,QAAQ;oBAAEC,IAAI,EAAE;kBAAY;gBACjE,CAAE;gBACFiB,UAAU,EAAE;kBACVrB,KAAK,EAAE,GAAG;kBACVC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;kBACrBL,UAAU,EAAE;oBAAEC,QAAQ,EAAE;kBAAI;gBAC9B,CAAE;gBAAAP,QAAA,GAEDlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,EAAC,GACd;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGJ,CAAC,eAGbxD,OAAA,CAACjB,MAAM,CAACwD,GAAG;cACTF,SAAS,EAAC,eAAe;cACzBG,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBE,OAAO,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cACxBG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEc,KAAK,EAAE;cAAI,CAAE;cAAArB,QAAA,eAExCtC,OAAA,CAACjB,MAAM,CAACwD,GAAG;gBACTF,SAAS,EAAC,mDAAmD;gBAC7DS,KAAK,EAAE;kBACL0B,KAAK,EAAE,KAAK;kBACZzB,UAAU,EAAE,qEAAqE;kBACjF0B,SAAS,EAAE;gBACb,CAAE;gBACF9B,OAAO,EAAE;kBACP8B,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;gBAEtC,CAAE;gBACF7B,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXK,MAAM,EAAEC,QAAQ;kBAChBC,IAAI,EAAE;gBACR,CAAE;gBAAAd,QAAA,eAGFtC,OAAA,CAACjB,MAAM,CAACwD,GAAG;kBACTF,SAAS,EAAC,+BAA+B;kBACzCS,KAAK,EAAE;oBACLC,UAAU,EAAE,yEAAyE;oBACrFyB,KAAK,EAAE;kBACT,CAAE;kBACF7B,OAAO,EAAE;oBACPc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;kBACrB,CAAE;kBACFb,UAAU,EAAE;oBACVC,QAAQ,EAAE,CAAC;oBACXK,MAAM,EAAEC,QAAQ;oBAChBC,IAAI,EAAE,WAAW;oBACjBO,KAAK,EAAE;kBACT;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENxD,OAAA;UAAGqC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE5B;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJxD,OAAA;UAAKqC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtC,OAAA,CAACL,MAAM;YAACmD,KAAK,EAAE;cAAEX,KAAK,EAAE,SAAS;cAAEuC,WAAW,EAAE;YAAS;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,MAC7D,EAAC/B,eAAe,CAACnB,YAAY,CAAC,EAAC,IAChC,eAAAN,OAAA,CAACL,MAAM;YAACmD,KAAK,EAAE;cAAEX,KAAK,EAAE,SAAS;cAAEwC,UAAU,EAAE;YAAS;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DxD,OAAA;YAAK8C,KAAK,EAAE;cAAE8B,QAAQ,EAAE,UAAU;cAAEzC,KAAK,EAAE,SAAS;cAAE0C,SAAS,EAAE;YAAS,CAAE;YAAAvC,QAAA,EAAC;UAE7E;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZ,CAAChD,iBAAiB,CAAC,CAAC,iBACnBR,OAAA,CAACjB,MAAM,CAACwD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEc,KAAK,EAAE;QAAI,CAAE;QAC1CtB,SAAS,EAAC,WAAW;QAAAC,QAAA,eAErBtC,OAAA;UAAKqC,SAAS,EAAC,2GAA2G;UAAAC,QAAA,eACxHtC,OAAA;YAAKqC,SAAS,EAAC,8DAA8D;YAAAC,QAAA,gBAC3EtC,OAAA;cAAKqC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCtC,OAAA;gBAAKqC,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,eAC5EtC,OAAA,CAACX,MAAM;kBAACgD,SAAS,EAAC;gBAAoB;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACNxD,OAAA;gBAAKqC,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBtC,OAAA;kBAAIqC,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,GAAC,qCAC9B,EAAC,CAAAlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,SAAS,MAAI1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,IAAI,KAAI,SAAS,EAAC,GACvE;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAGqC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAGJxD,OAAA;kBAAKqC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCtC,OAAA;oBAAKqC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eAClDtC,OAAA,CAACjB,MAAM,CAACwD,GAAG;sBACTC,OAAO,EAAE;wBAAEgC,KAAK,EAAE;sBAAE,CAAE;sBACtB7B,OAAO,EAAE;wBAAE6B,KAAK,EAAG,GAAE1D,2BAA2B,CAAC,CAAE;sBAAG,CAAE;sBACxD8B,UAAU,EAAE;wBAAEC,QAAQ,EAAE,CAAC;wBAAEc,KAAK,EAAE;sBAAI,CAAE;sBACxCtB,SAAS,EAAC;oBAA+D;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1E;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNxD,OAAA;oBAAMqC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,GAChDxB,2BAA2B,CAAC,CAAC,EAAC,GACjC;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA,CAACjB,MAAM,CAACgG,MAAM;cACZV,UAAU,EAAE;gBAAErB,KAAK,EAAE;cAAK,CAAE;cAC5BgC,QAAQ,EAAE;gBAAEhC,KAAK,EAAE;cAAK,CAAE;cAC1BiC,OAAO,EAAEA,CAAA,KAAM9E,QAAQ,CAAC,UAAU,CAAE;cACpCkC,SAAS,EAAC,wKAAwK;cAAAC,QAAA,gBAElLtC,OAAA,CAACX,MAAM;gBAACgD,SAAS,EAAC;cAAS;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAEDxD,OAAA;QAAKqC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCtC,OAAA;UAAKqC,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtBR,eAAe,CAACoD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAAClD,IAAI;YAC/B,oBACEjC,OAAA,CAACjB,MAAM,CAACwD,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEc,KAAK,EAAEyB,KAAK,GAAG;cAAI,CAAE;cAClD/C,SAAS,EAAG,kBAAiB8C,IAAI,CAAC/C,UAAW,IAAG+C,IAAI,CAAChD,KAAM,IACzDgD,IAAI,CAACpD,KAAK,KAAK,SAAS,GAAG,+CAA+C,GAAG,EAC9E,EAAE;cACHkD,OAAO,EAAEA,CAAA,KAAM9E,QAAQ,CAACgF,IAAI,CAACjD,IAAI,CAAE;cACnCoD,QAAQ,EAAE,CAAE;cACZC,IAAI,EAAC,QAAQ;cACbC,SAAS,EAAGC,CAAC,IAAK;gBAChB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;kBACtCvF,QAAQ,CAACgF,IAAI,CAACjD,IAAI,CAAC;gBACrB;cACF,CAAE;cACFY,KAAK,EAAE;gBACL6C,MAAM,EAAE,SAAS;gBACjBC,WAAW,EAAE,cAAc,CAAE;cAC/B,CAAE;cAAAtD,QAAA,GAGD6C,IAAI,CAACpD,KAAK,KAAK,SAAS,iBACvB/B,OAAA,CAACjB,MAAM,CAACwD,GAAG;gBACTC,OAAO,EAAE;kBAAEQ,KAAK,EAAE;gBAAE,CAAE;gBACtBL,OAAO,EAAE;kBAAEK,KAAK,EAAE;gBAAE,CAAE;gBACtBJ,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEc,KAAK,EAAE;gBAAE,CAAE;gBACxCtB,SAAS,EAAC,oIAAoI;gBAAAC,QAAA,EAC/I;cAED;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb,eAEDxD,OAAA;gBAAKqC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BtC,OAAA,CAACqF,aAAa;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eAENxD,OAAA;gBAAIqC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC3B6C,IAAI,CAACpD,KAAK,EACVoD,IAAI,CAACpD,KAAK,KAAK,SAAS,iBACvB/B,OAAA;kBAAMqC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAELxD,OAAA;gBAAGqC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChC6C,IAAI,CAACpD,KAAK,KAAK,SAAS,GACrB,6CAA6C,GAC7CoD,IAAI,CAACnD;cAAW;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CAAC;YAAA,GAhDC2B,IAAI,CAACpD,KAAK;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiDL,CAAC;UAEjB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxD,OAAA,CAACjB,MAAM,CAACwD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEc,KAAK,EAAE;UAAI,CAAE;UAC1CtB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAEjCtC,OAAA;YAAKqC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCtC,OAAA,CAACP,eAAe;cAAC4C,SAAS,EAAC;YAAuC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrExD,OAAA;cAAAsC,QAAA,EAAM;YAAkC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CxD,OAAA,CAACJ,QAAQ;cAACyC,SAAS,EAAC;YAAuC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAGL,CAAChD,iBAAiB,CAAC,CAAC,iBACnBR,OAAA,CAACjB,MAAM,CAACwD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAE,CAAE;QAClCL,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAE,CAAE;QAClCJ,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEc,KAAK,EAAE;QAAE,CAAE;QACxCtB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAEzCtC,OAAA,CAACjB,MAAM,CAACgG,MAAM;UACZV,UAAU,EAAE;YAAErB,KAAK,EAAE;UAAI,CAAE;UAC3BgC,QAAQ,EAAE;YAAEhC,KAAK,EAAE;UAAI,CAAE;UACzBL,OAAO,EAAE;YACPD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACd+B,SAAS,EAAE,CACT,qCAAqC,EACrC,qCAAqC,EACrC,qCAAqC;UAEzC,CAAE;UACF7B,UAAU,EAAE;YACVF,CAAC,EAAE;cAAEG,QAAQ,EAAE,CAAC;cAAEK,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAY,CAAC;YACvDqB,SAAS,EAAE;cAAE5B,QAAQ,EAAE,CAAC;cAAEK,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAY;UAChE,CAAE;UACF6B,OAAO,EAAEA,CAAA,KAAM9E,QAAQ,CAAC,UAAU,CAAE;UACpCkC,SAAS,EAAC,wIAAwI;UAClJN,KAAK,EAAC,uBAAuB;UAAAO,QAAA,gBAE7BtC,OAAA;YAAKqC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCtC,OAAA,CAACX,MAAM;cAACgD,SAAS,EAAC;YAAS;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BxD,OAAA;cAAMqC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAGNxD,OAAA,CAACjB,MAAM,CAACwD,GAAG;YACTF,SAAS,EAAC,wDAAwD;YAClEM,OAAO,EAAE;cACPK,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBP,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,CAAE;YACFG,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXK,MAAM,EAAEC,QAAQ;cAChBC,IAAI,EAAE;YACR;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA7hBID,GAAG;EAAA,QACUpB,WAAW,EACXC,WAAW;AAAA;AAAA+G,EAAA,GAFxB5F,GAAG;AA+hBT,eAAeA,GAAG;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}