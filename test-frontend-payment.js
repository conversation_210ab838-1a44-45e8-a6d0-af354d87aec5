// Test Frontend Payment Request - Simulate exact frontend call
const axios = require('axios');

async function testFrontendPayment() {
  console.log('🧪 Testing Frontend Payment Request...\n');

  // First, let's try to login to get a real token
  const loginData = {
    username: "testuser", // You might need to use a real username
    password: "testpass"  // You might need to use a real password
  };

  console.log('🔐 Attempting login to get authentication token...');
  
  try {
    const loginResponse = await axios.post('http://localhost:5000/api/users/login', loginData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    if (loginResponse.data.success) {
      console.log('✅ Login successful');
      const token = loginResponse.data.data;
      const user = loginResponse.data.response;
      
      console.log('👤 User info:', {
        id: user._id,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phoneNumber: user.phoneNumber
      });

      // Now test payment with real token
      await testPaymentWithToken(token, user);
      
    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
      console.log('💡 Testing with mock data instead...');
      await testPaymentWithoutAuth();
    }

  } catch (error) {
    console.log('❌ Login error:', error.response?.data?.message || error.message);
    console.log('💡 Testing with mock data instead...');
    await testPaymentWithoutAuth();
  }
}

async function testPaymentWithToken(token, user) {
  console.log('\n🔄 Testing payment with real authentication...');
  
  // Sample plan data (similar to what frontend sends)
  const paymentData = {
    plan: {
      _id: "sample_plan_id",
      title: "Premium Plan",
      discountedPrice: 5000,
      duration: 1,
      features: ["Feature 1", "Feature 2"]
    },
    userId: user._id,
    userPhone: user.phoneNumber,
    userEmail: user.email
  };

  console.log('📤 Payment data being sent:');
  console.log(JSON.stringify(paymentData, null, 2));

  try {
    const response = await axios.post('http://localhost:5000/api/payment/create-invoice', paymentData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      timeout: 30000
    });

    console.log('✅ Payment request successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ Payment request failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 400) {
        console.log('\n🔍 This is the validation error we need to fix!');
        console.log('Error Type:', error.response.data.errorType);
        console.log('Message:', error.response.data.message);
      }
    } else {
      console.log('Network error:', error.message);
    }
  }
}

async function testPaymentWithoutAuth() {
  console.log('\n🔄 Testing payment validation without authentication...');
  
  const paymentData = {
    plan: {
      _id: "sample_plan_id",
      title: "Premium Plan",
      discountedPrice: 5000,
      duration: 1
    },
    userId: "sample_user_id",
    userPhone: "0712345678",
    userEmail: "<EMAIL>"
  };

  console.log('📤 Mock payment data:');
  console.log(JSON.stringify(paymentData, null, 2));

  try {
    const response = await axios.post('http://localhost:5000/api/payment/create-invoice', paymentData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid_token'
      },
      timeout: 10000
    });

    console.log('✅ Unexpected success:', response.data);

  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Authentication error (expected)');
      console.log('💡 The endpoint is working, authentication is required');
    } else {
      console.log('❌ Unexpected error:', error.response?.data || error.message);
    }
  }
}

// Test with different user scenarios
async function testUserScenarios() {
  console.log('\n🧪 Testing different user data scenarios...\n');
  
  const scenarios = [
    {
      name: "User with name field",
      user: {
        _id: "user1",
        name: "John Doe",
        firstName: "John",
        lastName: "Doe",
        phoneNumber: "0712345678",
        email: "<EMAIL>"
      }
    },
    {
      name: "User with only firstName/lastName",
      user: {
        _id: "user2",
        name: null,
        firstName: "Jane",
        lastName: "Smith",
        phoneNumber: "0712345679",
        email: "<EMAIL>"
      }
    },
    {
      name: "User with missing phone",
      user: {
        _id: "user3",
        name: "Bob Wilson",
        firstName: "Bob",
        lastName: "Wilson",
        phoneNumber: null,
        email: "<EMAIL>"
      }
    },
    {
      name: "User with missing email",
      user: {
        _id: "user4",
        name: "Alice Brown",
        firstName: "Alice",
        lastName: "Brown",
        phoneNumber: "0712345680",
        email: null
      }
    }
  ];

  scenarios.forEach(scenario => {
    console.log(`📋 ${scenario.name}:`);
    
    // Simulate the name computation logic
    let userName = scenario.user.name;
    if (!userName && scenario.user.firstName && scenario.user.lastName) {
      userName = `${scenario.user.firstName} ${scenario.user.lastName}`;
    } else if (!userName && scenario.user.firstName) {
      userName = scenario.user.firstName;
    }

    console.log(`  👤 Computed name: "${userName}"`);
    console.log(`  📱 Phone: ${scenario.user.phoneNumber || 'MISSING'}`);
    console.log(`  📧 Email: ${scenario.user.email || 'MISSING'}`);
    
    // Check validation
    const issues = [];
    if (!scenario.user.phoneNumber) issues.push('Missing phone number');
    if (!userName || userName.trim() === '') issues.push('Missing name');
    
    if (issues.length > 0) {
      console.log(`  ❌ Issues: ${issues.join(', ')}`);
    } else {
      console.log(`  ✅ Valid for payment`);
    }
    console.log('');
  });
}

// Main function
async function runTest() {
  console.log('🚀 Starting Frontend Payment Test...\n');
  
  await testFrontendPayment();
  await testUserScenarios();
  
  console.log('✅ Test completed!');
  console.log('\n💡 Next steps:');
  console.log('1. Check server logs for detailed validation messages');
  console.log('2. Ensure user has valid phone number and name');
  console.log('3. Try payment in the actual frontend');
}

runTest().catch(console.error);
