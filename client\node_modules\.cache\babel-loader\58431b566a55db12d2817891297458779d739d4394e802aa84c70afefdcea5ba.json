{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"glimp-plan-sample\",\n    title: \"Glimp Plan\",\n    features: [\"1-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 15000,\n    discountedPrice: 13000,\n    discountPercentage: 13,\n    duration: 1,\n    status: true\n  }, {\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n  const handlePlanSelect = async plan => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      setSelectedPlan(plan);\n      setPaymentLoading(true);\n      setShowProcessingModal(true);\n      setPaymentStatus('Initiating payment...');\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('Sending payment request...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        // Start checking payment status\n        setTimeout(() => {\n          checkPaymentConfirmation(response.order_id || 'demo_order');\n        }, 3000);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 1 second for up to 1 minute (very responsive)\n      let attempts = 0;\n      const maxAttempts = 60; // 60 attempts * 1 second = 1 minute\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {\n            // Payment confirmed immediately!\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n\n            // Show success immediately\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setShowSuccessModal(true);\n              setPaymentLoading(false);\n\n              // Refresh subscription data\n              checkCurrentSubscription();\n\n              // Show immediate success message\n              message.success({\n                content: '🎉 Payment confirmed! All features are now unlocked!',\n                duration: 5,\n                style: {\n                  marginTop: '20vh',\n                  fontSize: '16px'\n                }\n              });\n            }, 1000);\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            setPaymentStatus('Payment is taking longer than expected. Please check your phone and try again if needed.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(false);\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status in a few minutes.');\n            }, 3000);\n          } else {\n            // Continue polling with very frequent checks\n            const remainingSeconds = maxAttempts - attempts;\n            if (remainingSeconds > 30) {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${Math.ceil(remainingSeconds / 60)} minutes remaining)`);\n            } else {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${remainingSeconds} seconds remaining)`);\n            }\n            setTimeout(pollPaymentStatus, 1000); // Check every 1 second\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n          if (attempts >= maxAttempts) {\n            setShowProcessingModal(false);\n            setPaymentLoading(false);\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"current-price\",\n                    children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading,\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this), paymentLoading ? 'Processing...' : subscriptionStatus === 'active' ? 'Upgrade to This Plan' : subscriptionStatus === 'expired' ? 'Renew with This Plan' : 'Select This Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-processing-animation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"payment-status\",\n              children: paymentStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-details\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-plan-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-instructions\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\uD83D\\uDCF1 Check your phone for SMS confirmation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\uD83D\\uDCB3 Follow the instructions to complete payment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal success-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-animation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-checkmark\",\n                children: \"\\uD83C\\uDF89\\u2728\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-confetti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                color: '#52c41a',\n                fontSize: '32px',\n                fontWeight: 'bold',\n                marginBottom: '16px',\n                textAlign: 'center'\n              },\n              children: \"\\uD83C\\uDF8A Payment Successful! \\uD83C\\uDF8A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"success-message\",\n              style: {\n                fontSize: '18px',\n                color: '#1890ff',\n                fontWeight: '500',\n                textAlign: 'center',\n                marginBottom: '24px'\n              },\n              children: [\"\\uD83C\\uDF89 Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"! Your subscription is now ACTIVE and all features are unlocked! \\uD83C\\uDF89\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-details\",\n              style: {\n                background: 'linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%)',\n                border: '2px solid #52c41a',\n                borderRadius: '12px',\n                padding: '20px',\n                marginBottom: '20px',\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-plan-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    color: '#389e0d',\n                    marginBottom: '12px'\n                  },\n                  children: \"\\uD83C\\uDFAF Your Premium Plan Details:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'grid',\n                    gap: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\uD83D\\uDCCB Plan:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#1890ff'\n                      },\n                      children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 562,\n                      columnNumber: 51\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u23F0 Duration:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#1890ff'\n                      },\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 54\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\uD83D\\uDCB0 Amount:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#1890ff'\n                      },\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0',\n                      fontSize: '16px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\uD83D\\uDC8E Status:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 25\n                    }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#52c41a',\n                        fontWeight: 'bold'\n                      },\n                      children: \"ACTIVE & PREMIUM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-features\",\n              style: {\n                background: '#fff7e6',\n                border: '1px solid #ffd666',\n                borderRadius: '12px',\n                padding: '20px',\n                marginBottom: '24px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  color: '#d48806',\n                  marginBottom: '16px',\n                  textAlign: 'center'\n                },\n                children: \"\\uD83D\\uDE80 Everything is now unlocked for you:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                  gap: '8px',\n                  textAlign: 'left'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Unlimited Quizzes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 74\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83E\\uDD16 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"AI Chat Assistant\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83D\\uDCDA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Study Materials\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83D\\uDCCA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Progress Tracking\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83C\\uDFA5 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Learning Videos\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '4px 0',\n                      fontSize: '15px'\n                    },\n                    children: [\"\\uD83D\\uDCAC \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Forum Access\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 75\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-actions\",\n              style: {\n                display: 'flex',\n                gap: '16px',\n                justifyContent: 'center',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"success-btn primary\",\n                onClick: () => {\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                },\n                style: {\n                  background: 'linear-gradient(45deg, #1890ff, #52c41a)',\n                  border: 'none',\n                  color: 'white',\n                  padding: '12px 24px',\n                  borderRadius: '8px',\n                  fontSize: '16px',\n                  fontWeight: 'bold',\n                  cursor: 'pointer',\n                  minWidth: '160px'\n                },\n                children: \"\\uD83C\\uDFE0 Go to Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"success-btn secondary\",\n                onClick: () => setShowSuccessModal(false),\n                style: {\n                  background: 'white',\n                  border: '2px solid #1890ff',\n                  color: '#1890ff',\n                  padding: '12px 24px',\n                  borderRadius: '8px',\n                  fontSize: '16px',\n                  fontWeight: 'bold',\n                  cursor: 'pointer',\n                  minWidth: '160px'\n                },\n                children: \"Continue Here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginTop: '20px',\n                fontSize: '14px',\n                color: '#8c8c8c',\n                fontStyle: 'italic',\n                textAlign: 'center'\n              },\n              children: \"\\uD83C\\uDF89 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 294,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"7rikL3tWJsP/hz3mgn9dXHf5mpw=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "jsxDEV", "_jsxDEV", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "console", "log", "response", "success", "data", "length", "Array", "isArray", "warn", "info", "error", "warning", "handlePlanSelect", "plan", "phoneNumber", "test", "_user$name", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "setTimeout", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "demo", "content", "style", "marginTop", "fontSize", "remainingSeconds", "Math", "ceil", "getSubscriptionStatus", "endDate", "Date", "now", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "includes", "toLocaleString", "slice", "feature", "index", "disabled", "window", "location", "href", "color", "fontWeight", "marginBottom", "textAlign", "background", "border", "borderRadius", "padding", "display", "gap", "margin", "gridTemplateColumns", "justifyContent", "flexWrap", "cursor", "min<PERSON><PERSON><PERSON>", "fontStyle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"glimp-plan-sample\",\n      title: \"Glimp Plan\",\n      features: [\n        \"1-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 15000,\n      discountedPrice: 13000,\n      discountPercentage: 13,\n      duration: 1,\n      status: true\n    },\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  const handlePlanSelect = async (plan) => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n      setSelectedPlan(plan);\n      setPaymentLoading(true);\n      setShowProcessingModal(true);\n      setPaymentStatus('Initiating payment...');\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('Sending payment request...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        // Start checking payment status\n        setTimeout(() => {\n          checkPaymentConfirmation(response.order_id || 'demo_order');\n        }, 3000);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 1 second for up to 1 minute (very responsive)\n      let attempts = 0;\n      const maxAttempts = 60; // 60 attempts * 1 second = 1 minute\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n\n          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {\n            // Payment confirmed immediately!\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n\n            // Show success immediately\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setShowSuccessModal(true);\n              setPaymentLoading(false);\n\n              // Refresh subscription data\n              checkCurrentSubscription();\n\n              // Show immediate success message\n              message.success({\n                content: '🎉 Payment confirmed! All features are now unlocked!',\n                duration: 5,\n                style: {\n                  marginTop: '20vh',\n                  fontSize: '16px'\n                }\n              });\n\n            }, 1000);\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            setPaymentStatus('Payment is taking longer than expected. Please check your phone and try again if needed.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(false);\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status in a few minutes.');\n            }, 3000);\n\n          } else {\n            // Continue polling with very frequent checks\n            const remainingSeconds = (maxAttempts - attempts);\n            if (remainingSeconds > 30) {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${Math.ceil(remainingSeconds / 60)} minutes remaining)`);\n            } else {\n              setPaymentStatus(`🔍 Checking for payment confirmation... (${remainingSeconds} seconds remaining)`);\n            }\n            setTimeout(pollPaymentStatus, 1000); // Check every 1 second\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n          if (attempts >= maxAttempts) {\n            setShowProcessingModal(false);\n            setPaymentLoading(false);\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('glimp') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <span className=\"current-price\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Upgrade to This Plan'\n                        : subscriptionStatus === 'expired'\n                          ? 'Renew with This Plan'\n                          : 'Select This Plan'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Payment Processing Modal */}\n        {showProcessingModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal\">\n              <div className=\"payment-modal-content\">\n                <div className=\"payment-processing-animation\">\n                  <div className=\"payment-spinner\"></div>\n                  <div className=\"payment-pulse\"></div>\n                </div>\n                <h3>Processing Payment</h3>\n                <p className=\"payment-status\">{paymentStatus}</p>\n                <div className=\"payment-details\">\n                  <div className=\"payment-plan-info\">\n                    <h4>{selectedPlan?.title}</h4>\n                    <p>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</p>\n                    <p>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</p>\n                  </div>\n                </div>\n                <div className=\"payment-instructions\">\n                  <p>📱 Check your phone for SMS confirmation</p>\n                  <p>💳 Follow the instructions to complete payment</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Enhanced Payment Success Modal */}\n        {showSuccessModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal success-modal\">\n              <div className=\"payment-modal-content\">\n                <div className=\"success-animation\">\n                  <div className=\"success-checkmark\">🎉✨🚀</div>\n                  <div className=\"success-confetti\"></div>\n                </div>\n                <h2 style={{\n                  color: '#52c41a',\n                  fontSize: '32px',\n                  fontWeight: 'bold',\n                  marginBottom: '16px',\n                  textAlign: 'center'\n                }}>\n                  🎊 Payment Successful! 🎊\n                </h2>\n                <p className=\"success-message\" style={{\n                  fontSize: '18px',\n                  color: '#1890ff',\n                  fontWeight: '500',\n                  textAlign: 'center',\n                  marginBottom: '24px'\n                }}>\n                  🎉 Welcome to {selectedPlan?.title}! Your subscription is now ACTIVE and all features are unlocked! 🎉\n                </p>\n\n                <div className=\"success-details\" style={{\n                  background: 'linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%)',\n                  border: '2px solid #52c41a',\n                  borderRadius: '12px',\n                  padding: '20px',\n                  marginBottom: '20px',\n                  textAlign: 'center'\n                }}>\n                  <div className=\"success-plan-info\">\n                    <h3 style={{ color: '#389e0d', marginBottom: '12px' }}>\n                      🎯 Your Premium Plan Details:\n                    </h3>\n                    <div style={{ display: 'grid', gap: '8px' }}>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>📋 Plan:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.title}</span>\n                      </p>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>⏰ Duration:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</span>\n                      </p>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>💰 Amount:</strong> <span style={{ color: '#1890ff' }}>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</span>\n                      </p>\n                      <p style={{ margin: '0', fontSize: '16px' }}>\n                        <strong>💎 Status:</strong> <span style={{ color: '#52c41a', fontWeight: 'bold' }}>ACTIVE & PREMIUM</span>\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"success-features\" style={{\n                  background: '#fff7e6',\n                  border: '1px solid #ffd666',\n                  borderRadius: '12px',\n                  padding: '20px',\n                  marginBottom: '24px'\n                }}>\n                  <h3 style={{ color: '#d48806', marginBottom: '16px', textAlign: 'center' }}>\n                    🚀 Everything is now unlocked for you:\n                  </h3>\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '8px',\n                    textAlign: 'left'\n                  }}>\n                    <div>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>✅ <strong>Unlimited Quizzes</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>🤖 <strong>AI Chat Assistant</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>📚 <strong>Study Materials</strong></p>\n                    </div>\n                    <div>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>📊 <strong>Progress Tracking</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>🎥 <strong>Learning Videos</strong></p>\n                      <p style={{ margin: '4px 0', fontSize: '15px' }}>💬 <strong>Forum Access</strong></p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"success-actions\" style={{\n                  display: 'flex',\n                  gap: '16px',\n                  justifyContent: 'center',\n                  flexWrap: 'wrap'\n                }}>\n                  <button\n                    className=\"success-btn primary\"\n                    onClick={() => {\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                    style={{\n                      background: 'linear-gradient(45deg, #1890ff, #52c41a)',\n                      border: 'none',\n                      color: 'white',\n                      padding: '12px 24px',\n                      borderRadius: '8px',\n                      fontSize: '16px',\n                      fontWeight: 'bold',\n                      cursor: 'pointer',\n                      minWidth: '160px'\n                    }}\n                  >\n                    🏠 Go to Dashboard\n                  </button>\n                  <button\n                    className=\"success-btn secondary\"\n                    onClick={() => setShowSuccessModal(false)}\n                    style={{\n                      background: 'white',\n                      border: '2px solid #1890ff',\n                      color: '#1890ff',\n                      padding: '12px 24px',\n                      borderRadius: '8px',\n                      fontSize: '16px',\n                      fontWeight: 'bold',\n                      cursor: 'pointer',\n                      minWidth: '160px'\n                    }}\n                  >\n                    Continue Here\n                  </button>\n                </div>\n\n                <p style={{\n                  marginTop: '20px',\n                  fontSize: '14px',\n                  color: '#8c8c8c',\n                  fontStyle: 'italic',\n                  textAlign: 'center'\n                }}>\n                  🎉 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies!\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEsC;EAAK,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGtC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwC,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDlD,SAAS,CAAC,MAAM;IACdmD,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChB2B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMC,QAAQ,GAAG,MAAM5C,QAAQ,CAAC,CAAC;MACjC0C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjElC,QAAQ,CAAC+B,QAAQ,CAACE,IAAI,CAAC;QACvBJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACnE,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QACzD;QACAlC,QAAQ,CAAC+B,QAAQ,CAAC;QAClBF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;MAC1D,CAAC,MAAM;QACLF,OAAO,CAACQ,IAAI,CAAC,uCAAuC,CAAC;QACrDrC,QAAQ,CAACkB,WAAW,CAAC;QACrBtC,OAAO,CAAC0D,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDV,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C9B,QAAQ,CAACkB,WAAW,CAAC;MACrBtC,OAAO,CAAC4D,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM1C,kBAAkB,CAAC,CAAC;MAC3CwC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdV,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACvC,IAAI,CAAC7B,IAAI,CAAC8B,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC/B,IAAI,CAAC8B,WAAW,CAAC,EAAE;MACjE/D,OAAO,CAAC2D,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAM,UAAA;MACFnC,eAAe,CAACgC,IAAI,CAAC;MACrBtC,iBAAiB,CAAC,IAAI,CAAC;MACvBE,sBAAsB,CAAC,IAAI,CAAC;MAC5BM,gBAAgB,CAAC,uBAAuB,CAAC;MAEzC,MAAMkC,WAAW,GAAG;QAClBJ,IAAI,EAAEA,IAAI;QACVK,MAAM,EAAElC,IAAI,CAACM,GAAG;QAChB6B,SAAS,EAAEnC,IAAI,CAAC8B,WAAW;QAC3BM,SAAS,EAAEpC,IAAI,CAACqC,KAAK,IAAK,IAAAL,UAAA,GAAEhC,IAAI,CAACsC,IAAI,cAAAN,UAAA,uBAATA,UAAA,CAAWO,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAEDzC,gBAAgB,CAAC,4BAA4B,CAAC;MAC9C,MAAMmB,QAAQ,GAAG,MAAM3C,UAAU,CAAC0D,WAAW,CAAC;MAE9C,IAAIf,QAAQ,CAACC,OAAO,EAAE;QACpBpB,gBAAgB,CAAC,wDAAwD,CAAC;;QAE1E;QACA0C,UAAU,CAAC,MAAM;UACfC,wBAAwB,CAACxB,QAAQ,CAACyB,QAAQ,IAAI,YAAY,CAAC;QAC7D,CAAC,EAAE,IAAI,CAAC;MAEV,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC1B,QAAQ,CAACnD,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACdjC,sBAAsB,CAAC,KAAK,CAAC;MAC7B1B,OAAO,CAAC2D,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAAC3D,OAAO,CAAC;MACjDwB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMmD,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAI;MACF9C,gBAAgB,CAAC,0EAA0E,CAAC;;MAE5F;MACA,IAAI+C,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;;MAExB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QAEV,IAAI;UACF,MAAMG,cAAc,GAAG,MAAMzE,kBAAkB,CAAC;YAAEqE;UAAQ,CAAC,CAAC;UAE5D,IAAII,cAAc,CAAC9B,OAAO,KAAK8B,cAAc,CAACpC,MAAM,KAAK,WAAW,IAAIoC,cAAc,CAACC,IAAI,CAAC,EAAE;YAC5F;YACAnD,gBAAgB,CAAC,uDAAuD,CAAC;;YAEzE;YACA0C,UAAU,CAAC,MAAM;cACfhD,sBAAsB,CAAC,KAAK,CAAC;cAC7BE,mBAAmB,CAAC,IAAI,CAAC;cACzBJ,iBAAiB,CAAC,KAAK,CAAC;;cAExB;cACAwB,wBAAwB,CAAC,CAAC;;cAE1B;cACAhD,OAAO,CAACoD,OAAO,CAAC;gBACdgC,OAAO,EAAE,sDAAsD;gBAC/DvC,QAAQ,EAAE,CAAC;gBACXwC,KAAK,EAAE;kBACLC,SAAS,EAAE,MAAM;kBACjBC,QAAQ,EAAE;gBACZ;cACF,CAAC,CAAC;YAEJ,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM,IAAIR,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAhD,gBAAgB,CAAC,0FAA0F,CAAC;YAE5G0C,UAAU,CAAC,MAAM;cACfhD,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,KAAK,CAAC;cACxBxB,OAAO,CAAC4D,OAAO,CAAC,8GAA8G,CAAC;YACjI,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACA,MAAM4B,gBAAgB,GAAIR,WAAW,GAAGD,QAAS;YACjD,IAAIS,gBAAgB,GAAG,EAAE,EAAE;cACzBxD,gBAAgB,CAAE,4CAA2CyD,IAAI,CAACC,IAAI,CAACF,gBAAgB,GAAG,EAAE,CAAE,qBAAoB,CAAC;YACrH,CAAC,MAAM;cACLxD,gBAAgB,CAAE,4CAA2CwD,gBAAiB,qBAAoB,CAAC;YACrG;YACAd,UAAU,CAACO,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;UACvC;QAEF,CAAC,CAAC,OAAOtB,KAAK,EAAE;UACdV,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,IAAIoB,QAAQ,IAAIC,WAAW,EAAE;YAC3BtD,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,KAAK,CAAC;YACxBxB,OAAO,CAAC2D,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAe,UAAU,CAACO,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAP,UAAU,CAACO,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdjC,sBAAsB,CAAC,KAAK,CAAC;MAC7B1B,OAAO,CAAC2D,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAAC3D,OAAO,CAAC;MAC9DwB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMmE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIxD,gBAAgB,IAAIA,gBAAgB,CAACJ,aAAa,KAAK,MAAM,IAAII,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAM8C,OAAO,GAAG,IAAIC,IAAI,CAAC1D,gBAAgB,CAACyD,OAAO,CAAC;MAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGE,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,kBAAkB,MAAK,SAAS,IAAK5D,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAMkD,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIJ,IAAI,CAACI,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACnE,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEyD,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAAC1D,gBAAgB,CAACyD,OAAO,CAAC;IAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMU,QAAQ,GAAGX,OAAO,GAAGE,GAAG;IAC9B,MAAMU,QAAQ,GAAGf,IAAI,CAACC,IAAI,CAACa,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOd,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAED,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGJ,qBAAqB,CAAC,CAAC;EAElD,oBACE9E,OAAA;IAAK6F,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC9F,OAAA;MAAK6F,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC9F,OAAA,CAACd,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEpE,QAAQ,EAAE;QAAI,CAAE;QAC9B6D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAE/B9F,OAAA;UAAI6F,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB9F,OAAA,CAACZ,OAAO;YAACyG,SAAS,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxG,OAAA;UAAG6F,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGbxG,OAAA,CAACd,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEpE,QAAQ,EAAE,GAAG;UAAEyE,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhC9F,OAAA;UAAI6F,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDtB,kBAAkB,KAAK,QAAQ,iBAC9BlF,OAAA;UAAK6F,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC9F,OAAA;YAAK6F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC9F,OAAA,CAACV,aAAa;cAACuG,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDxG,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNxG,OAAA;YAAK6F,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC9F,OAAA;cAAK6F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9F,OAAA,CAACZ,OAAO;gBAACyG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCxG,OAAA;gBAAA8F,QAAA,GAAM,QAAM,EAAC,CAAAxE,gBAAgB,aAAhBA,gBAAgB,wBAAAnB,qBAAA,GAAhBmB,gBAAgB,CAAEoF,UAAU,cAAAvG,qBAAA,uBAA5BA,qBAAA,CAA8BwB,KAAK,KAAI,cAAc;cAAA;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNxG,OAAA;cAAK6F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9F,OAAA,CAACX,aAAa;gBAACwG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCxG,OAAA;gBAAA8F,QAAA,GAAM,WAAS,EAACX,UAAU,CAAC7D,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyD,OAAO,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNxG,OAAA;cAAK6F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9F,OAAA,CAACV,aAAa;gBAACuG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCxG,OAAA;gBAAA8F,QAAA,GAAM,kBAAgB,EAACL,gBAAgB,CAAC,CAAC;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAtB,kBAAkB,KAAK,SAAS,iBAC/BlF,OAAA;UAAK6F,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC9F,OAAA;YAAK6F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC9F,OAAA,CAACT,aAAa;cAACsG,SAAS,EAAC;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDxG,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNxG,OAAA;YAAK6F,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC9F,OAAA;cAAK6F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9F,OAAA,CAACX,aAAa;gBAACwG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCxG,OAAA;gBAAA8F,QAAA,GAAM,WAAS,EAACX,UAAU,CAAC7D,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyD,OAAO,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNxG,OAAA;cAAG6F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAtB,kBAAkB,KAAK,MAAM,iBAC5BlF,OAAA;UAAK6F,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC9F,OAAA;YAAK6F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC9F,OAAA,CAACP,MAAM;cAACoG,SAAS,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCxG,OAAA;cAAM6F,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNxG,OAAA;YAAK6F,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC9F,OAAA;cAAG6F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGbxG,OAAA,CAACd,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEpE,QAAQ,EAAE,GAAG;UAAEyE,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3B9F,OAAA;UAAI6F,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1BZ,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eACLxG,OAAA;UAAG6F,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5BZ,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEHhG,OAAO,gBACNR,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9F,OAAA;YAAK6F,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BxG,OAAA;YAAA8F,QAAA,EAAG;UAAgB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJlG,KAAK,CAACmC,MAAM,KAAK,CAAC,gBACpBzC,OAAA;UAAK6F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B9F,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCxG,OAAA;YAAA8F,QAAA,EAAI;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BxG,OAAA;YAAA8F,QAAA,EAAG;UAA6E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFxG,OAAA;YAAQ6F,SAAS,EAAC,aAAa;YAACc,OAAO,EAAEzE,UAAW;YAAA4D,QAAA,EAAC;UAErD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENxG,OAAA;UAAK6F,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBxF,KAAK,CAACsG,GAAG,CAAE3D,IAAI;YAAA,IAAA4D,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACdhH,OAAA,CAACd,MAAM,CAAC6G,GAAG;cAETkB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BrB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErB9F,OAAA;gBAAK6F,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B9F,OAAA;kBAAI6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE7C,IAAI,CAACtB;gBAAK;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAK,WAAA,GAAA5D,IAAI,CAACtB,KAAK,cAAAkF,WAAA,uBAAVA,WAAA,CAAYjD,WAAW,CAAC,CAAC,CAACwD,QAAQ,CAAC,OAAO,CAAC,kBAC1CpH,OAAA;kBAAM6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENxG,OAAA;gBAAK6F,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B9F,OAAA;kBAAK6F,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B9F,OAAA;oBAAM6F,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAAgB,qBAAA,GAAE7D,IAAI,CAACnB,eAAe,cAAAgF,qBAAA,uBAApBA,qBAAA,CAAsBO,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClFvD,IAAI,CAACpB,WAAW,GAAGoB,IAAI,CAACnB,eAAe,iBACtC9B,OAAA;oBAAM6F,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,IAAAiB,iBAAA,GAAE9D,IAAI,CAACpB,WAAW,cAAAkF,iBAAA,uBAAhBA,iBAAA,CAAkBM,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAChF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNxG,OAAA;kBAAK6F,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAE7C,IAAI,CAACjB,QAAQ,EAAC,QAAM,EAACiB,IAAI,CAACjB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC,eAENxG,OAAA;gBAAK6F,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAkB,cAAA,GAC3B/D,IAAI,CAACrB,QAAQ,cAAAoF,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7CxH,OAAA;kBAAiB6F,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvC9F,OAAA,CAACV,aAAa;oBAACuG,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1CxG,OAAA;oBAAA8F,QAAA,EAAOyB;kBAAO;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdgB,KAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxG,OAAA;gBACE6F,SAAS,EAAC,iBAAiB;gBAC3Bc,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAACC,IAAI,CAAE;gBACtCwE,QAAQ,EAAE/G,cAAe;gBAAAoF,QAAA,gBAEzB9F,OAAA,CAACR,YAAY;kBAACqG,SAAS,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpC9F,cAAc,GACX,eAAe,GACfwE,kBAAkB,KAAK,QAAQ,GAC7B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,sBAAsB,GACtB,kBAAkB;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEpB,CAAC;YAAA,GA7CJvD,IAAI,CAACvB,GAAG;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAACpF,IAAI,CAAC8B,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC/B,IAAI,CAAC8B,WAAW,CAAC,kBAC7DlD,OAAA,CAACd,MAAM,CAAC6G,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEpE,QAAQ,EAAE,GAAG;UAAEyE,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzB9F,OAAA;UAAK6F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9F,OAAA,CAACT,aAAa;YAACsG,SAAS,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CxG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAA8F,QAAA,EAAI;YAAqB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BxG,OAAA;cAAA8F,QAAA,EAAG;YAAuE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9ExG,OAAA;cACE6F,SAAS,EAAC,kBAAkB;cAC5Bc,OAAO,EAAEA,CAAA,KAAMe,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAA9B,QAAA,EAClD;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGA5F,mBAAmB,iBAClBZ,OAAA;QAAK6F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC9F,OAAA;UAAK6F,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B9F,OAAA;YAAK6F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC9F,OAAA;cAAK6F,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3C9F,OAAA;gBAAK6F,SAAS,EAAC;cAAiB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCxG,OAAA;gBAAK6F,SAAS,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNxG,OAAA;cAAA8F,QAAA,EAAI;YAAkB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BxG,OAAA;cAAG6F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAE5E;YAAa;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDxG,OAAA;cAAK6F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B9F,OAAA;gBAAK6F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9F,OAAA;kBAAA8F,QAAA,EAAK9E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW;gBAAK;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BxG,OAAA;kBAAA8F,QAAA,GAAI9E,YAAY,aAAZA,YAAY,wBAAAZ,qBAAA,GAAZY,YAAY,CAAEc,eAAe,cAAA1B,qBAAA,uBAA7BA,qBAAA,CAA+BiH,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DxG,OAAA;kBAAA8F,QAAA,GAAI9E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,EAAC,QAAM,EAAC,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxG,OAAA;cAAK6F,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC9F,OAAA;gBAAA8F,QAAA,EAAG;cAAwC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/CxG,OAAA;gBAAA8F,QAAA,EAAG;cAA8C;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA1F,gBAAgB,iBACfd,OAAA;QAAK6F,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC9F,OAAA;UAAK6F,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1C9F,OAAA;YAAK6F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC9F,OAAA;cAAK6F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9F,OAAA;gBAAK6F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CxG,OAAA;gBAAK6F,SAAS,EAAC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNxG,OAAA;cAAIwE,KAAK,EAAE;gBACTqD,KAAK,EAAE,SAAS;gBAChBnD,QAAQ,EAAE,MAAM;gBAChBoD,UAAU,EAAE,MAAM;gBAClBC,YAAY,EAAE,MAAM;gBACpBC,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAAC;YAEH;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxG,OAAA;cAAG6F,SAAS,EAAC,iBAAiB;cAACrB,KAAK,EAAE;gBACpCE,QAAQ,EAAE,MAAM;gBAChBmD,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBE,SAAS,EAAE,QAAQ;gBACnBD,YAAY,EAAE;cAChB,CAAE;cAAAjC,QAAA,GAAC,0BACa,EAAC9E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW,KAAK,EAAC,+EACrC;YAAA;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJxG,OAAA;cAAK6F,SAAS,EAAC,iBAAiB;cAACrB,KAAK,EAAE;gBACtCyD,UAAU,EAAE,mDAAmD;gBAC/DC,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfL,YAAY,EAAE,MAAM;gBACpBC,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,eACA9F,OAAA;gBAAK6F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9F,OAAA;kBAAIwE,KAAK,EAAE;oBAAEqD,KAAK,EAAE,SAAS;oBAAEE,YAAY,EAAE;kBAAO,CAAE;kBAAAjC,QAAA,EAAC;gBAEvD;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxG,OAAA;kBAAKwE,KAAK,EAAE;oBAAE6D,OAAO,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAM,CAAE;kBAAAxC,QAAA,gBAC1C9F,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,GAAG;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,gBAC1C9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAQ;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAAxG,OAAA;sBAAMwE,KAAK,EAAE;wBAAEqD,KAAK,EAAE;sBAAU,CAAE;sBAAA/B,QAAA,EAAE9E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW;oBAAK;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CAAC,eACJxG,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,GAAG;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,gBAC1C9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAW;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAAxG,OAAA;sBAAMwE,KAAK,EAAE;wBAAEqD,KAAK,EAAE;sBAAU,CAAE;sBAAA/B,QAAA,GAAE9E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,EAAC,QAAM,EAAC,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3I,CAAC,eACJxG,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,GAAG;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,gBAC1C9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAU;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAAxG,OAAA;sBAAMwE,KAAK,EAAE;wBAAEqD,KAAK,EAAE;sBAAU,CAAE;sBAAA/B,QAAA,GAAE9E,YAAY,aAAZA,YAAY,wBAAAX,sBAAA,GAAZW,YAAY,CAAEc,eAAe,cAAAzB,sBAAA,uBAA7BA,sBAAA,CAA+BgH,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1H,CAAC,eACJxG,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,GAAG;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,gBAC1C9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAU;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,eAAAxG,OAAA;sBAAMwE,KAAK,EAAE;wBAAEqD,KAAK,EAAE,SAAS;wBAAEC,UAAU,EAAE;sBAAO,CAAE;sBAAAhC,QAAA,EAAC;oBAAgB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxG,OAAA;cAAK6F,SAAS,EAAC,kBAAkB;cAACrB,KAAK,EAAE;gBACvCyD,UAAU,EAAE,SAAS;gBACrBC,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,MAAM;gBACpBC,OAAO,EAAE,MAAM;gBACfL,YAAY,EAAE;cAChB,CAAE;cAAAjC,QAAA,gBACA9F,OAAA;gBAAIwE,KAAK,EAAE;kBAAEqD,KAAK,EAAE,SAAS;kBAAEE,YAAY,EAAE,MAAM;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAlC,QAAA,EAAC;cAE5E;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxG,OAAA;gBAAKwE,KAAK,EAAE;kBACV6D,OAAO,EAAE,MAAM;kBACfG,mBAAmB,EAAE,sCAAsC;kBAC3DF,GAAG,EAAE,KAAK;kBACVN,SAAS,EAAE;gBACb,CAAE;gBAAAlC,QAAA,gBACA9F,OAAA;kBAAA8F,QAAA,gBACE9F,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,OAAO;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,GAAC,SAAE,eAAA9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzFxG,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,OAAO;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,GAAC,eAAG,eAAA9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1FxG,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,OAAO;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,GAAC,eAAG,eAAA9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACNxG,OAAA;kBAAA8F,QAAA,gBACE9F,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,OAAO;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,GAAC,eAAG,eAAA9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAiB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1FxG,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,OAAO;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,GAAC,eAAG,eAAA9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACxFxG,OAAA;oBAAGwE,KAAK,EAAE;sBAAE+D,MAAM,EAAE,OAAO;sBAAE7D,QAAQ,EAAE;oBAAO,CAAE;oBAAAoB,QAAA,GAAC,eAAG,eAAA9F,OAAA;sBAAA8F,QAAA,EAAQ;oBAAY;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENxG,OAAA;cAAK6F,SAAS,EAAC,iBAAiB;cAACrB,KAAK,EAAE;gBACtC6D,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,MAAM;gBACXG,cAAc,EAAE,QAAQ;gBACxBC,QAAQ,EAAE;cACZ,CAAE;cAAA5C,QAAA,gBACA9F,OAAA;gBACE6F,SAAS,EAAC,qBAAqB;gBAC/Bc,OAAO,EAAEA,CAAA,KAAM;kBACb5F,mBAAmB,CAAC,KAAK,CAAC;kBAC1B2G,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;gBACpC,CAAE;gBACFpD,KAAK,EAAE;kBACLyD,UAAU,EAAE,0CAA0C;kBACtDC,MAAM,EAAE,MAAM;kBACdL,KAAK,EAAE,OAAO;kBACdO,OAAO,EAAE,WAAW;kBACpBD,YAAY,EAAE,KAAK;kBACnBzD,QAAQ,EAAE,MAAM;kBAChBoD,UAAU,EAAE,MAAM;kBAClBa,MAAM,EAAE,SAAS;kBACjBC,QAAQ,EAAE;gBACZ,CAAE;gBAAA9C,QAAA,EACH;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxG,OAAA;gBACE6F,SAAS,EAAC,uBAAuB;gBACjCc,OAAO,EAAEA,CAAA,KAAM5F,mBAAmB,CAAC,KAAK,CAAE;gBAC1CyD,KAAK,EAAE;kBACLyD,UAAU,EAAE,OAAO;kBACnBC,MAAM,EAAE,mBAAmB;kBAC3BL,KAAK,EAAE,SAAS;kBAChBO,OAAO,EAAE,WAAW;kBACpBD,YAAY,EAAE,KAAK;kBACnBzD,QAAQ,EAAE,MAAM;kBAChBoD,UAAU,EAAE,MAAM;kBAClBa,MAAM,EAAE,SAAS;kBACjBC,QAAQ,EAAE;gBACZ,CAAE;gBAAA9C,QAAA,EACH;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxG,OAAA;cAAGwE,KAAK,EAAE;gBACRC,SAAS,EAAE,MAAM;gBACjBC,QAAQ,EAAE,MAAM;gBAChBmD,KAAK,EAAE,SAAS;gBAChBgB,SAAS,EAAE,QAAQ;gBACnBb,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAAC;YAEH;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtG,EAAA,CAhpBID,YAAY;EAAA,QAQCjB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAA6J,EAAA,GAVxB7I,YAAY;AAkpBlB,eAAeA,YAAY;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}