const mongoose = require('mongoose');
require('dotenv').config({ path: './server/.env' });

// Import models
const User = require('./server/models/userModel');
const Subscription = require('./server/models/subscriptionModel');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

const fixLeilaSubscription = async () => {
  console.log('🔧 Fixing Leila Dana subscription status...\n');

  try {
    await connectDB();

    // Find Leila Dana
    const leilaUser = await User.findOne({ name: '<PERSON><PERSON> Dana' });
    
    if (!leilaUser) {
      console.log('❌ Leila Dana not found');
      return;
    }

    console.log(`👤 Found user: ${leilaUser.name}`);
    console.log(`   Email: ${leilaUser.email}`);
    console.log(`   Phone: ${leilaUser.phoneNumber}`);

    // Find her subscription
    const subscription = await Subscription.findOne({
      user: leilaUser._id,
      paymentStatus: 'paid'
    }).populate('activePlan');

    if (!subscription) {
      console.log('❌ No paid subscription found for Leila Dana');
      return;
    }

    console.log('\n📋 Current Subscription:');
    console.log(`   Payment Status: ${subscription.paymentStatus}`);
    console.log(`   Status: ${subscription.status || 'MISSING!'}`);
    console.log(`   Start Date: ${subscription.startDate}`);
    console.log(`   End Date: ${subscription.endDate}`);
    console.log(`   Plan: ${subscription.activePlan?.title || 'Unknown'}`);

    // Fix the missing status field
    if (!subscription.status || subscription.status !== 'active') {
      console.log('\n🔧 Fixing subscription status...');
      
      subscription.status = 'active';
      await subscription.save();
      
      console.log('✅ Updated subscription status to "active"');
    }

    // Also update user status
    if (leilaUser.subscriptionStatus !== 'active') {
      console.log('🔧 Updating user subscription status...');
      
      leilaUser.subscriptionStatus = 'active';
      leilaUser.paymentRequired = false;
      await leilaUser.save();
      
      console.log('✅ Updated user status');
    }

    // Verify the fix
    const verifySubscription = await Subscription.findOne({
      user: leilaUser._id,
      paymentStatus: 'paid'
    }).populate('activePlan');

    console.log('\n✅ Verification - Fixed Subscription:');
    console.log(`   Payment Status: ${verifySubscription.paymentStatus}`);
    console.log(`   Status: ${verifySubscription.status}`);
    console.log(`   Start Date: ${verifySubscription.startDate}`);
    console.log(`   End Date: ${verifySubscription.endDate}`);
    console.log(`   Plan: ${verifySubscription.activePlan?.title}`);

    // Test the frontend logic
    const isSubscriptionActive = verifySubscription.paymentStatus === "paid" && 
                                verifySubscription.status === "active";
    
    console.log(`\n🎯 Frontend Logic Test:`);
    console.log(`   Payment Status: "${verifySubscription.paymentStatus}" === "paid" = ${verifySubscription.paymentStatus === "paid"}`);
    console.log(`   Status: "${verifySubscription.status}" === "active" = ${verifySubscription.status === "active"}`);
    console.log(`   Is Active: ${isSubscriptionActive ? 'YES ✅' : 'NO ❌'}`);

    if (isSubscriptionActive) {
      console.log('\n🎉 SUCCESS! Leila Dana should now see her subscription details!');
      console.log('\n📋 Expected Display:');
      console.log(`   Plan Title: ${verifySubscription.activePlan?.title}`);
      console.log(`   Start Date: ${new Date(verifySubscription.startDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })}`);
      console.log(`   End Date: ${new Date(verifySubscription.endDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })}`);
      
      // Calculate days remaining
      const endDate = new Date(verifySubscription.endDate);
      const now = new Date();
      const diffTime = endDate - now;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      console.log(`   Days Remaining: ${Math.max(0, diffDays)} days`);
    } else {
      console.log('\n❌ Still not working. Additional debugging needed.');
    }

    process.exit(0);

  } catch (error) {
    console.error('❌ Error fixing subscription:', error);
    process.exit(1);
  }
};

fixLeilaSubscription();
