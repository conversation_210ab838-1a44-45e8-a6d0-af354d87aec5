const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config({ path: './server/.env' });

// Import models
const User = require('./server/models/userModel');
const Subscription = require('./server/models/subscriptionModel');
const Plan = require('./server/models/planModel');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

const testSubscriptionDisplay = async () => {
  console.log('🧪 Testing Subscription Display Logic...\n');

  try {
    await connectDB();

    // Find users with active subscriptions
    console.log('1️⃣ Finding users with active subscriptions...');
    
    const activeSubscriptions = await Subscription.find({
      paymentStatus: 'paid',
      status: 'active'
    }).populate('user').populate('activePlan');

    console.log(`Found ${activeSubscriptions.length} active subscriptions\n`);

    if (activeSubscriptions.length === 0) {
      console.log('❌ No active subscriptions found. Creating test subscription...');
      
      // Create a test subscription for Lucy Mosha
      const lucyUser = await User.findOne({ name: 'Lucy Mosha' });
      if (!lucyUser) {
        console.log('❌ Lucy Mosha not found');
        return;
      }

      const basicPlan = await Plan.findOne({ title: /basic/i });
      if (!basicPlan) {
        console.log('❌ Basic plan not found');
        return;
      }

      const startDate = new Date();
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 2); // 2 months

      const testSubscription = new Subscription({
        user: lucyUser._id,
        activePlan: basicPlan._id,
        paymentStatus: 'paid',
        status: 'active',
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        paymentHistory: [{
          orderId: `TEST_${Date.now()}`,
          plan: basicPlan._id,
          amount: basicPlan.discountedPrice,
          paymentStatus: 'paid',
          paymentDate: startDate.toISOString().split('T')[0]
        }]
      });

      await testSubscription.save();
      console.log('✅ Created test subscription for Lucy Mosha');
      
      // Update user status
      lucyUser.subscriptionStatus = 'active';
      lucyUser.paymentRequired = false;
      await lucyUser.save();
      
      console.log('✅ Updated Lucy Mosha user status\n');
    }

    // Test the subscription display logic
    console.log('2️⃣ Testing subscription display logic...');
    
    const testUsers = await User.find({ 
      subscriptionStatus: 'active',
      paymentRequired: false 
    }).limit(3);

    for (const user of testUsers) {
      console.log(`\n👤 Testing user: ${user.name}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Phone: ${user.phoneNumber}`);
      console.log(`   Status: ${user.subscriptionStatus}`);
      console.log(`   Payment Required: ${user.paymentRequired}`);

      // Find their subscription
      const subscription = await Subscription.findOne({
        user: user._id,
        paymentStatus: 'paid',
        status: 'active'
      }).populate('activePlan');

      if (subscription) {
        console.log('   📋 Subscription Details:');
        console.log(`      Plan: ${subscription.activePlan?.title || 'Unknown'}`);
        console.log(`      Payment Status: ${subscription.paymentStatus}`);
        console.log(`      Status: ${subscription.status}`);
        console.log(`      Start Date: ${subscription.startDate}`);
        console.log(`      End Date: ${subscription.endDate}`);
        
        // Calculate days remaining
        const endDate = new Date(subscription.endDate);
        const now = new Date();
        const diffTime = endDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        console.log(`      Days Remaining: ${Math.max(0, diffDays)}`);
        
        // Test the frontend logic
        const isSubscriptionActive = subscription.paymentStatus === "paid" && 
                                    subscription.status === "active";
        console.log(`      Frontend Logic Result: ${isSubscriptionActive ? '✅ ACTIVE' : '❌ INACTIVE'}`);
      } else {
        console.log('   ❌ No active subscription found');
      }
    }

    // Test API endpoint simulation
    console.log('\n3️⃣ Testing API response format...');
    
    const sampleUser = testUsers[0];
    if (sampleUser) {
      const subscription = await Subscription.findOne({
        user: sampleUser._id,
        paymentStatus: 'paid',
        status: 'active'
      }).populate('activePlan');

      if (subscription) {
        const lastPayment = subscription.paymentHistory.slice(-1)[0];
        
        const apiResponse = {
          paymentStatus: subscription.paymentStatus,
          status: subscription.status,
          amount: lastPayment?.amount || 0,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          plan: subscription.activePlan || "No active plan found"
        };

        console.log('📡 API Response Format:');
        console.log(JSON.stringify(apiResponse, null, 2));
        
        // Test frontend display logic
        console.log('\n🎨 Frontend Display Test:');
        console.log(`Plan Title: ${apiResponse.plan?.title || 'Premium Plan'}`);
        
        const formatDate = (dateString) => {
          if (!dateString) return "Not available";
          try {
            return new Date(dateString).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            });
          } catch {
            return "Invalid date";
          }
        };

        const getDaysRemaining = (endDate) => {
          if (!endDate) return 0;
          try {
            const end = new Date(endDate);
            const now = new Date();
            const diffTime = end - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return Math.max(0, diffDays);
          } catch {
            return 0;
          }
        };

        console.log(`Start Date: ${formatDate(apiResponse.startDate)}`);
        console.log(`End Date: ${formatDate(apiResponse.endDate)}`);
        console.log(`Days Remaining: ${getDaysRemaining(apiResponse.endDate)} days`);
        console.log(`Plan Type: ${apiResponse.plan?.title || 'Premium'}`);
      }
    }

    console.log('\n🎯 Test Summary:');
    console.log('✅ Subscription data structure verified');
    console.log('✅ Date formatting logic tested');
    console.log('✅ Days remaining calculation tested');
    console.log('✅ Frontend display logic validated');
    
    console.log('\n📋 What should happen in Plans page:');
    console.log('1. Users with paid subscriptions see current plan details');
    console.log('2. Start date and end date are displayed properly');
    console.log('3. Days remaining countdown is shown');
    console.log('4. Plan benefits are listed');
    console.log('5. Action buttons for continuing learning');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
};

testSubscriptionDisplay();
