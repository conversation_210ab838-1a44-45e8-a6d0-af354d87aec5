const axios = require('axios');

const fixLucySubscription = async () => {
  try {
    console.log('🔧 Calling API to fix Lucy Mosha subscription...\n');

    // Check if server is running
    try {
      const healthResponse = await axios.get('http://localhost:5000/api/health');
      console.log('✅ Server is running:', healthResponse.data.status);
    } catch (error) {
      console.log('❌ Server is not responding. Please make sure the server is running.');
      return;
    }

    // Call the fix endpoint
    console.log('\n🔄 Calling fix endpoint...');
    
    const response = await axios.post('http://localhost:5000/api/payment/fix-lucy-subscription', {});
    
    if (response.data.success) {
      console.log('✅ SUCCESS! Lucy Mosha subscription has been fixed!');
      console.log('\n📋 Updated Details:');
      console.log('- User ID:', response.data.data.userId);
      console.log('- Name:', response.data.data.name);
      console.log('- Subscription Status:', response.data.data.subscriptionStatus);
      console.log('- Payment Required:', response.data.data.paymentRequired);
      console.log('- Plan:', response.data.data.plan);
      console.log('- Subscription End Date:', response.data.data.subscriptionEndDate);
      
      console.log('\n🎉 Lucy Mosha can now:');
      console.log('✅ Access all premium features');
      console.log('✅ Take quizzes without payment restrictions');
      console.log('✅ View study materials');
      console.log('✅ Use AI features');
      
      console.log('\n📱 Instructions for Lucy:');
      console.log('1. Refresh the browser page');
      console.log('2. Or log out and log back in');
      console.log('3. The payment loading screen should disappear');
      console.log('4. She should now have full access to the platform');
      
    } else {
      console.log('❌ Failed to fix subscription:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Error calling fix API:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
};

fixLucySubscription();
