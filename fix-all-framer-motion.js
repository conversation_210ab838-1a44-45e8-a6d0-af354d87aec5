const fs = require('fs');
const path = require('path');

const findFramerMotionFiles = (dir, files = []) => {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      findFramerMotionFiles(fullPath, files);
    } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.jsx'))) {
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        if (content.includes('framer-motion') || content.includes('motion.') || content.includes('AnimatePresence')) {
          files.push(fullPath);
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
  }
  
  return files;
};

const fixFramerMotionFiles = () => {
  console.log('🔍 Finding ALL Framer Motion files...\n');
  
  const clientDir = path.join(__dirname, 'client', 'src');
  const framerMotionFiles = findFramerMotionFiles(clientDir);
  
  console.log(`Found ${framerMotionFiles.length} files using Framer Motion:\n`);
  
  framerMotionFiles.forEach((file, index) => {
    const relativePath = path.relative(__dirname, file);
    console.log(`${index + 1}. ${relativePath}`);
    
    try {
      const content = fs.readFileSync(file, 'utf8');
      const motionMatches = content.match(/motion\./g);
      const animatePresenceMatches = content.match(/AnimatePresence/g);
      const importMatches = content.match(/from ['"]framer-motion['"]/g);
      
      console.log(`   - motion. usage: ${motionMatches ? motionMatches.length : 0}`);
      console.log(`   - AnimatePresence usage: ${animatePresenceMatches ? animatePresenceMatches.length : 0}`);
      console.log(`   - framer-motion imports: ${importMatches ? importMatches.length : 0}`);
    } catch (error) {
      console.log(`   - Error reading file: ${error.message}`);
    }
    console.log('');
  });
  
  console.log('\n🎯 CRITICAL FILES TO FIX IMMEDIATELY:');
  console.log('These are likely causing the suspension error:\n');
  
  const criticalFiles = [
    'client/src/components/modern/Loading.js',
    'client/src/components/modern/XPProgressBar.js', 
    'client/src/components/modern/LazyImage.js',
    'client/src/components/modern/XPResultDisplay.js',
    'client/src/components/modern/ErrorBoundary.js',
    'client/src/components/modern/Card.js',
    'client/src/components/modern/Button.js',
    'client/src/components/trial/TrialRegistrationPrompt.js',
    'client/src/components/AdminCard.js'
  ];
  
  criticalFiles.forEach((file, index) => {
    const fullPath = path.join(__dirname, file);
    if (fs.existsSync(fullPath)) {
      console.log(`${index + 1}. ❌ ${file} - NEEDS IMMEDIATE FIX`);
    }
  });
  
  console.log('\n🚨 IMMEDIATE ACTION REQUIRED:');
  console.log('1. Replace ALL motion.div with regular div');
  console.log('2. Replace ALL motion.button with regular button');
  console.log('3. Replace ALL motion.img with regular img');
  console.log('4. Remove ALL AnimatePresence components');
  console.log('5. Remove ALL framer-motion imports');
  console.log('6. Replace animations with CSS alternatives');
  
  console.log('\n💡 SOLUTION:');
  console.log('Create CSS-only versions of ALL these components');
  console.log('Use CSS animations instead of Framer Motion');
  console.log('This will completely eliminate suspension errors');
};

fixFramerMotionFiles();
