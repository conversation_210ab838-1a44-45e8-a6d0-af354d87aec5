// Test Payment Data Validation
const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

console.log('🧪 Testing Payment Data Validation...\n');

// Test different payment data scenarios
const testScenarios = [
  {
    name: "Valid Payment Data",
    data: {
      order_id: `ORDER_${Date.now()}_TEST123`,
      buyer_email: '<EMAIL>',
      buyer_name: '<PERSON>',
      buyer_phone: '0712345678',
      amount: 5000
    },
    shouldPass: true
  },
  {
    name: "Invalid Phone Format (9 digits)",
    data: {
      order_id: `ORDER_${Date.now()}_TEST124`,
      buyer_email: '<EMAIL>',
      buyer_name: '<PERSON>',
      buyer_phone: '071234567', // Missing one digit
      amount: 5000
    },
    shouldPass: false
  },
  {
    name: "Invalid Phone Format (starts with 08)",
    data: {
      order_id: `ORDER_${Date.now()}_TEST125`,
      buyer_email: '<EMAIL>',
      buyer_name: '<PERSON>',
      buyer_phone: '0812345678', // Should start with 06 or 07
      amount: 5000
    },
    shouldPass: false
  },
  {
    name: "Invalid Email Format",
    data: {
      order_id: `ORDER_${Date.now()}_TEST126`,
      buyer_email: 'invalid-email',
      buyer_name: 'John Doe',
      buyer_phone: '0712345678',
      amount: 5000
    },
    shouldPass: false
  },
  {
    name: "Amount Too Small",
    data: {
      order_id: `ORDER_${Date.now()}_TEST127`,
      buyer_email: '<EMAIL>',
      buyer_name: 'John Doe',
      buyer_phone: '0712345678',
      amount: 50 // Less than 100 TZS
    },
    shouldPass: false
  },
  {
    name: "Empty Name",
    data: {
      order_id: `ORDER_${Date.now()}_TEST128`,
      buyer_email: '<EMAIL>',
      buyer_name: '',
      buyer_phone: '0712345678',
      amount: 5000
    },
    shouldPass: false
  }
];

// Validation functions
function validatePhoneNumber(phone) {
  const phoneRegex = /^0[67]\d{8}$/;
  return phoneRegex.test(phone);
}

function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validatePaymentData(data) {
  const errors = [];
  
  // Validate phone
  if (!validatePhoneNumber(data.buyer_phone)) {
    errors.push('Invalid phone format (must be 06xxxxxxxx or 07xxxxxxxx)');
  }
  
  // Validate email
  if (!validateEmail(data.buyer_email)) {
    errors.push('Invalid email format');
  }
  
  // Validate name
  if (!data.buyer_name || data.buyer_name.trim().length < 2) {
    errors.push('Name must be at least 2 characters');
  }
  
  // Validate amount
  if (!data.amount || typeof data.amount !== 'number' || data.amount <= 0) {
    errors.push('Amount must be a positive number');
  }
  
  if (data.amount < 100) {
    errors.push('Amount must be at least 100 TZS');
  }
  
  // Validate order ID
  if (!data.order_id || data.order_id.length < 5) {
    errors.push('Order ID must be at least 5 characters');
  }
  
  return errors;
}

// Test ZenoPay API with validated data
async function testZenoPayAPI(data) {
  try {
    // Add webhook URL if configured
    if (process.env.ZENOPAY_WEBHOOK_URL) {
      data.webhook_url = process.env.ZENOPAY_WEBHOOK_URL;
    }
    
    console.log('📤 Sending to ZenoPay:', JSON.stringify(data, null, 2));
    
    const response = await axios.post('https://zenoapi.com/api/payments/mobile_money_tanzania', data, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 30000
    });

    return {
      success: true,
      status: response.status,
      data: response.data
    };

  } catch (error) {
    return {
      success: false,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    };
  }
}

// Run validation tests
async function runValidationTests() {
  console.log('🚀 Running Payment Data Validation Tests...\n');
  
  for (const scenario of testScenarios) {
    console.log(`📋 Testing: ${scenario.name}`);
    console.log('📤 Data:', JSON.stringify(scenario.data, null, 2));
    
    // Validate data locally first
    const validationErrors = validatePaymentData(scenario.data);
    
    if (validationErrors.length > 0) {
      console.log('❌ Local validation failed:');
      validationErrors.forEach(error => console.log(`  - ${error}`));
      
      if (scenario.shouldPass) {
        console.log('⚠️ Expected to pass but failed local validation!');
      } else {
        console.log('✅ Expected to fail and failed as expected');
      }
    } else {
      console.log('✅ Local validation passed');
      
      if (!scenario.shouldPass) {
        console.log('⚠️ Expected to fail but passed local validation!');
      }
      
      // Test with ZenoPay API if validation passes
      console.log('🔄 Testing with ZenoPay API...');
      const apiResult = await testZenoPayAPI({...scenario.data});
      
      if (apiResult.success) {
        console.log('✅ ZenoPay API accepted the data');
        console.log('📥 Response:', JSON.stringify(apiResult.data, null, 2));
      } else {
        console.log('❌ ZenoPay API rejected the data');
        console.log('📥 Error:', apiResult.message);
        if (apiResult.data) {
          console.log('📥 Response:', JSON.stringify(apiResult.data, null, 2));
        }
      }
    }
    
    console.log('─'.repeat(60));
  }
}

// Check environment first
if (!process.env.ZENOPAY_API_KEY) {
  console.error('❌ ZENOPAY_API_KEY not found in environment variables');
  console.error('Please check your server/.env file');
  process.exit(1);
}

console.log('📋 Environment Check:');
console.log('✓ ZENOPAY_API_KEY:', process.env.ZENOPAY_API_KEY ? 'SET' : 'MISSING');
console.log('✓ ZENOPAY_WEBHOOK_URL:', process.env.ZENOPAY_WEBHOOK_URL || 'NOT SET');
console.log('');

runValidationTests().catch(console.error);
