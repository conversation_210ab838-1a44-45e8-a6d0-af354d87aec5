<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainWave Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007BFF;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 BrainWave Connection Test</h1>
        
        <div class="info">
            <h3>📋 Expected Configuration:</h3>
            <ul>
                <li><strong>Server (Backend):</strong> http://localhost:5000</li>
                <li><strong>Client (Frontend):</strong> http://localhost:3000</li>
                <li><strong>Database:</strong> MongoDB Atlas</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🖥️ Server Status (Port 5000)</h3>
            <div id="server-status" class="status loading">Testing server connection...</div>
            <button onclick="testServer()">Test Server</button>
            <button onclick="window.open('http://localhost:5000', '_blank')">Open Server</button>
        </div>

        <div class="test-section">
            <h3>🌐 Client Status (Port 3000)</h3>
            <div id="client-status" class="status loading">Testing client connection...</div>
            <button onclick="testClient()">Test Client</button>
            <button onclick="window.open('http://localhost:3000', '_blank')">Open Client</button>
        </div>

        <div class="test-section">
            <h3>🗄️ Database Status</h3>
            <div id="db-status" class="status loading">Testing database connection...</div>
            <button onclick="testDatabase()">Test Database</button>
        </div>

        <div class="test-section">
            <h3>🔗 API Health Check</h3>
            <div id="api-status" class="status loading">Testing API endpoints...</div>
            <button onclick="testAPI()">Test API</button>
        </div>
    </div>

    <script>
        // Test server connection
        async function testServer() {
            const statusDiv = document.getElementById('server-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Testing server connection...';
            
            try {
                const response = await fetch('http://localhost:5000');
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Server is running on port 5000';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Server responded with status: ${response.status}`;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Cannot connect to server: ${error.message}`;
            }
        }

        // Test client connection
        async function testClient() {
            const statusDiv = document.getElementById('client-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Testing client connection...';
            
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Client is running on port 3000';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Client responded with status: ${response.status}`;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Cannot connect to client: ${error.message}`;
            }
        }

        // Test database connection
        async function testDatabase() {
            const statusDiv = document.getElementById('db-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Testing database connection...';
            
            try {
                const response = await fetch('http://localhost:5000/api/test/db');
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Database connection successful';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Database test failed with status: ${response.status}`;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Database connection error: ${error.message}`;
            }
        }

        // Test API endpoints
        async function testAPI() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = 'Testing API endpoints...';
            
            try {
                const response = await fetch('http://localhost:5000/api/health');
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ API Health Check: ${data.message || 'OK'}`;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ API health check failed with status: ${response.status}`;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ API connection error: ${error.message}`;
            }
        }

        // Auto-run tests when page loads
        window.onload = function() {
            setTimeout(() => {
                testServer();
                setTimeout(() => testClient(), 1000);
                setTimeout(() => testDatabase(), 2000);
                setTimeout(() => testAPI(), 3000);
            }, 500);
        };
    </script>
</body>
</html>
