<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Update Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007BFF;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Profile Update Test</h1>
        
        <div class="test-section">
            <h3>🔐 Authentication Test</h3>
            <div id="auth-status" class="status loading">Not tested</div>
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="email" placeholder="Enter your email">
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="password" placeholder="Enter your password">
            </div>
            <button onclick="testLogin()">Test Login</button>
        </div>

        <div class="test-section">
            <h3>👤 Profile Update Test</h3>
            <div id="update-status" class="status loading">Login first</div>
            
            <div class="form-group">
                <label>Name:</label>
                <input type="text" id="name" value="Test User Updated">
            </div>
            <div class="form-group">
                <label>School:</label>
                <input type="text" id="school" value="Test School">
            </div>
            <div class="form-group">
                <label>Level:</label>
                <select id="level">
                    <option value="">Select Level</option>
                    <option value="Primary">Primary</option>
                    <option value="Secondary">Secondary</option>
                    <option value="Advance">Advance</option>
                </select>
            </div>
            <div class="form-group">
                <label>Class:</label>
                <select id="class">
                    <option value="">Select Class</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    <option value="7">7</option>
                </select>
            </div>
            <div class="form-group">
                <label>Phone Number:</label>
                <input type="tel" id="phoneNumber" value="1234567890">
            </div>
            
            <button onclick="testProfileUpdate()" id="updateBtn" disabled>Test Profile Update</button>
        </div>

        <div class="test-section">
            <h3>📋 Debug Log</h3>
            <div id="debug-log" class="log">
                Ready to test...<br>
            </div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        let authToken = null;
        let userId = null;

        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = 'Log cleared...<br>';
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const statusDiv = document.getElementById('auth-status');
            
            if (!email || !password) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Please enter email and password';
                return;
            }

            statusDiv.className = 'status loading';
            statusDiv.textContent = '🔄 Testing login...';
            log(`Attempting login with email: ${email}`);

            try {
                const response = await fetch('http://localhost:5000/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                log(`Login response: ${JSON.stringify(data, null, 2)}`);

                if (data.success) {
                    authToken = data.data.token;
                    userId = data.data.user._id;
                    
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Login successful';
                    
                    document.getElementById('updateBtn').disabled = false;
                    document.getElementById('email').value = data.data.user.email;
                    
                    log(`✅ Login successful. Token: ${authToken.substring(0, 20)}...`);
                    log(`User ID: ${userId}`);
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Login failed: ${data.message}`;
                    log(`❌ Login failed: ${data.message}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Login error: ${error.message}`;
                log(`❌ Login error: ${error.message}`);
            }
        }

        async function testProfileUpdate() {
            if (!authToken || !userId) {
                log('❌ No auth token or user ID. Please login first.');
                return;
            }

            const statusDiv = document.getElementById('update-status');
            statusDiv.className = 'status loading';
            statusDiv.textContent = '🔄 Testing profile update...';

            const updateData = {
                userId: userId,
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                school: document.getElementById('school').value,
                level: document.getElementById('level').value,
                class_: document.getElementById('class').value,
                phoneNumber: document.getElementById('phoneNumber').value
            };

            log(`📤 Sending update data: ${JSON.stringify(updateData, null, 2)}`);

            try {
                const response = await fetch('http://localhost:5000/api/users/update-user-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(updateData)
                });

                const data = await response.json();
                log(`📥 Update response (${response.status}): ${JSON.stringify(data, null, 2)}`);

                if (response.ok && data.success) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Profile updated successfully';
                    log('✅ Profile update successful');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Update failed: ${data.message || 'Unknown error'}`;
                    log(`❌ Update failed (${response.status}): ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Update error: ${error.message}`;
                log(`❌ Update error: ${error.message}`);
            }
        }

        // Auto-populate level-based class options
        document.getElementById('level').addEventListener('change', function() {
            const level = this.value;
            const classSelect = document.getElementById('class');
            
            classSelect.innerHTML = '<option value="">Select Class</option>';
            
            if (level === 'Primary') {
                for (let i = 1; i <= 7; i++) {
                    classSelect.innerHTML += `<option value="${i}">${i}</option>`;
                }
            } else if (level === 'Secondary') {
                for (let i = 1; i <= 4; i++) {
                    classSelect.innerHTML += `<option value="${i}">${i}</option>`;
                }
            } else if (level === 'Advance') {
                for (let i = 5; i <= 6; i++) {
                    classSelect.innerHTML += `<option value="${i}">${i}</option>`;
                }
            }
        });
    </script>
</body>
</html>
