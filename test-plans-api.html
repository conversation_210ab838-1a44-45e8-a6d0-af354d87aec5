<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plans API Test</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .plan-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid #e5e7eb;
            transition: all 0.3s;
        }
        .plan-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
        }
        .plan-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
        }
        .plan-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        .plan-duration {
            color: #6b7280;
            margin-bottom: 15px;
        }
        .plan-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 5px;
        }
        .feature-item {
            color: #374151;
            font-size: 0.9rem;
            padding: 2px 0;
        }
        .test-btn {
            background: #4f46e5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s;
        }
        .test-btn:hover {
            background: #4338ca;
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: 600;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .api-response {
            background: #f1f5f9;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Plans API Test</h1>
        <p>Test the plans API endpoint and verify data structure</p>

        <div class="test-section">
            <h3>🔧 API Tests</h3>
            <button class="test-btn" onclick="testPlansAPI()">📋 Test Plans API</button>
            <button class="test-btn" onclick="testCreateGlimp()">🔥 Test Create Glimp</button>
            <button class="test-btn" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="status"></div>
        <div id="apiResponse"></div>
        <div id="plansDisplay"></div>

        <div class="test-section">
            <h3>🌐 Quick Links</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <a href="http://localhost:5000/api/plans" target="_blank" class="test-btn">🔗 Direct API</a>
                <a href="http://localhost:3000/subscription" target="_blank" class="test-btn">📋 Subscription Page</a>
                <a href="http://localhost:3000/login" target="_blank" class="test-btn">🔐 Login</a>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';

        async function testPlansAPI() {
            showStatus('info', 'Testing Plans API...');
            
            try {
                const response = await fetch(`${API_BASE}/api/plans`);
                const data = await response.json();
                
                console.log('Plans API Response:', data);
                
                if (response.ok) {
                    showStatus('success', `✅ Plans API Success! Found ${Array.isArray(data) ? data.length : (data.data ? data.data.length : 0)} plans`);
                    showAPIResponse(data);
                    displayPlans(data);
                } else {
                    showStatus('error', `❌ Plans API Error: ${response.status} ${response.statusText}`);
                    showAPIResponse(data);
                }
            } catch (error) {
                showStatus('error', `❌ Network Error: ${error.message}`);
                console.error('Plans API Error:', error);
            }
        }

        async function testCreateGlimp() {
            showStatus('info', 'Testing Create Glimp endpoint...');
            
            try {
                const response = await fetch(`${API_BASE}/api/plans/create-glimp`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                console.log('Create Glimp Response:', data);
                
                if (response.ok) {
                    showStatus('success', '✅ Create Glimp Success!');
                    showAPIResponse(data);
                } else {
                    showStatus('error', `❌ Create Glimp Error: ${response.status} ${response.statusText}`);
                    showAPIResponse(data);
                }
            } catch (error) {
                showStatus('error', `❌ Network Error: ${error.message}`);
                console.error('Create Glimp Error:', error);
            }
        }

        function displayPlans(data) {
            const plansDiv = document.getElementById('plansDisplay');
            let plans = [];
            
            if (Array.isArray(data)) {
                plans = data;
            } else if (data.data && Array.isArray(data.data)) {
                plans = data.data;
            } else if (data.success && data.data) {
                plans = Array.isArray(data.data) ? data.data : [data.data];
            }
            
            if (plans.length === 0) {
                plansDiv.innerHTML = '<div class="status error">No plans found in response</div>';
                return;
            }
            
            plansDiv.innerHTML = `
                <div class="test-section">
                    <h3>📋 Available Plans (${plans.length})</h3>
                    ${plans.map(plan => `
                        <div class="plan-card">
                            <div class="plan-title">${plan.title || 'Untitled Plan'}</div>
                            <div class="plan-price">${plan.discountedPrice?.toLocaleString() || 'N/A'} TZS</div>
                            <div class="plan-duration">${plan.duration || 'N/A'} month${(plan.duration || 0) > 1 ? 's' : ''}</div>
                            <div class="plan-features">
                                ${(plan.features || []).slice(0, 6).map(feature => 
                                    `<div class="feature-item">✓ ${feature}</div>`
                                ).join('')}
                                ${(plan.features || []).length > 6 ? 
                                    `<div class="feature-item">... and ${(plan.features || []).length - 6} more</div>` : ''
                                }
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function showStatus(type, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function showAPIResponse(data) {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.innerHTML = `
                <div class="test-section">
                    <h3>📡 API Response</h3>
                    <div class="api-response">${JSON.stringify(data, null, 2)}</div>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('status').innerHTML = '';
            document.getElementById('apiResponse').innerHTML = '';
            document.getElementById('plansDisplay').innerHTML = '';
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testPlansAPI, 1000);
        });
    </script>
</body>
</html>
