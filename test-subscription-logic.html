<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Logic Test</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .user-type {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .user-type.free {
            background: #fee2e2;
            color: #991b1b;
            border: 2px solid #ef4444;
        }
        .user-type.active {
            background: #d1fae5;
            color: #065f46;
            border: 2px solid #10b981;
        }
        .user-type.expired {
            background: #fef3c7;
            color: #92400e;
            border: 2px solid #f59e0b;
        }
        .result {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 600;
        }
        .result.show-modal {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .result.no-modal {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .result.allow-access {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .btn {
            background: #4f46e5;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #4338ca;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 BrainWave Subscription Logic Test</h1>
        <p>Test the new subscription logic to ensure it works correctly for different user types.</p>

        <div class="test-section">
            <h3>📋 Expected Behavior</h3>
            <div class="user-type free">🆕 Free Users: Show subscription modal</div>
            <div class="user-type active">👑 Active Subscription: NO modal, full access</div>
            <div class="user-type expired">⏰ Expired Subscription: NO modal, access to subscription page + logout</div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Scenarios</h3>
            <button class="btn" onclick="testUserType('free')">Test Free User</button>
            <button class="btn" onclick="testUserType('active')">Test Active User</button>
            <button class="btn" onclick="testUserType('expired')">Test Expired User</button>
            <button class="btn" onclick="testAllScenarios()">Test All Scenarios</button>
        </div>

        <div id="results"></div>

        <div class="test-section">
            <h3>🌐 Live Testing</h3>
            <p>Test the actual application:</p>
            <button class="btn" onclick="openApp()">📱 Open React App</button>
            <button class="btn" onclick="openSubscription()">📋 Open Subscription Page</button>
            <button class="btn" onclick="openLogin()">🔐 Open Login</button>
        </div>

        <div class="test-section">
            <h3>✅ Verification Checklist</h3>
            <div id="checklist">
                <label><input type="checkbox"> Free users see subscription modal after login</label><br>
                <label><input type="checkbox"> Active users don't see subscription modal</label><br>
                <label><input type="checkbox"> Expired users don't see subscription modal</label><br>
                <label><input type="checkbox"> All users can access Profile page</label><br>
                <label><input type="checkbox"> All users can access Subscription page</label><br>
                <label><input type="checkbox"> All users can logout</label><br>
                <label><input type="checkbox"> Subscription page shows current plan status</label><br>
                <label><input type="checkbox"> Users with expired plans can upgrade</label><br>
            </div>
        </div>
    </div>

    <script>
        // Simulate the subscription logic from the React app
        function needsSubscription(user, subscriptionData) {
            if (!user || user.isAdmin) return false;
            
            // If user has active subscription, they don't need subscription modal
            if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {
                return false;
            }
            
            // Check if subscription is not expired
            if (subscriptionData && subscriptionData.endDate) {
                const endDate = new Date(subscriptionData.endDate);
                const now = new Date();
                if (endDate > now) return false;
            }
            
            // Only show subscription modal if user has NO subscription at all
            if (user.subscriptionStatus === 'free' || !user.subscriptionStatus) {
                return true;
            }
            
            return false;
        }

        function shouldAllowAccess(user, subscriptionData) {
            if (!user || user.isAdmin) return true;
            
            // Always allow access to Profile, Subscription, and Logout
            return true;
        }

        function testUserType(userType) {
            const resultsDiv = document.getElementById('results');
            
            let user, subscriptionData;
            
            switch(userType) {
                case 'free':
                    user = {
                        _id: 'user1',
                        name: 'Free User',
                        subscriptionStatus: 'free',
                        isAdmin: false
                    };
                    subscriptionData = null;
                    break;
                    
                case 'active':
                    user = {
                        _id: 'user2',
                        name: 'Active User',
                        subscriptionStatus: 'active',
                        isAdmin: false
                    };
                    subscriptionData = {
                        paymentStatus: 'paid',
                        status: 'active',
                        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
                        activePlan: { title: 'Premium Plan' }
                    };
                    break;
                    
                case 'expired':
                    user = {
                        _id: 'user3',
                        name: 'Expired User',
                        subscriptionStatus: 'expired',
                        isAdmin: false
                    };
                    subscriptionData = {
                        paymentStatus: 'paid',
                        status: 'expired',
                        endDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
                        activePlan: { title: 'Premium Plan' }
                    };
                    break;
            }
            
            const showModal = needsSubscription(user, subscriptionData);
            const allowAccess = shouldAllowAccess(user, subscriptionData);
            
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h4>🧪 Test Result: ${userType.toUpperCase()} User</h4>
                <div class="result ${showModal ? 'show-modal' : 'no-modal'}">
                    Subscription Modal: ${showModal ? '✅ SHOW' : '❌ DON\'T SHOW'}
                </div>
                <div class="result allow-access">
                    Page Access: ${allowAccess ? '✅ ALLOWED' : '❌ RESTRICTED'}
                </div>
                <p><strong>User:</strong> ${JSON.stringify(user, null, 2)}</p>
                <p><strong>Subscription:</strong> ${JSON.stringify(subscriptionData, null, 2)}</p>
                <hr>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        function testAllScenarios() {
            document.getElementById('results').innerHTML = '<h3>🔄 Running All Tests...</h3>';
            setTimeout(() => testUserType('free'), 100);
            setTimeout(() => testUserType('active'), 200);
            setTimeout(() => testUserType('expired'), 300);
        }

        function openApp() {
            window.open('http://localhost:3000', '_blank');
        }

        function openSubscription() {
            window.open('http://localhost:3000/subscription', '_blank');
        }

        function openLogin() {
            window.open('http://localhost:3000/login', '_blank');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(testAllScenarios, 1000);
        });
    </script>
</body>
</html>
