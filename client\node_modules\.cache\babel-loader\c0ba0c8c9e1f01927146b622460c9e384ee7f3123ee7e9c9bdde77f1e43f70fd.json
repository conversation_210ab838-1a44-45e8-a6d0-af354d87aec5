{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Register\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input, Select } from \"antd\";\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\nimport Logo from \"../../../assets/logo.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction Register() {\n  _s();\n  const [verification, setVerification] = useState(false);\n  const [data, setData] = useState(\"\");\n  const [otp, setOTP] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\");\n  const navigate = useNavigate();\n  const onFinish = async values => {\n    try {\n      const response = await registerUser(values);\n      if (response.success) {\n        message.success({\n          content: response.message,\n          duration: 6,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        // Add a small delay to let user see the success message\n        setTimeout(() => {\n          navigate(\"/login\");\n        }, 1500);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Registration failed\");\n        setVerification(false);\n      }\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\n      setVerification(false);\n    }\n  };\n  const verifyUser = async values => {\n    var _values$otp;\n    if (!((_values$otp = values.otp) !== null && _values$otp !== void 0 && _values$otp.trim())) {\n      message.error(\"🔢 Please enter the verification code\");\n      return;\n    }\n    if (values.otp.length !== 6) {\n      message.error(\"🔢 Verification code must be 6 digits\");\n      return;\n    }\n    if (values.otp === otp) {\n      message.loading(\"✅ Verifying your code...\", 1);\n      setTimeout(() => {\n        onFinish(data);\n      }, 1000);\n    } else {\n      message.error({\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\n        duration: 5,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n\n  // Enhanced error handling function\n  const showUserFriendlyError = (error, defaultMessage) => {\n    var _error$response, _error$response$data, _error$response2, _error$response2$data;\n    const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || defaultMessage;\n    const errorType = (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.errorType;\n\n    // Show different message styles based on error type\n    if (errorType === \"EMAIL_EXISTS\" || errorType === \"PHONE_EXISTS\") {\n      message.warning({\n        content: errorMessage,\n        duration: 6,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else if (errorType === \"INVALID_EMAIL\" || errorType === \"INVALID_PHONE\") {\n      message.error({\n        content: errorMessage,\n        duration: 5,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else {\n      message.error({\n        content: errorMessage,\n        duration: 4,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n  const generateOTP = async formData => {\n    console.log(\"🚀 Form submitted with data:\", formData);\n\n    // Simple validation - let Ant Design handle the form validation\n    console.log(\"✅ Form validation passed, proceeding with OTP generation...\");\n    setLoading(true);\n    try {\n      const response = await sendOTP(formData);\n      if (response.success) {\n        message.success({\n          content: response.message,\n          duration: 5,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        setData(formData);\n        setOTP(response.data);\n        setVerification(true);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Failed to send verification code\");\n      }\n    } catch (error) {\n      console.error(\"OTP generation error:\", error);\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"register-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register-card\",\n      children: verification ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Verify Your Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"We've sent a verification code to your email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"otp-instructions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"otp-info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"otp-info-title\",\n              children: \"\\uD83D\\uDCE7 Check Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"otp-info-text\",\n              children: [\"We've sent a \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"6-digit verification code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 32\n              }, this), \" to your email address.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-steps\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Open your email app or website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Look for an email from BrainWave\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Copy the 6-digit code from the email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Enter the code in the box below\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-help\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"help-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCA1 Can't find the email?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this), \" Check your spam/junk folder or wait a few minutes for delivery.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: verifyUser,\n          className: \"register-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"otp\",\n            label: \"Verification Code\",\n            rules: [{\n              required: true,\n              message: \"Please enter the OTP!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"number\",\n              className: \"form-input otp-input\",\n              placeholder: \"Enter 6-digit code (e.g., 123456)\",\n              maxLength: 6\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter the verification code from your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"register-btn\",\n            disabled: loading,\n            children: loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resend-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"resend-text\",\n              children: \"Didn't receive the code?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"resend-btn\",\n              onClick: () => generateOTP(data),\n              disabled: loading,\n              children: \"\\uD83D\\uDCE7 Resend Verification Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"Join thousands of students learning with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: generateOTP,\n          className: \"register-form\",\n          onFinishFailed: errorInfo => {\n            console.log(\"❌ Form validation failed:\", errorInfo);\n            message.error(\"Please fill all required fields correctly\");\n          },\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Full Name\",\n            rules: [{\n              required: true,\n              message: \"Please enter your full name\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your full name (e.g., John Doe)\",\n              autoComplete: \"name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"school\",\n            label: \"School Name\",\n            rules: [{\n              required: true,\n              message: \"Please enter your school name\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your school name (e.g., Dar es Salaam Secondary School)\",\n              autoComplete: \"organization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Education Level\",\n            rules: [{\n              required: true,\n              message: \"Please select your education level\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              onChange: value => setSchoolType(value),\n              className: \"form-input\",\n              placeholder: \"Choose your current education level\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"Primary\",\n                children: \"\\uD83C\\uDF92 Primary Education (Classes 1-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Secondary\",\n                children: \"\\uD83D\\uDCDA Secondary Education (Forms 1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Advance\",\n                children: \"\\uD83C\\uDF93 Advanced Level (Forms 5-6)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class\",\n            label: \"Class/Form\",\n            rules: [{\n              required: true,\n              message: \"Please select your class or form\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              className: \"form-input\",\n              placeholder: schoolType ? \"Select your class/form\" : \"Please select education level first\",\n              disabled: !schoolType,\n              children: [schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: i,\n                children: `📚 Class ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this)), schoolType === \"Secondary\" && [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `📖 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this)), schoolType === \"Advance\" && [5, 6].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `🎓 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: [!schoolType && \"Please select your education level first\", schoolType === \"Primary\" && \"Select your current class (1-7)\", schoolType === \"Secondary\" && \"Select your current form (1-4)\", schoolType === \"Advance\" && \"Select your current form (5-6)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email Address\",\n            rules: [{\n              required: true,\n              message: \"Please enter your email\"\n            }, {\n              type: \"email\",\n              message: \"Email is wrong written\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              className: \"form-input\",\n              placeholder: \"Enter your email address (e.g., <EMAIL>)\",\n              autoComplete: \"email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"phoneNumber\",\n            label: \"Phone Number\",\n            rules: [{\n              required: true,\n              message: \"Please enter your phone number\"\n            }, {\n              pattern: /^0[67]\\d{8}$/,\n              message: \"Phone number is wrong written\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"tel\",\n              className: \"form-input\",\n              placeholder: \"Enter mobile number (e.g., ********** or 0612345678)\",\n              autoComplete: \"tel\",\n              maxLength: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-help-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"\\uD83D\\uDCB3 Enter your mobile number for payment processing (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: [\"\\uD83D\\uDCDE Must start with \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"06\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 68\n                }, this), \" or \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"07\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 91\n                }, this), \" and be exactly 10 digits\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"\\uD83D\\uDCE7 Note: Email verification code will be sent to your email, not phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            rules: [{\n              required: true,\n              message: \"Please enter your password\"\n            }, {\n              min: 8,\n              message: \"Password must be at least 8 characters long\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Create a strong password (min 8 characters)\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Must include: uppercase, lowercase, number, and special character\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"confirmPassword\",\n            label: \"Retype Password\",\n            dependencies: ['password'],\n            rules: [{\n              required: true,\n              message: \"Please retype your password\"\n            }, ({\n              getFieldValue\n            }) => ({\n              validator(_, value) {\n                if (!value || getFieldValue('password') === value) {\n                  return Promise.resolve();\n                }\n                return Promise.reject(new Error('The two passwords do not match'));\n              }\n            })],\n            children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Retype your password to confirm\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Must match the password above\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"register-btn\",\n              disabled: loading,\n              onClick: () => console.log(\"🔘 Create Account button clicked\"),\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 23\n                }, this), \"Creating Account...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: \"\\uD83D\\uDE80 Create Account\"\n              }, void 0, false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"register-btn\",\n              style: {\n                marginTop: '10px',\n                background: '#28a745'\n              },\n              onClick: () => {\n                // Get form instance and values\n                const form = document.querySelector('.register-form');\n                const formData = new FormData(form);\n                const values = {};\n                for (let [key, value] of formData.entries()) {\n                  values[key] = value;\n                }\n                console.log(\"📋 Form values:\", values);\n\n                // Call generateOTP directly with test data\n                const testData = {\n                  name: \"Test User\",\n                  school: \"Test School\",\n                  level: \"Primary\",\n                  class: 1,\n                  email: \"<EMAIL>\",\n                  phoneNumber: \"**********\",\n                  password: \"password123\",\n                  confirmPassword: \"password123\"\n                };\n                generateOTP(testData);\n              },\n              children: \"\\uD83E\\uDDEA Test Submit (Bypass Validation)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account? \", \" \", /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"register-link\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"ZCOX0z1U6pPkSU7THaoXima+72A=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["Form", "message", "Input", "Select", "React", "useState", "Link", "useNavigate", "registerUser", "sendOTP", "Logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "Register", "_s", "verification", "setVerification", "data", "setData", "otp", "setOTP", "loading", "setLoading", "schoolType", "setSchoolType", "navigate", "onFinish", "values", "response", "success", "content", "duration", "style", "marginTop", "setTimeout", "showUserFriendlyError", "error", "console", "verifyUser", "_values$otp", "trim", "length", "defaultMessage", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errorMessage", "errorType", "warning", "generateOTP", "formData", "log", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "type", "placeholder", "max<PERSON><PERSON><PERSON>", "disabled", "onClick", "onFinishFailed", "errorInfo", "autoComplete", "onChange", "value", "map", "i", "pattern", "min", "Password", "dependencies", "getFieldValue", "validator", "_", "Promise", "resolve", "reject", "Error", "background", "form", "document", "querySelector", "FormData", "key", "entries", "testData", "school", "level", "class", "email", "phoneNumber", "password", "confirmPassword", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Register/index.js"], "sourcesContent": ["import { Form, message, Input, Select } from \"antd\";\r\nimport React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\r\nimport Logo from \"../../../assets/logo.png\";\r\n\r\nconst { Option } = Select;\r\n\r\nfunction Register() {\r\n  const [verification, setVerification] = useState(false);\r\n  const [data, setData] = useState(\"\");\r\n  const [otp, setOTP] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [schoolType, setSchoolType] = useState(\"\");\r\n  const navigate = useNavigate();\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      const response = await registerUser(values);\r\n      if (response.success) {\r\n        message.success({\r\n          content: response.message,\r\n          duration: 6,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        // Add a small delay to let user see the success message\r\n        setTimeout(() => {\r\n          navigate(\"/login\");\r\n        }, 1500);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Registration failed\");\r\n        setVerification(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Registration error:\", error);\r\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\r\n      setVerification(false);\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (!values.otp?.trim()) {\r\n      message.error(\"🔢 Please enter the verification code\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp.length !== 6) {\r\n      message.error(\"🔢 Verification code must be 6 digits\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp === otp) {\r\n      message.loading(\"✅ Verifying your code...\", 1);\r\n      setTimeout(() => {\r\n        onFinish(data);\r\n      }, 1000);\r\n    } else {\r\n      message.error({\r\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\r\n        duration: 5,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  // Enhanced error handling function\r\n  const showUserFriendlyError = (error, defaultMessage) => {\r\n    const errorMessage = error.response?.data?.message || error.message || defaultMessage;\r\n    const errorType = error.response?.data?.errorType;\r\n\r\n    // Show different message styles based on error type\r\n    if (errorType === \"EMAIL_EXISTS\" || errorType === \"PHONE_EXISTS\") {\r\n      message.warning({\r\n        content: errorMessage,\r\n        duration: 6,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else if (errorType === \"INVALID_EMAIL\" || errorType === \"INVALID_PHONE\") {\r\n      message.error({\r\n        content: errorMessage,\r\n        duration: 5,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else {\r\n      message.error({\r\n        content: errorMessage,\r\n        duration: 4,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  const generateOTP = async (formData) => {\r\n    console.log(\"🚀 Form submitted with data:\", formData);\r\n\r\n    // Simple validation - let Ant Design handle the form validation\r\n    console.log(\"✅ Form validation passed, proceeding with OTP generation...\");\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await sendOTP(formData);\r\n      if (response.success) {\r\n        message.success({\r\n          content: response.message,\r\n          duration: 5,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        setData(formData);\r\n        setOTP(response.data);\r\n        setVerification(true);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Failed to send verification code\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"OTP generation error:\", error);\r\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"register-container\">\r\n      <div className=\"register-card\">\r\n        {verification ? (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Verify Your Email</h1>\r\n              <p className=\"register-subtitle\">We've sent a verification code to your email</p>\r\n            </div>\r\n\r\n            {/* OTP Instructions */}\r\n            <div className=\"otp-instructions\">\r\n              <div className=\"otp-info-card\">\r\n                <h3 className=\"otp-info-title\">📧 Check Your Email</h3>\r\n                <p className=\"otp-info-text\">\r\n                  We've sent a <strong>6-digit verification code</strong> to your email address.\r\n                </p>\r\n                <div className=\"otp-steps\">\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">1</span>\r\n                    <span className=\"step-text\">Open your email app or website</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">2</span>\r\n                    <span className=\"step-text\">Look for an email from BrainWave</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">3</span>\r\n                    <span className=\"step-text\">Copy the 6-digit code from the email</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">4</span>\r\n                    <span className=\"step-text\">Enter the code in the box below</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"otp-help\">\r\n                  <p className=\"help-text\">\r\n                    <strong>💡 Can't find the email?</strong> Check your spam/junk folder or wait a few minutes for delivery.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Form layout=\"vertical\" onFinish={verifyUser} className=\"register-form\">\r\n              <Form.Item name=\"otp\" label=\"Verification Code\" rules={[{ required: true, message: \"Please enter the OTP!\" }]}>\r\n                <Input\r\n                  type=\"number\"\r\n                  className=\"form-input otp-input\"\r\n                  placeholder=\"Enter 6-digit code (e.g., 123456)\"\r\n                  maxLength={6}\r\n                />\r\n                <p className=\"form-help-text\">Enter the verification code from your email</p>\r\n              </Form.Item>\r\n\r\n              <button type=\"submit\" className=\"register-btn\" disabled={loading}>\r\n                {loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"}\r\n              </button>\r\n\r\n              <div className=\"resend-section\">\r\n                <p className=\"resend-text\">Didn't receive the code?</p>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"resend-btn\"\r\n                  onClick={() => generateOTP(data)}\r\n                  disabled={loading}\r\n                >\r\n                  📧 Resend Verification Code\r\n                </button>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Create Account</h1>\r\n              <p className=\"register-subtitle\">Join thousands of students learning with BrainWave</p>\r\n            </div>\r\n\r\n\r\n\r\n            <Form\r\n              layout=\"vertical\"\r\n              onFinish={generateOTP}\r\n              className=\"register-form\"\r\n              onFinishFailed={(errorInfo) => {\r\n                console.log(\"❌ Form validation failed:\", errorInfo);\r\n                message.error(\"Please fill all required fields correctly\");\r\n              }}\r\n            >\r\n\r\n\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"Full Name\"\r\n                rules={[{ required: true, message: \"Please enter your full name\" }]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your full name (e.g., John Doe)\"\r\n                  autoComplete=\"name\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"school\"\r\n                label=\"School Name\"\r\n                rules={[{ required: true, message: \"Please enter your school name\" }]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your school name (e.g., Dar es Salaam Secondary School)\"\r\n                  autoComplete=\"organization\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"level\"\r\n                label=\"Education Level\"\r\n                rules={[{ required: true, message: \"Please select your education level\" }]}\r\n              >\r\n                <Select\r\n                  onChange={(value) => setSchoolType(value)}\r\n                  className=\"form-input\"\r\n                  placeholder=\"Choose your current education level\"\r\n                >\r\n                  <Option value=\"Primary\">🎒 Primary Education (Classes 1-7)</Option>\r\n                  <Option value=\"Secondary\">📚 Secondary Education (Forms 1-4)</Option>\r\n                  <Option value=\"Advance\">🎓 Advanced Level (Forms 5-6)</Option>\r\n                </Select>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"class\"\r\n                label=\"Class/Form\"\r\n                rules={[{ required: true, message: \"Please select your class or form\" }]}\r\n              >\r\n                <Select\r\n                  className=\"form-input\"\r\n                  placeholder={schoolType ? \"Select your class/form\" : \"Please select education level first\"}\r\n                  disabled={!schoolType}\r\n                >\r\n                  {schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map((i) => (\r\n                    <Option key={i} value={i}>{`📚 Class ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Secondary\" && [1, 2, 3, 4].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`📖 Form ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Advance\" && [5, 6].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`🎓 Form ${i}`}</Option>\r\n                  ))}\r\n                </Select>\r\n                <p className=\"form-help-text\">\r\n                  {!schoolType && \"Please select your education level first\"}\r\n                  {schoolType === \"Primary\" && \"Select your current class (1-7)\"}\r\n                  {schoolType === \"Secondary\" && \"Select your current form (1-4)\"}\r\n                  {schoolType === \"Advance\" && \"Select your current form (5-6)\"}\r\n                </p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"Email Address\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your email\" },\r\n                  { type: \"email\", message: \"Email is wrong written\" }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"email\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your email address (e.g., <EMAIL>)\"\r\n                  autoComplete=\"email\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phoneNumber\"\r\n                label=\"Phone Number\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your phone number\" },\r\n                  { pattern: /^0[67]\\d{8}$/, message: \"Phone number is wrong written\" }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"tel\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter mobile number (e.g., ********** or 0612345678)\"\r\n                  autoComplete=\"tel\"\r\n                  maxLength={10}\r\n                />\r\n                <div className=\"phone-help-section\">\r\n                  <p className=\"form-help-text\">💳 Enter your mobile number for payment processing (M-Pesa, Tigo Pesa, Airtel Money)</p>\r\n                  <p className=\"form-help-text\">📞 Must start with <strong>06</strong> or <strong>07</strong> and be exactly 10 digits</p>\r\n                  <p className=\"form-help-text\">📧 Note: Email verification code will be sent to your email, not phone</p>\r\n                </div>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                label=\"Password\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your password\" },\r\n                  { min: 8, message: \"Password must be at least 8 characters long\" }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Create a strong password (min 8 characters)\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must include: uppercase, lowercase, number, and special character</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                label=\"Retype Password\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: \"Please retype your password\" },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('The two passwords do not match'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Retype your password to confirm\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must match the password above</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"register-btn\"\r\n                  disabled={loading}\r\n                  onClick={() => console.log(\"🔘 Create Account button clicked\")}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <span className=\"loading-spinner\"></span>\r\n                      Creating Account...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      🚀 Create Account\r\n                    </>\r\n                  )}\r\n                </button>\r\n\r\n                {/* Test button to bypass validation */}\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"register-btn\"\r\n                  style={{marginTop: '10px', background: '#28a745'}}\r\n                  onClick={() => {\r\n                    // Get form instance and values\r\n                    const form = document.querySelector('.register-form');\r\n                    const formData = new FormData(form);\r\n                    const values = {};\r\n                    for (let [key, value] of formData.entries()) {\r\n                      values[key] = value;\r\n                    }\r\n                    console.log(\"📋 Form values:\", values);\r\n\r\n                    // Call generateOTP directly with test data\r\n                    const testData = {\r\n                      name: \"Test User\",\r\n                      school: \"Test School\",\r\n                      level: \"Primary\",\r\n                      class: 1,\r\n                      email: \"<EMAIL>\",\r\n                      phoneNumber: \"**********\",\r\n                      password: \"password123\",\r\n                      confirmPassword: \"password123\"\r\n                    };\r\n                    generateOTP(testData);\r\n                  }}\r\n                >\r\n                  🧪 Test Submit (Bypass Validation)\r\n                </button>\r\n              </Form.Item>\r\n            </Form>\r\n\r\n            <div className=\"register-footer\">\r\n              <p>\r\n                Already have an account? {\" \"}\r\n                <Link to=\"/login\" className=\"register-link\">\r\n                  Sign In\r\n                </Link>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACnD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAC/D,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAM;EAAEC;AAAO,CAAC,GAAGZ,MAAM;AAEzB,SAASa,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiB,GAAG,EAAEC,MAAM,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMuB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAE9B,MAAMsB,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMvB,YAAY,CAACsB,MAAM,CAAC;MAC3C,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB/B,OAAO,CAAC+B,OAAO,CAAC;UACdC,OAAO,EAAEF,QAAQ,CAAC9B,OAAO;UACzBiC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACF;QACAC,UAAU,CAAC,MAAM;UACfT,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLU,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,qBAAqB,CAAC;QAC1DZ,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CD,qBAAqB,CAACC,KAAK,EAAE,wCAAwC,CAAC;MACtEpB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsB,UAAU,GAAG,MAAOX,MAAM,IAAK;IAAA,IAAAY,WAAA;IACnC,IAAI,GAAAA,WAAA,GAACZ,MAAM,CAACR,GAAG,cAAAoB,WAAA,eAAVA,WAAA,CAAYC,IAAI,CAAC,CAAC,GAAE;MACvB1C,OAAO,CAACsC,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIT,MAAM,CAACR,GAAG,CAACsB,MAAM,KAAK,CAAC,EAAE;MAC3B3C,OAAO,CAACsC,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIT,MAAM,CAACR,GAAG,KAAKA,GAAG,EAAE;MACtBrB,OAAO,CAACuB,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;MAC9Ca,UAAU,CAAC,MAAM;QACfR,QAAQ,CAACT,IAAI,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLnB,OAAO,CAACsC,KAAK,CAAC;QACZN,OAAO,EAAE,8EAA8E;QACvFC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAACC,KAAK,EAAEM,cAAc,KAAK;IAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACvD,MAAMC,YAAY,GAAG,EAAAJ,eAAA,GAAAP,KAAK,CAACR,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB1B,IAAI,cAAA2B,oBAAA,uBAApBA,oBAAA,CAAsB9C,OAAO,KAAIsC,KAAK,CAACtC,OAAO,IAAI4C,cAAc;IACrF,MAAMM,SAAS,IAAAH,gBAAA,GAAGT,KAAK,CAACR,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBE,SAAS;;IAEjD;IACA,IAAIA,SAAS,KAAK,cAAc,IAAIA,SAAS,KAAK,cAAc,EAAE;MAChElD,OAAO,CAACmD,OAAO,CAAC;QACdnB,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIe,SAAS,KAAK,eAAe,IAAIA,SAAS,KAAK,eAAe,EAAE;MACzElD,OAAO,CAACsC,KAAK,CAAC;QACZN,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLnC,OAAO,CAACsC,KAAK,CAAC;QACZN,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMiB,WAAW,GAAG,MAAOC,QAAQ,IAAK;IACtCd,OAAO,CAACe,GAAG,CAAC,8BAA8B,EAAED,QAAQ,CAAC;;IAErD;IACAd,OAAO,CAACe,GAAG,CAAC,6DAA6D,CAAC;IAE1E9B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMtB,OAAO,CAAC6C,QAAQ,CAAC;MACxC,IAAIvB,QAAQ,CAACC,OAAO,EAAE;QACpB/B,OAAO,CAAC+B,OAAO,CAAC;UACdC,OAAO,EAAEF,QAAQ,CAAC9B,OAAO;UACzBiC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACFf,OAAO,CAACiC,QAAQ,CAAC;QACjB/B,MAAM,CAACQ,QAAQ,CAACX,IAAI,CAAC;QACrBD,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACLmB,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,kCAAkC,CAAC;MACzE;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,qBAAqB,CAACC,KAAK,EAAE,oEAAoE,CAAC;IACpG;IACAd,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAID,oBACEb,OAAA;IAAK4C,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjC7C,OAAA;MAAK4C,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BvC,YAAY,gBACXN,OAAA;QAAA6C,QAAA,gBACE7C,OAAA;UAAK4C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7C,OAAA;YAAK8C,GAAG,EAAEhD,IAAK;YAACiD,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjEnD,OAAA;YAAI4C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAiB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDnD,OAAA;YAAG4C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAA4C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAGNnD,OAAA;UAAK4C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B7C,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7C,OAAA;cAAI4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDnD,OAAA;cAAG4C,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,eACd,eAAA7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAyB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,2BACzD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnD,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB7C,OAAA;gBAAK4C,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCnD,OAAA;kBAAM4C,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNnD,OAAA;gBAAK4C,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCnD,OAAA;kBAAM4C,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAgC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNnD,OAAA;gBAAK4C,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCnD,OAAA;kBAAM4C,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAoC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNnD,OAAA;gBAAK4C,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7C,OAAA;kBAAM4C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCnD,OAAA;kBAAM4C,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB7C,OAAA;gBAAG4C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtB7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAwB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oEAC3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA,CAACZ,IAAI;UAACgE,MAAM,EAAC,UAAU;UAACnC,QAAQ,EAAEY,UAAW;UAACe,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACrE7C,OAAA,CAACZ,IAAI,CAACiE,IAAI;YAACC,IAAI,EAAC,KAAK;YAACC,KAAK,EAAC,mBAAmB;YAACC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAAwD,QAAA,gBAC5G7C,OAAA,CAACV,KAAK;cACJoE,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,sBAAsB;cAChCe,WAAW,EAAC,mCAAmC;cAC/CC,SAAS,EAAE;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACFnD,OAAA;cAAG4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA2C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEZnD,OAAA;YAAQ0D,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,cAAc;YAACiB,QAAQ,EAAEjD,OAAQ;YAAAiC,QAAA,EAC9DjC,OAAO,GAAG,gBAAgB,GAAG;UAAkC;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAETnD,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7C,OAAA;cAAG4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAwB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvDnD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,YAAY;cACtBkB,OAAO,EAAEA,CAAA,KAAMrB,WAAW,CAACjC,IAAI,CAAE;cACjCqD,QAAQ,EAAEjD,OAAQ;cAAAiC,QAAA,EACnB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,gBAENnD,OAAA;QAAA6C,QAAA,gBACE7C,OAAA;UAAK4C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7C,OAAA;YAAK8C,GAAG,EAAEhD,IAAK;YAACiD,GAAG,EAAC,gBAAgB;YAACH,SAAS,EAAC;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjEnD,OAAA;YAAI4C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDnD,OAAA;YAAG4C,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAINnD,OAAA,CAACZ,IAAI;UACHgE,MAAM,EAAC,UAAU;UACjBnC,QAAQ,EAAEwB,WAAY;UACtBG,SAAS,EAAC,eAAe;UACzBmB,cAAc,EAAGC,SAAS,IAAK;YAC7BpC,OAAO,CAACe,GAAG,CAAC,2BAA2B,EAAEqB,SAAS,CAAC;YACnD3E,OAAO,CAACsC,KAAK,CAAC,2CAA2C,CAAC;UAC5D,CAAE;UAAAkB,QAAA,gBAIF7C,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAA8B,CAAC,CAAE;YAAAwD,QAAA,eAEpE7C,OAAA,CAACV,KAAK;cACJoE,IAAI,EAAC,MAAM;cACXd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,uCAAuC;cACnDM,YAAY,EAAC;YAAM;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAAgC,CAAC,CAAE;YAAAwD,QAAA,eAEtE7C,OAAA,CAACV,KAAK;cACJoE,IAAI,EAAC,MAAM;cACXd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,+DAA+D;cAC3EM,YAAY,EAAC;YAAc;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,iBAAiB;YACvBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAAqC,CAAC,CAAE;YAAAwD,QAAA,eAE3E7C,OAAA,CAACT,MAAM;cACL2E,QAAQ,EAAGC,KAAK,IAAKpD,aAAa,CAACoD,KAAK,CAAE;cAC1CvB,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,qCAAqC;cAAAd,QAAA,gBAEjD7C,OAAA,CAACG,MAAM;gBAACgE,KAAK,EAAC,SAAS;gBAAAtB,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnEnD,OAAA,CAACG,MAAM;gBAACgE,KAAK,EAAC,WAAW;gBAAAtB,QAAA,EAAC;cAAkC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrEnD,OAAA,CAACG,MAAM;gBAACgE,KAAK,EAAC,SAAS;gBAAAtB,QAAA,EAAC;cAA6B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,YAAY;YAClBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAAmC,CAAC,CAAE;YAAAwD,QAAA,gBAEzE7C,OAAA,CAACT,MAAM;cACLqD,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAE7C,UAAU,GAAG,wBAAwB,GAAG,qCAAsC;cAC3F+C,QAAQ,EAAE,CAAC/C,UAAW;cAAA+B,QAAA,GAErB/B,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACsD,GAAG,CAAEC,CAAC,iBACvDrE,OAAA,CAACG,MAAM;gBAASgE,KAAK,EAAEE,CAAE;gBAAAxB,QAAA,EAAG,YAAWwB,CAAE;cAAC,GAA7BA,CAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CACpD,CAAC,EACDrC,UAAU,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACsD,GAAG,CAAEC,CAAC,iBAChDrE,OAAA,CAACG,MAAM;gBAASgE,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAAxB,QAAA,EAAG,WAAUwB,CAAE;cAAC,GAAtCA,CAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC,EACDrC,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACsD,GAAG,CAAEC,CAAC,iBACxCrE,OAAA,CAACG,MAAM;gBAASgE,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAAxB,QAAA,EAAG,WAAUwB,CAAE;cAAC,GAAtCA,CAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTnD,OAAA;cAAG4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAC1B,CAAC/B,UAAU,IAAI,0CAA0C,EACzDA,UAAU,KAAK,SAAS,IAAI,iCAAiC,EAC7DA,UAAU,KAAK,WAAW,IAAI,gCAAgC,EAC9DA,UAAU,KAAK,SAAS,IAAI,gCAAgC;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAA0B,CAAC,EACtD;cAAEqE,IAAI,EAAE,OAAO;cAAErE,OAAO,EAAE;YAAyB,CAAC,CACpD;YAAAwD,QAAA,eAEF7C,OAAA,CAACV,KAAK;cACJoE,IAAI,EAAC,OAAO;cACZd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,sDAAsD;cAClEM,YAAY,EAAC;YAAO;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAAiC,CAAC,EAC7D;cAAEiF,OAAO,EAAE,cAAc;cAAEjF,OAAO,EAAE;YAAgC,CAAC,CACrE;YAAAwD,QAAA,gBAEF7C,OAAA,CAACV,KAAK;cACJoE,IAAI,EAAC,KAAK;cACVd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,sDAAsD;cAClEM,YAAY,EAAC,KAAK;cAClBL,SAAS,EAAE;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFnD,OAAA;cAAK4C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjC7C,OAAA;gBAAG4C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAoF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtHnD,OAAA;gBAAG4C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,+BAAmB,eAAA7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,QAAI,eAAAnD,OAAA;kBAAA6C,QAAA,EAAQ;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6BAAyB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxHnD,OAAA;gBAAG4C,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAsE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,UAAU;YACfC,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAA6B,CAAC,EACzD;cAAEkF,GAAG,EAAE,CAAC;cAAElF,OAAO,EAAE;YAA8C,CAAC,CAClE;YAAAwD,QAAA,gBAEF7C,OAAA,CAACV,KAAK,CAACkF,QAAQ;cACb5B,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,6CAA6C;cACzDM,YAAY,EAAC;YAAc;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFnD,OAAA;cAAG4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAiE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YACRC,IAAI,EAAC,iBAAiB;YACtBC,KAAK,EAAC,iBAAiB;YACvBkB,YAAY,EAAE,CAAC,UAAU,CAAE;YAC3BjB,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpE,OAAO,EAAE;YAA8B,CAAC,EAC1D,CAAC;cAAEqF;YAAc,CAAC,MAAM;cACtBC,SAASA,CAACC,CAAC,EAAET,KAAK,EAAE;gBAClB,IAAI,CAACA,KAAK,IAAIO,aAAa,CAAC,UAAU,CAAC,KAAKP,KAAK,EAAE;kBACjD,OAAOU,OAAO,CAACC,OAAO,CAAC,CAAC;gBAC1B;gBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,gCAAgC,CAAC,CAAC;cACpE;YACF,CAAC,CAAC,CACF;YAAAnC,QAAA,gBAEF7C,OAAA,CAACV,KAAK,CAACkF,QAAQ;cACb5B,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,iCAAiC;cAC7CM,YAAY,EAAC;YAAc;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACFnD,OAAA;cAAG4C,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAEZnD,OAAA,CAACZ,IAAI,CAACiE,IAAI;YAAAR,QAAA,gBACR7C,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,cAAc;cACxBiB,QAAQ,EAAEjD,OAAQ;cAClBkD,OAAO,EAAEA,CAAA,KAAMlC,OAAO,CAACe,GAAG,CAAC,kCAAkC,CAAE;cAAAE,QAAA,EAE9DjC,OAAO,gBACNZ,OAAA,CAAAE,SAAA;gBAAA2C,QAAA,gBACE7C,OAAA;kBAAM4C,SAAS,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,uBAE3C;cAAA,eAAE,CAAC,gBAEHnD,OAAA,CAAAE,SAAA;gBAAA2C,QAAA,EAAE;cAEF,gBAAE;YACH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAGTnD,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,cAAc;cACxBrB,KAAK,EAAE;gBAACC,SAAS,EAAE,MAAM;gBAAEyD,UAAU,EAAE;cAAS,CAAE;cAClDnB,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMoB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;gBACrD,MAAM1C,QAAQ,GAAG,IAAI2C,QAAQ,CAACH,IAAI,CAAC;gBACnC,MAAMhE,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,IAAI,CAACoE,GAAG,EAAEnB,KAAK,CAAC,IAAIzB,QAAQ,CAAC6C,OAAO,CAAC,CAAC,EAAE;kBAC3CrE,MAAM,CAACoE,GAAG,CAAC,GAAGnB,KAAK;gBACrB;gBACAvC,OAAO,CAACe,GAAG,CAAC,iBAAiB,EAAEzB,MAAM,CAAC;;gBAEtC;gBACA,MAAMsE,QAAQ,GAAG;kBACflC,IAAI,EAAE,WAAW;kBACjBmC,MAAM,EAAE,aAAa;kBACrBC,KAAK,EAAE,SAAS;kBAChBC,KAAK,EAAE,CAAC;kBACRC,KAAK,EAAE,kBAAkB;kBACzBC,WAAW,EAAE,YAAY;kBACzBC,QAAQ,EAAE,aAAa;kBACvBC,eAAe,EAAE;gBACnB,CAAC;gBACDtD,WAAW,CAAC+C,QAAQ,CAAC;cACvB,CAAE;cAAA3C,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEPnD,OAAA;UAAK4C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B7C,OAAA;YAAA6C,QAAA,GAAG,2BACwB,EAAC,GAAG,eAC7B7C,OAAA,CAACN,IAAI;cAACsG,EAAE,EAAC,QAAQ;cAACpD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE5C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9C,EAAA,CAraQD,QAAQ;EAAA,QAMET,WAAW;AAAA;AAAAsG,EAAA,GANrB7F,QAAQ;AAuajB,eAAeA,QAAQ;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}