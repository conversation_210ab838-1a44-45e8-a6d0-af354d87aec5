{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionTrigger\\\\SubscriptionTrigger.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useLocation } from 'react-router-dom';\nimport SubscriptionModal from '../SubscriptionModal/SubscriptionModal';\nimport './SubscriptionTrigger.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionTrigger = () => {\n  _s();\n  const [showModal, setShowModal] = useState(false);\n  const [hasShownModal, setHasShownModal] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const location = useLocation();\n\n  // Check if user needs subscription - ONLY show for users with NO plan at all\n  const needsSubscription = () => {\n    if (!user || user.isAdmin) return false;\n\n    // If user has active subscription, they don't need subscription modal\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      return false;\n    }\n\n    // Check if subscription is not expired\n    if (subscriptionData && subscriptionData.endDate) {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) return false;\n    }\n\n    // Show subscription modal/overlay for users with paymentRequired or no subscription\n    if ((user.paymentRequired || user.subscriptionStatus === 'free' || !user.subscriptionStatus) && !user.isAdmin) {\n      return true;\n    }\n    return false;\n  };\n  useEffect(() => {\n    // Show subscription modal after login if user needs subscription\n    if (needsSubscription() && !hasShownModal) {\n      const timer = setTimeout(() => {\n        setShowModal(true);\n        setHasShownModal(true);\n      }, 2000); // Show after 2 seconds\n\n      return () => clearTimeout(timer);\n    }\n  }, [user, subscriptionData, hasShownModal]);\n  const handleSuccess = () => {\n    setShowModal(false);\n    setHasShownModal(true);\n  };\n  const handleClose = () => {\n    setShowModal(false);\n    // Don't set hasShownModal to true on close, so it can show again later\n  };\n\n  // Don't render anything if user doesn't need subscription or is on profile/plans page\n  if (!needsSubscription()) {\n    return null;\n  }\n\n  // Don't show overlay on allowed pages - let users access these\n  const allowedPages = ['/profile', '/subscription', '/logout'];\n  const isOnAllowedPage = allowedPages.some(page => location.pathname.includes(page));\n  if (isOnAllowedPage) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [needsSubscription() && !showModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-required-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-prompt\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prompt-icon\",\n          children: \"\\uD83D\\uDD12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Premium Access Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Choose a subscription plan to unlock all features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"choose-plan-btn\",\n            onClick: () => setShowModal(true),\n            children: \"Choose Plan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"profile-btn\",\n            onClick: () => window.location.href = '/profile',\n            children: \"Go to Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-btn\",\n            onClick: () => {\n              localStorage.removeItem('token');\n              localStorage.removeItem('user');\n              window.location.href = '/login';\n            },\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(SubscriptionModal, {\n      isOpen: showModal,\n      onClose: handleClose,\n      onSuccess: handleSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SubscriptionTrigger, \"F7J1oQoqTLGVNe8AcovD0yWBCVw=\", false, function () {\n  return [useSelector, useSelector, useLocation];\n});\n_c = SubscriptionTrigger;\nexport default SubscriptionTrigger;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionTrigger\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useLocation", "SubscriptionModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionTrigger", "_s", "showModal", "setShowModal", "hasShownModal", "setHasShownModal", "user", "state", "subscriptionData", "subscription", "location", "needsSubscription", "isAdmin", "paymentStatus", "status", "endDate", "Date", "now", "paymentRequired", "subscriptionStatus", "timer", "setTimeout", "clearTimeout", "handleSuccess", "handleClose", "allowedPages", "isOnAllowedPage", "some", "page", "pathname", "includes", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "href", "localStorage", "removeItem", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionTrigger/SubscriptionTrigger.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useLocation } from 'react-router-dom';\nimport SubscriptionModal from '../SubscriptionModal/SubscriptionModal';\nimport './SubscriptionTrigger.css';\n\nconst SubscriptionTrigger = () => {\n  const [showModal, setShowModal] = useState(false);\n  const [hasShownModal, setHasShownModal] = useState(false);\n\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const location = useLocation();\n\n  // Check if user needs subscription - ONLY show for users with NO plan at all\n  const needsSubscription = () => {\n    if (!user || user.isAdmin) return false;\n\n    // If user has active subscription, they don't need subscription modal\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      return false;\n    }\n\n    // Check if subscription is not expired\n    if (subscriptionData && subscriptionData.endDate) {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) return false;\n    }\n\n    // Show subscription modal/overlay for users with paymentRequired or no subscription\n    if ((user.paymentRequired || user.subscriptionStatus === 'free' || !user.subscriptionStatus) && !user.isAdmin) {\n      return true;\n    }\n\n    return false;\n  };\n\n  useEffect(() => {\n    // Show subscription modal after login if user needs subscription\n    if (needsSubscription() && !hasShownModal) {\n      const timer = setTimeout(() => {\n        setShowModal(true);\n        setHasShownModal(true);\n      }, 2000); // Show after 2 seconds\n\n      return () => clearTimeout(timer);\n    }\n  }, [user, subscriptionData, hasShownModal]);\n\n  const handleSuccess = () => {\n    setShowModal(false);\n    setHasShownModal(true);\n  };\n\n  const handleClose = () => {\n    setShowModal(false);\n    // Don't set hasShownModal to true on close, so it can show again later\n  };\n\n  // Don't render anything if user doesn't need subscription or is on profile/plans page\n  if (!needsSubscription()) {\n    return null;\n  }\n\n  // Don't show overlay on allowed pages - let users access these\n  const allowedPages = ['/profile', '/subscription', '/logout'];\n  const isOnAllowedPage = allowedPages.some(page => location.pathname.includes(page));\n  if (isOnAllowedPage) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Subscription Required Overlay */}\n      {needsSubscription() && !showModal && (\n        <div className=\"subscription-required-overlay\">\n          <div className=\"subscription-prompt\">\n            <div className=\"prompt-icon\">🔒</div>\n            <h3>Premium Access Required</h3>\n            <p>Choose a subscription plan to unlock all features</p>\n            <div className=\"subscription-actions\">\n              <button\n                className=\"choose-plan-btn\"\n                onClick={() => setShowModal(true)}\n              >\n                Choose Plan\n              </button>\n              <button\n                className=\"profile-btn\"\n                onClick={() => window.location.href = '/profile'}\n              >\n                Go to Profile\n              </button>\n              <button\n                className=\"logout-btn\"\n                onClick={() => {\n                  localStorage.removeItem('token');\n                  localStorage.removeItem('user');\n                  window.location.href = '/login';\n                }}\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Subscription Modal */}\n      <SubscriptionModal \n        isOpen={showModal}\n        onClose={handleClose}\n        onSuccess={handleSuccess}\n      />\n    </>\n  );\n};\n\nexport default SubscriptionTrigger;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEnC,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM;IAAEe;EAAK,CAAC,GAAGb,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGf,WAAW,CAAEc,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMiB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACL,IAAI,IAAIA,IAAI,CAACM,OAAO,EAAE,OAAO,KAAK;;IAEvC;IACA,IAAIJ,gBAAgB,IAAIA,gBAAgB,CAACK,aAAa,KAAK,MAAM,IAAIL,gBAAgB,CAACM,MAAM,KAAK,QAAQ,EAAE;MACzG,OAAO,KAAK;IACd;;IAEA;IACA,IAAIN,gBAAgB,IAAIA,gBAAgB,CAACO,OAAO,EAAE;MAChD,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAACR,gBAAgB,CAACO,OAAO,CAAC;MAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGE,GAAG,EAAE,OAAO,KAAK;IACjC;;IAEA;IACA,IAAI,CAACX,IAAI,CAACY,eAAe,IAAIZ,IAAI,CAACa,kBAAkB,KAAK,MAAM,IAAI,CAACb,IAAI,CAACa,kBAAkB,KAAK,CAACb,IAAI,CAACM,OAAO,EAAE;MAC7G,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACd;IACA,IAAImB,iBAAiB,CAAC,CAAC,IAAI,CAACP,aAAa,EAAE;MACzC,MAAMgB,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BlB,YAAY,CAAC,IAAI,CAAC;QAClBE,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMiB,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACd,IAAI,EAAEE,gBAAgB,EAAEJ,aAAa,CAAC,CAAC;EAE3C,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IAC1BpB,YAAY,CAAC,KAAK,CAAC;IACnBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMmB,WAAW,GAAGA,CAAA,KAAM;IACxBrB,YAAY,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,IAAI,CAACQ,iBAAiB,CAAC,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;;EAEA;EACA,MAAMc,YAAY,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,SAAS,CAAC;EAC7D,MAAMC,eAAe,GAAGD,YAAY,CAACE,IAAI,CAACC,IAAI,IAAIlB,QAAQ,CAACmB,QAAQ,CAACC,QAAQ,CAACF,IAAI,CAAC,CAAC;EACnF,IAAIF,eAAe,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,oBACE7B,OAAA,CAAAE,SAAA;IAAAgC,QAAA,GAEGpB,iBAAiB,CAAC,CAAC,IAAI,CAACT,SAAS,iBAChCL,OAAA;MAAKmC,SAAS,EAAC,+BAA+B;MAAAD,QAAA,eAC5ClC,OAAA;QAAKmC,SAAS,EAAC,qBAAqB;QAAAD,QAAA,gBAClClC,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAD,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrCvC,OAAA;UAAAkC,QAAA,EAAI;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCvC,OAAA;UAAAkC,QAAA,EAAG;QAAiD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxDvC,OAAA;UAAKmC,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACnClC,OAAA;YACEmC,SAAS,EAAC,iBAAiB;YAC3BK,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,IAAI,CAAE;YAAA4B,QAAA,EACnC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvC,OAAA;YACEmC,SAAS,EAAC,aAAa;YACvBK,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAAC5B,QAAQ,CAAC6B,IAAI,GAAG,UAAW;YAAAR,QAAA,EAClD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvC,OAAA;YACEmC,SAAS,EAAC,YAAY;YACtBK,OAAO,EAAEA,CAAA,KAAM;cACbG,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;cAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;cAC/BH,MAAM,CAAC5B,QAAQ,CAAC6B,IAAI,GAAG,QAAQ;YACjC,CAAE;YAAAR,QAAA,EACH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDvC,OAAA,CAACF,iBAAiB;MAChB+C,MAAM,EAAExC,SAAU;MAClByC,OAAO,EAAEnB,WAAY;MACrBoB,SAAS,EAAErB;IAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACnC,EAAA,CA/GID,mBAAmB;EAAA,QAINP,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAAmD,EAAA,GANxB7C,mBAAmB;AAiHzB,eAAeA,mBAAmB;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}