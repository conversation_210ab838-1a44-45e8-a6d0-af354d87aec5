{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"glimp-plan-sample\",\n    title: \"Glimp Plan\",\n    features: [\"1-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 15000,\n    discountedPrice: 13000,\n    discountPercentage: 13,\n    duration: 1,\n    status: true\n  }, {\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n  const handlePlanSelect = async plan => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      setSelectedPlan(plan);\n      setPaymentLoading(true);\n      setShowProcessingModal(true);\n      setPaymentStatus('Initiating payment...');\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('Sending payment request...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        // Start checking payment status\n        setTimeout(() => {\n          checkPaymentConfirmation(response.order_id || 'demo_order');\n        }, 3000);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    try {\n      setPaymentStatus('Waiting for payment confirmation...');\n\n      // Poll payment status every 3 seconds for up to 2 minutes\n      let attempts = 0;\n      const maxAttempts = 40; // 40 attempts * 3 seconds = 2 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {\n            // Payment confirmed!\n            setPaymentStatus('Payment confirmed! Activating your subscription...');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setShowSuccessModal(true);\n              setPaymentLoading(false);\n\n              // Refresh subscription data\n              checkCurrentSubscription();\n            }, 1500);\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            setPaymentStatus('Payment is taking longer than expected. Please check your phone and try again if needed.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(false);\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status in a few minutes.');\n            }, 3000);\n          } else {\n            // Continue polling\n            setPaymentStatus(`Waiting for payment confirmation... (${Math.ceil((maxAttempts - attempts) * 3 / 60)} minutes remaining)`);\n            setTimeout(pollPaymentStatus, 3000);\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n          if (attempts >= maxAttempts) {\n            setShowProcessingModal(false);\n            setPaymentLoading(false);\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error\n            setTimeout(pollPaymentStatus, 3000);\n          }\n        }\n      };\n\n      // Start polling after 3 seconds\n      setTimeout(pollPaymentStatus, 3000);\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"current-price\",\n                    children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"original-price\",\n                    children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading,\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this), paymentLoading ? 'Processing...' : subscriptionStatus === 'active' ? 'Upgrade to This Plan' : subscriptionStatus === 'expired' ? 'Renew with This Plan' : 'Select This Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-processing-animation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"payment-status\",\n              children: paymentStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-details\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-plan-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-instructions\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\uD83D\\uDCF1 Check your phone for SMS confirmation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\uD83D\\uDCB3 Follow the instructions to complete payment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal success-modal\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-animation\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-checkmark\",\n                children: \"\\u2705\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-confetti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDF89 Payment Successful!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"success-message\",\n              children: [\"Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"! Your subscription is now active.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-details\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-plan-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Duration: \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"Amount: \", selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-features\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"\\uD83D\\uDE80 You now have access to:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2705 Unlimited quizzes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2705 AI chat assistance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2705 Study materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2705 Progress tracking\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"\\u2705 All premium features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"success-btn primary\",\n                onClick: () => {\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                },\n                children: \"\\uD83C\\uDFE0 Go to Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"success-btn secondary\",\n                onClick: () => setShowSuccessModal(false),\n                children: \"Continue Here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"7rikL3tWJsP/hz3mgn9dXHf5mpw=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "jsxDEV", "_jsxDEV", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "console", "log", "response", "success", "data", "length", "Array", "isArray", "warn", "info", "error", "warning", "handlePlanSelect", "plan", "phoneNumber", "test", "_user$name", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "setTimeout", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "demo", "Math", "ceil", "getSubscriptionStatus", "endDate", "Date", "now", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "includes", "toLocaleString", "slice", "feature", "index", "disabled", "window", "location", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"glimp-plan-sample\",\n      title: \"Glimp Plan\",\n      features: [\n        \"1-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 15000,\n      discountedPrice: 13000,\n      discountPercentage: 13,\n      duration: 1,\n      status: true\n    },\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  const handlePlanSelect = async (plan) => {\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n      setSelectedPlan(plan);\n      setPaymentLoading(true);\n      setShowProcessingModal(true);\n      setPaymentStatus('Initiating payment...');\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('Sending payment request...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        // Start checking payment status\n        setTimeout(() => {\n          checkPaymentConfirmation(response.order_id || 'demo_order');\n        }, 3000);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    try {\n      setPaymentStatus('Waiting for payment confirmation...');\n\n      // Poll payment status every 3 seconds for up to 2 minutes\n      let attempts = 0;\n      const maxAttempts = 40; // 40 attempts * 3 seconds = 2 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n\n          if (statusResponse.success && (statusResponse.status === 'completed' || statusResponse.demo)) {\n            // Payment confirmed!\n            setPaymentStatus('Payment confirmed! Activating your subscription...');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setShowSuccessModal(true);\n              setPaymentLoading(false);\n\n              // Refresh subscription data\n              checkCurrentSubscription();\n            }, 1500);\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            setPaymentStatus('Payment is taking longer than expected. Please check your phone and try again if needed.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(false);\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status in a few minutes.');\n            }, 3000);\n\n          } else {\n            // Continue polling\n            setPaymentStatus(`Waiting for payment confirmation... (${Math.ceil((maxAttempts - attempts) * 3 / 60)} minutes remaining)`);\n            setTimeout(pollPaymentStatus, 3000);\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n          if (attempts >= maxAttempts) {\n            setShowProcessingModal(false);\n            setPaymentLoading(false);\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error\n            setTimeout(pollPaymentStatus, 3000);\n          }\n        }\n      };\n\n      // Start polling after 3 seconds\n      setTimeout(pollPaymentStatus, 3000);\n\n    } catch (error) {\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(false);\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('glimp') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <span className=\"current-price\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Upgrade to This Plan'\n                        : subscriptionStatus === 'expired'\n                          ? 'Renew with This Plan'\n                          : 'Select This Plan'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Payment Processing Modal */}\n        {showProcessingModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal\">\n              <div className=\"payment-modal-content\">\n                <div className=\"payment-processing-animation\">\n                  <div className=\"payment-spinner\"></div>\n                  <div className=\"payment-pulse\"></div>\n                </div>\n                <h3>Processing Payment</h3>\n                <p className=\"payment-status\">{paymentStatus}</p>\n                <div className=\"payment-details\">\n                  <div className=\"payment-plan-info\">\n                    <h4>{selectedPlan?.title}</h4>\n                    <p>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</p>\n                    <p>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</p>\n                  </div>\n                </div>\n                <div className=\"payment-instructions\">\n                  <p>📱 Check your phone for SMS confirmation</p>\n                  <p>💳 Follow the instructions to complete payment</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Payment Success Modal */}\n        {showSuccessModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal success-modal\">\n              <div className=\"payment-modal-content\">\n                <div className=\"success-animation\">\n                  <div className=\"success-checkmark\">✅</div>\n                  <div className=\"success-confetti\"></div>\n                </div>\n                <h3>🎉 Payment Successful!</h3>\n                <p className=\"success-message\">\n                  Welcome to {selectedPlan?.title}! Your subscription is now active.\n                </p>\n                <div className=\"success-details\">\n                  <div className=\"success-plan-info\">\n                    <h4>{selectedPlan?.title}</h4>\n                    <p>Duration: {selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</p>\n                    <p>Amount: {selectedPlan?.discountedPrice?.toLocaleString()} TZS</p>\n                  </div>\n                </div>\n                <div className=\"success-features\">\n                  <h4>🚀 You now have access to:</h4>\n                  <ul>\n                    <li>✅ Unlimited quizzes</li>\n                    <li>✅ AI chat assistance</li>\n                    <li>✅ Study materials</li>\n                    <li>✅ Progress tracking</li>\n                    <li>✅ All premium features</li>\n                  </ul>\n                </div>\n                <div className=\"success-actions\">\n                  <button\n                    className=\"success-btn primary\"\n                    onClick={() => {\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                  >\n                    🏠 Go to Dashboard\n                  </button>\n                  <button\n                    className=\"success-btn secondary\"\n                    onClick={() => setShowSuccessModal(false)}\n                  >\n                    Continue Here\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEsC;EAAK,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGtC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGvC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwC,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDlD,SAAS,CAAC,MAAM;IACdmD,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChB2B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMC,QAAQ,GAAG,MAAM5C,QAAQ,CAAC,CAAC;MACjC0C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjElC,QAAQ,CAAC+B,QAAQ,CAACE,IAAI,CAAC;QACvBJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MACnE,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QACzD;QACAlC,QAAQ,CAAC+B,QAAQ,CAAC;QAClBF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;MAC1D,CAAC,MAAM;QACLF,OAAO,CAACQ,IAAI,CAAC,uCAAuC,CAAC;QACrDrC,QAAQ,CAACkB,WAAW,CAAC;QACrBtC,OAAO,CAAC0D,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDV,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C9B,QAAQ,CAACkB,WAAW,CAAC;MACrBtC,OAAO,CAAC4D,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAM1C,kBAAkB,CAAC,CAAC;MAC3CwC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdV,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACvC,IAAI,CAAC7B,IAAI,CAAC8B,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC/B,IAAI,CAAC8B,WAAW,CAAC,EAAE;MACjE/D,OAAO,CAAC2D,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAM,UAAA;MACFnC,eAAe,CAACgC,IAAI,CAAC;MACrBtC,iBAAiB,CAAC,IAAI,CAAC;MACvBE,sBAAsB,CAAC,IAAI,CAAC;MAC5BM,gBAAgB,CAAC,uBAAuB,CAAC;MAEzC,MAAMkC,WAAW,GAAG;QAClBJ,IAAI,EAAEA,IAAI;QACVK,MAAM,EAAElC,IAAI,CAACM,GAAG;QAChB6B,SAAS,EAAEnC,IAAI,CAAC8B,WAAW;QAC3BM,SAAS,EAAEpC,IAAI,CAACqC,KAAK,IAAK,IAAAL,UAAA,GAAEhC,IAAI,CAACsC,IAAI,cAAAN,UAAA,uBAATA,UAAA,CAAWO,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAEDzC,gBAAgB,CAAC,4BAA4B,CAAC;MAC9C,MAAMmB,QAAQ,GAAG,MAAM3C,UAAU,CAAC0D,WAAW,CAAC;MAE9C,IAAIf,QAAQ,CAACC,OAAO,EAAE;QACpBpB,gBAAgB,CAAC,wDAAwD,CAAC;;QAE1E;QACA0C,UAAU,CAAC,MAAM;UACfC,wBAAwB,CAACxB,QAAQ,CAACyB,QAAQ,IAAI,YAAY,CAAC;QAC7D,CAAC,EAAE,IAAI,CAAC;MAEV,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC1B,QAAQ,CAACnD,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO2D,KAAK,EAAE;MACdjC,sBAAsB,CAAC,KAAK,CAAC;MAC7B1B,OAAO,CAAC2D,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAAC3D,OAAO,CAAC;MACjDwB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMmD,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAI;MACF9C,gBAAgB,CAAC,qCAAqC,CAAC;;MAEvD;MACA,IAAI+C,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;;MAExB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QAEV,IAAI;UACF,MAAMG,cAAc,GAAG,MAAMzE,kBAAkB,CAAC;YAAEqE;UAAQ,CAAC,CAAC;UAE5D,IAAII,cAAc,CAAC9B,OAAO,KAAK8B,cAAc,CAACpC,MAAM,KAAK,WAAW,IAAIoC,cAAc,CAACC,IAAI,CAAC,EAAE;YAC5F;YACAnD,gBAAgB,CAAC,oDAAoD,CAAC;YAEtE0C,UAAU,CAAC,MAAM;cACfhD,sBAAsB,CAAC,KAAK,CAAC;cAC7BE,mBAAmB,CAAC,IAAI,CAAC;cACzBJ,iBAAiB,CAAC,KAAK,CAAC;;cAExB;cACAwB,wBAAwB,CAAC,CAAC;YAC5B,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM,IAAI+B,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAhD,gBAAgB,CAAC,0FAA0F,CAAC;YAE5G0C,UAAU,CAAC,MAAM;cACfhD,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,KAAK,CAAC;cACxBxB,OAAO,CAAC4D,OAAO,CAAC,8GAA8G,CAAC;YACjI,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACA5B,gBAAgB,CAAE,wCAAuCoD,IAAI,CAACC,IAAI,CAAC,CAACL,WAAW,GAAGD,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAE,qBAAoB,CAAC;YAC3HL,UAAU,CAACO,iBAAiB,EAAE,IAAI,CAAC;UACrC;QAEF,CAAC,CAAC,OAAOtB,KAAK,EAAE;UACdV,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,IAAIoB,QAAQ,IAAIC,WAAW,EAAE;YAC3BtD,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,KAAK,CAAC;YACxBxB,OAAO,CAAC2D,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAe,UAAU,CAACO,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAP,UAAU,CAACO,iBAAiB,EAAE,IAAI,CAAC;IAErC,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdjC,sBAAsB,CAAC,KAAK,CAAC;MAC7B1B,OAAO,CAAC2D,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAAC3D,OAAO,CAAC;MAC9DwB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAM8D,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAInD,gBAAgB,IAAIA,gBAAgB,CAACJ,aAAa,KAAK,MAAM,IAAII,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMyC,OAAO,GAAG,IAAIC,IAAI,CAACrD,gBAAgB,CAACoD,OAAO,CAAC;MAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGE,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAAxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,kBAAkB,MAAK,SAAS,IAAKvD,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAM6C,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIJ,IAAI,CAACI,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAC9D,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEoD,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAACrD,gBAAgB,CAACoD,OAAO,CAAC;IAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMU,QAAQ,GAAGX,OAAO,GAAGE,GAAG;IAC9B,MAAMU,QAAQ,GAAGf,IAAI,CAACC,IAAI,CAACa,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOd,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAED,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGJ,qBAAqB,CAAC,CAAC;EAElD,oBACEzE,OAAA;IAAKwF,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChCzF,OAAA;MAAKwF,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCzF,OAAA,CAACd,MAAM,CAACwG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE/D,QAAQ,EAAE;QAAI,CAAE;QAC9BwD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAE/BzF,OAAA;UAAIwF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxBzF,OAAA,CAACZ,OAAO;YAACoG,SAAS,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnG,OAAA;UAAGwF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGbnG,OAAA,CAACd,MAAM,CAACwG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE/D,QAAQ,EAAE,GAAG;UAAEoE,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhCzF,OAAA;UAAIwF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDtB,kBAAkB,KAAK,QAAQ,iBAC9B7E,OAAA;UAAKwF,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCzF,OAAA;YAAKwF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCzF,OAAA,CAACV,aAAa;cAACkG,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDnG,OAAA;cAAMwF,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNnG,OAAA;YAAKwF,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCzF,OAAA;cAAKwF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzF,OAAA,CAACZ,OAAO;gBAACoG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCnG,OAAA;gBAAAyF,QAAA,GAAM,QAAM,EAAC,CAAAnE,gBAAgB,aAAhBA,gBAAgB,wBAAAnB,qBAAA,GAAhBmB,gBAAgB,CAAE+E,UAAU,cAAAlG,qBAAA,uBAA5BA,qBAAA,CAA8BwB,KAAK,KAAI,cAAc;cAAA;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNnG,OAAA;cAAKwF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzF,OAAA,CAACX,aAAa;gBAACmG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCnG,OAAA;gBAAAyF,QAAA,GAAM,WAAS,EAACX,UAAU,CAACxD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoD,OAAO,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNnG,OAAA;cAAKwF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzF,OAAA,CAACV,aAAa;gBAACkG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCnG,OAAA;gBAAAyF,QAAA,GAAM,kBAAgB,EAACL,gBAAgB,CAAC,CAAC;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAtB,kBAAkB,KAAK,SAAS,iBAC/B7E,OAAA;UAAKwF,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCzF,OAAA;YAAKwF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCzF,OAAA,CAACT,aAAa;cAACiG,SAAS,EAAC;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDnG,OAAA;cAAMwF,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNnG,OAAA;YAAKwF,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCzF,OAAA;cAAKwF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzF,OAAA,CAACX,aAAa;gBAACmG,SAAS,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzCnG,OAAA;gBAAAyF,QAAA,GAAM,WAAS,EAACX,UAAU,CAACxD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoD,OAAO,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNnG,OAAA;cAAGwF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAtB,kBAAkB,KAAK,MAAM,iBAC5B7E,OAAA;UAAKwF,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCzF,OAAA;YAAKwF,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCzF,OAAA,CAACP,MAAM;cAAC+F,SAAS,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCnG,OAAA;cAAMwF,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNnG,OAAA;YAAKwF,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCzF,OAAA;cAAGwF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGbnG,OAAA,CAACd,MAAM,CAACwG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE/D,QAAQ,EAAE,GAAG;UAAEoE,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3BzF,OAAA;UAAIwF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1BZ,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eACLnG,OAAA;UAAGwF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5BZ,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEH3F,OAAO,gBACNR,OAAA;UAAKwF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzF,OAAA;YAAKwF,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BnG,OAAA;YAAAyF,QAAA,EAAG;UAAgB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJ7F,KAAK,CAACmC,MAAM,KAAK,CAAC,gBACpBzC,OAAA;UAAKwF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BzF,OAAA;YAAKwF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCnG,OAAA;YAAAyF,QAAA,EAAI;UAAkB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnG,OAAA;YAAAyF,QAAA,EAAG;UAA6E;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFnG,OAAA;YAAQwF,SAAS,EAAC,aAAa;YAACc,OAAO,EAAEpE,UAAW;YAAAuD,QAAA,EAAC;UAErD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENnG,OAAA;UAAKwF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBnF,KAAK,CAACiG,GAAG,CAAEtD,IAAI;YAAA,IAAAuD,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACd3G,OAAA,CAACd,MAAM,CAACwG,GAAG;cAETkB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BrB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErBzF,OAAA;gBAAKwF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzF,OAAA;kBAAIwF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAExC,IAAI,CAACtB;gBAAK;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAK,WAAA,GAAAvD,IAAI,CAACtB,KAAK,cAAA6E,WAAA,uBAAVA,WAAA,CAAY5C,WAAW,CAAC,CAAC,CAACmD,QAAQ,CAAC,OAAO,CAAC,kBAC1C/G,OAAA;kBAAMwF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENnG,OAAA;gBAAKwF,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BzF,OAAA;kBAAKwF,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BzF,OAAA;oBAAMwF,SAAS,EAAC,eAAe;oBAAAC,QAAA,IAAAgB,qBAAA,GAAExD,IAAI,CAACnB,eAAe,cAAA2E,qBAAA,uBAApBA,qBAAA,CAAsBO,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAClFlD,IAAI,CAACpB,WAAW,GAAGoB,IAAI,CAACnB,eAAe,iBACtC9B,OAAA;oBAAMwF,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,IAAAiB,iBAAA,GAAEzD,IAAI,CAACpB,WAAW,cAAA6E,iBAAA,uBAAhBA,iBAAA,CAAkBM,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAChF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNnG,OAAA;kBAAKwF,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAExC,IAAI,CAACjB,QAAQ,EAAC,QAAM,EAACiB,IAAI,CAACjB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC,eAENnG,OAAA;gBAAKwF,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAkB,cAAA,GAC3B1D,IAAI,CAACrB,QAAQ,cAAA+E,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7CnH,OAAA;kBAAiBwF,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvCzF,OAAA,CAACV,aAAa;oBAACkG,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1CnG,OAAA;oBAAAyF,QAAA,EAAOyB;kBAAO;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdgB,KAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnG,OAAA;gBACEwF,SAAS,EAAC,iBAAiB;gBAC3Bc,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAACC,IAAI,CAAE;gBACtCmE,QAAQ,EAAE1G,cAAe;gBAAA+E,QAAA,gBAEzBzF,OAAA,CAACR,YAAY;kBAACgG,SAAS,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCzF,cAAc,GACX,eAAe,GACfmE,kBAAkB,KAAK,QAAQ,GAC7B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,sBAAsB,GACtB,kBAAkB;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEpB,CAAC;YAAA,GA7CJlD,IAAI,CAACvB,GAAG;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAAC/E,IAAI,CAAC8B,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC/B,IAAI,CAAC8B,WAAW,CAAC,kBAC7DlD,OAAA,CAACd,MAAM,CAACwG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE/D,QAAQ,EAAE,GAAG;UAAEoE,KAAK,EAAE;QAAI,CAAE;QAC1CZ,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzBzF,OAAA;UAAKwF,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzF,OAAA,CAACT,aAAa;YAACiG,SAAS,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CnG,OAAA;YAAAyF,QAAA,gBACEzF,OAAA;cAAAyF,QAAA,EAAI;YAAqB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BnG,OAAA;cAAAyF,QAAA,EAAG;YAAuE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9EnG,OAAA;cACEwF,SAAS,EAAC,kBAAkB;cAC5Bc,OAAO,EAAEA,CAAA,KAAMe,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAA9B,QAAA,EAClD;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGAvF,mBAAmB,iBAClBZ,OAAA;QAAKwF,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCzF,OAAA;UAAKwF,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzF,OAAA;YAAKwF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCzF,OAAA;cAAKwF,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3CzF,OAAA;gBAAKwF,SAAS,EAAC;cAAiB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvCnG,OAAA;gBAAKwF,SAAS,EAAC;cAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNnG,OAAA;cAAAyF,QAAA,EAAI;YAAkB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BnG,OAAA;cAAGwF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEvE;YAAa;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDnG,OAAA;cAAKwF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzF,OAAA;gBAAKwF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCzF,OAAA;kBAAAyF,QAAA,EAAKzE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW;gBAAK;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BnG,OAAA;kBAAAyF,QAAA,GAAIzE,YAAY,aAAZA,YAAY,wBAAAZ,qBAAA,GAAZY,YAAY,CAAEc,eAAe,cAAA1B,qBAAA,uBAA7BA,qBAAA,CAA+B4G,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DnG,OAAA;kBAAAyF,QAAA,GAAIzE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,EAAC,QAAM,EAAC,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnG,OAAA;cAAKwF,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCzF,OAAA;gBAAAyF,QAAA,EAAG;cAAwC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC/CnG,OAAA;gBAAAyF,QAAA,EAAG;cAA8C;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArF,gBAAgB,iBACfd,OAAA;QAAKwF,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpCzF,OAAA;UAAKwF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CzF,OAAA;YAAKwF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCzF,OAAA;cAAKwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCzF,OAAA;gBAAKwF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1CnG,OAAA;gBAAKwF,SAAS,EAAC;cAAkB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNnG,OAAA;cAAAyF,QAAA,EAAI;YAAsB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BnG,OAAA;cAAGwF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,aAClB,EAACzE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW,KAAK,EAAC,oCAClC;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnG,OAAA;cAAKwF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BzF,OAAA;gBAAKwF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCzF,OAAA;kBAAAyF,QAAA,EAAKzE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW;gBAAK;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BnG,OAAA;kBAAAyF,QAAA,GAAG,YAAU,EAACzE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,EAAC,QAAM,EAAC,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgB,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtFnG,OAAA;kBAAAyF,QAAA,GAAG,UAAQ,EAACzE,YAAY,aAAZA,YAAY,wBAAAX,sBAAA,GAAZW,YAAY,CAAEc,eAAe,cAAAzB,sBAAA,uBAA7BA,sBAAA,CAA+B2G,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnG,OAAA;cAAKwF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BzF,OAAA;gBAAAyF,QAAA,EAAI;cAA0B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCnG,OAAA;gBAAAyF,QAAA,gBACEzF,OAAA;kBAAAyF,QAAA,EAAI;gBAAmB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5BnG,OAAA;kBAAAyF,QAAA,EAAI;gBAAoB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BnG,OAAA;kBAAAyF,QAAA,EAAI;gBAAiB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BnG,OAAA;kBAAAyF,QAAA,EAAI;gBAAmB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5BnG,OAAA;kBAAAyF,QAAA,EAAI;gBAAsB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNnG,OAAA;cAAKwF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzF,OAAA;gBACEwF,SAAS,EAAC,qBAAqB;gBAC/Bc,OAAO,EAAEA,CAAA,KAAM;kBACbvF,mBAAmB,CAAC,KAAK,CAAC;kBAC1BsG,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;gBACpC,CAAE;gBAAA9B,QAAA,EACH;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTnG,OAAA;gBACEwF,SAAS,EAAC,uBAAuB;gBACjCc,OAAO,EAAEA,CAAA,KAAMvF,mBAAmB,CAAC,KAAK,CAAE;gBAAA0E,QAAA,EAC3C;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjG,EAAA,CAliBID,YAAY;EAAA,QAQCjB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAAuI,EAAA,GAVxBvH,YAAY;AAoiBlB,eAAeA,YAAY;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}