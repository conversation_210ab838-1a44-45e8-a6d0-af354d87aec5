{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion}from'framer-motion';import{useDispatch,useSelector}from'react-redux';import{useNavigate}from'react-router-dom';import{Progress}from'antd';import{TbU<PERSON><PERSON>,Tb<PERSON><PERSON>,TbFileText,TbChartBar,TbTrendingUp,TbTarget,TbAward,TbClock,TbPlus,TbEye,TbRobot,TbBell,TbMessageCircle}from'react-icons/tb';import{getAllUsers}from'../../../apicalls/users';import{getAllExams}from'../../../apicalls/exams';import{getAllReports}from'../../../apicalls/reports';import{HideLoading,ShowLoading}from'../../../redux/loaderSlice';import AdminLayout from'../../../components/AdminLayout';import AdminCard from'../../../components/AdminCard';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const AdminDashboard=()=>{const dispatch=useDispatch();const navigate=useNavigate();const{user}=useSelector(state=>state.user);const[stats,setStats]=useState({totalUsers:0,activeUsers:0,totalExams:0,totalReports:0,averageScore:0,completionRate:0});const[loading,setLoading]=useState(true);useEffect(()=>{fetchDashboardData();},[]);const fetchDashboardData=async()=>{try{setLoading(true);dispatch(ShowLoading());// Fetch users data\nconst usersResponse=await getAllUsers();const users=usersResponse.success?usersResponse.users:[];// Fetch exams data\nconst examsResponse=await getAllExams();const exams=examsResponse.success?examsResponse.data:[];// Fetch reports data (with empty filters to get all reports)\nconst reportsResponse=await getAllReports({examName:'',userName:'',page:1,limit:1000});const reports=reportsResponse.success?reportsResponse.data:[];// Calculate statistics\nconst totalUsers=users.length;const activeUsers=users.filter(u=>!u.isBlocked).length;const totalExams=exams.length;const totalReports=reports.length;// Calculate average score from reports\nconst averageScore=reports.length>0?reports.reduce((sum,report)=>sum+(report.percentage||0),0)/reports.length:0;// Calculate completion rate\nconst completionRate=totalUsers>0?totalReports/totalUsers*100:0;setStats({totalUsers,activeUsers,totalExams,totalReports,averageScore:Math.round(averageScore),completionRate:Math.round(completionRate)});setLoading(false);dispatch(HideLoading());}catch(error){console.error('Error fetching dashboard data:',error);setLoading(false);dispatch(HideLoading());}};const quickActions=[{title:'Manage Users',description:'View and manage student accounts',icon:TbUsers,path:'/admin/users',color:'bg-blue-500'},{title:'Create Exam',description:'Add new exams and questions',icon:TbFileText,path:'/admin/exams/add',color:'bg-green-500'},{title:'Study Materials',description:'Manage learning resources',icon:TbBook,path:'/admin/study-materials',color:'bg-orange-500'},{title:'View Reports',description:'Analytics and performance',icon:TbChartBar,path:'/admin/reports',color:'bg-indigo-500'},{title:'Notifications',description:'Send announcements',icon:TbBell,path:'/admin/notifications',color:'bg-pink-500'},{title:'Forum Management',description:'Manage community forum',icon:TbMessageCircle,path:'/admin/forum',color:'bg-purple-500'}];const quickActionButtons=[/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>navigate('/admin/users'),className:\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(TbUsers,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:\"Manage Users\"})]},\"users\"),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>navigate('/admin/exams/add'),className:\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(TbPlus,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:\"Create Exam\"})]},\"exams\"),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>navigate('/admin/ai-questions'),className:\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(TbRobot,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:\"AI Questions\"})]},\"ai\")];return/*#__PURE__*/_jsxs(AdminLayout,{showHeader:false,children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6 sm:mb-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 sm:p-8 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-2xl sm:text-3xl font-bold mb-2\",children:[\"Welcome back, \",user===null||user===void 0?void 0:user.name,\"! \\uD83D\\uDC4B\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm sm:text-base\",children:\"Here's what's happening with your educational platform today.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-2\",children:quickActionButtons})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\",children:[/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm font-medium mb-1\",children:\"Total Students\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:stats.totalUsers}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-200 text-xs mt-1\",children:\"Registered users\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbUsers,{className:\"w-6 h-6\"})})]})}),/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-green-100 text-sm font-medium mb-1\",children:\"Active Users\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:stats.activeUsers}),/*#__PURE__*/_jsx(\"p\",{className:\"text-green-200 text-xs mt-1\",children:\"Currently active\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbTrendingUp,{className:\"w-6 h-6\"})})]})}),/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-purple-100 text-sm font-medium mb-1\",children:\"Total Exams\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:stats.totalExams}),/*#__PURE__*/_jsx(\"p\",{className:\"text-purple-200 text-xs mt-1\",children:\"Available exams\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbFileText,{className:\"w-6 h-6\"})})]})}),/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-orange-100 text-sm font-medium mb-1\",children:\"Avg Score\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:[stats.averageScore,\"%\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-orange-200 text-xs mt-1\",children:\"Overall performance\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbAward,{className:\"w-6 h-6\"})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8\",children:[/*#__PURE__*/_jsx(AdminCard,{title:\"Quick Actions\",subtitle:\"Common administrative tasks\",children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 gap-4\",children:quickActions.map((action,index)=>{const IconComponent=action.icon;return/*#__PURE__*/_jsxs(motion.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:0.3,delay:index*0.1},onClick:()=>navigate(action.path),className:\"p-4 rounded-xl \".concat(action.color,\" text-white hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-left\"),children:[/*#__PURE__*/_jsx(IconComponent,{className:\"w-8 h-8 mb-3\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-sm sm:text-base mb-1\",children:action.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs sm:text-sm opacity-90\",children:action.description})]},action.title);})})}),/*#__PURE__*/_jsx(AdminCard,{title:\"Performance Analytics\",subtitle:\"System performance overview\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-slate-700\",children:\"Completion Rate\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-slate-900\",children:[stats.completionRate,\"%\"]})]}),/*#__PURE__*/_jsx(Progress,{percent:stats.completionRate,strokeColor:{'0%':'#3B82F6','100%':'#8B5CF6'},className:\"mb-4\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-slate-700\",children:\"Average Score\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-bold text-slate-900\",children:[stats.averageScore,\"%\"]})]}),/*#__PURE__*/_jsx(Progress,{percent:stats.averageScore,strokeColor:{'0%':'#10B981','100%':'#059669'},className:\"mb-4\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4 pt-4 border-t border-slate-100\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl font-bold text-slate-900\",children:stats.totalReports}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-slate-600\",children:\"Total Attempts\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold text-slate-900\",children:[Math.round(stats.activeUsers/stats.totalUsers*100)||0,\"%\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-slate-600\",children:\"User Activity\"})]})]})]})})]})]});};export default AdminDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useDispatch", "useSelector", "useNavigate", "Progress", "TbUsers", "TbBook", "TbFileText", "TbChartBar", "TbTrendingUp", "TbTarget", "TbAward", "TbClock", "TbPlus", "TbEye", "TbRobot", "TbBell", "TbMessageCircle", "getAllUsers", "getAllExams", "getAllReports", "HideLoading", "ShowLoading", "AdminLayout", "AdminCard", "jsx", "_jsx", "jsxs", "_jsxs", "AdminDashboard", "dispatch", "navigate", "user", "state", "stats", "setStats", "totalUsers", "activeUsers", "totalExams", "totalReports", "averageScore", "completionRate", "loading", "setLoading", "fetchDashboardData", "usersResponse", "users", "success", "examsResponse", "exams", "data", "reportsResponse", "examName", "userName", "page", "limit", "reports", "length", "filter", "u", "isBlocked", "reduce", "sum", "report", "percentage", "Math", "round", "error", "console", "quickActions", "title", "description", "icon", "path", "color", "quickActionButtons", "button", "whileHover", "scale", "whileTap", "onClick", "className", "children", "showHeader", "name", "subtitle", "map", "action", "index", "IconComponent", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "concat", "percent", "strokeColor"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Dashboard/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Progress } from 'antd';\nimport {\n  TbU<PERSON><PERSON>,\n  TbB<PERSON>,\n  TbFileText,\n  TbChartBar,\n  TbTrendingUp,\n  TbTarget,\n  TbAward,\n  TbClock,\n  TbPlus,\n  TbEye,\n  TbRobot,\n  TbBell,\n  TbMessageCircle\n} from 'react-icons/tb';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReports } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport AdminLayout from '../../../components/AdminLayout';\nimport AdminCard from '../../../components/AdminCard';\n\nconst AdminDashboard = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    totalExams: 0,\n    totalReports: 0,\n    averageScore: 0,\n    completionRate: 0\n  });\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      // Fetch users data\n      const usersResponse = await getAllUsers();\n      const users = usersResponse.success ? usersResponse.users : [];\n\n      // Fetch exams data\n      const examsResponse = await getAllExams();\n      const exams = examsResponse.success ? examsResponse.data : [];\n\n      // Fetch reports data (with empty filters to get all reports)\n      const reportsResponse = await getAllReports({ examName: '', userName: '', page: 1, limit: 1000 });\n      const reports = reportsResponse.success ? reportsResponse.data : [];\n\n      // Calculate statistics\n      const totalUsers = users.length;\n      const activeUsers = users.filter(u => !u.isBlocked).length;\n      const totalExams = exams.length;\n      const totalReports = reports.length;\n      \n      // Calculate average score from reports\n      const averageScore = reports.length > 0 \n        ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length\n        : 0;\n      \n      // Calculate completion rate\n      const completionRate = totalUsers > 0 ? (totalReports / totalUsers) * 100 : 0;\n\n      setStats({\n        totalUsers,\n        activeUsers,\n        totalExams,\n        totalReports,\n        averageScore: Math.round(averageScore),\n        completionRate: Math.round(completionRate)\n      });\n\n      setLoading(false);\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n\n\n  const quickActions = [\n    {\n      title: 'Manage Users',\n      description: 'View and manage student accounts',\n      icon: TbUsers,\n      path: '/admin/users',\n      color: 'bg-blue-500'\n    },\n    {\n      title: 'Create Exam',\n      description: 'Add new exams and questions',\n      icon: TbFileText,\n      path: '/admin/exams/add',\n      color: 'bg-green-500'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Manage learning resources',\n      icon: TbBook,\n      path: '/admin/study-materials',\n      color: 'bg-orange-500'\n    },\n    {\n      title: 'View Reports',\n      description: 'Analytics and performance',\n      icon: TbChartBar,\n      path: '/admin/reports',\n      color: 'bg-indigo-500'\n    },\n    {\n      title: 'Notifications',\n      description: 'Send announcements',\n      icon: TbBell,\n      path: '/admin/notifications',\n      color: 'bg-pink-500'\n    },\n    {\n      title: 'Forum Management',\n      description: 'Manage community forum',\n      icon: TbMessageCircle,\n      path: '/admin/forum',\n      color: 'bg-purple-500'\n    }\n  ];\n\n  const quickActionButtons = [\n    <motion.button\n      key=\"users\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/users')}\n      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbUsers className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">Manage Users</span>\n    </motion.button>,\n    <motion.button\n      key=\"exams\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/exams/add')}\n      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbPlus className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">Create Exam</span>\n    </motion.button>,\n    <motion.button\n      key=\"ai\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/ai-questions')}\n      className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbRobot className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">AI Questions</span>\n    </motion.button>,\n  ];\n\n  return (\n    <AdminLayout showHeader={false}>\n      {/* Welcome Section */}\n      <div className=\"mb-6 sm:mb-8\">\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 sm:p-8 text-white\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n                Welcome back, {user?.name}! 👋\n              </h1>\n              <p className=\"text-blue-100 text-sm sm:text-base\">\n                Here's what's happening with your educational platform today.\n              </p>\n            </div>\n            <div className=\"flex flex-wrap gap-2\">\n              {quickActionButtons}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n        <AdminCard className=\"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-blue-100 text-sm font-medium mb-1\">Total Students</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.totalUsers}</p>\n              <p className=\"text-blue-200 text-xs mt-1\">Registered users</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbUsers className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-green-100 text-sm font-medium mb-1\">Active Users</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.activeUsers}</p>\n              <p className=\"text-green-200 text-xs mt-1\">Currently active</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbTrendingUp className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-purple-100 text-sm font-medium mb-1\">Total Exams</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.totalExams}</p>\n              <p className=\"text-purple-200 text-xs mt-1\">Available exams</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbFileText className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-orange-100 text-sm font-medium mb-1\">Avg Score</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.averageScore}%</p>\n              <p className=\"text-orange-200 text-xs mt-1\">Overall performance</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbAward className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n      </div>\n\n      {/* Quick Actions and Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8\">\n        {/* Quick Actions */}\n        <AdminCard \n          title=\"Quick Actions\" \n          subtitle=\"Common administrative tasks\"\n        >\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            {quickActions.map((action, index) => {\n              const IconComponent = action.icon;\n              return (\n                <motion.button\n                  key={action.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                  onClick={() => navigate(action.path)}\n                  className={`p-4 rounded-xl ${action.color} text-white hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-left`}\n                >\n                  <IconComponent className=\"w-8 h-8 mb-3\" />\n                  <h3 className=\"font-semibold text-sm sm:text-base mb-1\">{action.title}</h3>\n                  <p className=\"text-xs sm:text-sm opacity-90\">{action.description}</p>\n                </motion.button>\n              );\n            })}\n          </div>\n        </AdminCard>\n\n        {/* Performance Analytics */}\n        <AdminCard \n          title=\"Performance Analytics\" \n          subtitle=\"System performance overview\"\n        >\n          <div className=\"space-y-6\">\n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-slate-700\">Completion Rate</span>\n                <span className=\"text-sm font-bold text-slate-900\">{stats.completionRate}%</span>\n              </div>\n              <Progress \n                percent={stats.completionRate} \n                strokeColor={{\n                  '0%': '#3B82F6',\n                  '100%': '#8B5CF6',\n                }}\n                className=\"mb-4\"\n              />\n            </div>\n            \n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-slate-700\">Average Score</span>\n                <span className=\"text-sm font-bold text-slate-900\">{stats.averageScore}%</span>\n              </div>\n              <Progress \n                percent={stats.averageScore} \n                strokeColor={{\n                  '0%': '#10B981',\n                  '100%': '#059669',\n                }}\n                className=\"mb-4\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4 pt-4 border-t border-slate-100\">\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.totalReports}</p>\n                <p className=\"text-xs text-slate-600\">Total Attempts</p>\n              </div>\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-slate-900\">{Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%</p>\n                <p className=\"text-xs text-slate-600\">User Activity</p>\n              </div>\n            </div>\n          </div>\n        </AdminCard>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,QAAQ,KAAQ,MAAM,CAC/B,OACEC,OAAO,CACPC,MAAM,CACNC,UAAU,CACVC,UAAU,CACVC,YAAY,CACZC,QAAQ,CACRC,OAAO,CACPC,OAAO,CACPC,MAAM,CACNC,KAAK,CACLC,OAAO,CACPC,MAAM,CACNC,eAAe,KACV,gBAAgB,CACvB,OAASC,WAAW,KAAQ,yBAAyB,CACrD,OAASC,WAAW,KAAQ,yBAAyB,CACrD,OAASC,aAAa,KAAQ,2BAA2B,CACzD,OAASC,WAAW,CAAEC,WAAW,KAAQ,4BAA4B,CACrE,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8B,QAAQ,CAAG5B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE6B,IAAK,CAAC,CAAG9B,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACD,IAAI,CAAC,CACnD,KAAM,CAACE,KAAK,CAAEC,QAAQ,CAAC,CAAGrC,QAAQ,CAAC,CACjCsC,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,CAAC,CACdC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,CAAC,CACfC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAClB,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CAE5CC,SAAS,CAAC,IAAM,CACd6C,kBAAkB,CAAC,CAAC,CACtB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACFD,UAAU,CAAC,IAAI,CAAC,CAChBb,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAuB,aAAa,CAAG,KAAM,CAAA3B,WAAW,CAAC,CAAC,CACzC,KAAM,CAAA4B,KAAK,CAAGD,aAAa,CAACE,OAAO,CAAGF,aAAa,CAACC,KAAK,CAAG,EAAE,CAE9D;AACA,KAAM,CAAAE,aAAa,CAAG,KAAM,CAAA7B,WAAW,CAAC,CAAC,CACzC,KAAM,CAAA8B,KAAK,CAAGD,aAAa,CAACD,OAAO,CAAGC,aAAa,CAACE,IAAI,CAAG,EAAE,CAE7D;AACA,KAAM,CAAAC,eAAe,CAAG,KAAM,CAAA/B,aAAa,CAAC,CAAEgC,QAAQ,CAAE,EAAE,CAAEC,QAAQ,CAAE,EAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,KAAK,CAAE,IAAK,CAAC,CAAC,CACjG,KAAM,CAAAC,OAAO,CAAGL,eAAe,CAACJ,OAAO,CAAGI,eAAe,CAACD,IAAI,CAAG,EAAE,CAEnE;AACA,KAAM,CAAAd,UAAU,CAAGU,KAAK,CAACW,MAAM,CAC/B,KAAM,CAAApB,WAAW,CAAGS,KAAK,CAACY,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAACC,SAAS,CAAC,CAACH,MAAM,CAC1D,KAAM,CAAAnB,UAAU,CAAGW,KAAK,CAACQ,MAAM,CAC/B,KAAM,CAAAlB,YAAY,CAAGiB,OAAO,CAACC,MAAM,CAEnC;AACA,KAAM,CAAAjB,YAAY,CAAGgB,OAAO,CAACC,MAAM,CAAG,CAAC,CACnCD,OAAO,CAACK,MAAM,CAAC,CAACC,GAAG,CAAEC,MAAM,GAAKD,GAAG,EAAIC,MAAM,CAACC,UAAU,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAAGR,OAAO,CAACC,MAAM,CACnF,CAAC,CAEL;AACA,KAAM,CAAAhB,cAAc,CAAGL,UAAU,CAAG,CAAC,CAAIG,YAAY,CAAGH,UAAU,CAAI,GAAG,CAAG,CAAC,CAE7ED,QAAQ,CAAC,CACPC,UAAU,CACVC,WAAW,CACXC,UAAU,CACVC,YAAY,CACZC,YAAY,CAAEyB,IAAI,CAACC,KAAK,CAAC1B,YAAY,CAAC,CACtCC,cAAc,CAAEwB,IAAI,CAACC,KAAK,CAACzB,cAAc,CAC3C,CAAC,CAAC,CAEFE,UAAU,CAAC,KAAK,CAAC,CACjBb,QAAQ,CAACT,WAAW,CAAC,CAAC,CAAC,CACzB,CAAE,MAAO8C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDxB,UAAU,CAAC,KAAK,CAAC,CACjBb,QAAQ,CAACT,WAAW,CAAC,CAAC,CAAC,CACzB,CACF,CAAC,CAID,KAAM,CAAAgD,YAAY,CAAG,CACnB,CACEC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,kCAAkC,CAC/CC,IAAI,CAAEnE,OAAO,CACboE,IAAI,CAAE,cAAc,CACpBC,KAAK,CAAE,aACT,CAAC,CACD,CACEJ,KAAK,CAAE,aAAa,CACpBC,WAAW,CAAE,6BAA6B,CAC1CC,IAAI,CAAEjE,UAAU,CAChBkE,IAAI,CAAE,kBAAkB,CACxBC,KAAK,CAAE,cACT,CAAC,CACD,CACEJ,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,2BAA2B,CACxCC,IAAI,CAAElE,MAAM,CACZmE,IAAI,CAAE,wBAAwB,CAC9BC,KAAK,CAAE,eACT,CAAC,CACD,CACEJ,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,2BAA2B,CACxCC,IAAI,CAAEhE,UAAU,CAChBiE,IAAI,CAAE,gBAAgB,CACtBC,KAAK,CAAE,eACT,CAAC,CACD,CACEJ,KAAK,CAAE,eAAe,CACtBC,WAAW,CAAE,oBAAoB,CACjCC,IAAI,CAAExD,MAAM,CACZyD,IAAI,CAAE,sBAAsB,CAC5BC,KAAK,CAAE,aACT,CAAC,CACD,CACEJ,KAAK,CAAE,kBAAkB,CACzBC,WAAW,CAAE,wBAAwB,CACrCC,IAAI,CAAEvD,eAAe,CACrBwD,IAAI,CAAE,cAAc,CACpBC,KAAK,CAAE,eACT,CAAC,CACF,CAED,KAAM,CAAAC,kBAAkB,CAAG,cACzB/C,KAAA,CAAC5B,MAAM,CAAC4E,MAAM,EAEZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAMjD,QAAQ,CAAC,cAAc,CAAE,CACxCkD,SAAS,CAAC,sHAAsH,CAAAC,QAAA,eAEhIxD,IAAA,CAACrB,OAAO,EAAC4E,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/BvD,IAAA,SAAMuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,GAPlD,OAQS,CAAC,cAChBtD,KAAA,CAAC5B,MAAM,CAAC4E,MAAM,EAEZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAMjD,QAAQ,CAAC,kBAAkB,CAAE,CAC5CkD,SAAS,CAAC,wHAAwH,CAAAC,QAAA,eAElIxD,IAAA,CAACb,MAAM,EAACoE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC9BvD,IAAA,SAAMuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,GAPjD,OAQS,CAAC,cAChBtD,KAAA,CAAC5B,MAAM,CAAC4E,MAAM,EAEZC,UAAU,CAAE,CAAEC,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BE,OAAO,CAAEA,CAAA,GAAMjD,QAAQ,CAAC,qBAAqB,CAAE,CAC/CkD,SAAS,CAAC,0HAA0H,CAAAC,QAAA,eAEpIxD,IAAA,CAACX,OAAO,EAACkE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/BvD,IAAA,SAAMuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,GAPlD,IAQS,CAAC,CACjB,CAED,mBACEtD,KAAA,CAACL,WAAW,EAAC4D,UAAU,CAAE,KAAM,CAAAD,QAAA,eAE7BxD,IAAA,QAAKuD,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BxD,IAAA,QAAKuD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC7FtD,KAAA,QAAKqD,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjFtD,KAAA,QAAAsD,QAAA,eACEtD,KAAA,OAAIqD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,EAAC,gBACpC,CAAClD,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoD,IAAI,CAAC,gBAC5B,EAAI,CAAC,cACL1D,IAAA,MAAGuD,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,+DAElD,CAAG,CAAC,EACD,CAAC,cACNxD,IAAA,QAAKuD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClCP,kBAAkB,CAChB,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAGN/C,KAAA,QAAKqD,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eACzFxD,IAAA,CAACF,SAAS,EAACyD,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cACpFtD,KAAA,QAAKqD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDtD,KAAA,QAAAsD,QAAA,eACExD,IAAA,MAAGuD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,cACxExD,IAAA,MAAGuD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEhD,KAAK,CAACE,UAAU,CAAI,CAAC,cACpEV,IAAA,MAAGuD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,EAC3D,CAAC,cACNxD,IAAA,QAAKuD,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFxD,IAAA,CAACrB,OAAO,EAAC4E,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,EACH,CAAC,CACG,CAAC,cAEZvD,IAAA,CAACF,SAAS,EAACyD,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cACtFtD,KAAA,QAAKqD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDtD,KAAA,QAAAsD,QAAA,eACExD,IAAA,MAAGuD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,cACvExD,IAAA,MAAGuD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEhD,KAAK,CAACG,WAAW,CAAI,CAAC,cACrEX,IAAA,MAAGuD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,EAC5D,CAAC,cACNxD,IAAA,QAAKuD,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFxD,IAAA,CAACjB,YAAY,EAACwE,SAAS,CAAC,SAAS,CAAE,CAAC,CACjC,CAAC,EACH,CAAC,CACG,CAAC,cAEZvD,IAAA,CAACF,SAAS,EAACyD,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cACxFtD,KAAA,QAAKqD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDtD,KAAA,QAAAsD,QAAA,eACExD,IAAA,MAAGuD,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,aAAW,CAAG,CAAC,cACvExD,IAAA,MAAGuD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEhD,KAAK,CAACI,UAAU,CAAI,CAAC,cACpEZ,IAAA,MAAGuD,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,EAC5D,CAAC,cACNxD,IAAA,QAAKuD,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFxD,IAAA,CAACnB,UAAU,EAAC0E,SAAS,CAAC,SAAS,CAAE,CAAC,CAC/B,CAAC,EACH,CAAC,CACG,CAAC,cAEZvD,IAAA,CAACF,SAAS,EAACyD,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cACxFtD,KAAA,QAAKqD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDtD,KAAA,QAAAsD,QAAA,eACExD,IAAA,MAAGuD,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,WAAS,CAAG,CAAC,cACrEtD,KAAA,MAAGqD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAAEhD,KAAK,CAACM,YAAY,CAAC,GAAC,EAAG,CAAC,cACvEd,IAAA,MAAGuD,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,qBAAmB,CAAG,CAAC,EAChE,CAAC,cACNxD,IAAA,QAAKuD,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFxD,IAAA,CAACf,OAAO,EAACsE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,EACH,CAAC,CACG,CAAC,EACT,CAAC,cAGNrD,KAAA,QAAKqD,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eAEjExD,IAAA,CAACF,SAAS,EACR8C,KAAK,CAAC,eAAe,CACrBe,QAAQ,CAAC,6BAA6B,CAAAH,QAAA,cAEtCxD,IAAA,QAAKuD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDb,YAAY,CAACiB,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,GAAK,CACnC,KAAM,CAAAC,aAAa,CAAGF,MAAM,CAACf,IAAI,CACjC,mBACE5C,KAAA,CAAC5B,MAAM,CAAC4E,MAAM,EAEZc,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAEC,KAAK,CAAER,KAAK,CAAG,GAAI,CAAE,CAClDR,OAAO,CAAEA,CAAA,GAAMjD,QAAQ,CAACwD,MAAM,CAACd,IAAI,CAAE,CACrCQ,SAAS,mBAAAgB,MAAA,CAAoBV,MAAM,CAACb,KAAK,+FAA8F,CAAAQ,QAAA,eAEvIxD,IAAA,CAAC+D,aAAa,EAACR,SAAS,CAAC,cAAc,CAAE,CAAC,cAC1CvD,IAAA,OAAIuD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAEK,MAAM,CAACjB,KAAK,CAAK,CAAC,cAC3E5C,IAAA,MAAGuD,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAEK,MAAM,CAAChB,WAAW,CAAI,CAAC,GAThEgB,MAAM,CAACjB,KAUC,CAAC,CAEpB,CAAC,CAAC,CACC,CAAC,CACG,CAAC,cAGZ5C,IAAA,CAACF,SAAS,EACR8C,KAAK,CAAC,uBAAuB,CAC7Be,QAAQ,CAAC,6BAA6B,CAAAH,QAAA,cAEtCtD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBtD,KAAA,QAAAsD,QAAA,eACEtD,KAAA,QAAKqD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDxD,IAAA,SAAMuD,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,cAC3EtD,KAAA,SAAMqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAEhD,KAAK,CAACO,cAAc,CAAC,GAAC,EAAM,CAAC,EAC9E,CAAC,cACNf,IAAA,CAACtB,QAAQ,EACP8F,OAAO,CAAEhE,KAAK,CAACO,cAAe,CAC9B0D,WAAW,CAAE,CACX,IAAI,CAAE,SAAS,CACf,MAAM,CAAE,SACV,CAAE,CACFlB,SAAS,CAAC,MAAM,CACjB,CAAC,EACC,CAAC,cAENrD,KAAA,QAAAsD,QAAA,eACEtD,KAAA,QAAKqD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDxD,IAAA,SAAMuD,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,eAAa,CAAM,CAAC,cACzEtD,KAAA,SAAMqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAEhD,KAAK,CAACM,YAAY,CAAC,GAAC,EAAM,CAAC,EAC5E,CAAC,cACNd,IAAA,CAACtB,QAAQ,EACP8F,OAAO,CAAEhE,KAAK,CAACM,YAAa,CAC5B2D,WAAW,CAAE,CACX,IAAI,CAAE,SAAS,CACf,MAAM,CAAE,SACV,CAAE,CACFlB,SAAS,CAAC,MAAM,CACjB,CAAC,EACC,CAAC,cAENrD,KAAA,QAAKqD,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpEtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxD,IAAA,MAAGuD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEhD,KAAK,CAACK,YAAY,CAAI,CAAC,cACzEb,IAAA,MAAGuD,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,EACrD,CAAC,cACNtD,KAAA,QAAKqD,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BtD,KAAA,MAAGqD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAEjB,IAAI,CAACC,KAAK,CAAEhC,KAAK,CAACG,WAAW,CAAGH,KAAK,CAACE,UAAU,CAAI,GAAG,CAAC,EAAI,CAAC,CAAC,GAAC,EAAG,CAAC,cACrHV,IAAA,MAAGuD,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,EACpD,CAAC,EACH,CAAC,EACH,CAAC,CACG,CAAC,EACT,CAAC,EACK,CAAC,CAElB,CAAC,CAED,cAAe,CAAArD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}