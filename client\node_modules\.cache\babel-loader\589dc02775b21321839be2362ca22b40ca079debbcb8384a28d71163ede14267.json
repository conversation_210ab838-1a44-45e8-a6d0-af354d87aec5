{"ast": null, "code": "import React,{createContext,useContext,useEffect,useState}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const ThemeContext=/*#__PURE__*/createContext();export const useTheme=()=>{const context=useContext(ThemeContext);if(!context){throw new Error('useTheme must be used within a ThemeProvider');}return context;};export const ThemeProvider=_ref=>{let{children}=_ref;const[isDarkMode,setIsDarkMode]=useState(false);useEffect(()=>{// Check for saved theme preference or default to light mode\nconst savedTheme=localStorage.getItem('theme');const prefersDark=window.matchMedia('(prefers-color-scheme: dark)').matches;if(savedTheme==='dark'||!savedTheme&&prefersDark){setIsDarkMode(true);document.documentElement.classList.add('dark');}else{setIsDarkMode(false);document.documentElement.classList.remove('dark');}},[]);const toggleTheme=()=>{const newTheme=!isDarkMode;setIsDarkMode(newTheme);if(newTheme){document.documentElement.classList.add('dark');localStorage.setItem('theme','dark');}else{document.documentElement.classList.remove('dark');localStorage.setItem('theme','light');}};const value={isDarkMode,toggleTheme};return/*#__PURE__*/_jsx(ThemeContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsx", "_jsx", "ThemeContext", "useTheme", "context", "Error", "ThemeProvider", "_ref", "children", "isDarkMode", "setIsDarkMode", "savedTheme", "localStorage", "getItem", "prefersDark", "window", "matchMedia", "matches", "document", "documentElement", "classList", "add", "remove", "toggleTheme", "newTheme", "setItem", "value", "Provider"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/contexts/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {\n      setIsDarkMode(true);\n      document.documentElement.classList.add('dark');\n    } else {\n      setIsDarkMode(false);\n      document.documentElement.classList.remove('dark');\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = !isDarkMode;\n    setIsDarkMode(newTheme);\n    \n    if (newTheme) {\n      document.documentElement.classList.add('dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  const value = {\n    isDarkMode,\n    toggleTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAE9E,KAAM,CAAAC,YAAY,cAAGN,aAAa,CAAC,CAAC,CAEpC,MAAO,MAAM,CAAAO,QAAQ,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,OAAO,CAAGP,UAAU,CAACK,YAAY,CAAC,CACxC,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,8CAA8C,CAAC,CACjE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,aAAa,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACxC,KAAM,CAACE,UAAU,CAAEC,aAAa,CAAC,CAAGX,QAAQ,CAAC,KAAK,CAAC,CAEnDD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAa,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAChD,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,CAE7E,GAAIN,UAAU,GAAK,MAAM,EAAK,CAACA,UAAU,EAAIG,WAAY,CAAE,CACzDJ,aAAa,CAAC,IAAI,CAAC,CACnBQ,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC,CAChD,CAAC,IAAM,CACLX,aAAa,CAAC,KAAK,CAAC,CACpBQ,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC,CACnD,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,QAAQ,CAAG,CAACf,UAAU,CAC5BC,aAAa,CAACc,QAAQ,CAAC,CAEvB,GAAIA,QAAQ,CAAE,CACZN,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC,CAC9CT,YAAY,CAACa,OAAO,CAAC,OAAO,CAAE,MAAM,CAAC,CACvC,CAAC,IAAM,CACLP,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC,CACjDV,YAAY,CAACa,OAAO,CAAC,OAAO,CAAE,OAAO,CAAC,CACxC,CACF,CAAC,CAED,KAAM,CAAAC,KAAK,CAAG,CACZjB,UAAU,CACVc,WACF,CAAC,CAED,mBACEtB,IAAA,CAACC,YAAY,CAACyB,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAlB,QAAA,CACjCA,QAAQ,CACY,CAAC,CAE5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}