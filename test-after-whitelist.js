const axios = require('axios');

const testAfterWhitelist = async () => {
  console.log('🧪 Testing ZenoPay API After IP Whitelisting...\n');

  const API_KEY = '-YIkdkUWpqEyy9DOaKPTDeaEZ5O97_DkSxmZdBLwYrE';
  const ENDPOINT = 'https://zenoapi.com/api/payments/mobile_money_tanzania';

  console.log('🔑 API Key:', API_KEY.substring(0, 15) + '...');
  console.log('🌐 Endpoint:', ENDPOINT);
  console.log('📍 Server IP: ************* (should be whitelisted)');

  const testData = {
    order_id: `TEST_WHITELIST_${Date.now()}`,
    buyer_email: '<EMAIL>',
    buyer_name: 'Test User',
    buyer_phone: '0712345678',
    amount: 1000,
    webhook_url: 'https://server-fmff.onrender.com/api/payment/webhook'
  };

  console.log('\n📤 Test Payment Data:');
  console.log(JSON.stringify(testData, null, 2));

  console.log('\n🔄 Testing payment request...');
  
  try {
    const response = await axios.post(ENDPOINT, testData, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY
      },
      timeout: 15000,
      validateStatus: () => true
    });

    console.log('📥 Response Status:', response.status);
    console.log('📥 Response Data:', response.data);

    if (response.status === 200 || response.status === 201) {
      if (response.data.status === 'success') {
        console.log('\n🎉 SUCCESS! IP Whitelisting worked!');
        console.log('✅ Payment request accepted');
        console.log('🆔 Order ID:', response.data.order_id);
        console.log('💬 Message:', response.data.message);
        console.log('🔢 Result Code:', response.data.resultcode);
        
        console.log('\n🎯 Next Steps:');
        console.log('1. ✅ ZenoPay API is now working');
        console.log('2. ✅ Test payment through your app');
        console.log('3. ✅ Check webhook functionality');
        console.log('4. ✅ Verify subscription updates');
        
      } else {
        console.log('\n⚠️ Payment request failed:', response.data.message);
        console.log('This might be a different issue (not IP whitelisting)');
      }
    } else if (response.status === 403) {
      if (response.data.message && response.data.message.includes('Imunify360')) {
        console.log('\n❌ IP Still Not Whitelisted');
        console.log('The server IP (*************) is still being blocked');
        console.log('Please contact ZenoPay support again');
      } else {
        console.log('\n❌ Different 403 error:', response.data);
      }
    } else {
      console.log('\n⚠️ Unexpected status code:', response.status);
      console.log('Response:', response.data);
    }

  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    }
  }

  console.log('\n📞 If still having issues:');
  console.log('Phone: +255 793 166 166');
  console.log('Email: <EMAIL>');
  console.log('Say: "IP ************* still blocked by Imunify360"');
};

console.log('🎯 IP Whitelisting Test');
console.log('═══════════════════════════════════════');
console.log('📍 Server IP: *************');
console.log('🆔 Account ID: zp38236');
console.log('🌐 Server URL: https://server-fmff.onrender.com');
console.log('═══════════════════════════════════════');
console.log('');

testAfterWhitelist().catch(console.error);
