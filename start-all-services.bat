@echo off
echo ========================================
echo    🚀 Starting BrainWave Application
echo ========================================
echo.

echo 📋 Checking if ports are available...
netstat -an | findstr "LISTENING" | findstr ":3000\|:5000" > nul
if %errorlevel% == 0 (
    echo ⚠️  Some ports are already in use. Continuing anyway...
) else (
    echo ✅ Ports are available
)
echo.

echo 🔧 Starting Node.js Server (Port 5000)...
start "BrainWave Server" cmd /k "cd /d server && echo Starting server... && node server.js"
timeout /t 3 > nul

echo ⚛️  Starting React Client (Port 3000)...
start "BrainWave Client" cmd /k "cd /d client && echo Starting React client... && set BROWSER=none && npm start"
timeout /t 5 > nul

echo.
echo ========================================
echo    🎉 Services Starting...
echo ========================================
echo.
echo 🚀 Server: http://localhost:5000
echo ⚛️  Client: http://localhost:3000
echo 📋 Subscription: http://localhost:3000/subscription
echo 🔐 Login: http://localhost:3000/login
echo.
echo ⏰ Please wait 30-60 seconds for React to compile...
echo.

timeout /t 10 > nul

echo 🌐 Opening application in browser...
start http://localhost:3000

echo.
echo ========================================
echo    ✅ Startup Complete!
echo ========================================
echo.
echo 📝 Note: Keep the terminal windows open to keep services running
echo 🔄 To restart, close terminals and run this script again
echo.
pause
