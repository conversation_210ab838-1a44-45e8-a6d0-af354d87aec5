// Restart Server with Fixed Routes
const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');

// Load environment variables
require('dotenv').config({ path: './server/.env' });

console.log('🚀 Restarting BrainWave Server with Fixed Payment Routes...');

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Database connection
let dbConnected = false;

async function connectDatabase() {
  try {
    console.log('🔗 Connecting to MongoDB Atlas...');
    
    const options = {
      serverSelectionTimeoutMS: 15000,
      socketTimeoutMS: 15000,
      connectTimeoutMS: 15000,
      family: 4,
      maxPoolSize: 10,
      minPoolSize: 2,
      retryWrites: true,
      retryReads: true,
    };

    await mongoose.connect(process.env.MONGO_URL, options);
    dbConnected = true;
    console.log('✅ MongoDB Atlas connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    dbConnected = false;
  }
}

// Health check route
app.get('/api/health', (req, res) => {
  console.log('📝 Health check requested');
  res.json({
    status: 'success',
    message: 'BrainWave Server is running with fixed payment routes',
    timestamp: new Date().toISOString(),
    port: port,
    database: dbConnected ? 'connected' : 'disconnected',
    environment: process.env.NODE_ENV || 'development',
    paymentValidation: 'Updated Tanzania phone format (076xxxxxxx now supported)'
  });
});

// Load routes safely
console.log('📦 Loading routes...');

try {
  // Users route
  const usersRoute = require('./server/routes/usersRoute');
  app.use("/api/users", usersRoute);
  console.log('✅ Users route loaded');
} catch (error) {
  console.error('❌ Error loading users route:', error.message);
}

try {
  // Payment route (with fixed validation)
  const paymentRoute = require('./server/routes/paymentRoute');
  app.use("/api/payment", paymentRoute);
  console.log('✅ Payment route loaded (with updated phone validation)');
} catch (error) {
  console.error('❌ Error loading payment route:', error.message);
}

try {
  // Plans route
  const plansRoute = require('./server/routes/planRoute');
  app.use("/api/plans", plansRoute);
  console.log('✅ Plans route loaded');
} catch (error) {
  console.error('❌ Error loading plans route:', error.message);
}

try {
  // Quiz routes (load carefully)
  const quizRoute = require('./server/routes/quizRoute');
  app.use("/api/quiz", quizRoute);
  console.log('✅ Quiz route loaded');
} catch (error) {
  console.error('❌ Error loading quiz route:', error.message);
}

try {
  // Enhanced quiz routes (might have the problematic route)
  const enhancedQuizRoute = require('./server/routes/enhancedQuizRoute');
  app.use("/api/quiz", enhancedQuizRoute);
  console.log('✅ Enhanced quiz route loaded');
} catch (error) {
  console.error('❌ Error loading enhanced quiz route:', error.message);
  console.error('This might be the source of the path-to-regexp error');
}

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('❌ Server error:', error.message);
  res.status(500).json({
    status: 'error',
    message: 'Internal server error',
    error: error.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  console.log('❌ 404 - Route not found:', req.originalUrl);
  res.status(404).json({
    status: 'error',
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
async function startServer() {
  try {
    await connectDatabase();
    
    app.listen(port, () => {
      console.log('🎉 Server restarted successfully!');
      console.log(`📍 Server running on http://localhost:${port}`);
      console.log(`📍 Health check: http://localhost:${port}/api/health`);
      console.log(`📍 Payment endpoint: http://localhost:${port}/api/payment/create-invoice`);
      console.log(`📍 Database: ${dbConnected ? 'Connected' : 'Disconnected'}`);
      console.log('');
      console.log('🔧 Changes made:');
      console.log('  ✅ Updated phone validation to support 076xxxxxxx format');
      console.log('  ✅ Enhanced error logging for payment validation');
      console.log('  ✅ Fixed Tanzania mobile number regex pattern');
      console.log('');
      console.log('💡 Your phone number ********** should now work!');
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  mongoose.connection.close(() => {
    console.log('📴 Database connection closed');
    process.exit(0);
  });
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

startServer();
