{"ast": null, "code": "import React,{useEffect}from'react';import{useSelector}from'react-redux';import{useNavigate}from'react-router-dom';import{message}from'antd';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const AdminProtectedRoute=_ref=>{let{children}=_ref;const{user}=useSelector(state=>state.user);const navigate=useNavigate();useEffect(()=>{console.log(\"AdminProtectedRoute: User state changed\",{user:user?{name:user.name,isAdmin:user.isAdmin}:null});// Check if user is loaded and is not an admin\nif(user&&!user.isAdmin){console.log(\"AdminProtectedRoute: Non-admin user detected, redirecting to user hub\");message.error('Access denied. Admin privileges required.');navigate('/user/hub');}},[user,navigate]);// If user is not loaded yet, show loading or return null\nif(!user){console.log(\"AdminProtectedRoute: User not loaded yet, showing loading...\");return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Loading admin panel...\"})]})});}// If user is not admin, return null (will redirect in useEffect)\nif(!user.isAdmin){console.log(\"AdminProtectedRoute: User is not admin, will redirect\");return null;}console.log(\"AdminProtectedRoute: Admin user confirmed, rendering children\");// If user is admin, render the children\nreturn children;};export default AdminProtectedRoute;", "map": {"version": 3, "names": ["React", "useEffect", "useSelector", "useNavigate", "message", "jsx", "_jsx", "jsxs", "_jsxs", "AdminProtectedRoute", "_ref", "children", "user", "state", "navigate", "console", "log", "name", "isAdmin", "error", "className"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/AdminProtectedRoute.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { message } from 'antd';\n\nconst AdminProtectedRoute = ({ children }) => {\n  const { user } = useSelector((state) => state.user);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    console.log(\"AdminProtectedRoute: User state changed\", {\n      user: user ? { name: user.name, isAdmin: user.isAdmin } : null\n    });\n\n    // Check if user is loaded and is not an admin\n    if (user && !user.isAdmin) {\n      console.log(\"AdminProtectedRoute: Non-admin user detected, redirecting to user hub\");\n      message.error('Access denied. Admin privileges required.');\n      navigate('/user/hub');\n    }\n  }, [user, navigate]);\n\n  // If user is not loaded yet, show loading or return null\n  if (!user) {\n    console.log(\"AdminProtectedRoute: User not loaded yet, showing loading...\");\n    return <div className=\"flex items-center justify-center min-h-screen\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n        <p className=\"text-gray-600\">Loading admin panel...</p>\n      </div>\n    </div>;\n  }\n\n  // If user is not admin, return null (will redirect in useEffect)\n  if (!user.isAdmin) {\n    console.log(\"AdminProtectedRoute: User is not admin, will redirect\");\n    return null;\n  }\n\n  console.log(\"AdminProtectedRoute: Admin user confirmed, rendering children\");\n  // If user is admin, render the children\n  return children;\n};\n\nexport default AdminProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,MAAM,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE/B,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAAEE,IAAK,CAAC,CAAGV,WAAW,CAAEW,KAAK,EAAKA,KAAK,CAACD,IAAI,CAAC,CACnD,KAAM,CAAAE,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAE9BF,SAAS,CAAC,IAAM,CACdc,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAE,CACrDJ,IAAI,CAAEA,IAAI,CAAG,CAAEK,IAAI,CAAEL,IAAI,CAACK,IAAI,CAAEC,OAAO,CAAEN,IAAI,CAACM,OAAQ,CAAC,CAAG,IAC5D,CAAC,CAAC,CAEF;AACA,GAAIN,IAAI,EAAI,CAACA,IAAI,CAACM,OAAO,CAAE,CACzBH,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC,CACpFZ,OAAO,CAACe,KAAK,CAAC,2CAA2C,CAAC,CAC1DL,QAAQ,CAAC,WAAW,CAAC,CACvB,CACF,CAAC,CAAE,CAACF,IAAI,CAAEE,QAAQ,CAAC,CAAC,CAEpB;AACA,GAAI,CAACF,IAAI,CAAE,CACTG,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC,CAC3E,mBAAOV,IAAA,QAAKc,SAAS,CAAC,+CAA+C,CAAAT,QAAA,cACnEH,KAAA,QAAKY,SAAS,CAAC,aAAa,CAAAT,QAAA,eAC1BL,IAAA,QAAKc,SAAS,CAAC,2EAA2E,CAAM,CAAC,cACjGd,IAAA,MAAGc,SAAS,CAAC,eAAe,CAAAT,QAAA,CAAC,wBAAsB,CAAG,CAAC,EACpD,CAAC,CACH,CAAC,CACR,CAEA;AACA,GAAI,CAACC,IAAI,CAACM,OAAO,CAAE,CACjBH,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CACpE,MAAO,KAAI,CACb,CAEAD,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC,CAC5E;AACA,MAAO,CAAAL,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}