// Test login route in isolation
require('dotenv').config({ path: './server/.env' });
const express = require('express');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();
app.use(express.json());

// Simple User model for testing
const userSchema = new mongoose.Schema({
  email: String,
  username: String,
  password: String,
  isBlocked: { type: Boolean, default: false }
});

const User = mongoose.model('TestUser', userSchema, 'users'); // Use existing users collection

// Test login route
app.post('/test-login', async (req, res) => {
  try {
    console.log("🔐 Test login request body:", req.body);

    const loginField = req.body.email;
    console.log("🔍 Looking for user with:", loginField);

    const user = await User.findOne({
      $or: [
        { email: loginField },
        { username: login<PERSON><PERSON> }
      ]
    });

    console.log("👤 Found user:", user ? "Yes" : "No");

    if (!user) {
      return res.status(200).send({ 
        message: "User does not exist", 
        success: false 
      });
    }

    if (user.isBlocked) {
      return res.status(403).send({
        message: "You are blocked. Please contact your moderator",
        success: false,
      });
    }

    // Check password
    console.log("🔑 Checking password...");
    const validPassword = await bcrypt.compare(req.body.password, user.password);
    console.log("🔑 Password valid:", validPassword);
    
    if (!validPassword) {
      return res.status(200).send({ 
        message: "Invalid password", 
        success: false 
      });
    }

    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || "1d",
    });

    res.send({
      message: "User logged in successfully",
      success: true,
      data: token,
      response: user,
    });
  } catch (error) {
    console.error("❌ Login error:", error);
    res.status(500).send({
      message: error.message,
      data: error,
      success: false,
    });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Test server running' });
});

// Connect to database and start server
mongoose.connect(process.env.MONGO_URL)
  .then(() => {
    console.log('✅ Connected to MongoDB');
    
    app.listen(5002, () => {
      console.log('✅ Test server running on port 5002');
      console.log('🔗 Test login: POST http://localhost:5002/test-login');
      console.log('🔗 Health: GET http://localhost:5002/health');
    });
  })
  .catch(err => {
    console.error('❌ MongoDB connection error:', err);
    process.exit(1);
  });
