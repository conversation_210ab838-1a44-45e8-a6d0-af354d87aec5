import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import SubscriptionModal from '../SubscriptionModal/SubscriptionModal';
import './SubscriptionTrigger.css';

const SubscriptionTrigger = () => {
  const [showModal, setShowModal] = useState(false);
  const [hasShownModal, setHasShownModal] = useState(false);

  const { user } = useSelector((state) => state.user);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const location = useLocation();

  // Check if user needs subscription - ONLY show for users with NO plan at all
  const needsSubscription = () => {
    if (!user || user.isAdmin) return false;

    // If user has active subscription, they don't need subscription modal
    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {
      return false;
    }

    // Check if subscription is not expired
    if (subscriptionData && subscriptionData.endDate) {
      const endDate = new Date(subscriptionData.endDate);
      const now = new Date();
      if (endDate > now) return false;
    }

    // ONLY show subscription modal/overlay for users with NO subscription at all (free users)
    // Users with active, expired, or any other subscription status should NOT see this
    if ((user.subscriptionStatus === 'free' || !user.subscriptionStatus) && !user.isAdmin) {
      return true;
    }

    return false;
  };

  useEffect(() => {
    // Show subscription modal after login if user needs subscription
    if (needsSubscription() && !hasShownModal) {
      const timer = setTimeout(() => {
        setShowModal(true);
        setHasShownModal(true);
      }, 2000); // Show after 2 seconds

      return () => clearTimeout(timer);
    }
  }, [user, subscriptionData, hasShownModal]);

  const handleSuccess = () => {
    setShowModal(false);
    setHasShownModal(true);
  };

  const handleClose = () => {
    setShowModal(false);
    // Don't set hasShownModal to true on close, so it can show again later
  };

  // Don't render anything if user doesn't need subscription or is on profile/plans page
  if (!needsSubscription()) {
    return null;
  }

  // Don't show overlay on allowed pages - let users access these
  const allowedPages = ['/profile', '/subscription', '/logout'];
  const isOnAllowedPage = allowedPages.some(page => location.pathname.includes(page));
  if (isOnAllowedPage) {
    return null;
  }

  return (
    <>
      {/* Subscription Required Overlay */}
      {needsSubscription() && !showModal && (
        <div className="subscription-required-overlay">
          <div className="subscription-prompt">
            <div className="prompt-icon">🔒</div>
            <h3>Premium Access Required</h3>
            <p>Choose a subscription plan to unlock all features</p>
            <div className="subscription-actions">
              <button
                className="choose-plan-btn"
                onClick={() => setShowModal(true)}
              >
                Choose Plan
              </button>
              <button
                className="profile-btn"
                onClick={() => window.location.href = '/user/profile'}
              >
                Update Phone Number
              </button>
              <button
                className="logout-btn"
                onClick={() => {
                  localStorage.removeItem('token');
                  localStorage.removeItem('user');
                  window.location.href = '/login';
                }}
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Subscription Modal */}
      <SubscriptionModal 
        isOpen={showModal}
        onClose={handleClose}
        onSuccess={handleSuccess}
      />
    </>
  );
};

export default SubscriptionTrigger;
