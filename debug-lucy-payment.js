const axios = require('axios');

const debugLucyPayment = async () => {
  try {
    console.log('🔍 Debugging Lucy Mosha payment issue...\n');

    // Test server connection
    const healthResponse = await axios.get('http://localhost:5000/api/health');
    console.log('✅ Server Status:', healthResponse.data.status);

    // Test database connection
    const dbResponse = await axios.get('http://localhost:5000/api/test/db');
    console.log('✅ Database Status:', dbResponse.data.status);
    console.log('📊 Collections:', dbResponse.data.collections);

    console.log('\n🔍 Searching for user "lucymosha"...');

    // Try to login as lucymosha to get user info
    const loginAttempts = [
      { email: 'lucymosha', password: 'password' },
      { email: 'lucymosha', password: '123456' },
      { email: '<EMAIL>', password: 'password' },
      { email: '<EMAIL>', password: 'password' }
    ];

    let userToken = null;
    let userData = null;

    for (const attempt of loginAttempts) {
      try {
        console.log(`🔐 Trying login with: ${attempt.email}`);
        const loginResponse = await axios.post('http://localhost:5000/api/users/login', attempt);
        
        if (loginResponse.data.success) {
          console.log('✅ Login successful!');
          userToken = loginResponse.data.data;
          userData = loginResponse.data.response;
          break;
        }
      } catch (loginError) {
        console.log(`❌ Login failed for ${attempt.email}:`, loginError.response?.data?.message || 'Unknown error');
      }
    }

    if (!userData) {
      console.log('\n❌ Could not login as lucymosha. Let me try a different approach...');
      
      // Try to create a test script that runs on the server side
      console.log('\n🔄 Creating server-side debug script...');
      
      const debugScript = `
        const User = require('./models/userModel');
        const Subscription = require('./models/subscriptionModel');
        
        const findLucy = async () => {
          try {
            const users = await User.find({
              $or: [
                { username: { $regex: 'lucy', $options: 'i' } },
                { name: { $regex: 'lucy', $options: 'i' } },
                { email: { $regex: 'lucy', $options: 'i' } }
              ]
            }).limit(5);
            
            console.log('Found users:', users.map(u => ({
              id: u._id,
              name: u.name,
              username: u.username,
              email: u.email,
              paymentRequired: u.paymentRequired,
              subscriptionStatus: u.subscriptionStatus
            })));
            
            for (const user of users) {
              const subscriptions = await Subscription.find({ user: user._id }).populate('activePlan');
              console.log(\`Subscriptions for \${user.name}:\`, subscriptions);
            }
            
          } catch (error) {
            console.error('Error:', error.message);
          }
        };
        
        findLucy();
      `;
      
      // Save this script to the server directory
      const fs = require('fs');
      const path = require('path');
      const scriptPath = path.join(__dirname, 'server', 'debug-lucy.js');
      
      try {
        fs.writeFileSync(scriptPath, debugScript);
        console.log('✅ Debug script created at:', scriptPath);
        console.log('📝 Please run: cd server && node debug-lucy.js');
      } catch (writeError) {
        console.log('❌ Could not create debug script:', writeError.message);
      }
      
      return;
    }

    console.log('\n👤 User Data Found:');
    console.log('- ID:', userData._id);
    console.log('- Name:', userData.name);
    console.log('- Username:', userData.username);
    console.log('- Email:', userData.email);
    console.log('- Phone:', userData.phoneNumber);
    console.log('- Payment Required:', userData.paymentRequired);
    console.log('- Subscription Status:', userData.subscriptionStatus);
    console.log('- Subscription Plan:', userData.subscriptionPlan);

    // Now check payment status with the token
    console.log('\n💳 Checking payment status...');
    
    try {
      const paymentResponse = await axios.get('http://localhost:5000/api/payment/check-payment-status', {
        headers: {
          'Authorization': `Bearer ${userToken}`
        },
        data: { userId: userData._id }
      });
      console.log('✅ Payment Status Response:', paymentResponse.data);
    } catch (paymentError) {
      console.log('❌ Payment Status Error:', paymentError.response?.data || paymentError.message);
      
      // The issue might be with the GET request having a body
      // Let's try a POST request instead
      try {
        console.log('\n🔄 Trying POST request for payment status...');
        const paymentResponse2 = await axios.post('http://localhost:5000/api/payment/check-payment-status', 
          { userId: userData._id },
          {
            headers: {
              'Authorization': `Bearer ${userToken}`,
              'Content-Type': 'application/json'
            }
          }
        );
        console.log('✅ Payment Status (POST):', paymentResponse2.data);
      } catch (postError) {
        console.log('❌ POST Payment Status Error:', postError.response?.data || postError.message);
      }
    }

    // Check user info endpoint
    console.log('\n👤 Getting detailed user info...');
    try {
      const userInfoResponse = await axios.post('http://localhost:5000/api/users/get-user-info', 
        { userId: userData._id },
        {
          headers: {
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('✅ Detailed User Info:', userInfoResponse.data.data);
    } catch (userInfoError) {
      console.log('❌ User Info Error:', userInfoError.response?.data || userInfoError.message);
    }

  } catch (error) {
    console.error('❌ Debug Error:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
};

debugLucyPayment();
