{"ast": null, "code": "import{createSlice}from'@reduxjs/toolkit';const initialState={paymentVerificationNeeded:false};const paymentSlice=createSlice({name:'payment',initialState,reducers:{setPaymentVerificationNeeded:(state,action)=>{state.paymentVerificationNeeded=action.payload;}}});export const{setPaymentVerificationNeeded}=paymentSlice.actions;export default paymentSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "paymentVerificationNeeded", "paymentSlice", "name", "reducers", "setPaymentVerificationNeeded", "state", "action", "payload", "actions", "reducer"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/redux/paymentSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\r\n\r\nconst initialState = {\r\n  paymentVerificationNeeded: false,\r\n};\r\n\r\nconst paymentSlice = createSlice({\r\n  name: 'payment',\r\n  initialState,\r\n  reducers: {\r\n    setPaymentVerificationNeeded: (state, action) => {\r\n      state.paymentVerificationNeeded = action.payload;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setPaymentVerificationNeeded } = paymentSlice.actions;\r\n\r\nexport default paymentSlice.reducer;"], "mappings": "AAAA,OAASA,WAAW,KAAQ,kBAAkB,CAE9C,KAAM,CAAAC,YAAY,CAAG,CACnBC,yBAAyB,CAAE,KAC7B,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGH,WAAW,CAAC,CAC/BI,IAAI,CAAE,SAAS,CACfH,YAAY,CACZI,QAAQ,CAAE,CACRC,4BAA4B,CAAEA,CAACC,KAAK,CAAEC,MAAM,GAAK,CAC/CD,KAAK,CAACL,yBAAyB,CAAGM,MAAM,CAACC,OAAO,CAClD,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEH,4BAA6B,CAAC,CAAGH,YAAY,CAACO,OAAO,CAEpE,cAAe,CAAAP,YAAY,CAACQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}