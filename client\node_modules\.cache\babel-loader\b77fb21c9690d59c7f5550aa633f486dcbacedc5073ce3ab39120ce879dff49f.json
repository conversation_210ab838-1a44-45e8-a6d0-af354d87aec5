{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Plans\\\\Plans.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport \"./Plans.css\";\nimport ConfirmModal from \"./components/ConfirmModal\";\nimport WaitingModal from \"./components/WaitingModal\";\nimport { addPayment } from \"../../../apicalls/payment\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { useNavigate } from \"react-router-dom\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Plans = () => {\n  _s();\n  var _subscriptionData$pla;\n  const [plans, setPlans] = useState([]);\n  const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\n  const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\n  const [paymentInProgress, setPaymentInProgress] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n\n  // Validate if user has a valid phone number for payment\n  const validatePhoneForPayment = () => {\n    if (!(user !== null && user !== void 0 && user.phoneNumber) || user.phoneNumber.trim() === \"\") {\n      return {\n        valid: false,\n        message: \"Phone number is required for payment confirmation. Please update your profile.\"\n      };\n    }\n\n    // Tanzania phone number format validation\n    const phoneRegex = /^0[67]\\d{8}$/;\n    if (!phoneRegex.test(user.phoneNumber)) {\n      return {\n        valid: false,\n        message: \"Please update your phone number to a valid Tanzania format (06xxxxxxxx or 07xxxxxxxx).\"\n      };\n    }\n    return {\n      valid: true\n    };\n  };\n  useEffect(() => {\n    const fetchPlans = async () => {\n      try {\n        const response = await getPlans();\n        setPlans(response);\n      } catch (error) {\n        console.error(\"Error fetching plans:\", error);\n      }\n    };\n    fetchPlans();\n  }, []);\n  const transactionDetails = {\n    amount: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.discountedPrice) || 'N/A',\n    currency: \"TZS\",\n    destination: \"brainwave.zone\"\n  };\n  const handlePaymentStart = async plan => {\n    setSelectedPlan(plan);\n\n    // Validate phone number before proceeding\n    const phoneValidation = validatePhoneForPayment();\n    if (!phoneValidation.valid) {\n      message.error({\n        content: phoneValidation.message,\n        duration: 5,\n        onClick: () => navigate('/user/profile') // Allow user to click message to go to profile\n      });\n\n      return;\n    }\n    try {\n      dispatch(ShowLoading());\n      console.log('💳 Initiating payment for plan:', plan.title);\n      console.log('📱 User phone number:', user.phoneNumber);\n      const response = await addPayment({\n        plan\n      });\n      console.log('📥 Payment response:', response);\n      if (response.success) {\n        localStorage.setItem(\"order_id\", response.order_id);\n        setWaitingModalOpen(true);\n        setPaymentInProgress(true);\n        dispatch(setPaymentVerificationNeeded(true));\n\n        // Show success message with phone number confirmation\n        message.success({\n          content: response.message || `Payment request sent! Please check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8\n        });\n      } else {\n        // Handle specific error types\n        if (response.errorType === \"MISSING_PHONE\") {\n          message.error({\n            content: \"Please add a phone number to your profile before making a payment.\",\n            duration: 5,\n            onClick: () => navigate('/user/profile')\n          });\n        } else if (response.errorType === \"INVALID_PHONE_FORMAT\") {\n          message.error({\n            content: \"Please update your phone number to a valid Tanzania format (06xxxxxxxx or 07xxxxxxxx).\",\n            duration: 5,\n            onClick: () => navigate('/user/profile')\n          });\n        } else if (response.errorType === \"PAYMENT_CONFIG_ERROR\") {\n          message.error(\"Payment service is temporarily unavailable. Please contact support.\");\n        } else {\n          message.error(response.message || \"Payment initiation failed. Please try again.\");\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"❌ Error processing payment:\", error);\n\n      // Handle network or other errors\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.message) {\n        message.error(error.response.data.message);\n      } else if (error.message) {\n        message.error(`Payment error: ${error.message}`);\n      } else {\n        message.error(\"Unable to process payment. Please check your internet connection and try again.\");\n      }\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    console.log(\"subscription Data in Plans\", subscriptionData);\n    if ((user === null || user === void 0 ? void 0 : user.paymentRequired) === true && (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) === \"paid\" && paymentInProgress) {\n      setWaitingModalOpen(false);\n      setConfirmModalOpen(true);\n      setPaymentInProgress(false);\n    }\n  }, [user, subscriptionData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [!user ? /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false) : !user.paymentRequired ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-plan-required\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-plan-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"No Plan Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 21\n    }, this) : (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-container\",\n      children: plans.sort((a, b) => {\n        // Sort order: Glimp Plan first, then Basic Membership, then others\n        if (a.title === \"Glimp Plan\") return -1;\n        if (b.title === \"Glimp Plan\") return 1;\n        if (a.title === \"Basic Membership\") return -1;\n        if (b.title === \"Basic Membership\") return 1;\n        return 0;\n      }).map(plan => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `plan-card ${plan.title === \"Basic Membership\" ? \"basic\" : plan.title === \"Glimp Plan\" ? \"glimp\" : \"\"}`,\n        children: [plan.title === \"Basic Membership\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"most-popular-label\",\n          children: \"MOST POPULAR\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 41\n        }, this), plan.title === \"Glimp Plan\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"glimp-label\",\n          children: \"QUICK START\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 41\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"plan-title\",\n            children: plan.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 41\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-duration-highlight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"duration-number\",\n              children: plan.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 45\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"duration-text\",\n              children: [\"Month\", plan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-pricing\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"plan-actual-price\",\n            children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 41\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"plan-discounted-price\",\n            children: [plan.discountedPrice.toLocaleString(), \" TZS\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 41\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"plan-discount-tag\",\n            children: [plan.discountPercentage, \"% OFF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-value\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value-text\",\n            children: [Math.round(plan.discountedPrice / plan.duration).toLocaleString(), \" TZS/month\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"plan-button\",\n          onClick: () => handlePaymentStart(plan),\n          children: plan.title === \"Glimp Plan\" ? \"🚀 Start Quick\" : \"Choose Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"plan-features\",\n          children: plan.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"plan-feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"plan-feature-icon\",\n              children: \"\\u2714\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 49\n            }, this), feature]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 45\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 37\n        }, this)]\n      }, plan._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 33\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 25\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-details\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"plan-title\",\n        children: subscriptionData.plan.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"64px\",\n        height: \"64px\",\n        viewBox: \"-3.2 -3.2 38.40 38.40\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"#10B981\",\n        stroke: \"#253864\",\n        transform: \"matrix(1, 0, 0, 1, 0, 0)\",\n        children: [/*#__PURE__*/_jsxDEV(\"g\", {\n          id: \"SVGRepo_bgCarrier\",\n          strokeWidth: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n          id: \"SVGRepo_tracerCarrier\",\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          stroke: \"#CCCCCC\",\n          strokeWidth: \"0.064\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n          id: \"SVGRepo_iconCarrier\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z\",\n            fill: \"#202327\",\n            fillRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"plan-description\",\n        children: subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$pla = subscriptionData.plan) === null || _subscriptionData$pla === void 0 ? void 0 : _subscriptionData$pla.subscriptionData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"plan-dates\",\n        children: [\"Start Date: \", subscriptionData.startDate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"plan-dates\",\n        children: [\"End Date: \", subscriptionData.endDate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 25\n    }, this), /*#__PURE__*/_jsxDEV(WaitingModal, {\n      isOpen: isWaitingModalOpen,\n      onClose: () => setWaitingModalOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: isConfirmModalOpen,\n      onClose: () => setConfirmModalOpen(false),\n      transaction: transactionDetails\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 9\n  }, this);\n};\n_s(Plans, \"2p4MB4rxyXadxPAb2VoMYdg0Pik=\", false, function () {\n  return [useSelector, useSelector, useDispatch, useNavigate];\n});\n_c = Plans;\nexport default Plans;\nvar _c;\n$RefreshReg$(_c, \"Plans\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "getPlans", "ConfirmModal", "WaitingModal", "addPayment", "useDispatch", "useSelector", "setPaymentVerificationNeeded", "HideLoading", "ShowLoading", "message", "useNavigate", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "Plans", "_s", "_subscriptionData$pla", "plans", "setPlans", "isConfirmModalOpen", "setConfirmModalOpen", "isWaitingModalOpen", "setWaitingModalOpen", "paymentInProgress", "setPaymentInProgress", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "user", "state", "subscriptionData", "subscription", "dispatch", "navigate", "validatePhoneForPayment", "phoneNumber", "trim", "valid", "phoneRegex", "test", "fetchPlans", "response", "error", "console", "transactionDetails", "amount", "discountedPrice", "currency", "destination", "handlePaymentStart", "plan", "phoneValidation", "content", "duration", "onClick", "log", "title", "success", "localStorage", "setItem", "order_id", "errorType", "_error$response", "_error$response$data", "data", "paymentRequired", "paymentStatus", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sort", "a", "b", "map", "actualPrice", "toLocaleString", "discountPercentage", "Math", "round", "features", "feature", "index", "_id", "width", "height", "viewBox", "xmlns", "fill", "stroke", "transform", "id", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "fillRule", "startDate", "endDate", "isOpen", "onClose", "transaction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Plans/Plans.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { getPlans } from \"../../../apicalls/plans\";\r\nimport \"./Plans.css\";\r\nimport ConfirmModal from \"./components/ConfirmModal\";\r\nimport WaitingModal from \"./components/WaitingModal\";\r\nimport { addPayment } from \"../../../apicalls/payment\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { message } from \"antd\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nconst Plans = () => {\r\n    const [plans, setPlans] = useState([]);\r\n    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\r\n    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\r\n    const [paymentInProgress, setPaymentInProgress] = useState(false);\r\n    const [selectedPlan, setSelectedPlan] = useState(null);\r\n    const { user } = useSelector((state) => state.user);\r\n    const { subscriptionData } = useSelector((state) => state.subscription);\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n\r\n    // Validate if user has a valid phone number for payment\r\n    const validatePhoneForPayment = () => {\r\n        if (!user?.phoneNumber || user.phoneNumber.trim() === \"\") {\r\n            return {\r\n                valid: false,\r\n                message: \"Phone number is required for payment confirmation. Please update your profile.\"\r\n            };\r\n        }\r\n\r\n        // Tanzania phone number format validation\r\n        const phoneRegex = /^0[67]\\d{8}$/;\r\n        if (!phoneRegex.test(user.phoneNumber)) {\r\n            return {\r\n                valid: false,\r\n                message: \"Please update your phone number to a valid Tanzania format (06xxxxxxxx or 07xxxxxxxx).\"\r\n            };\r\n        }\r\n\r\n        return { valid: true };\r\n    };\r\n\r\n    useEffect(() => {\r\n        const fetchPlans = async () => {\r\n            try {\r\n                const response = await getPlans();\r\n                setPlans(response);\r\n            } catch (error) {\r\n                console.error(\"Error fetching plans:\", error);\r\n            }\r\n        };\r\n\r\n        fetchPlans();\r\n    }, []);\r\n\r\n    const transactionDetails = {\r\n        amount: selectedPlan?.discountedPrice || 'N/A',\r\n        currency: \"TZS\",\r\n        destination: \"brainwave.zone\",\r\n    };\r\n\r\n\r\n    const handlePaymentStart = async (plan) => {\r\n        setSelectedPlan(plan);\r\n\r\n        // Validate phone number before proceeding\r\n        const phoneValidation = validatePhoneForPayment();\r\n        if (!phoneValidation.valid) {\r\n            message.error({\r\n                content: phoneValidation.message,\r\n                duration: 5,\r\n                onClick: () => navigate('/user/profile') // Allow user to click message to go to profile\r\n            });\r\n            return;\r\n        }\r\n\r\n        try {\r\n            dispatch(ShowLoading());\r\n            console.log('💳 Initiating payment for plan:', plan.title);\r\n            console.log('📱 User phone number:', user.phoneNumber);\r\n\r\n            const response = await addPayment({ plan });\r\n            console.log('📥 Payment response:', response);\r\n\r\n            if (response.success) {\r\n                localStorage.setItem(\"order_id\", response.order_id);\r\n                setWaitingModalOpen(true);\r\n                setPaymentInProgress(true);\r\n                dispatch(setPaymentVerificationNeeded(true));\r\n\r\n                // Show success message with phone number confirmation\r\n                message.success({\r\n                    content: response.message || `Payment request sent! Please check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\r\n                    duration: 8\r\n                });\r\n            } else {\r\n                // Handle specific error types\r\n                if (response.errorType === \"MISSING_PHONE\") {\r\n                    message.error({\r\n                        content: \"Please add a phone number to your profile before making a payment.\",\r\n                        duration: 5,\r\n                        onClick: () => navigate('/user/profile')\r\n                    });\r\n                } else if (response.errorType === \"INVALID_PHONE_FORMAT\") {\r\n                    message.error({\r\n                        content: \"Please update your phone number to a valid Tanzania format (06xxxxxxxx or 07xxxxxxxx).\",\r\n                        duration: 5,\r\n                        onClick: () => navigate('/user/profile')\r\n                    });\r\n                } else if (response.errorType === \"PAYMENT_CONFIG_ERROR\") {\r\n                    message.error(\"Payment service is temporarily unavailable. Please contact support.\");\r\n                } else {\r\n                    message.error(response.message || \"Payment initiation failed. Please try again.\");\r\n                }\r\n            }\r\n        } catch (error) {\r\n            console.error(\"❌ Error processing payment:\", error);\r\n\r\n            // Handle network or other errors\r\n            if (error.response?.data?.message) {\r\n                message.error(error.response.data.message);\r\n            } else if (error.message) {\r\n                message.error(`Payment error: ${error.message}`);\r\n            } else {\r\n                message.error(\"Unable to process payment. Please check your internet connection and try again.\");\r\n            }\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        console.log(\"subscription Data in Plans\", subscriptionData)\r\n        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === \"paid\" && paymentInProgress) {\r\n            setWaitingModalOpen(false);\r\n            setConfirmModalOpen(true);\r\n            setPaymentInProgress(false);\r\n        }\r\n    }, [user, subscriptionData]);\r\n\r\n    return (\r\n        <div>\r\n            {!user ?\r\n                <>\r\n                </>\r\n                :\r\n                !user.paymentRequired ?\r\n                    <div className=\"no-plan-required\">\r\n                        <div className=\"no-plan-content\">\r\n                            <h2>No Plan Required</h2>\r\n                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>\r\n                        </div>\r\n                    </div>\r\n                    :\r\n                    subscriptionData?.paymentStatus !== \"paid\" ?\r\n                        <div className=\"plans-container\">\r\n                            {plans\r\n                                .sort((a, b) => {\r\n                                    // Sort order: Glimp Plan first, then Basic Membership, then others\r\n                                    if (a.title === \"Glimp Plan\") return -1;\r\n                                    if (b.title === \"Glimp Plan\") return 1;\r\n                                    if (a.title === \"Basic Membership\") return -1;\r\n                                    if (b.title === \"Basic Membership\") return 1;\r\n                                    return 0;\r\n                                })\r\n                                .map((plan) => (\r\n                                <div\r\n                                    key={plan._id}\r\n                                    className={`plan-card ${\r\n                                        plan.title === \"Basic Membership\" ? \"basic\" :\r\n                                        plan.title === \"Glimp Plan\" ? \"glimp\" : \"\"\r\n                                    }`}\r\n                                >\r\n                                    {plan.title === \"Basic Membership\" && (\r\n                                        <div className=\"most-popular-label\">MOST POPULAR</div>\r\n                                    )}\r\n                                    {plan.title === \"Glimp Plan\" && (\r\n                                        <div className=\"glimp-label\">QUICK START</div>\r\n                                    )}\r\n\r\n                                    <div className=\"plan-header\">\r\n                                        <h2 className=\"plan-title\">{plan.title}</h2>\r\n                                        <div className=\"plan-duration-highlight\">\r\n                                            <span className=\"duration-number\">{plan.duration}</span>\r\n                                            <span className=\"duration-text\">Month{plan.duration > 1 ? 's' : ''}</span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"plan-pricing\">\r\n                                        <p className=\"plan-actual-price\">\r\n                                            {plan.actualPrice.toLocaleString()} TZS\r\n                                        </p>\r\n                                        <p className=\"plan-discounted-price\">\r\n                                            {plan.discountedPrice.toLocaleString()} TZS\r\n                                        </p>\r\n                                        <span className=\"plan-discount-tag\">\r\n                                            {plan.discountPercentage}% OFF\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    <div className=\"plan-value\">\r\n                                        <span className=\"value-text\">\r\n                                            {Math.round(plan.discountedPrice / plan.duration).toLocaleString()} TZS/month\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    <button className=\"plan-button\"\r\n                                        onClick={() => handlePaymentStart(plan)}\r\n                                    >\r\n                                        {plan.title === \"Glimp Plan\" ? \"🚀 Start Quick\" : \"Choose Plan\"}\r\n                                    </button>\r\n\r\n                                    <ul className=\"plan-features\">\r\n                                        {plan.features.map((feature, index) => (\r\n                                            <li key={index} className=\"plan-feature\">\r\n                                                <span className=\"plan-feature-icon\">✔</span>\r\n                                                {feature}\r\n                                            </li>\r\n                                        ))}\r\n                                    </ul>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        :\r\n                        <div className=\"subscription-details\">\r\n                            <h1 className=\"plan-title\">{subscriptionData.plan.title}</h1>\r\n\r\n                            <svg\r\n                                width=\"64px\"\r\n                                height=\"64px\"\r\n                                viewBox=\"-3.2 -3.2 38.40 38.40\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"#10B981\"\r\n                                stroke=\"#253864\"\r\n                                transform=\"matrix(1, 0, 0, 1, 0, 0)\"\r\n                            >\r\n                                <g id=\"SVGRepo_bgCarrier\" strokeWidth=\"0\"></g>\r\n                                <g id=\"SVGRepo_tracerCarrier\" strokeLinecap=\"round\" strokeLinejoin=\"round\" stroke=\"#CCCCCC\" strokeWidth=\"0.064\"></g>\r\n                                <g id=\"SVGRepo_iconCarrier\">\r\n                                    <path\r\n                                        d=\"m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z\"\r\n                                        fill=\"#202327\"\r\n                                        fillRule=\"evenodd\"\r\n                                    ></path>\r\n                                </g>\r\n                            </svg>\r\n\r\n                            <p className=\"plan-description\">{subscriptionData?.plan?.subscriptionData}</p>\r\n                            <p className=\"plan-dates\">Start Date: {subscriptionData.startDate}</p>\r\n                            <p className=\"plan-dates\">End Date: {subscriptionData.endDate}</p>\r\n                        </div>\r\n            }\r\n\r\n            <WaitingModal\r\n                isOpen={isWaitingModalOpen}\r\n                onClose={() => setWaitingModalOpen(false)}\r\n            />\r\n\r\n            <ConfirmModal\r\n                isOpen={isConfirmModalOpen}\r\n                onClose={() => setConfirmModalOpen(false)}\r\n                transaction={transactionDetails}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Plans;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAO,aAAa;AACpB,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,4BAA4B,QAAQ,6BAA6B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,QAAA,IAAAC,SAAA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuB,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM;IAAE6B;EAAK,CAAC,GAAGvB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGzB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwB,uBAAuB,GAAGA,CAAA,KAAM;IAClC,IAAI,EAACN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,WAAW,KAAIP,IAAI,CAACO,WAAW,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACtD,OAAO;QACHC,KAAK,EAAE,KAAK;QACZ5B,OAAO,EAAE;MACb,CAAC;IACL;;IAEA;IACA,MAAM6B,UAAU,GAAG,cAAc;IACjC,IAAI,CAACA,UAAU,CAACC,IAAI,CAACX,IAAI,CAACO,WAAW,CAAC,EAAE;MACpC,OAAO;QACHE,KAAK,EAAE,KAAK;QACZ5B,OAAO,EAAE;MACb,CAAC;IACL;IAEA,OAAO;MAAE4B,KAAK,EAAE;IAAK,CAAC;EAC1B,CAAC;EAEDvC,SAAS,CAAC,MAAM;IACZ,MAAM0C,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACA,MAAMC,QAAQ,GAAG,MAAMzC,QAAQ,CAAC,CAAC;QACjCmB,QAAQ,CAACsB,QAAQ,CAAC;MACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD;IACJ,CAAC;IAEDF,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,kBAAkB,GAAG;IACvBC,MAAM,EAAE,CAAAnB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoB,eAAe,KAAI,KAAK;IAC9CC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACjB,CAAC;EAGD,MAAMC,kBAAkB,GAAG,MAAOC,IAAI,IAAK;IACvCvB,eAAe,CAACuB,IAAI,CAAC;;IAErB;IACA,MAAMC,eAAe,GAAGjB,uBAAuB,CAAC,CAAC;IACjD,IAAI,CAACiB,eAAe,CAACd,KAAK,EAAE;MACxB5B,OAAO,CAACiC,KAAK,CAAC;QACVU,OAAO,EAAED,eAAe,CAAC1C,OAAO;QAChC4C,QAAQ,EAAE,CAAC;QACXC,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAAC,eAAe,CAAC,CAAC;MAC7C,CAAC,CAAC;;MACF;IACJ;IAEA,IAAI;MACAD,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvBmC,OAAO,CAACY,GAAG,CAAC,iCAAiC,EAAEL,IAAI,CAACM,KAAK,CAAC;MAC1Db,OAAO,CAACY,GAAG,CAAC,uBAAuB,EAAE3B,IAAI,CAACO,WAAW,CAAC;MAEtD,MAAMM,QAAQ,GAAG,MAAMtC,UAAU,CAAC;QAAE+C;MAAK,CAAC,CAAC;MAC3CP,OAAO,CAACY,GAAG,CAAC,sBAAsB,EAAEd,QAAQ,CAAC;MAE7C,IAAIA,QAAQ,CAACgB,OAAO,EAAE;QAClBC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAElB,QAAQ,CAACmB,QAAQ,CAAC;QACnDrC,mBAAmB,CAAC,IAAI,CAAC;QACzBE,oBAAoB,CAAC,IAAI,CAAC;QAC1BO,QAAQ,CAAC1B,4BAA4B,CAAC,IAAI,CAAC,CAAC;;QAE5C;QACAG,OAAO,CAACgD,OAAO,CAAC;UACZL,OAAO,EAAEX,QAAQ,CAAChC,OAAO,IAAK,kDAAiDmB,IAAI,CAACO,WAAY,sCAAqC;UACrIkB,QAAQ,EAAE;QACd,CAAC,CAAC;MACN,CAAC,MAAM;QACH;QACA,IAAIZ,QAAQ,CAACoB,SAAS,KAAK,eAAe,EAAE;UACxCpD,OAAO,CAACiC,KAAK,CAAC;YACVU,OAAO,EAAE,oEAAoE;YAC7EC,QAAQ,EAAE,CAAC;YACXC,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAAC,eAAe;UAC3C,CAAC,CAAC;QACN,CAAC,MAAM,IAAIQ,QAAQ,CAACoB,SAAS,KAAK,sBAAsB,EAAE;UACtDpD,OAAO,CAACiC,KAAK,CAAC;YACVU,OAAO,EAAE,wFAAwF;YACjGC,QAAQ,EAAE,CAAC;YACXC,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAAC,eAAe;UAC3C,CAAC,CAAC;QACN,CAAC,MAAM,IAAIQ,QAAQ,CAACoB,SAAS,KAAK,sBAAsB,EAAE;UACtDpD,OAAO,CAACiC,KAAK,CAAC,qEAAqE,CAAC;QACxF,CAAC,MAAM;UACHjC,OAAO,CAACiC,KAAK,CAACD,QAAQ,CAAChC,OAAO,IAAI,8CAA8C,CAAC;QACrF;MACJ;IACJ,CAAC,CAAC,OAAOiC,KAAK,EAAE;MAAA,IAAAoB,eAAA,EAAAC,oBAAA;MACZpB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;MAEnD;MACA,KAAAoB,eAAA,GAAIpB,KAAK,CAACD,QAAQ,cAAAqB,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBE,IAAI,cAAAD,oBAAA,eAApBA,oBAAA,CAAsBtD,OAAO,EAAE;QAC/BA,OAAO,CAACiC,KAAK,CAACA,KAAK,CAACD,QAAQ,CAACuB,IAAI,CAACvD,OAAO,CAAC;MAC9C,CAAC,MAAM,IAAIiC,KAAK,CAACjC,OAAO,EAAE;QACtBA,OAAO,CAACiC,KAAK,CAAE,kBAAiBA,KAAK,CAACjC,OAAQ,EAAC,CAAC;MACpD,CAAC,MAAM;QACHA,OAAO,CAACiC,KAAK,CAAC,iFAAiF,CAAC;MACpG;IACJ,CAAC,SAAS;MACNV,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAGDT,SAAS,CAAC,MAAM;IACZ6C,OAAO,CAACY,GAAG,CAAC,4BAA4B,EAAEzB,gBAAgB,CAAC;IAC3D,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,eAAe,MAAK,IAAI,IAAI,CAAAnC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoC,aAAa,MAAK,MAAM,IAAI1C,iBAAiB,EAAE;MACnGD,mBAAmB,CAAC,KAAK,CAAC;MAC1BF,mBAAmB,CAAC,IAAI,CAAC;MACzBI,oBAAoB,CAAC,KAAK,CAAC;IAC/B;EACJ,CAAC,EAAE,CAACG,IAAI,EAAEE,gBAAgB,CAAC,CAAC;EAE5B,oBACIhB,OAAA;IAAAqD,QAAA,GACK,CAACvC,IAAI,gBACFd,OAAA,CAAAF,SAAA,mBACE,CAAC,GAEH,CAACgB,IAAI,CAACqC,eAAe,gBACjBnD,OAAA;MAAKsD,SAAS,EAAC,kBAAkB;MAAAD,QAAA,eAC7BrD,OAAA;QAAKsD,SAAS,EAAC,iBAAiB;QAAAD,QAAA,gBAC5BrD,OAAA;UAAAqD,QAAA,EAAI;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzB1D,OAAA;UAAAqD,QAAA,EAAG;QAAoG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,GAEN,CAAA1C,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoC,aAAa,MAAK,MAAM,gBACtCpD,OAAA;MAAKsD,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC3BjD,KAAK,CACDuD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACZ;QACA,IAAID,CAAC,CAAClB,KAAK,KAAK,YAAY,EAAE,OAAO,CAAC,CAAC;QACvC,IAAImB,CAAC,CAACnB,KAAK,KAAK,YAAY,EAAE,OAAO,CAAC;QACtC,IAAIkB,CAAC,CAAClB,KAAK,KAAK,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAImB,CAAC,CAACnB,KAAK,KAAK,kBAAkB,EAAE,OAAO,CAAC;QAC5C,OAAO,CAAC;MACZ,CAAC,CAAC,CACDoB,GAAG,CAAE1B,IAAI,iBACVpC,OAAA;QAEIsD,SAAS,EAAG,aACRlB,IAAI,CAACM,KAAK,KAAK,kBAAkB,GAAG,OAAO,GAC3CN,IAAI,CAACM,KAAK,KAAK,YAAY,GAAG,OAAO,GAAG,EAC3C,EAAE;QAAAW,QAAA,GAEFjB,IAAI,CAACM,KAAK,KAAK,kBAAkB,iBAC9B1C,OAAA;UAAKsD,SAAS,EAAC,oBAAoB;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACxD,EACAtB,IAAI,CAACM,KAAK,KAAK,YAAY,iBACxB1C,OAAA;UAAKsD,SAAS,EAAC,aAAa;UAAAD,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAChD,eAED1D,OAAA;UAAKsD,SAAS,EAAC,aAAa;UAAAD,QAAA,gBACxBrD,OAAA;YAAIsD,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAEjB,IAAI,CAACM;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5C1D,OAAA;YAAKsD,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpCrD,OAAA;cAAMsD,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAEjB,IAAI,CAACG;YAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxD1D,OAAA;cAAMsD,SAAS,EAAC,eAAe;cAAAD,QAAA,GAAC,OAAK,EAACjB,IAAI,CAACG,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN1D,OAAA;UAAKsD,SAAS,EAAC,cAAc;UAAAD,QAAA,gBACzBrD,OAAA;YAAGsD,SAAS,EAAC,mBAAmB;YAAAD,QAAA,GAC3BjB,IAAI,CAAC2B,WAAW,CAACC,cAAc,CAAC,CAAC,EAAC,MACvC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ1D,OAAA;YAAGsD,SAAS,EAAC,uBAAuB;YAAAD,QAAA,GAC/BjB,IAAI,CAACJ,eAAe,CAACgC,cAAc,CAAC,CAAC,EAAC,MAC3C;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ1D,OAAA;YAAMsD,SAAS,EAAC,mBAAmB;YAAAD,QAAA,GAC9BjB,IAAI,CAAC6B,kBAAkB,EAAC,OAC7B;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1D,OAAA;UAAKsD,SAAS,EAAC,YAAY;UAAAD,QAAA,eACvBrD,OAAA;YAAMsD,SAAS,EAAC,YAAY;YAAAD,QAAA,GACvBa,IAAI,CAACC,KAAK,CAAC/B,IAAI,CAACJ,eAAe,GAAGI,IAAI,CAACG,QAAQ,CAAC,CAACyB,cAAc,CAAC,CAAC,EAAC,YACvE;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN1D,OAAA;UAAQsD,SAAS,EAAC,aAAa;UAC3Bd,OAAO,EAAEA,CAAA,KAAML,kBAAkB,CAACC,IAAI,CAAE;UAAAiB,QAAA,EAEvCjB,IAAI,CAACM,KAAK,KAAK,YAAY,GAAG,gBAAgB,GAAG;QAAa;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAET1D,OAAA;UAAIsD,SAAS,EAAC,eAAe;UAAAD,QAAA,EACxBjB,IAAI,CAACgC,QAAQ,CAACN,GAAG,CAAC,CAACO,OAAO,EAAEC,KAAK,kBAC9BtE,OAAA;YAAgBsD,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACpCrD,OAAA;cAAMsD,SAAS,EAAC,mBAAmB;cAAAD,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC3CW,OAAO;UAAA,GAFHC,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,GApDAtB,IAAI,CAACmC,GAAG;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqDZ,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAEN1D,OAAA;MAAKsD,SAAS,EAAC,sBAAsB;MAAAD,QAAA,gBACjCrD,OAAA;QAAIsD,SAAS,EAAC,YAAY;QAAAD,QAAA,EAAErC,gBAAgB,CAACoB,IAAI,CAACM;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAE7D1D,OAAA;QACIwE,KAAK,EAAC,MAAM;QACZC,MAAM,EAAC,MAAM;QACbC,OAAO,EAAC,uBAAuB;QAC/BC,KAAK,EAAC,4BAA4B;QAClCC,IAAI,EAAC,SAAS;QACdC,MAAM,EAAC,SAAS;QAChBC,SAAS,EAAC,0BAA0B;QAAAzB,QAAA,gBAEpCrD,OAAA;UAAG+E,EAAE,EAAC,mBAAmB;UAACC,WAAW,EAAC;QAAG;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9C1D,OAAA;UAAG+E,EAAE,EAAC,uBAAuB;UAACE,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACL,MAAM,EAAC,SAAS;UAACG,WAAW,EAAC;QAAO;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpH1D,OAAA;UAAG+E,EAAE,EAAC,qBAAqB;UAAA1B,QAAA,eACvBrD,OAAA;YACImF,CAAC,EAAC,8MAA8M;YAChNP,IAAI,EAAC,SAAS;YACdQ,QAAQ,EAAC;UAAS;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAGsD,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAAErC,gBAAgB,aAAhBA,gBAAgB,wBAAAb,qBAAA,GAAhBa,gBAAgB,CAAEoB,IAAI,cAAAjC,qBAAA,uBAAtBA,qBAAA,CAAwBa;MAAgB;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9E1D,OAAA;QAAGsD,SAAS,EAAC,YAAY;QAAAD,QAAA,GAAC,cAAY,EAACrC,gBAAgB,CAACqE,SAAS;MAAA;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtE1D,OAAA;QAAGsD,SAAS,EAAC,YAAY;QAAAD,QAAA,GAAC,YAAU,EAACrC,gBAAgB,CAACsE,OAAO;MAAA;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAGlB1D,OAAA,CAACZ,YAAY;MACTmG,MAAM,EAAE/E,kBAAmB;MAC3BgF,OAAO,EAAEA,CAAA,KAAM/E,mBAAmB,CAAC,KAAK;IAAE;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC,eAEF1D,OAAA,CAACb,YAAY;MACToG,MAAM,EAAEjF,kBAAmB;MAC3BkF,OAAO,EAAEA,CAAA,KAAMjF,mBAAmB,CAAC,KAAK,CAAE;MAC1CkF,WAAW,EAAE3D;IAAmB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACxD,EAAA,CAhQID,KAAK;EAAA,QAMUV,WAAW,EACCA,WAAW,EACvBD,WAAW,EACXM,WAAW;AAAA;AAAA8F,EAAA,GAT1BzF,KAAK;AAkQX,eAAeA,KAAK;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}