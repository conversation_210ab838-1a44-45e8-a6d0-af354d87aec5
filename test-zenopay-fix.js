const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

const testZenoPayFix = async () => {
  console.log('🧪 Testing ZenoPay API Fix...\n');

  // Test data
  const testPaymentData = {
    order_id: `ORDER_${Date.now()}_TEST123`,
    buyer_name: "Test User",
    buyer_phone: "0744963858",
    buyer_email: "<EMAIL>",
    amount: 1000,
    webhook_url: process.env.ZENOPAY_WEBHOOK_URL
  };

  console.log('📋 Test Payment Data:');
  console.log(JSON.stringify(testPaymentData, null, 2));

  console.log('\n🔧 Environment Variables:');
  console.log('- ZENOPAY_API_KEY:', process.env.ZENOPAY_API_KEY ? 'SET ✅' : 'NOT SET ❌');
  console.log('- ZENOPAY_WEBHOOK_URL:', process.env.ZENOPAY_WEBHOOK_URL || 'NOT SET ❌');

  console.log('\n📤 What will be sent to ZenoPay:');
  console.log('URL:', 'https://api.zeno.africa/api/payments/mobile_money_tanzania');
  console.log('Headers:', {
    'Content-Type': 'application/x-www-form-urlencoded',
    'x-api-key': process.env.ZENOPAY_API_KEY ? '***' + process.env.ZENOPAY_API_KEY.slice(-4) : 'NOT_SET'
  });
  console.log('Body (form-encoded):', testPaymentData);

  console.log('\n🔍 Key Changes Made:');
  console.log('✅ 1. Added required order_id parameter');
  console.log('✅ 2. Removed account_id from body (not needed)');
  console.log('✅ 3. Removed secret_key from body (not for payment requests)');
  console.log('✅ 4. Moved api_key to headers as x-api-key');
  console.log('✅ 5. Fixed API endpoint URL');

  console.log('\n💰 Why Money Wasn\'t Reaching Your Wallet:');
  console.log('❌ Wrong API parameters were being sent');
  console.log('❌ ZenoPay couldn\'t process payments correctly');
  console.log('❌ Payments may have failed silently');
  console.log('❌ No proper order tracking');

  console.log('\n🎯 Expected Results After Fix:');
  console.log('✅ Payments will be processed correctly');
  console.log('✅ Money will reach your ZenoPay wallet (zp38236)');
  console.log('✅ Proper order tracking with order_id');
  console.log('✅ Webhook notifications will work');

  console.log('\n🧪 Next Steps:');
  console.log('1. Test a small payment (1000 TZS)');
  console.log('2. Check ZenoPay dashboard for the transaction');
  console.log('3. Verify webhook is called');
  console.log('4. Confirm money appears in wallet');

  console.log('\n📱 Test Payment Instructions:');
  console.log('1. Go to /user/plans in your app');
  console.log('2. Select any plan');
  console.log('3. Complete payment via mobile money');
  console.log('4. Check server logs for API response');
  console.log('5. Check ZenoPay dashboard for transaction');

  console.log('\n🔧 If Still Not Working:');
  console.log('1. Verify your ZenoPay API key is correct');
  console.log('2. Check if your account (zp38236) is active');
  console.log('3. Ensure webhook URL is accessible');
  console.log('4. Contact ZenoPay support if needed');

  console.log('\n📊 Monitoring:');
  console.log('- Server logs will show detailed API requests/responses');
  console.log('- Webhook logs will show payment confirmations');
  console.log('- Database will track subscription activations');

  console.log('\n✅ SUMMARY:');
  console.log('The payment API has been corrected to match ZenoPay specifications.');
  console.log('Money should now properly reach your wallet after successful payments.');
  console.log('Test with a small amount to verify the fix works.');
};

testZenoPayFix();
