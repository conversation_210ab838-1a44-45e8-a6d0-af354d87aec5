{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from\"react\";import{motion}from\"framer-motion\";import{useNavigate}from\"react-router-dom\";import{TbDashboard,TbChartBar,TbUsers,TbTarget,TbTrendingUp,TbDownload,Tb<PERSON><PERSON><PERSON>,Tb<PERSON><PERSON>,TbCheck,TbX,TbCalendar,TbClock,TbFileText}from\"react-icons/tb\";import PageTitle from\"../../../components/PageTitle\";import{message,Table,Card,Statistic,Input,Select,DatePicker,Button,Tag,Progress}from\"antd\";import{useDispatch}from\"react-redux\";import{HideLoading,ShowLoading}from\"../../../redux/loaderSlice\";import{getAllReports}from\"../../../apicalls/reports\";import moment from\"moment\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const{Option}=Select;const{RangePicker}=DatePicker;const{Search}=Input;function AdminReports(){const navigate=useNavigate();const[reportsData,setReportsData]=useState([]);const[pagination,setPagination]=useState({current:1,pageSize:10,total:0});const[stats,setStats]=useState({totalReports:0,totalStudents:0,averageScore:0,passRate:0,totalExams:0,activeToday:0});const dispatch=useDispatch();const[filters,setFilters]=useState({examName:\"\",userName:\"\",verdict:\"\",dateRange:null});const calculateStats=data=>{if(!data||data.length===0)return;const totalReports=data.length;const uniqueStudents=new Set(data.map(report=>{var _report$user;return(_report$user=report.user)===null||_report$user===void 0?void 0:_report$user._id;})).size;const passedReports=data.filter(report=>{var _report$result;return((_report$result=report.result)===null||_report$result===void 0?void 0:_report$result.verdict)==='Pass';}).length;const passRate=totalReports>0?Math.round(passedReports/totalReports*100):0;const scores=data.map(report=>{var _report$result2,_report$result2$corre,_report$exam;const obtained=((_report$result2=report.result)===null||_report$result2===void 0?void 0:(_report$result2$corre=_report$result2.correctAnswers)===null||_report$result2$corre===void 0?void 0:_report$result2$corre.length)||0;const total=((_report$exam=report.exam)===null||_report$exam===void 0?void 0:_report$exam.totalMarks)||1;return obtained/total*100;});const averageScore=scores.length>0?Math.round(scores.reduce((sum,score)=>sum+score,0)/scores.length):0;const uniqueExams=new Set(data.map(report=>{var _report$exam2;return(_report$exam2=report.exam)===null||_report$exam2===void 0?void 0:_report$exam2._id;})).size;const today=moment().startOf('day');const activeToday=data.filter(report=>moment(report.createdAt).isSame(today,'day')).length;setStats({totalReports,totalStudents:uniqueStudents,averageScore,passRate,totalExams:uniqueExams,activeToday});};const columns=[{title:\"Student\",dataIndex:\"userName\",render:(text,record)=>{var _record$user,_record$user2;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbUsers,{className:\"w-4 h-4 text-blue-600\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-gray-900\",children:((_record$user=record.user)===null||_record$user===void 0?void 0:_record$user.name)||'N/A'}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:((_record$user2=record.user)===null||_record$user2===void 0?void 0:_record$user2.email)||''})]})]});},width:200},{title:\"Exam\",dataIndex:\"examName\",render:(text,record)=>{var _record$exam,_record$exam2;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-gray-900\",children:((_record$exam=record.exam)===null||_record$exam===void 0?void 0:_record$exam.name)||'N/A'}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:((_record$exam2=record.exam)===null||_record$exam2===void 0?void 0:_record$exam2.subject)||'General'})]});},width:200},{title:\"Date & Time\",dataIndex:\"date\",render:(text,record)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbCalendar,{className:\"w-4 h-4 text-gray-400\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium\",children:moment(record.createdAt).format(\"MMM DD, YYYY\")}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500\",children:moment(record.createdAt).format(\"HH:mm\")})]})]}),width:150},{title:\"Score\",dataIndex:\"score\",render:(text,record)=>{var _record$result,_record$result$correc,_record$exam3;const obtained=((_record$result=record.result)===null||_record$result===void 0?void 0:(_record$result$correc=_record$result.correctAnswers)===null||_record$result$correc===void 0?void 0:_record$result$correc.length)||0;const total=((_record$exam3=record.exam)===null||_record$exam3===void 0?void 0:_record$exam3.totalMarks)||1;const percentage=Math.round(obtained/total*100);return/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-lg font-bold text-gray-900\",children:[obtained,\"/\",total]}),/*#__PURE__*/_jsx(Progress,{percent:percentage,size:\"small\",strokeColor:percentage>=60?'#10b981':'#ef4444',showInfo:false}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm font-medium \".concat(percentage>=60?'text-green-600':'text-red-600'),children:[percentage,\"%\"]})]});},width:120},{title:\"Result\",dataIndex:\"verdict\",render:(text,record)=>{var _record$result2;const verdict=(_record$result2=record.result)===null||_record$result2===void 0?void 0:_record$result2.verdict;const isPassed=verdict==='Pass';return/*#__PURE__*/_jsx(Tag,{icon:isPassed?/*#__PURE__*/_jsx(TbCheck,{}):/*#__PURE__*/_jsx(TbX,{}),color:isPassed?'success':'error',className:\"font-medium\",children:verdict||'N/A'});},width:100},{title:\"Actions\",key:\"actions\",render:(text,record)=>/*#__PURE__*/_jsx(Button,{type:\"primary\",size:\"small\",icon:/*#__PURE__*/_jsx(TbEye,{}),onClick:()=>{/* Handle view details */},className:\"bg-blue-500 hover:bg-blue-600\",children:\"View\"}),width:80}];const getData=async function(tempFilters){let page=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1;let limit=arguments.length>2&&arguments[2]!==undefined?arguments[2]:10;try{dispatch(ShowLoading());const response=await getAllReports(_objectSpread(_objectSpread({},tempFilters),{},{page,limit}));if(response.success){setReportsData(response.data);calculateStats(response.data);setPagination(_objectSpread(_objectSpread({},pagination),{},{current:page,total:response.pagination.totalReports}));}else{message.error(response.message);}dispatch(HideLoading());}catch(error){dispatch(HideLoading());message.error(error.message);}};const handleSearch=(value,field)=>{setFilters(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));};const handleDateRangeChange=dates=>{setFilters(prev=>_objectSpread(_objectSpread({},prev),{},{dateRange:dates}));setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));};const clearFilters=()=>{setFilters({examName:\"\",userName:\"\",verdict:\"\",dateRange:null});setPagination(prev=>_objectSpread(_objectSpread({},prev),{},{current:1}));};useEffect(()=>{getData(filters,pagination.current,pagination.pageSize);},[filters,pagination.current]);const handleTableChange=pagination=>{getData(filters,pagination.current,pagination.pageSize);};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",children:[/*#__PURE__*/_jsx(PageTitle,{title:\"Admin Reports\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:\"text-center mb-12\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\",children:/*#__PURE__*/_jsx(TbChartBar,{className:\"w-8 h-8 text-white\"})}),/*#__PURE__*/_jsxs(\"h1\",{className:\"text-4xl font-bold text-gray-900 mb-4\",children:[\"Student \",/*#__PURE__*/_jsx(\"span\",{className:\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",children:\"Performance\"}),\" Analytics\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 max-w-2xl mx-auto\",children:\"Comprehensive insights into student performance and exam analytics\"})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.1},className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\",children:[/*#__PURE__*/_jsx(Card,{className:\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3\",children:/*#__PURE__*/_jsx(TbFileText,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(Statistic,{title:\"Total Reports\",value:stats.totalReports,valueStyle:{color:'#1e40af',fontSize:'24px',fontWeight:'bold'}})]})}),/*#__PURE__*/_jsx(Card,{className:\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3\",children:/*#__PURE__*/_jsx(TbUsers,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(Statistic,{title:\"Active Students\",value:stats.totalStudents,valueStyle:{color:'#059669',fontSize:'24px',fontWeight:'bold'}})]})}),/*#__PURE__*/_jsx(Card,{className:\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3\",children:/*#__PURE__*/_jsx(TbTrendingUp,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(Statistic,{title:\"Average Score\",value:stats.averageScore,suffix:\"%\",valueStyle:{color:'#7c3aed',fontSize:'24px',fontWeight:'bold'}})]})}),/*#__PURE__*/_jsx(Card,{className:\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3\",children:/*#__PURE__*/_jsx(TbTarget,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(Statistic,{title:\"Pass Rate\",value:stats.passRate,suffix:\"%\",valueStyle:{color:'#ea580c',fontSize:'24px',fontWeight:'bold'}})]})}),/*#__PURE__*/_jsx(Card,{className:\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3\",children:/*#__PURE__*/_jsx(TbFileText,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(Statistic,{title:\"Total Exams\",value:stats.totalExams,valueStyle:{color:'#db2777',fontSize:'24px',fontWeight:'bold'}})]})}),/*#__PURE__*/_jsx(Card,{className:\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-indigo-50 to-indigo-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mb-3\",children:/*#__PURE__*/_jsx(TbClock,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(Statistic,{title:\"Today's Activity\",value:stats.activeToday,valueStyle:{color:'#4338ca',fontSize:'24px',fontWeight:'bold'}})]})})]}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.2},className:\"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(TbFilter,{className:\"w-5 h-5 text-gray-600\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"Filter Reports\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-4\",children:[/*#__PURE__*/_jsx(Search,{placeholder:\"Search by exam name\",value:filters.examName,onChange:e=>handleSearch(e.target.value,'examName'),className:\"w-full sm:w-48\",size:\"large\"}),/*#__PURE__*/_jsx(Search,{placeholder:\"Search by student name\",value:filters.userName,onChange:e=>handleSearch(e.target.value,'userName'),className:\"w-full sm:w-48\",size:\"large\"}),/*#__PURE__*/_jsxs(Select,{placeholder:\"Select Result\",value:filters.verdict,onChange:value=>handleSearch(value,'verdict'),className:\"w-full sm:w-48\",size:\"large\",children:[/*#__PURE__*/_jsx(Option,{value:\"\",children:\"All Results\"}),/*#__PURE__*/_jsx(Option,{value:\"Pass\",children:\"Passed\"}),/*#__PURE__*/_jsx(Option,{value:\"Fail\",children:\"Failed\"})]}),/*#__PURE__*/_jsx(RangePicker,{value:filters.dateRange,onChange:handleDateRangeChange,className:\"w-full sm:w-64\",size:\"large\",placeholder:['Start Date','End Date']}),/*#__PURE__*/_jsx(Button,{onClick:clearFilters,size:\"large\",className:\"w-full sm:w-auto\",children:\"Clear Filters\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(TbDownload,{}),size:\"large\",className:\"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 border-none\",children:\"Export\"})]})]})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.3},className:\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\",children:/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:reportsData,pagination:{current:pagination.current,pageSize:pagination.pageSize,total:pagination.total,showSizeChanger:true,showQuickJumper:true,showTotal:(total,range)=>\"\".concat(range[0],\"-\").concat(range[1],\" of \").concat(total,\" reports\"),className:\"px-6 py-4\"},onChange:handleTableChange,rowKey:record=>record._id,scroll:{x:1200},className:\"modern-table\",size:\"large\"})})]})]});}export default AdminReports;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useNavigate", "TbDashboard", "TbChartBar", "TbUsers", "TbTarget", "TbTrendingUp", "TbDownload", "Tb<PERSON><PERSON>er", "TbEye", "TbCheck", "TbX", "TbCalendar", "TbClock", "TbFileText", "Page<PERSON><PERSON>le", "message", "Table", "Card", "Statistic", "Input", "Select", "DatePicker", "<PERSON><PERSON>", "Tag", "Progress", "useDispatch", "HideLoading", "ShowLoading", "getAllReports", "moment", "jsx", "_jsx", "jsxs", "_jsxs", "Option", "RangePicker", "Search", "AdminReports", "navigate", "reportsData", "setReportsData", "pagination", "setPagination", "current", "pageSize", "total", "stats", "setStats", "totalReports", "totalStudents", "averageScore", "passRate", "totalExams", "activeToday", "dispatch", "filters", "setFilters", "examName", "userName", "verdict", "date<PERSON><PERSON><PERSON>", "calculateStats", "data", "length", "uniqueStudents", "Set", "map", "report", "_report$user", "user", "_id", "size", "passedReports", "filter", "_report$result", "result", "Math", "round", "scores", "_report$result2", "_report$result2$corre", "_report$exam", "obtained", "correctAnswers", "exam", "totalMarks", "reduce", "sum", "score", "uniqueExams", "_report$exam2", "today", "startOf", "createdAt", "isSame", "columns", "title", "dataIndex", "render", "text", "record", "_record$user", "_record$user2", "className", "children", "name", "email", "width", "_record$exam", "_record$exam2", "subject", "format", "_record$result", "_record$result$correc", "_record$exam3", "percentage", "percent", "strokeColor", "showInfo", "concat", "_record$result2", "isPassed", "icon", "color", "key", "type", "onClick", "getData", "tempFilters", "page", "arguments", "undefined", "limit", "response", "_objectSpread", "success", "error", "handleSearch", "value", "field", "prev", "handleDateRangeChange", "dates", "clearFilters", "handleTableChange", "div", "initial", "opacity", "y", "animate", "transition", "delay", "valueStyle", "fontSize", "fontWeight", "suffix", "placeholder", "onChange", "e", "target", "dataSource", "showSizeChanger", "showQuickJumper", "showTotal", "range", "<PERSON><PERSON><PERSON>", "scroll", "x"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AdminReports/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport {\r\n  TbDashboard,\r\n  TbChartBar,\r\n  TbUsers,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheck,\r\n  TbX,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbFileText\r\n} from \"react-icons/tb\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Table, Card, Statistic, Input, Select, DatePicker, Button, Tag, Progress } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReports } from \"../../../apicalls/reports\";\r\nimport moment from \"moment\";\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\nconst { Search } = Input;\r\n\r\nfunction AdminReports() {\r\n  const navigate = useNavigate();\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0,\r\n  });\r\n  const [stats, setStats] = useState({\r\n    totalReports: 0,\r\n    totalStudents: 0,\r\n    averageScore: 0,\r\n    passRate: 0,\r\n    totalExams: 0,\r\n    activeToday: 0\r\n  });\r\n  const dispatch = useDispatch();\r\n  const [filters, setFilters] = useState({\r\n    examName: \"\",\r\n    userName: \"\",\r\n    verdict: \"\",\r\n    dateRange: null\r\n  });\r\n\r\n  const calculateStats = (data) => {\r\n    if (!data || data.length === 0) return;\r\n\r\n    const totalReports = data.length;\r\n    const uniqueStudents = new Set(data.map(report => report.user?._id)).size;\r\n    const passedReports = data.filter(report => report.result?.verdict === 'Pass').length;\r\n    const passRate = totalReports > 0 ? Math.round((passedReports / totalReports) * 100) : 0;\r\n\r\n    const scores = data.map(report => {\r\n      const obtained = report.result?.correctAnswers?.length || 0;\r\n      const total = report.exam?.totalMarks || 1;\r\n      return (obtained / total) * 100;\r\n    });\r\n\r\n    const averageScore = scores.length > 0 ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;\r\n    const uniqueExams = new Set(data.map(report => report.exam?._id)).size;\r\n    const today = moment().startOf('day');\r\n    const activeToday = data.filter(report => moment(report.createdAt).isSame(today, 'day')).length;\r\n\r\n    setStats({\r\n      totalReports,\r\n      totalStudents: uniqueStudents,\r\n      averageScore,\r\n      passRate,\r\n      totalExams: uniqueExams,\r\n      activeToday\r\n    });\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Student\",\r\n      dataIndex: \"userName\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n            <TbUsers className=\"w-4 h-4 text-blue-600\" />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{record.user?.name || 'N/A'}</div>\r\n            <div className=\"text-sm text-gray-500\">{record.user?.email || ''}</div>\r\n          </div>\r\n        </div>\r\n      ),\r\n      width: 200,\r\n    },\r\n    {\r\n      title: \"Exam\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => (\r\n        <div>\r\n          <div className=\"font-medium text-gray-900\">{record.exam?.name || 'N/A'}</div>\r\n          <div className=\"text-sm text-gray-500\">{record.exam?.subject || 'General'}</div>\r\n        </div>\r\n      ),\r\n      width: 200,\r\n    },\r\n    {\r\n      title: \"Date & Time\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <TbCalendar className=\"w-4 h-4 text-gray-400\" />\r\n          <div>\r\n            <div className=\"text-sm font-medium\">{moment(record.createdAt).format(\"MMM DD, YYYY\")}</div>\r\n            <div className=\"text-xs text-gray-500\">{moment(record.createdAt).format(\"HH:mm\")}</div>\r\n          </div>\r\n        </div>\r\n      ),\r\n      width: 150,\r\n    },\r\n    {\r\n      title: \"Score\",\r\n      dataIndex: \"score\",\r\n      render: (text, record) => {\r\n        const obtained = record.result?.correctAnswers?.length || 0;\r\n        const total = record.exam?.totalMarks || 1;\r\n        const percentage = Math.round((obtained / total) * 100);\r\n\r\n        return (\r\n          <div className=\"text-center\">\r\n            <div className=\"text-lg font-bold text-gray-900\">{obtained}/{total}</div>\r\n            <Progress\r\n              percent={percentage}\r\n              size=\"small\"\r\n              strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}\r\n              showInfo={false}\r\n            />\r\n            <div className={`text-sm font-medium ${percentage >= 60 ? 'text-green-600' : 'text-red-600'}`}>\r\n              {percentage}%\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n      width: 120,\r\n    },\r\n    {\r\n      title: \"Result\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => {\r\n        const verdict = record.result?.verdict;\r\n        const isPassed = verdict === 'Pass';\r\n\r\n        return (\r\n          <Tag\r\n            icon={isPassed ? <TbCheck /> : <TbX />}\r\n            color={isPassed ? 'success' : 'error'}\r\n            className=\"font-medium\"\r\n          >\r\n            {verdict || 'N/A'}\r\n          </Tag>\r\n        );\r\n      },\r\n      width: 100,\r\n    },\r\n    {\r\n      title: \"Actions\",\r\n      key: \"actions\",\r\n      render: (text, record) => (\r\n        <Button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          icon={<TbEye />}\r\n          onClick={() => {/* Handle view details */}}\r\n          className=\"bg-blue-500 hover:bg-blue-600\"\r\n        >\r\n          View\r\n        </Button>\r\n      ),\r\n      width: 80,\r\n    },\r\n  ];\r\n\r\n  const getData = async (tempFilters, page = 1, limit = 10) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReports({\r\n        ...tempFilters,\r\n        page,\r\n        limit,\r\n      });\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        calculateStats(response.data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: page,\r\n          total: response.pagination.totalReports,\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleSearch = (value, field) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  const handleDateRangeChange = (dates) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      dateRange: dates\r\n    }));\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      examName: \"\",\r\n      userName: \"\",\r\n      verdict: \"\",\r\n      dateRange: null\r\n    });\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  }, [filters, pagination.current]);\r\n\r\n  const handleTableChange = (pagination) => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <PageTitle title=\"Admin Reports\" />\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\">\r\n            <TbChartBar className=\"w-8 h-8 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\r\n            Student <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Performance</span> Analytics\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\r\n            Comprehensive insights into student performance and exam analytics\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Stats Cards */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\"\r\n        >\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbFileText className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Reports\"\r\n                value={stats.totalReports}\r\n                valueStyle={{ color: '#1e40af', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbUsers className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Active Students\"\r\n                value={stats.totalStudents}\r\n                valueStyle={{ color: '#059669', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbTrendingUp className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Average Score\"\r\n                value={stats.averageScore}\r\n                suffix=\"%\"\r\n                valueStyle={{ color: '#7c3aed', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbTarget className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Pass Rate\"\r\n                value={stats.passRate}\r\n                suffix=\"%\"\r\n                valueStyle={{ color: '#ea580c', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbFileText className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Exams\"\r\n                value={stats.totalExams}\r\n                valueStyle={{ color: '#db2777', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-indigo-50 to-indigo-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbClock className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Today's Activity\"\r\n                value={stats.activeToday}\r\n                valueStyle={{ color: '#4338ca', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n        </motion.div>\r\n\r\n        {/* Filters Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\"\r\n        >\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <TbFilter className=\"w-5 h-5 text-gray-600\" />\r\n              <h3 className=\"text-lg font-semibold text-gray-900\">Filter Reports</h3>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Search\r\n                placeholder=\"Search by exam name\"\r\n                value={filters.examName}\r\n                onChange={(e) => handleSearch(e.target.value, 'examName')}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              />\r\n\r\n              <Search\r\n                placeholder=\"Search by student name\"\r\n                value={filters.userName}\r\n                onChange={(e) => handleSearch(e.target.value, 'userName')}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              />\r\n\r\n              <Select\r\n                placeholder=\"Select Result\"\r\n                value={filters.verdict}\r\n                onChange={(value) => handleSearch(value, 'verdict')}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              >\r\n                <Option value=\"\">All Results</Option>\r\n                <Option value=\"Pass\">Passed</Option>\r\n                <Option value=\"Fail\">Failed</Option>\r\n              </Select>\r\n\r\n              <RangePicker\r\n                value={filters.dateRange}\r\n                onChange={handleDateRangeChange}\r\n                className=\"w-full sm:w-64\"\r\n                size=\"large\"\r\n                placeholder={['Start Date', 'End Date']}\r\n              />\r\n\r\n              <Button\r\n                onClick={clearFilters}\r\n                size=\"large\"\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Clear Filters\r\n              </Button>\r\n\r\n              <Button\r\n                type=\"primary\"\r\n                icon={<TbDownload />}\r\n                size=\"large\"\r\n                className=\"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 border-none\"\r\n              >\r\n                Export\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Reports Table */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\r\n        >\r\n          <Table\r\n            columns={columns}\r\n            dataSource={reportsData}\r\n            pagination={{\r\n              current: pagination.current,\r\n              pageSize: pagination.pageSize,\r\n              total: pagination.total,\r\n              showSizeChanger: true,\r\n              showQuickJumper: true,\r\n              showTotal: (total, range) =>\r\n                `${range[0]}-${range[1]} of ${total} reports`,\r\n              className: \"px-6 py-4\"\r\n            }}\r\n            onChange={handleTableChange}\r\n            rowKey={(record) => record._id}\r\n            scroll={{ x: 1200 }}\r\n            className=\"modern-table\"\r\n            size=\"large\"\r\n          />\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdminReports;\r\n"], "mappings": "+HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,WAAW,CACXC,UAAU,CACVC,OAAO,CACPC,QAAQ,CACRC,YAAY,CACZC,UAAU,CACVC,QAAQ,CACRC,KAAK,CACLC,OAAO,CACPC,GAAG,CACHC,UAAU,CACVC,OAAO,CACPC,UAAU,KACL,gBAAgB,CACvB,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,OAASC,OAAO,CAAEC,KAAK,CAAEC,IAAI,CAAEC,SAAS,CAAEC,KAAK,CAAEC,MAAM,CAAEC,UAAU,CAAEC,MAAM,CAAEC,GAAG,CAAEC,QAAQ,KAAQ,MAAM,CACxG,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,WAAW,CAAEC,WAAW,KAAQ,4BAA4B,CACrE,OAASC,aAAa,KAAQ,2BAA2B,CACzD,MAAO,CAAAC,MAAM,KAAM,QAAQ,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE5B,KAAM,CAAEC,MAAO,CAAC,CAAGd,MAAM,CACzB,KAAM,CAAEe,WAAY,CAAC,CAAGd,UAAU,CAClC,KAAM,CAAEe,MAAO,CAAC,CAAGjB,KAAK,CAExB,QAAS,CAAAkB,YAAYA,CAAA,CAAG,CACtB,KAAM,CAAAC,QAAQ,CAAGtC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACuC,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAC,CAC3C8C,OAAO,CAAE,CAAC,CACVC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CACT,CAAC,CAAC,CACF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGlD,QAAQ,CAAC,CACjCmD,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,CAAC,CACXC,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,CACf,CAAC,CAAC,CACF,KAAM,CAAAC,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG3D,QAAQ,CAAC,CACrC4D,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,IACb,CAAC,CAAC,CAEF,KAAM,CAAAC,cAAc,CAAIC,IAAI,EAAK,CAC/B,GAAI,CAACA,IAAI,EAAIA,IAAI,CAACC,MAAM,GAAK,CAAC,CAAE,OAEhC,KAAM,CAAAf,YAAY,CAAGc,IAAI,CAACC,MAAM,CAChC,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAAC,GAAG,CAACH,IAAI,CAACI,GAAG,CAACC,MAAM,OAAAC,YAAA,QAAAA,YAAA,CAAID,MAAM,CAACE,IAAI,UAAAD,YAAA,iBAAXA,YAAA,CAAaE,GAAG,GAAC,CAAC,CAACC,IAAI,CACzE,KAAM,CAAAC,aAAa,CAAGV,IAAI,CAACW,MAAM,CAACN,MAAM,OAAAO,cAAA,OAAI,EAAAA,cAAA,CAAAP,MAAM,CAACQ,MAAM,UAAAD,cAAA,iBAAbA,cAAA,CAAef,OAAO,IAAK,MAAM,GAAC,CAACI,MAAM,CACrF,KAAM,CAAAZ,QAAQ,CAAGH,YAAY,CAAG,CAAC,CAAG4B,IAAI,CAACC,KAAK,CAAEL,aAAa,CAAGxB,YAAY,CAAI,GAAG,CAAC,CAAG,CAAC,CAExF,KAAM,CAAA8B,MAAM,CAAGhB,IAAI,CAACI,GAAG,CAACC,MAAM,EAAI,KAAAY,eAAA,CAAAC,qBAAA,CAAAC,YAAA,CAChC,KAAM,CAAAC,QAAQ,CAAG,EAAAH,eAAA,CAAAZ,MAAM,CAACQ,MAAM,UAAAI,eAAA,kBAAAC,qBAAA,CAAbD,eAAA,CAAeI,cAAc,UAAAH,qBAAA,iBAA7BA,qBAAA,CAA+BjB,MAAM,GAAI,CAAC,CAC3D,KAAM,CAAAlB,KAAK,CAAG,EAAAoC,YAAA,CAAAd,MAAM,CAACiB,IAAI,UAAAH,YAAA,iBAAXA,YAAA,CAAaI,UAAU,GAAI,CAAC,CAC1C,MAAQ,CAAAH,QAAQ,CAAGrC,KAAK,CAAI,GAAG,CACjC,CAAC,CAAC,CAEF,KAAM,CAAAK,YAAY,CAAG4B,MAAM,CAACf,MAAM,CAAG,CAAC,CAAGa,IAAI,CAACC,KAAK,CAACC,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,CAAEC,KAAK,GAAKD,GAAG,CAAGC,KAAK,CAAE,CAAC,CAAC,CAAGV,MAAM,CAACf,MAAM,CAAC,CAAG,CAAC,CACtH,KAAM,CAAA0B,WAAW,CAAG,GAAI,CAAAxB,GAAG,CAACH,IAAI,CAACI,GAAG,CAACC,MAAM,OAAAuB,aAAA,QAAAA,aAAA,CAAIvB,MAAM,CAACiB,IAAI,UAAAM,aAAA,iBAAXA,aAAA,CAAapB,GAAG,GAAC,CAAC,CAACC,IAAI,CACtE,KAAM,CAAAoB,KAAK,CAAG9D,MAAM,CAAC,CAAC,CAAC+D,OAAO,CAAC,KAAK,CAAC,CACrC,KAAM,CAAAvC,WAAW,CAAGS,IAAI,CAACW,MAAM,CAACN,MAAM,EAAItC,MAAM,CAACsC,MAAM,CAAC0B,SAAS,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,KAAK,CAAC,CAAC,CAAC5B,MAAM,CAE/FhB,QAAQ,CAAC,CACPC,YAAY,CACZC,aAAa,CAAEe,cAAc,CAC7Bd,YAAY,CACZC,QAAQ,CACRC,UAAU,CAAEqC,WAAW,CACvBpC,WACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA0C,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,SAAS,CAChBC,SAAS,CAAE,UAAU,CACrBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,QAAAC,YAAA,CAAAC,aAAA,oBACnBrE,KAAA,QAAKsE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzE,IAAA,QAAKwE,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFzE,IAAA,CAAC5B,OAAO,EAACoG,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC1C,CAAC,cACNtE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,QAAKwE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE,EAAAH,YAAA,CAAAD,MAAM,CAAC/B,IAAI,UAAAgC,YAAA,iBAAXA,YAAA,CAAaI,IAAI,GAAI,KAAK,CAAM,CAAC,cAC7E1E,IAAA,QAAKwE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE,EAAAF,aAAA,CAAAF,MAAM,CAAC/B,IAAI,UAAAiC,aAAA,iBAAXA,aAAA,CAAaI,KAAK,GAAI,EAAE,CAAM,CAAC,EACpE,CAAC,EACH,CAAC,EACP,CACDC,KAAK,CAAE,GACT,CAAC,CACD,CACEX,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,QAAAQ,YAAA,CAAAC,aAAA,oBACnB5E,KAAA,QAAAuE,QAAA,eACEzE,IAAA,QAAKwE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAE,EAAAI,YAAA,CAAAR,MAAM,CAAChB,IAAI,UAAAwB,YAAA,iBAAXA,YAAA,CAAaH,IAAI,GAAI,KAAK,CAAM,CAAC,cAC7E1E,IAAA,QAAKwE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE,EAAAK,aAAA,CAAAT,MAAM,CAAChB,IAAI,UAAAyB,aAAA,iBAAXA,aAAA,CAAaC,OAAO,GAAI,SAAS,CAAM,CAAC,EAC7E,CAAC,EACP,CACDH,KAAK,CAAE,GACT,CAAC,CACD,CACEX,KAAK,CAAE,aAAa,CACpBC,SAAS,CAAE,MAAM,CACjBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,gBACnBnE,KAAA,QAAKsE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzE,IAAA,CAACpB,UAAU,EAAC4F,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAChDtE,KAAA,QAAAuE,QAAA,eACEzE,IAAA,QAAKwE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE3E,MAAM,CAACuE,MAAM,CAACP,SAAS,CAAC,CAACkB,MAAM,CAAC,cAAc,CAAC,CAAM,CAAC,cAC5FhF,IAAA,QAAKwE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE3E,MAAM,CAACuE,MAAM,CAACP,SAAS,CAAC,CAACkB,MAAM,CAAC,OAAO,CAAC,CAAM,CAAC,EACpF,CAAC,EACH,CACN,CACDJ,KAAK,CAAE,GACT,CAAC,CACD,CACEX,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,OAAO,CAClBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,GAAK,KAAAY,cAAA,CAAAC,qBAAA,CAAAC,aAAA,CACxB,KAAM,CAAAhC,QAAQ,CAAG,EAAA8B,cAAA,CAAAZ,MAAM,CAACzB,MAAM,UAAAqC,cAAA,kBAAAC,qBAAA,CAAbD,cAAA,CAAe7B,cAAc,UAAA8B,qBAAA,iBAA7BA,qBAAA,CAA+BlD,MAAM,GAAI,CAAC,CAC3D,KAAM,CAAAlB,KAAK,CAAG,EAAAqE,aAAA,CAAAd,MAAM,CAAChB,IAAI,UAAA8B,aAAA,iBAAXA,aAAA,CAAa7B,UAAU,GAAI,CAAC,CAC1C,KAAM,CAAA8B,UAAU,CAAGvC,IAAI,CAACC,KAAK,CAAEK,QAAQ,CAAGrC,KAAK,CAAI,GAAG,CAAC,CAEvD,mBACEZ,KAAA,QAAKsE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BvE,KAAA,QAAKsE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,EAAEtB,QAAQ,CAAC,GAAC,CAACrC,KAAK,EAAM,CAAC,cACzEd,IAAA,CAACP,QAAQ,EACP4F,OAAO,CAAED,UAAW,CACpB5C,IAAI,CAAC,OAAO,CACZ8C,WAAW,CAAEF,UAAU,EAAI,EAAE,CAAG,SAAS,CAAG,SAAU,CACtDG,QAAQ,CAAE,KAAM,CACjB,CAAC,cACFrF,KAAA,QAAKsE,SAAS,wBAAAgB,MAAA,CAAyBJ,UAAU,EAAI,EAAE,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAAX,QAAA,EAC3FW,UAAU,CAAC,GACd,EAAK,CAAC,EACH,CAAC,CAEV,CAAC,CACDR,KAAK,CAAE,GACT,CAAC,CACD,CACEX,KAAK,CAAE,QAAQ,CACfC,SAAS,CAAE,SAAS,CACpBC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,GAAK,KAAAoB,eAAA,CACxB,KAAM,CAAA7D,OAAO,EAAA6D,eAAA,CAAGpB,MAAM,CAACzB,MAAM,UAAA6C,eAAA,iBAAbA,eAAA,CAAe7D,OAAO,CACtC,KAAM,CAAA8D,QAAQ,CAAG9D,OAAO,GAAK,MAAM,CAEnC,mBACE5B,IAAA,CAACR,GAAG,EACFmG,IAAI,CAAED,QAAQ,cAAG1F,IAAA,CAACtB,OAAO,GAAE,CAAC,cAAGsB,IAAA,CAACrB,GAAG,GAAE,CAAE,CACvCiH,KAAK,CAAEF,QAAQ,CAAG,SAAS,CAAG,OAAQ,CACtClB,SAAS,CAAC,aAAa,CAAAC,QAAA,CAEtB7C,OAAO,EAAI,KAAK,CACd,CAAC,CAEV,CAAC,CACDgD,KAAK,CAAE,GACT,CAAC,CACD,CACEX,KAAK,CAAE,SAAS,CAChB4B,GAAG,CAAE,SAAS,CACd1B,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,gBACnBrE,IAAA,CAACT,MAAM,EACLuG,IAAI,CAAC,SAAS,CACdtD,IAAI,CAAC,OAAO,CACZmD,IAAI,cAAE3F,IAAA,CAACvB,KAAK,GAAE,CAAE,CAChBsH,OAAO,CAAEA,CAAA,GAAM,CAAC,0BAA2B,CAC3CvB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAC1C,MAED,CAAQ,CACT,CACDG,KAAK,CAAE,EACT,CAAC,CACF,CAED,KAAM,CAAAoB,OAAO,CAAG,cAAAA,CAAOC,WAAW,CAA2B,IAAzB,CAAAC,IAAI,CAAAC,SAAA,CAAAnE,MAAA,IAAAmE,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,IAAE,CAAAE,KAAK,CAAAF,SAAA,CAAAnE,MAAA,IAAAmE,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,EAAE,CACtD,GAAI,CACF5E,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC,CACvB,KAAM,CAAA0G,QAAQ,CAAG,KAAM,CAAAzG,aAAa,CAAA0G,aAAA,CAAAA,aAAA,IAC/BN,WAAW,MACdC,IAAI,CACJG,KAAK,EACN,CAAC,CACF,GAAIC,QAAQ,CAACE,OAAO,CAAE,CACpB/F,cAAc,CAAC6F,QAAQ,CAACvE,IAAI,CAAC,CAC7BD,cAAc,CAACwE,QAAQ,CAACvE,IAAI,CAAC,CAC7BpB,aAAa,CAAA4F,aAAA,CAAAA,aAAA,IACR7F,UAAU,MACbE,OAAO,CAAEsF,IAAI,CACbpF,KAAK,CAAEwF,QAAQ,CAAC5F,UAAU,CAACO,YAAY,EACxC,CAAC,CACJ,CAAC,IAAM,CACLjC,OAAO,CAACyH,KAAK,CAACH,QAAQ,CAACtH,OAAO,CAAC,CACjC,CACAuC,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC,CACzB,CAAE,MAAO8G,KAAK,CAAE,CACdlF,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC,CACvBX,OAAO,CAACyH,KAAK,CAACA,KAAK,CAACzH,OAAO,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAA0H,YAAY,CAAGA,CAACC,KAAK,CAAEC,KAAK,GAAK,CACrCnF,UAAU,CAACoF,IAAI,EAAAN,aAAA,CAAAA,aAAA,IACVM,IAAI,MACP,CAACD,KAAK,EAAGD,KAAK,EACd,CAAC,CACHhG,aAAa,CAACkG,IAAI,EAAAN,aAAA,CAAAA,aAAA,IAAUM,IAAI,MAAEjG,OAAO,CAAE,CAAC,EAAG,CAAC,CAClD,CAAC,CAED,KAAM,CAAAkG,qBAAqB,CAAIC,KAAK,EAAK,CACvCtF,UAAU,CAACoF,IAAI,EAAAN,aAAA,CAAAA,aAAA,IACVM,IAAI,MACPhF,SAAS,CAAEkF,KAAK,EAChB,CAAC,CACHpG,aAAa,CAACkG,IAAI,EAAAN,aAAA,CAAAA,aAAA,IAAUM,IAAI,MAAEjG,OAAO,CAAE,CAAC,EAAG,CAAC,CAClD,CAAC,CAED,KAAM,CAAAoG,YAAY,CAAGA,CAAA,GAAM,CACzBvF,UAAU,CAAC,CACTC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,IACb,CAAC,CAAC,CACFlB,aAAa,CAACkG,IAAI,EAAAN,aAAA,CAAAA,aAAA,IAAUM,IAAI,MAAEjG,OAAO,CAAE,CAAC,EAAG,CAAC,CAClD,CAAC,CAED7C,SAAS,CAAC,IAAM,CACdiI,OAAO,CAACxE,OAAO,CAAEd,UAAU,CAACE,OAAO,CAAEF,UAAU,CAACG,QAAQ,CAAC,CAC3D,CAAC,CAAE,CAACW,OAAO,CAAEd,UAAU,CAACE,OAAO,CAAC,CAAC,CAEjC,KAAM,CAAAqG,iBAAiB,CAAIvG,UAAU,EAAK,CACxCsF,OAAO,CAACxE,OAAO,CAAEd,UAAU,CAACE,OAAO,CAAEF,UAAU,CAACG,QAAQ,CAAC,CAC3D,CAAC,CAED,mBACEX,KAAA,QAAKsE,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjFzE,IAAA,CAACjB,SAAS,EAACkF,KAAK,CAAC,eAAe,CAAE,CAAC,cAEnC/D,KAAA,QAAKsE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAE1DvE,KAAA,CAAClC,MAAM,CAACkJ,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9B7C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAE7BzE,IAAA,QAAKwE,SAAS,CAAC,2HAA2H,CAAAC,QAAA,cACxIzE,IAAA,CAAC7B,UAAU,EAACqG,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC1C,CAAC,cACNtE,KAAA,OAAIsE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,EAAC,UAC5C,cAAAzE,IAAA,SAAMwE,SAAS,CAAC,4EAA4E,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,aACzH,EAAI,CAAC,cACLzE,IAAA,MAAGwE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,oEAEvD,CAAG,CAAC,EACM,CAAC,cAGbvE,KAAA,CAAClC,MAAM,CAACkJ,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhD,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAEpFzE,IAAA,CAACd,IAAI,EAACsF,SAAS,CAAC,6GAA6G,CAAAC,QAAA,cAC3HvE,KAAA,QAAKsE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzE,IAAA,QAAKwE,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cACvFzE,IAAA,CAAClB,UAAU,EAAC0F,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC1C,CAAC,cACNxE,IAAA,CAACb,SAAS,EACR8E,KAAK,CAAC,eAAe,CACrB0C,KAAK,CAAE5F,KAAK,CAACE,YAAa,CAC1BwG,UAAU,CAAE,CAAE7B,KAAK,CAAE,SAAS,CAAE8B,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CACxE,CAAC,EACC,CAAC,CACF,CAAC,cAEP3H,IAAA,CAACd,IAAI,EAACsF,SAAS,CAAC,+GAA+G,CAAAC,QAAA,cAC7HvE,KAAA,QAAKsE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzE,IAAA,QAAKwE,SAAS,CAAC,2EAA2E,CAAAC,QAAA,cACxFzE,IAAA,CAAC5B,OAAO,EAACoG,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACvC,CAAC,cACNxE,IAAA,CAACb,SAAS,EACR8E,KAAK,CAAC,iBAAiB,CACvB0C,KAAK,CAAE5F,KAAK,CAACG,aAAc,CAC3BuG,UAAU,CAAE,CAAE7B,KAAK,CAAE,SAAS,CAAE8B,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CACxE,CAAC,EACC,CAAC,CACF,CAAC,cAEP3H,IAAA,CAACd,IAAI,EAACsF,SAAS,CAAC,iHAAiH,CAAAC,QAAA,cAC/HvE,KAAA,QAAKsE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzE,IAAA,QAAKwE,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFzE,IAAA,CAAC1B,YAAY,EAACkG,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC5C,CAAC,cACNxE,IAAA,CAACb,SAAS,EACR8E,KAAK,CAAC,eAAe,CACrB0C,KAAK,CAAE5F,KAAK,CAACI,YAAa,CAC1ByG,MAAM,CAAC,GAAG,CACVH,UAAU,CAAE,CAAE7B,KAAK,CAAE,SAAS,CAAE8B,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CACxE,CAAC,EACC,CAAC,CACF,CAAC,cAEP3H,IAAA,CAACd,IAAI,EAACsF,SAAS,CAAC,iHAAiH,CAAAC,QAAA,cAC/HvE,KAAA,QAAKsE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzE,IAAA,QAAKwE,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFzE,IAAA,CAAC3B,QAAQ,EAACmG,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACxC,CAAC,cACNxE,IAAA,CAACb,SAAS,EACR8E,KAAK,CAAC,WAAW,CACjB0C,KAAK,CAAE5F,KAAK,CAACK,QAAS,CACtBwG,MAAM,CAAC,GAAG,CACVH,UAAU,CAAE,CAAE7B,KAAK,CAAE,SAAS,CAAE8B,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CACxE,CAAC,EACC,CAAC,CACF,CAAC,cAEP3H,IAAA,CAACd,IAAI,EAACsF,SAAS,CAAC,6GAA6G,CAAAC,QAAA,cAC3HvE,KAAA,QAAKsE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzE,IAAA,QAAKwE,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cACvFzE,IAAA,CAAClB,UAAU,EAAC0F,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC1C,CAAC,cACNxE,IAAA,CAACb,SAAS,EACR8E,KAAK,CAAC,aAAa,CACnB0C,KAAK,CAAE5F,KAAK,CAACM,UAAW,CACxBoG,UAAU,CAAE,CAAE7B,KAAK,CAAE,SAAS,CAAE8B,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CACxE,CAAC,EACC,CAAC,CACF,CAAC,cAEP3H,IAAA,CAACd,IAAI,EAACsF,SAAS,CAAC,iHAAiH,CAAAC,QAAA,cAC/HvE,KAAA,QAAKsE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzE,IAAA,QAAKwE,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFzE,IAAA,CAACnB,OAAO,EAAC2F,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACvC,CAAC,cACNxE,IAAA,CAACb,SAAS,EACR8E,KAAK,CAAC,kBAAkB,CACxB0C,KAAK,CAAE5F,KAAK,CAACO,WAAY,CACzBmG,UAAU,CAAE,CAAE7B,KAAK,CAAE,SAAS,CAAE8B,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CACxE,CAAC,EACC,CAAC,CACF,CAAC,EACG,CAAC,cAGb3H,IAAA,CAAChC,MAAM,CAACkJ,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhD,SAAS,CAAC,gEAAgE,CAAAC,QAAA,cAE1EvE,KAAA,QAAKsE,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjFvE,KAAA,QAAKsE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCzE,IAAA,CAACxB,QAAQ,EAACgG,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC9CxE,IAAA,OAAIwE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,EACpE,CAAC,cAENvE,KAAA,QAAKsE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CzE,IAAA,CAACK,MAAM,EACLwH,WAAW,CAAC,qBAAqB,CACjClB,KAAK,CAAEnF,OAAO,CAACE,QAAS,CACxBoG,QAAQ,CAAGC,CAAC,EAAKrB,YAAY,CAACqB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE,UAAU,CAAE,CAC1DnC,SAAS,CAAC,gBAAgB,CAC1BhC,IAAI,CAAC,OAAO,CACb,CAAC,cAEFxC,IAAA,CAACK,MAAM,EACLwH,WAAW,CAAC,wBAAwB,CACpClB,KAAK,CAAEnF,OAAO,CAACG,QAAS,CACxBmG,QAAQ,CAAGC,CAAC,EAAKrB,YAAY,CAACqB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE,UAAU,CAAE,CAC1DnC,SAAS,CAAC,gBAAgB,CAC1BhC,IAAI,CAAC,OAAO,CACb,CAAC,cAEFtC,KAAA,CAACb,MAAM,EACLwI,WAAW,CAAC,eAAe,CAC3BlB,KAAK,CAAEnF,OAAO,CAACI,OAAQ,CACvBkG,QAAQ,CAAGnB,KAAK,EAAKD,YAAY,CAACC,KAAK,CAAE,SAAS,CAAE,CACpDnC,SAAS,CAAC,gBAAgB,CAC1BhC,IAAI,CAAC,OAAO,CAAAiC,QAAA,eAEZzE,IAAA,CAACG,MAAM,EAACwG,KAAK,CAAC,EAAE,CAAAlC,QAAA,CAAC,aAAW,CAAQ,CAAC,cACrCzE,IAAA,CAACG,MAAM,EAACwG,KAAK,CAAC,MAAM,CAAAlC,QAAA,CAAC,QAAM,CAAQ,CAAC,cACpCzE,IAAA,CAACG,MAAM,EAACwG,KAAK,CAAC,MAAM,CAAAlC,QAAA,CAAC,QAAM,CAAQ,CAAC,EAC9B,CAAC,cAETzE,IAAA,CAACI,WAAW,EACVuG,KAAK,CAAEnF,OAAO,CAACK,SAAU,CACzBiG,QAAQ,CAAEhB,qBAAsB,CAChCtC,SAAS,CAAC,gBAAgB,CAC1BhC,IAAI,CAAC,OAAO,CACZqF,WAAW,CAAE,CAAC,YAAY,CAAE,UAAU,CAAE,CACzC,CAAC,cAEF7H,IAAA,CAACT,MAAM,EACLwG,OAAO,CAAEiB,YAAa,CACtBxE,IAAI,CAAC,OAAO,CACZgC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC7B,eAED,CAAQ,CAAC,cAETzE,IAAA,CAACT,MAAM,EACLuG,IAAI,CAAC,SAAS,CACdH,IAAI,cAAE3F,IAAA,CAACzB,UAAU,GAAE,CAAE,CACrBiE,IAAI,CAAC,OAAO,CACZgC,SAAS,CAAC,2EAA2E,CAAAC,QAAA,CACtF,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACI,CAAC,cAGbzE,IAAA,CAAChC,MAAM,CAACkJ,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BhD,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cAEjFzE,IAAA,CAACf,KAAK,EACJ+E,OAAO,CAAEA,OAAQ,CACjBiE,UAAU,CAAEzH,WAAY,CACxBE,UAAU,CAAE,CACVE,OAAO,CAAEF,UAAU,CAACE,OAAO,CAC3BC,QAAQ,CAAEH,UAAU,CAACG,QAAQ,CAC7BC,KAAK,CAAEJ,UAAU,CAACI,KAAK,CACvBoH,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAEA,CAACtH,KAAK,CAAEuH,KAAK,MAAA7C,MAAA,CACnB6C,KAAK,CAAC,CAAC,CAAC,MAAA7C,MAAA,CAAI6C,KAAK,CAAC,CAAC,CAAC,SAAA7C,MAAA,CAAO1E,KAAK,YAAU,CAC/C0D,SAAS,CAAE,WACb,CAAE,CACFsD,QAAQ,CAAEb,iBAAkB,CAC5BqB,MAAM,CAAGjE,MAAM,EAAKA,MAAM,CAAC9B,GAAI,CAC/BgG,MAAM,CAAE,CAAEC,CAAC,CAAE,IAAK,CAAE,CACpBhE,SAAS,CAAC,cAAc,CACxBhC,IAAI,CAAC,OAAO,CACb,CAAC,CACQ,CAAC,EACV,CAAC,EACH,CAAC,CAEV,CAEA,cAAe,CAAAlC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}