const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
require('dotenv').config();

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const checkSubscriptionData = async () => {
  try {
    await connectDB();
    
    console.log('🔍 Checking Lucy Mosha subscription data...\n');

    // Find Lucy Mosha specifically
    const lucyUser = await User.findOne({ name: 'Lucy Mosha' });

    if (!lucyUser) {
      console.log('❌ <PERSON> Mosha not found');
      return;
    }

    console.log('👤 Lucy Mosha Details:');
    console.log(`   🆔 User ID: ${lucyUser._id}`);
    console.log(`   📧 Email: ${lucyUser.email || 'NO EMAIL'}`);
    console.log(`   📱 Phone: ${lucyUser.phoneNumber || 'NO PHONE'}`);
    console.log(`   📊 Subscription Status: ${lucyUser.subscriptionStatus || 'undefined'}`);
    console.log(`   📅 Subscription Start: ${lucyUser.subscriptionStartDate || 'undefined'}`);
    console.log(`   📅 Subscription End: ${lucyUser.subscriptionEndDate || 'undefined'}`);
    console.log(`   📋 Subscription Plan: ${lucyUser.subscriptionPlan || 'undefined'}`);
    console.log(`   💳 Payment Required: ${lucyUser.paymentRequired}`);
    console.log(`   🎓 Level: ${lucyUser.level}`);
    console.log(`   📚 Class: ${lucyUser.class}`);
    console.log('');

    // Check subscription records
    const subscriptions = await Subscription.find({ user: lucyUser._id })
      .populate('activePlan')
      .sort({ createdAt: -1 });

    console.log('💳 Subscription Records:');
    if (subscriptions.length === 0) {
      console.log('   ❌ No subscription records found');
    } else {
      subscriptions.forEach((sub, index) => {
        console.log(`   📋 Subscription ${index + 1}:`);
        console.log(`      Plan: ${sub.activePlan?.title || 'Unknown'}`);
        console.log(`      Payment Status: ${sub.paymentStatus}`);
        console.log(`      Status: ${sub.status}`);
        console.log(`      Start Date: ${sub.startDate}`);
        console.log(`      End Date: ${sub.endDate}`);
        console.log(`      Created: ${sub.createdAt}`);

        if (sub.paymentHistory.length > 0) {
          console.log(`      Payment History:`);
          sub.paymentHistory.forEach((payment, pIndex) => {
            console.log(`        ${pIndex + 1}. Order ID: ${payment.orderId}`);
            console.log(`           Amount: ${payment.amount}`);
            console.log(`           Status: ${payment.paymentStatus}`);
            console.log(`           Date: ${payment.paymentDate}`);
          });
        }
        console.log('');
      });
    }

    // Get all non-admin users for comparison
    const users = await User.find({ isAdmin: { $ne: true } }).limit(10);
    
    console.log(`👥 Found ${users.length} users\n`);
    
    users.forEach((user, index) => {
      console.log(`👤 User ${index + 1}: ${user.name}`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   📊 Subscription Status: ${user.subscriptionStatus || 'undefined'}`);
      console.log(`   📅 Subscription End Date: ${user.subscriptionEndDate || 'undefined'}`);
      console.log(`   📋 Subscription Plan: ${user.subscriptionPlan || 'undefined'}`);
      console.log(`   🆔 User ID: ${user._id}`);
      console.log('');
    });
    
    // Count subscription statuses
    const statusCounts = await User.aggregate([
      { $match: { isAdmin: { $ne: true } } },
      { $group: { 
          _id: "$subscriptionStatus", 
          count: { $sum: 1 } 
        } 
      }
    ]);
    
    console.log('📊 Subscription Status Summary:');
    statusCounts.forEach(status => {
      console.log(`   ${status._id || 'undefined'}: ${status.count} users`);
    });
    
    // Count subscription plans
    const planCounts = await User.aggregate([
      { $match: { isAdmin: { $ne: true } } },
      { $group: { 
          _id: "$subscriptionPlan", 
          count: { $sum: 1 } 
        } 
      }
    ]);
    
    console.log('\n📋 Subscription Plan Summary:');
    planCounts.forEach(plan => {
      console.log(`   ${plan._id || 'undefined'}: ${plan.count} users`);
    });
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Check failed:', error);
    process.exit(1);
  }
};

checkSubscriptionData();
