// Simple Payment Test for ZenoPay Integration
const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

console.log('🧪 Simple ZenoPay Payment Test\n');

// Test configuration
const testConfig = {
  serverUrl: 'http://localhost:5000',
  testUser: {
    name: 'Test User',
    email: '<EMAIL>',
    phoneNumber: '**********'
  },
  testPlan: {
    title: 'Test Plan',
    discountedPrice: 1000,
    duration: 1
  }
};

// Test 1: Check server health
async function testServerHealth() {
  try {
    console.log('🔍 Testing server health...');
    const response = await axios.get(`${testConfig.serverUrl}/api/health`, {
      timeout: 5000
    });
    
    console.log('✅ Server is running:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Server health check failed:', error.message);
    return false;
  }
}

// Test 2: Check payment endpoint
async function testPaymentEndpoint() {
  try {
    console.log('\n🔍 Testing payment endpoint...');
    
    // Create test payment data
    const paymentData = {
      plan: testConfig.testPlan,
      userId: 'test_user_id'
    };
    
    const response = await axios.post(`${testConfig.serverUrl}/api/payment/create-invoice`, paymentData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test_token' // This will fail auth but test the endpoint
      },
      timeout: 10000
    });
    
    console.log('✅ Payment endpoint response:', response.data);
    return true;
  } catch (error) {
    console.log('📝 Payment endpoint test result:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Message:', error.response.data?.message || 'No message');
      
      if (error.response.status === 401) {
        console.log('✅ Endpoint is working (authentication required as expected)');
        return true;
      }
    } else {
      console.error('❌ Payment endpoint failed:', error.message);
    }
    return false;
  }
}

// Test 3: Check webhook endpoint
async function testWebhookEndpoint() {
  try {
    console.log('\n🔍 Testing webhook endpoint...');
    
    const response = await axios.get(`${testConfig.serverUrl}/api/payment/webhook-test`, {
      timeout: 5000
    });
    
    console.log('✅ Webhook endpoint is working:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Webhook endpoint failed:', error.message);
    return false;
  }
}

// Test 4: Check environment configuration
function testEnvironmentConfig() {
  console.log('\n🔍 Testing environment configuration...');
  
  const requiredVars = [
    'ZENOPAY_ACCOUNT_ID',
    'ZENOPAY_API_KEY',
    'ZENOPAY_WEBHOOK_URL',
    'MONGO_URL',
    'JWT_SECRET'
  ];
  
  let allConfigured = true;
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value && value !== 'your_secret_key_here') {
      console.log(`✅ ${varName}: SET`);
    } else {
      console.log(`❌ ${varName}: MISSING OR PLACEHOLDER`);
      allConfigured = false;
    }
  });
  
  return allConfigured;
}

// Main test function
async function runPaymentTests() {
  console.log('🚀 Starting Payment System Tests...\n');
  
  const results = {
    serverHealth: false,
    paymentEndpoint: false,
    webhookEndpoint: false,
    environmentConfig: false
  };
  
  // Run all tests
  results.environmentConfig = testEnvironmentConfig();
  results.serverHealth = await testServerHealth();
  
  if (results.serverHealth) {
    results.paymentEndpoint = await testPaymentEndpoint();
    results.webhookEndpoint = await testWebhookEndpoint();
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('='.repeat(40));
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! Payment system is ready.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the issues above.');
    
    if (!results.serverHealth) {
      console.log('\n💡 To fix server issues:');
      console.log('1. Make sure the server is running: npm start');
      console.log('2. Check if port 5000 is available');
      console.log('3. Verify MongoDB connection');
    }
    
    if (!results.environmentConfig) {
      console.log('\n💡 To fix configuration issues:');
      console.log('1. Update placeholder values in server/.env');
      console.log('2. Get real ZenoPay credentials from your account');
      console.log('3. Restart the server after updating .env');
    }
  }
  
  console.log('\n🔗 Next steps:');
  console.log('1. Start the server: npm start (in server directory)');
  console.log('2. Start the client: npm start (in client directory)');
  console.log('3. Test payment flow in the browser');
}

// Run the tests
runPaymentTests().catch(console.error);
