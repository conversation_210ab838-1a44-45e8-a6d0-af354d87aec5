{"ast": null, "code": "import React,{useState,useEffect,useCallback,startTransition}from'react';import{use<PERSON>ara<PERSON>,useNavigate}from'react-router-dom';import{useSelector}from'react-redux';import{message}from'antd';import{Tb<PERSON><PERSON>,TbArrowLeft,TbArrowRight,Tb<PERSON>heck}from'react-icons/tb';import{getExamById}from'../../../apicalls/exams';import{addReport}from'../../../apicalls/reports';// Professional Sound System\nimport{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";const playSound=type=>{try{const audioContext=new(window.AudioContext||window.webkitAudioContext)();const createTone=function(frequency,duration){let type=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'sine';const oscillator=audioContext.createOscillator();const gainNode=audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(audioContext.destination);oscillator.frequency.setValueAtTime(frequency,audioContext.currentTime);oscillator.type=type;gainNode.gain.setValueAtTime(0,audioContext.currentTime);gainNode.gain.linearRampToValueAtTime(0.1,audioContext.currentTime+0.01);gainNode.gain.exponentialRampToValueAtTime(0.001,audioContext.currentTime+duration);oscillator.start(audioContext.currentTime);oscillator.stop(audioContext.currentTime+duration);};switch(type){case'select':// Professional click sound\ncreateTone(800,0.1,'square');break;case'navigate':// Smooth navigation sound\ncreateTone(600,0.15,'sine');setTimeout(()=>createTone(800,0.1,'sine'),50);break;case'submit':// Success sound\ncreateTone(523,0.2,'sine');// C\nsetTimeout(()=>createTone(659,0.2,'sine'),100);// E\nsetTimeout(()=>createTone(784,0.3,'sine'),200);// G\nbreak;default:createTone(600,0.1,'sine');}}catch(error){// Fallback for browsers that don't support Web Audio API\nconsole.log('Audio not supported');}};const QuizPlay=()=>{const{id}=useParams();const navigate=useNavigate();const{user}=useSelector(state=>state.user);const[loading,setLoading]=useState(true);const[submitting,setSubmitting]=useState(false);const[quiz,setQuiz]=useState(null);const[questions,setQuestions]=useState([]);const[currentQuestion,setCurrentQuestion]=useState(0);const[answers,setAnswers]=useState([]);const[timeLeft,setTimeLeft]=useState(0);const[startTime,setStartTime]=useState(null);// Load quiz data\nuseEffect(()=>{const loadQuizData=async()=>{try{setLoading(true);console.log('Loading quiz with ID:',id);if(!user||!user._id){const token=localStorage.getItem('token');if(!token){console.log('No token found, redirecting to login');message.error('Please login to access quizzes');startTransition(()=>{navigate('/login');});return;}}const response=await getExamById({examId:id});console.log('Quiz API response:',response);if(response.success){if(!response.data){message.error('Quiz data not found');startTransition(()=>{navigate('/quiz');});return;}if(!response.data.questions||response.data.questions.length===0){message.error('This quiz has no questions available');startTransition(()=>{navigate('/quiz');});return;}setQuiz(response.data);setQuestions(response.data.questions);setAnswers(new Array(response.data.questions.length).fill(''));setTimeLeft(response.data.duration*60);setStartTime(new Date());console.log('Quiz loaded successfully:',response.data);}else{console.error('Quiz API error:',response.message);message.error(response.message||'Failed to load quiz');startTransition(()=>{navigate('/quiz');});}}catch(error){console.error('Quiz loading error:',error);message.error('Failed to load quiz. Please try again.');startTransition(()=>{navigate('/quiz');});}finally{setLoading(false);}};if(id&&user){loadQuizData();}},[id,navigate,user]);// Submit quiz function\nconst handleSubmitQuiz=useCallback(async()=>{console.log('🚀 Submit button clicked - showing loading overlay');console.log('Current submitting state:',submitting);try{// Play submit sound\nplaySound('submit');// Show loading immediately\nsetSubmitting(true);console.log('✅ setSubmitting(true) called');console.log('📝 Starting quiz marking process...');let currentUser=user;if(!currentUser||!currentUser._id){const storedUser=localStorage.getItem('user');if(storedUser){try{currentUser=JSON.parse(storedUser);}catch(error){console.error('Error parsing stored user data:',error);startTransition(()=>{navigate('/login');});return;}}}if(!currentUser||!currentUser._id){message.error('User session expired. Please login again.');startTransition(()=>{navigate('/login');});return;}const endTime=new Date();const timeTaken=Math.floor((endTime-startTime)/1000);let correctAnswers=0;const resultDetails=questions.map((question,index)=>{const userAnswer=answers[index];let isCorrect=false;let actualCorrectAnswer='';// Determine the correct answer based on question type\nconst questionType=question.type||question.answerType||'mcq';if(questionType.toLowerCase()==='mcq'||questionType==='Options'){// For MCQ questions, check both correctAnswer and correctOption\nif(question.options&&typeof question.options==='object'){// If correctAnswer is a key (like \"B\"), get the actual text\nif(question.correctAnswer&&question.options[question.correctAnswer]){actualCorrectAnswer=question.options[question.correctAnswer];isCorrect=userAnswer===actualCorrectAnswer;}// If correctOption is available, use it\nelse if(question.correctOption&&question.options[question.correctOption]){actualCorrectAnswer=question.options[question.correctOption];isCorrect=userAnswer===actualCorrectAnswer;}// If correctAnswer is already the full text\nelse if(question.correctAnswer){actualCorrectAnswer=question.correctAnswer;isCorrect=userAnswer===actualCorrectAnswer;}}else{// Fallback for other option formats\nactualCorrectAnswer=question.correctAnswer||question.correctOption||'';isCorrect=userAnswer===actualCorrectAnswer;}}else{var _actualCorrectAnswer;// For fill-in-the-blank and other types, direct comparison\nactualCorrectAnswer=question.correctAnswer||'';isCorrect=(userAnswer===null||userAnswer===void 0?void 0:userAnswer.toLowerCase().trim())===((_actualCorrectAnswer=actualCorrectAnswer)===null||_actualCorrectAnswer===void 0?void 0:_actualCorrectAnswer.toLowerCase().trim());}if(isCorrect)correctAnswers++;return{questionId:question._id||\"question_\".concat(index),questionName:typeof question.name==='string'?question.name:\"Question \".concat(index+1),questionText:question.name||\"Question \".concat(index+1),userAnswer:typeof userAnswer==='string'?userAnswer:String(userAnswer||''),correctAnswer:actualCorrectAnswer,isCorrect,questionType:questionType,options:question.options||null,questionImage:question.image||question.questionImage||question.imageUrl||null,image:question.image||question.questionImage||question.imageUrl||null};});const percentage=Math.round(correctAnswers/questions.length*100);// Use the exam's actual passing marks instead of hardcoded 60%\nconst passingPercentage=quiz.passingMarks||quiz.passingPercentage||60;const verdict=percentage>=passingPercentage?'Pass':'Fail';const reportData={exam:id,user:currentUser._id,result:{correctAnswers,wrongAnswers:questions.length-correctAnswers,percentage,score:percentage,verdict:verdict,timeTaken,timeSpent:timeTaken,// Add timeSpent for XP calculation\npoints:correctAnswers*10,totalQuestions:questions.length}};try{const response=await addReport(reportData);if(response.success){console.log('✅ Quiz submitted successfully, preparing results...');// Include XP data in navigation state\nconst navigationState={percentage,correctAnswers,totalQuestions:questions.length,timeTaken,resultDetails,xpData:response.xpData||null,// Include XP data from server response\nquizName:quiz.name,quizSubject:quiz.subject||quiz.category,passingPercentage:passingPercentage,// Include actual passing marks\nverdict:verdict// Include calculated verdict\n};// Brief delay to show loading screen\nawait new Promise(resolve=>setTimeout(resolve,1000));console.log('🎯 Navigating to results page...');startTransition(()=>{navigate(\"/quiz/\".concat(id,\"/result\"),{state:navigationState});});}else{console.error('❌ Quiz submission failed:',response.message);// Show error in the loading overlay instead of notification\nsetTimeout(()=>{setSubmitting(false);message.error(response.message||'Failed to submit quiz');},1000);return;}}catch(apiError){console.error('❌ API Error during submission:',apiError);// Show error in the loading overlay instead of notification\nsetTimeout(()=>{setSubmitting(false);message.error('Network error while submitting quiz');},1000);return;}}catch(error){console.error('Quiz submission error:',error);// Show error in the loading overlay instead of notification\nsetTimeout(()=>{setSubmitting(false);message.error('Failed to submit quiz');},1000);return;}finally{setSubmitting(false);}},[startTime,questions,answers,id,navigate,user]);// Timer countdown\nuseEffect(()=>{if(timeLeft<=0){// Don't auto-submit, just stop the timer\nreturn;}const timer=setInterval(()=>{setTimeLeft(prev=>prev-1);},1000);return()=>clearInterval(timer);},[timeLeft]);// Handle answer selection\nconst handleAnswerSelect=answer=>{const newAnswers=[...answers];newAnswers[currentQuestion]=answer;setAnswers(newAnswers);};// Navigation functions\nconst goToNext=()=>{if(currentQuestion<questions.length-1){playSound('navigate');setCurrentQuestion(currentQuestion+1);}};const goToPrevious=()=>{if(currentQuestion>0){playSound('navigate');setCurrentQuestion(currentQuestion-1);}};// Format time display\nconst formatTime=seconds=>{const minutes=Math.floor(seconds/60);const remainingSeconds=seconds%60;return\"\".concat(minutes,\":\").concat(remainingSeconds.toString().padStart(2,'0'));};// Render different answer sections based on question type\nconst renderAnswerSection=()=>{const questionType=currentQ.type||currentQ.answerType||'mcq';switch(questionType.toLowerCase()){case'mcq':case'multiple-choice':case'multiplechoice':return renderMultipleChoice();case'fill':case'fill-in-the-blank':case'fillblank':case'text':return renderFillInTheBlank();case'image':case'diagram':return renderImageQuestion();default:// Default to multiple choice if type is unclear\nreturn renderMultipleChoice();}};// Render multiple choice options\nconst renderMultipleChoice=()=>{let options=[];// Handle different option formats\nif(Array.isArray(currentQ.options)){options=currentQ.options;}else if(currentQ.options&&typeof currentQ.options==='object'){// Handle object format like {A: \"option1\", B: \"option2\"}\noptions=Object.values(currentQ.options);}else if(currentQ.option1&&currentQ.option2){// Handle individual option properties\noptions=[currentQ.option1,currentQ.option2,currentQ.option3,currentQ.option4].filter(Boolean);}if(!options||options.length===0){// Show debug info and fallback options for testing\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-yellow-800 font-medium\",children:\"No options found for this question\"}),/*#__PURE__*/_jsxs(\"details\",{className:\"mt-2\",children:[/*#__PURE__*/_jsx(\"summary\",{className:\"text-sm text-yellow-600 cursor-pointer\",children:\"Show question data\"}),/*#__PURE__*/_jsx(\"pre\",{className:\"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\",children:JSON.stringify(currentQ,null,2)})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:['Option A (Test)','Option B (Test)','Option C (Test)','Option D (Test)'].map((option,index)=>{const optionLetter=String.fromCharCode(65+index);const isSelected=answers[currentQuestion]===option;return/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleAnswerSelect(option),className:\"w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md \".concat(isSelected?'border-blue-500 bg-blue-50 text-blue-900 shadow-md':'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 \".concat(isSelected?'bg-blue-500 text-white':'bg-gray-100 text-gray-600'),children:optionLetter}),/*#__PURE__*/_jsx(\"span\",{className:\"text-lg leading-relaxed flex-1 text-left text-gray-900\",children:option})]})},index);})})]});}return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:options.map((option,index)=>{const optionLetter=String.fromCharCode(65+index);const isSelected=answers[currentQuestion]===option;return/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleAnswerSelect(option),className:\"w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md \".concat(isSelected?'border-blue-500 bg-blue-50 text-blue-900 shadow-md':'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 \".concat(isSelected?'bg-blue-500 text-white':'bg-gray-100 text-gray-600'),children:optionLetter}),/*#__PURE__*/_jsx(\"span\",{className:\"text-lg leading-relaxed flex-1 text-left text-gray-900\",children:typeof option==='string'?option:JSON.stringify(option)})]})},index);})});};// Render fill in the blank input\nconst renderFillInTheBlank=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-blue-50 border border-blue-200 rounded-lg p-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-800 text-sm font-medium mb-2\",children:\"Fill in the blank:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700\",children:\"Type your answer in the box below\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"relative\",children:/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:answers[currentQuestion]||'',onChange:e=>handleAnswerSelect(e.target.value),placeholder:\"Type your answer here...\",className:\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\",autoFocus:true})})]});};// Render image/diagram question (could have options or be fill-in)\nconst renderImageQuestion=()=>{if(currentQ.options&&Array.isArray(currentQ.options)&&currentQ.options.length>0){return renderMultipleChoice();}else{return renderFillInTheBlank();}};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 font-medium\",children:\"Loading quiz...\"})]})});}if(!quiz||!questions.length){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-gray-900 mb-4\",children:\"No Questions Available\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"This quiz doesn't have any questions yet.\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>startTransition(()=>navigate('/quiz')),className:\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",children:\"Back to Quizzes\"})]})})});}// Safety check for current question\nif(!questions[currentQuestion]){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-gray-900 mb-4\",children:\"Question Not Found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"Unable to load the current question.\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>startTransition(()=>navigate('/quiz')),className:\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",children:\"Back to Quizzes\"})]})})});}const currentQ=questions[currentQuestion];const isLastQuestion=currentQuestion===questions.length-1;// Ensure currentQ is a valid object\nif(!currentQ||typeof currentQ!=='object'){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-bold text-gray-900 mb-4\",children:\"Invalid Question Data\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 mb-6\",children:\"The question data is corrupted or invalid.\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>startTransition(()=>navigate('/quiz')),className:\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",children:\"Back to Quizzes\"})]})})});}// Show enhanced loading screen when submitting\nif(submitting){return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"style\",{children:\"\\n          @keyframes professionalSpin {\\n            0% { transform: rotate(0deg) scale(1); }\\n            50% { transform: rotate(180deg) scale(1.1); }\\n            100% { transform: rotate(360deg) scale(1); }\\n          }\\n          @keyframes elegantPulse {\\n            0%, 100% { opacity: 1; transform: scale(1); }\\n            50% { opacity: 0.7; transform: scale(1.05); }\\n          }\\n          @keyframes smoothBounce {\\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\\n            40% { transform: translateY(-8px) scale(1.2); }\\n            60% { transform: translateY(-4px) scale(1.1); }\\n          }\\n          @keyframes gradientShift {\\n            0% { background-position: 0% 50%; }\\n            50% { background-position: 100% 50%; }\\n            100% { background-position: 0% 50%; }\\n          }\\n          @keyframes fadeInUp {\\n            0% { opacity: 0; transform: translateY(20px); }\\n            100% { opacity: 1; transform: translateY(0); }\\n          }\\n          @keyframes orbitalSpin {\\n            0% { transform: rotate(0deg); }\\n            100% { transform: rotate(360deg); }\\n          }\\n          .professional-dot {\\n            animation: smoothBounce 1.6s infinite ease-in-out both;\\n          }\\n          .professional-dot:nth-child(1) { animation-delay: -0.32s; }\\n          .professional-dot:nth-child(2) { animation-delay: -0.16s; }\\n          .professional-dot:nth-child(3) { animation-delay: 0s; }\\n          .professional-dot:nth-child(4) { animation-delay: 0.16s; }\\n          .gradient-bg {\\n            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);\\n            background-size: 400% 400%;\\n            animation: gradientShift 4s ease infinite;\\n          }\\n        \"}),/*#__PURE__*/_jsx(\"div\",{className:\"gradient-bg\",style:{position:'fixed',top:0,left:0,width:'100%',height:'100%',display:'flex',alignItems:'center',justifyContent:'center',zIndex:9999},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(255, 255, 255, 0.98)',borderRadius:'24px',padding:window.innerWidth<=768?'32px 24px':'48px 40px',textAlign:'center',boxShadow:'0 32px 64px rgba(0, 0, 0, 0.2), 0 16px 32px rgba(0, 0, 0, 0.1)',backdropFilter:'blur(20px)',border:'1px solid rgba(255, 255, 255, 0.3)',maxWidth:window.innerWidth<=768?'320px':'450px',width:'90%',animation:'fadeInUp 0.6s ease-out'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{width:window.innerWidth<=768?'100px':'120px',height:window.innerWidth<=768?'100px':'120px',margin:'0 auto 32px auto',background:'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',animation:'professionalSpin 3s ease-in-out infinite',boxShadow:'0 16px 40px rgba(102, 126, 234, 0.4), 0 8px 16px rgba(118, 75, 162, 0.3)',position:'relative'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:window.innerWidth<=768?'50px':'60px',height:window.innerWidth<=768?'50px':'60px',background:'white',borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 4px 12px rgba(0, 0, 0, 0.1)'},children:/*#__PURE__*/_jsx(\"span\",{style:{fontSize:window.innerWidth<=768?'24px':'28px',filter:'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'},children:\"\\uD83C\\uDFAF\"})}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',width:'140%',height:'140%',border:'2px solid rgba(255, 255, 255, 0.3)',borderRadius:'50%',animation:'orbitalSpin 4s linear infinite reverse'}})]}),/*#__PURE__*/_jsx(\"h2\",{style:{fontSize:window.innerWidth<=768?'24px':'32px',fontWeight:'700',background:'linear-gradient(135deg, #667eea, #764ba2)',WebkitBackgroundClip:'text',WebkitTextFillColor:'transparent',backgroundClip:'text',margin:'0 0 12px 0',animation:'elegantPulse 2.5s infinite',letterSpacing:'-0.5px'},children:\"Evaluating Your Quiz\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontSize:window.innerWidth<=768?'14px':'16px',color:'#64748b',margin:'0 0 32px 0',lineHeight:'1.6',fontWeight:'500'},children:\"Our advanced system is carefully reviewing your answers\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',alignItems:'center',gap:'6px',marginBottom:'16px'},children:[1,2,3,4].map(i=>/*#__PURE__*/_jsx(\"div\",{className:\"professional-dot\",style:{width:'10px',height:'10px',background:'linear-gradient(135deg, #667eea, #764ba2)',borderRadius:'50%',boxShadow:'0 2px 8px rgba(102, 126, 234, 0.4)'}},i))}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:window.innerWidth<=768?'12px':'14px',color:'#94a3b8',fontWeight:'500',textTransform:'uppercase',letterSpacing:'1px'},children:\"Processing...\"})]})})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative\",style:{padding:window.innerWidth<=768?'8px':'16px'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow-sm border-b border-gray-200 rounded-lg\",style:{marginBottom:window.innerWidth<=768?'12px':'24px'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"mx-auto\",style:{maxWidth:window.innerWidth<=768?'100%':window.innerWidth<=1024?'90%':'1200px',padding:window.innerWidth<=768?'12px':'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"font-bold text-gray-900 mb-2\",style:{fontSize:window.innerWidth<=768?'18px':window.innerWidth<=1024?'24px':'28px'},children:quiz.name}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600 mb-4\",style:{fontSize:window.innerWidth<=768?'14px':'16px'},children:[\"Question \",currentQuestion+1,\" of \",questions.length]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 rounded-lg \".concat(timeLeft<=300?'bg-red-100 text-red-700':'bg-blue-100 text-blue-700'),style:{padding:window.innerWidth<=768?'8px 12px':'12px 16px'},children:[/*#__PURE__*/_jsx(TbClock,{style:{width:window.innerWidth<=768?'16px':'20px',height:window.innerWidth<=768?'16px':'20px'}}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",style:{fontSize:window.innerWidth<=768?'14px':'16px'},children:formatTime(timeLeft)})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600 font-medium\",style:{fontSize:window.innerWidth<=768?'12px':'14px'},children:\"Progress\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-blue-600 font-bold\",style:{fontSize:window.innerWidth<=768?'12px':'14px'},children:[Math.round((currentQuestion+1)/questions.length*100),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:window.innerWidth<=768?'8px':'10px',backgroundColor:'#e5e7eb',borderRadius:'9999px',overflow:'hidden',boxShadow:'inset 0 2px 4px rgba(0,0,0,0.1)'},children:/*#__PURE__*/_jsx(\"div\",{style:{width:\"\".concat((currentQuestion+1)/questions.length*100,\"%\"),height:'100%',backgroundColor:'#2563eb',borderRadius:'9999px',transition:'width 0.5s ease-out',boxShadow:'0 2px 4px rgba(37, 99, 235, 0.4)'}})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-auto\",style:{maxWidth:window.innerWidth<=768?'100%':window.innerWidth<=1024?'90%':'1000px',padding:window.innerWidth<=768?'0 8px':'0 16px'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-xl border border-gray-200 transition-all duration-300\",style:{padding:window.innerWidth<=768?'16px':window.innerWidth<=1024?'24px':'32px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:window.innerWidth<=768?'16px':'32px'},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"font-bold text-gray-900 text-center\",style:{fontSize:window.innerWidth<=768?'18px':window.innerWidth<=1024?'24px':'28px',marginBottom:window.innerWidth<=768?'12px':'16px'},children:typeof currentQ.name==='string'?currentQ.name:'Question'}),currentQ.image&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 rounded-lg\",style:{marginBottom:window.innerWidth<=768?'16px':'24px',padding:window.innerWidth<=768?'12px':'16px'},children:/*#__PURE__*/_jsx(\"img\",{src:currentQ.image,alt:\"Question diagram\",className:\"max-w-full h-auto rounded-lg shadow-lg mx-auto block\",style:{maxHeight:window.innerWidth<=768?'250px':'400px'},onError:e=>{e.target.style.display='none';// Show fallback message\nconst fallback=document.createElement('div');fallback.className='text-center py-8 text-gray-500';fallback.innerHTML='<p>Could not load diagram</p>';e.target.parentNode.appendChild(fallback);}})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",style:{marginBottom:window.innerWidth<=768?'16px':'32px'},children:renderAnswerSection()}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",style:{flexDirection:window.innerWidth<=768?'column':'row',justifyContent:window.innerWidth<=768?'center':'space-between',gap:window.innerWidth<=768?'12px':'0'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:goToPrevious,disabled:currentQuestion===0,className:\"flex items-center gap-2 rounded-lg font-semibold transition-colors \".concat(currentQuestion===0?'bg-gray-100 text-gray-400 cursor-not-allowed':'bg-gray-200 text-gray-700 hover:bg-gray-300'),style:{padding:window.innerWidth<=768?'10px 16px':'12px 24px',fontSize:window.innerWidth<=768?'14px':'16px',width:window.innerWidth<=768?'100%':'auto',justifyContent:'center'},children:[/*#__PURE__*/_jsx(TbArrowLeft,{style:{width:window.innerWidth<=768?'16px':'20px',height:window.innerWidth<=768?'16px':'20px'}}),\"Previous\"]}),isLastQuestion?/*#__PURE__*/_jsx(\"button\",{onClick:handleSubmitQuiz,disabled:submitting,className:\"flex items-center gap-2 rounded-lg font-semibold transition-colors \".concat(submitting?'bg-gray-400 text-gray-200 cursor-not-allowed':'bg-green-600 text-white hover:bg-green-700'),style:{padding:window.innerWidth<=768?'10px 16px':'12px 32px',fontSize:window.innerWidth<=768?'14px':'16px',width:window.innerWidth<=768?'100%':'auto',justifyContent:'center'},children:submitting?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full border-2 border-white border-t-transparent\",style:{width:window.innerWidth<=768?'16px':'20px',height:window.innerWidth<=768?'16px':'20px'}}),\"Submitting...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TbCheck,{style:{width:window.innerWidth<=768?'16px':'20px',height:window.innerWidth<=768?'16px':'20px'}}),\"Submit Quiz\"]})}):/*#__PURE__*/_jsxs(\"button\",{onClick:goToNext,className:\"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",style:{padding:window.innerWidth<=768?'10px 16px':'12px 24px',fontSize:window.innerWidth<=768?'14px':'16px',width:window.innerWidth<=768?'100%':'auto',justifyContent:'center'},children:[\"Next\",/*#__PURE__*/_jsx(TbArrowRight,{style:{width:window.innerWidth<=768?'16px':'20px',height:window.innerWidth<=768?'16px':'20px'}})]})]})]})})]});};export default QuizPlay;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "message", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "playSound", "type", "audioContext", "window", "AudioContext", "webkitAudioContext", "createTone", "frequency", "duration", "arguments", "length", "undefined", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "currentTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "setTimeout", "error", "console", "log", "QuizPlay", "id", "navigate", "user", "state", "loading", "setLoading", "submitting", "setSubmitting", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "_id", "token", "localStorage", "getItem", "response", "examId", "success", "data", "Array", "fill", "Date", "handleSubmitQuiz", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "actualCorrectAnswer", "questionType", "answerType", "toLowerCase", "options", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "_actualCorrectAnswer", "trim", "questionId", "concat", "questionName", "name", "questionText", "String", "questionImage", "image", "imageUrl", "percentage", "round", "passingPercentage", "passingMarks", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "timeSpent", "points", "totalQuestions", "navigationState", "xpData", "quizName", "quizSubject", "subject", "category", "Promise", "resolve", "apiError", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "renderAnswerSection", "currentQ", "renderMultipleChoice", "renderFillInTheBlank", "renderImageQuestion", "isArray", "Object", "values", "option1", "option2", "option3", "option4", "filter", "Boolean", "className", "children", "stringify", "option", "optionLetter", "fromCharCode", "isSelected", "onClick", "value", "onChange", "e", "target", "placeholder", "autoFocus", "isLastQuestion", "style", "position", "top", "left", "width", "height", "display", "alignItems", "justifyContent", "zIndex", "background", "borderRadius", "padding", "innerWidth", "textAlign", "boxShadow", "<PERSON><PERSON>ilter", "border", "max<PERSON><PERSON><PERSON>", "animation", "margin", "fontSize", "fontWeight", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "letterSpacing", "color", "lineHeight", "gap", "marginBottom", "i", "textTransform", "backgroundColor", "overflow", "transition", "src", "alt", "maxHeight", "onError", "fallback", "document", "createElement", "innerHTML", "parentNode", "append<PERSON><PERSON><PERSON>", "flexDirection", "disabled"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { usePara<PERSON>, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { \n  Tb<PERSON><PERSON>, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\n// Professional Sound System\nconst playSound = (type) => {\n  try {\n    const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n    const createTone = (frequency, duration, type = 'sine') => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n      oscillator.type = type;\n\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);\n      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + duration);\n    };\n\n    switch(type) {\n      case 'select':\n        // Professional click sound\n        createTone(800, 0.1, 'square');\n        break;\n      case 'navigate':\n        // Smooth navigation sound\n        createTone(600, 0.15, 'sine');\n        setTimeout(() => createTone(800, 0.1, 'sine'), 50);\n        break;\n      case 'submit':\n        // Success sound\n        createTone(523, 0.2, 'sine'); // C\n        setTimeout(() => createTone(659, 0.2, 'sine'), 100); // E\n        setTimeout(() => createTone(784, 0.3, 'sine'), 200); // G\n        break;\n      default:\n        createTone(600, 0.1, 'sine');\n    }\n  } catch (error) {\n    // Fallback for browsers that don't support Web Audio API\n    console.log('Audio not supported');\n  }\n};\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        \n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n\n    try {\n      // Play submit sound\n      playSound('submit');\n\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n      console.log('📝 Starting quiz marking process...');\n\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = userAnswer?.toLowerCase().trim() === actualCorrectAnswer?.toLowerCase().trim();\n        }\n\n        if (isCorrect) correctAnswers++;\n\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null,\n          questionImage: question.image || question.questionImage || question.imageUrl || null,\n          image: question.image || question.questionImage || question.imageUrl || null\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken, // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n\n      try {\n        const response = await addReport(reportData);\n\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null, // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage, // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          // Brief delay to show loading screen\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Show error in the loading overlay instead of notification\n          setTimeout(() => {\n            setSubmitting(false);\n            message.error(response.message || 'Failed to submit quiz');\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Show error in the loading overlay instead of notification\n        setTimeout(() => {\n          setSubmitting(false);\n          message.error('Network error while submitting quiz');\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Show error in the loading overlay instead of notification\n      setTimeout(() => {\n        setSubmitting(false);\n        message.error('Failed to submit quiz');\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n\n\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return (\n        <div className=\"space-y-4\">\n          <div className=\"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <p className=\"text-yellow-800 font-medium\">No options found for this question</p>\n            <details className=\"mt-2\">\n              <summary className=\"text-sm text-yellow-600 cursor-pointer\">Show question data</summary>\n              <pre className=\"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\">\n                {JSON.stringify(currentQ, null, 2)}\n              </pre>\n            </details>\n          </div>\n\n          {/* Fallback test options */}\n          <div className=\"space-y-3\">\n            {['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion] === option;\n\n              return (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                >\n                  <div className=\"flex items-start gap-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                      {option}\n                    </span>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index);\n          const isSelected = answers[currentQuestion] === option;\n\n          return (\n            <button\n              key={index}\n              onClick={() => handleAnswerSelect(option)}\n              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {optionLetter}\n                </div>\n                <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                  {typeof option === 'string' ? option : JSON.stringify(option)}\n                </span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <p className=\"text-blue-800 text-sm font-medium mb-2\">Fill in the blank:</p>\n          <p className=\"text-gray-700\">Type your answer in the box below</p>\n        </div>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={answers[currentQuestion] || ''}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\"\n            autoFocus\n          />\n        </div>\n      </div>\n    );\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Question Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">Unable to load the current question.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Invalid Question Data</h2>\n            <p className=\"text-gray-600 mb-6\">The question data is corrupted or invalid.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  // Show enhanced loading screen when submitting\n  if (submitting) {\n    return (\n      <>\n        <style>{`\n          @keyframes professionalSpin {\n            0% { transform: rotate(0deg) scale(1); }\n            50% { transform: rotate(180deg) scale(1.1); }\n            100% { transform: rotate(360deg) scale(1); }\n          }\n          @keyframes elegantPulse {\n            0%, 100% { opacity: 1; transform: scale(1); }\n            50% { opacity: 0.7; transform: scale(1.05); }\n          }\n          @keyframes smoothBounce {\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n            40% { transform: translateY(-8px) scale(1.2); }\n            60% { transform: translateY(-4px) scale(1.1); }\n          }\n          @keyframes gradientShift {\n            0% { background-position: 0% 50%; }\n            50% { background-position: 100% 50%; }\n            100% { background-position: 0% 50%; }\n          }\n          @keyframes fadeInUp {\n            0% { opacity: 0; transform: translateY(20px); }\n            100% { opacity: 1; transform: translateY(0); }\n          }\n          @keyframes orbitalSpin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n          .professional-dot {\n            animation: smoothBounce 1.6s infinite ease-in-out both;\n          }\n          .professional-dot:nth-child(1) { animation-delay: -0.32s; }\n          .professional-dot:nth-child(2) { animation-delay: -0.16s; }\n          .professional-dot:nth-child(3) { animation-delay: 0s; }\n          .professional-dot:nth-child(4) { animation-delay: 0.16s; }\n          .gradient-bg {\n            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);\n            background-size: 400% 400%;\n            animation: gradientShift 4s ease infinite;\n          }\n        `}</style>\n        <div className=\"gradient-bg\" style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999\n        }}>\n          <div style={{\n            background: 'rgba(255, 255, 255, 0.98)',\n            borderRadius: '24px',\n            padding: window.innerWidth <= 768 ? '32px 24px' : '48px 40px',\n            textAlign: 'center',\n            boxShadow: '0 32px 64px rgba(0, 0, 0, 0.2), 0 16px 32px rgba(0, 0, 0, 0.1)',\n            backdropFilter: 'blur(20px)',\n            border: '1px solid rgba(255, 255, 255, 0.3)',\n            maxWidth: window.innerWidth <= 768 ? '320px' : '450px',\n            width: '90%',\n            animation: 'fadeInUp 0.6s ease-out'\n          }}>\n            {/* Professional Animated Icon */}\n            <div style={{\n              width: window.innerWidth <= 768 ? '100px' : '120px',\n              height: window.innerWidth <= 768 ? '100px' : '120px',\n              margin: '0 auto 32px auto',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              animation: 'professionalSpin 3s ease-in-out infinite',\n              boxShadow: '0 16px 40px rgba(102, 126, 234, 0.4), 0 8px 16px rgba(118, 75, 162, 0.3)',\n              position: 'relative'\n            }}>\n              <div style={{\n                width: window.innerWidth <= 768 ? '50px' : '60px',\n                height: window.innerWidth <= 768 ? '50px' : '60px',\n                background: 'white',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }}>\n                <span style={{\n                  fontSize: window.innerWidth <= 768 ? '24px' : '28px',\n                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                }}>🎯</span>\n              </div>\n              {/* Orbital rings */}\n              <div style={{\n                position: 'absolute',\n                width: '140%',\n                height: '140%',\n                border: '2px solid rgba(255, 255, 255, 0.3)',\n                borderRadius: '50%',\n                animation: 'orbitalSpin 4s linear infinite reverse'\n              }}></div>\n            </div>\n\n            {/* Enhanced Main Message */}\n            <h2 style={{\n              fontSize: window.innerWidth <= 768 ? '24px' : '32px',\n              fontWeight: '700',\n              background: 'linear-gradient(135deg, #667eea, #764ba2)',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              backgroundClip: 'text',\n              margin: '0 0 12px 0',\n              animation: 'elegantPulse 2.5s infinite',\n              letterSpacing: '-0.5px'\n            }}>Evaluating Your Quiz</h2>\n\n            {/* Professional Sub Message */}\n            <p style={{\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              color: '#64748b',\n              margin: '0 0 32px 0',\n              lineHeight: '1.6',\n              fontWeight: '500'\n            }}>Our advanced system is carefully reviewing your answers</p>\n\n            {/* Enhanced Progress Indicator */}\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              gap: '6px',\n              marginBottom: '16px'\n            }}>\n              {[1, 2, 3, 4].map(i => (\n                <div\n                  key={i}\n                  className=\"professional-dot\"\n                  style={{\n                    width: '10px',\n                    height: '10px',\n                    background: 'linear-gradient(135deg, #667eea, #764ba2)',\n                    borderRadius: '50%',\n                    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.4)'\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Progress Text */}\n            <div style={{\n              fontSize: window.innerWidth <= 768 ? '12px' : '14px',\n              color: '#94a3b8',\n              fontWeight: '500',\n              textTransform: 'uppercase',\n              letterSpacing: '1px'\n            }}>\n              Processing...\n            </div>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  return (\n    <div\n      className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative\"\n      style={{\n        padding: window.innerWidth <= 768 ? '8px' : '16px'\n      }}\n    >\n      {/* Header */}\n      <div\n        className=\"bg-white shadow-sm border-b border-gray-200 rounded-lg\"\n        style={{\n          marginBottom: window.innerWidth <= 768 ? '12px' : '24px'\n        }}\n      >\n        <div\n          className=\"mx-auto\"\n          style={{\n            maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '1200px',\n            padding: window.innerWidth <= 768 ? '12px' : '16px'\n          }}\n        >\n          <div className=\"text-center\">\n            {/* Centered Title and Question Counter */}\n            <div>\n              <h1\n                className=\"font-bold text-gray-900 mb-2\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '18px' : window.innerWidth <= 1024 ? '24px' : '28px'\n                }}\n              >\n                {quiz.name}\n              </h1>\n              <p\n                className=\"text-gray-600 mb-4\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                }}\n              >\n                Question {currentQuestion + 1} of {questions.length}\n              </p>\n            </div>\n\n            {/* Timer - Centered */}\n            <div className=\"flex justify-center mb-4\">\n              <div className={`flex items-center gap-2 rounded-lg ${\n                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}\n              style={{\n                padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px'\n              }}>\n                <TbClock\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n                <span\n                  className=\"font-semibold\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                  }}\n                >\n                  {formatTime(timeLeft)}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Progress bar */}\n          <div className=\"mb-2\">\n            <div className=\"flex justify-between items-center mb-1\">\n              <span\n                className=\"text-gray-600 font-medium\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n                }}\n              >\n                Progress\n              </span>\n              <span\n                className=\"text-blue-600 font-bold\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n                }}\n              >\n                {Math.round(((currentQuestion + 1) / questions.length) * 100)}%\n              </span>\n            </div>\n            <div\n              style={{\n                width: '100%',\n                height: window.innerWidth <= 768 ? '8px' : '10px',\n                backgroundColor: '#e5e7eb',\n                borderRadius: '9999px',\n                overflow: 'hidden',\n                boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.1)'\n              }}\n            >\n              <div\n                style={{\n                  width: `${((currentQuestion + 1) / questions.length) * 100}%`,\n                  height: '100%',\n                  backgroundColor: '#2563eb',\n                  borderRadius: '9999px',\n                  transition: 'width 0.5s ease-out',\n                  boxShadow: '0 2px 4px rgba(37, 99, 235, 0.4)'\n                }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div\n        className=\"mx-auto\"\n        style={{\n          maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '1000px',\n          padding: window.innerWidth <= 768 ? '0 8px' : '0 16px'\n        }}\n      >\n        <div\n          className=\"bg-white rounded-2xl shadow-xl border border-gray-200 transition-all duration-300\"\n          style={{\n            padding: window.innerWidth <= 768 ? '16px' : window.innerWidth <= 1024 ? '24px' : '32px'\n          }}\n        >\n          {/* Question */}\n          <div\n            style={{\n              marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n            }}\n          >\n            <h2\n              className=\"font-bold text-gray-900 text-center\"\n              style={{\n                fontSize: window.innerWidth <= 768 ? '18px' : window.innerWidth <= 1024 ? '24px' : '28px',\n                marginBottom: window.innerWidth <= 768 ? '12px' : '16px'\n              }}\n            >\n              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}\n            </h2>\n\n            {currentQ.image && (\n              <div\n                className=\"bg-gray-50 rounded-lg\"\n                style={{\n                  marginBottom: window.innerWidth <= 768 ? '16px' : '24px',\n                  padding: window.innerWidth <= 768 ? '12px' : '16px'\n                }}\n              >\n                <img\n                  src={currentQ.image}\n                  alt=\"Question diagram\"\n                  className=\"max-w-full h-auto rounded-lg shadow-lg mx-auto block\"\n                  style={{\n                    maxHeight: window.innerWidth <= 768 ? '250px' : '400px'\n                  }}\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    // Show fallback message\n                    const fallback = document.createElement('div');\n                    fallback.className = 'text-center py-8 text-gray-500';\n                    fallback.innerHTML = '<p>Could not load diagram</p>';\n                    e.target.parentNode.appendChild(fallback);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Section - Different types based on question type */}\n          <div\n            className=\"space-y-4\"\n            style={{\n              marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n            }}\n          >\n            {renderAnswerSection()}\n          </div>\n\n          {/* Navigation */}\n          <div\n            className=\"flex items-center\"\n            style={{\n              flexDirection: window.innerWidth <= 768 ? 'column' : 'row',\n              justifyContent: window.innerWidth <= 768 ? 'center' : 'space-between',\n              gap: window.innerWidth <= 768 ? '12px' : '0'\n            }}\n          >\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n              style={{\n                padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                width: window.innerWidth <= 768 ? '100%' : 'auto',\n                justifyContent: 'center'\n              }}\n            >\n              <TbArrowLeft\n                style={{\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }}\n              />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                disabled={submitting}\n                className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                  submitting\n                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                }`}\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 32px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                {submitting ? (\n                  <>\n                    <div\n                      className=\"animate-spin rounded-full border-2 border-white border-t-transparent\"\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    ></div>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <TbCheck\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    />\n                    Submit Quiz\n                  </>\n                )}\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                Next\n                <TbArrowRight\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,eAAe,KAAQ,OAAO,CAChF,OAASC,SAAS,CAAEC,WAAW,KAAQ,kBAAkB,CACzD,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,OAAO,KAAQ,MAAM,CAC9B,OACEC,OAAO,CACPC,WAAW,CACXC,YAAY,CACZC,OAAO,KACF,gBAAgB,CACvB,OAASC,WAAW,KAAQ,yBAAyB,CACrD,OAASC,SAAS,KAAQ,2BAA2B,CAErD;AAAA,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,gCAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,SAAS,CAAIC,IAAI,EAAK,CAC1B,GAAI,CACF,KAAM,CAAAC,YAAY,CAAG,IAAKC,MAAM,CAACC,YAAY,EAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC,CAE7E,KAAM,CAAAC,UAAU,CAAG,QAAAA,CAACC,SAAS,CAAEC,QAAQ,CAAoB,IAAlB,CAAAP,IAAI,CAAAQ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,MAAM,CACpD,KAAM,CAAAG,UAAU,CAAGV,YAAY,CAACW,gBAAgB,CAAC,CAAC,CAClD,KAAM,CAAAC,QAAQ,CAAGZ,YAAY,CAACa,UAAU,CAAC,CAAC,CAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAACd,YAAY,CAACe,WAAW,CAAC,CAE1CL,UAAU,CAACL,SAAS,CAACW,cAAc,CAACX,SAAS,CAAEL,YAAY,CAACiB,WAAW,CAAC,CACxEP,UAAU,CAACX,IAAI,CAAGA,IAAI,CAEtBa,QAAQ,CAACM,IAAI,CAACF,cAAc,CAAC,CAAC,CAAEhB,YAAY,CAACiB,WAAW,CAAC,CACzDL,QAAQ,CAACM,IAAI,CAACC,uBAAuB,CAAC,GAAG,CAAEnB,YAAY,CAACiB,WAAW,CAAG,IAAI,CAAC,CAC3EL,QAAQ,CAACM,IAAI,CAACE,4BAA4B,CAAC,KAAK,CAAEpB,YAAY,CAACiB,WAAW,CAAGX,QAAQ,CAAC,CAEtFI,UAAU,CAACW,KAAK,CAACrB,YAAY,CAACiB,WAAW,CAAC,CAC1CP,UAAU,CAACY,IAAI,CAACtB,YAAY,CAACiB,WAAW,CAAGX,QAAQ,CAAC,CACtD,CAAC,CAED,OAAOP,IAAI,EACT,IAAK,QAAQ,CACX;AACAK,UAAU,CAAC,GAAG,CAAE,GAAG,CAAE,QAAQ,CAAC,CAC9B,MACF,IAAK,UAAU,CACb;AACAA,UAAU,CAAC,GAAG,CAAE,IAAI,CAAE,MAAM,CAAC,CAC7BmB,UAAU,CAAC,IAAMnB,UAAU,CAAC,GAAG,CAAE,GAAG,CAAE,MAAM,CAAC,CAAE,EAAE,CAAC,CAClD,MACF,IAAK,QAAQ,CACX;AACAA,UAAU,CAAC,GAAG,CAAE,GAAG,CAAE,MAAM,CAAC,CAAE;AAC9BmB,UAAU,CAAC,IAAMnB,UAAU,CAAC,GAAG,CAAE,GAAG,CAAE,MAAM,CAAC,CAAE,GAAG,CAAC,CAAE;AACrDmB,UAAU,CAAC,IAAMnB,UAAU,CAAC,GAAG,CAAE,GAAG,CAAE,MAAM,CAAC,CAAE,GAAG,CAAC,CAAE;AACrD,MACF,QACEA,UAAU,CAAC,GAAG,CAAE,GAAG,CAAE,MAAM,CAAC,CAChC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACd;AACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CACpC,CACF,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,EAAG,CAAC,CAAG9C,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAA+C,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE+C,IAAK,CAAC,CAAG9C,WAAW,CAAE+C,KAAK,EAAKA,KAAK,CAACD,IAAI,CAAC,CAEnD,KAAM,CAACE,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwD,UAAU,CAAEC,aAAa,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC0D,IAAI,CAAEC,OAAO,CAAC,CAAG3D,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8D,eAAe,CAAEC,kBAAkB,CAAC,CAAG/D,QAAQ,CAAC,CAAC,CAAC,CACzD,KAAM,CAACgE,OAAO,CAAEC,UAAU,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkE,QAAQ,CAAEC,WAAW,CAAC,CAAGnE,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACoE,SAAS,CAAEC,YAAY,CAAC,CAAGrE,QAAQ,CAAC,IAAI,CAAC,CAEhD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqE,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACFf,UAAU,CAAC,IAAI,CAAC,CAChBR,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEE,EAAE,CAAC,CAExC,GAAI,CAACE,IAAI,EAAI,CAACA,IAAI,CAACmB,GAAG,CAAE,CACtB,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAI,CAACF,KAAK,CAAE,CACVzB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CACnDzC,OAAO,CAACuC,KAAK,CAAC,gCAAgC,CAAC,CAC/C3C,eAAe,CAAC,IAAM,CACpBgD,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAAC,CACF,OACF,CACF,CAEA,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAA/D,WAAW,CAAC,CAAEgE,MAAM,CAAE1B,EAAG,CAAC,CAAC,CAClDH,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE2B,QAAQ,CAAC,CAE3C,GAAIA,QAAQ,CAACE,OAAO,CAAE,CACpB,GAAI,CAACF,QAAQ,CAACG,IAAI,CAAE,CAClBvE,OAAO,CAACuC,KAAK,CAAC,qBAAqB,CAAC,CACpC3C,eAAe,CAAC,IAAM,CACpBgD,QAAQ,CAAC,OAAO,CAAC,CACnB,CAAC,CAAC,CACF,OACF,CAEA,GAAI,CAACwB,QAAQ,CAACG,IAAI,CAAClB,SAAS,EAAIe,QAAQ,CAACG,IAAI,CAAClB,SAAS,CAAC9B,MAAM,GAAK,CAAC,CAAE,CACpEvB,OAAO,CAACuC,KAAK,CAAC,sCAAsC,CAAC,CACrD3C,eAAe,CAAC,IAAM,CACpBgD,QAAQ,CAAC,OAAO,CAAC,CACnB,CAAC,CAAC,CACF,OACF,CAEAQ,OAAO,CAACgB,QAAQ,CAACG,IAAI,CAAC,CACtBjB,YAAY,CAACc,QAAQ,CAACG,IAAI,CAAClB,SAAS,CAAC,CACrCK,UAAU,CAAC,GAAI,CAAAc,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAAClB,SAAS,CAAC9B,MAAM,CAAC,CAACkD,IAAI,CAAC,EAAE,CAAC,CAAC,CAC9Db,WAAW,CAACQ,QAAQ,CAACG,IAAI,CAAClD,QAAQ,CAAG,EAAE,CAAC,CACxCyC,YAAY,CAAC,GAAI,CAAAY,IAAI,CAAC,CAAC,CAAC,CACxBlC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAE2B,QAAQ,CAACG,IAAI,CAAC,CACzD,CAAC,IAAM,CACL/B,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAE6B,QAAQ,CAACpE,OAAO,CAAC,CAClDA,OAAO,CAACuC,KAAK,CAAC6B,QAAQ,CAACpE,OAAO,EAAI,qBAAqB,CAAC,CACxDJ,eAAe,CAAC,IAAM,CACpBgD,QAAQ,CAAC,OAAO,CAAC,CACnB,CAAC,CAAC,CACJ,CACF,CAAE,MAAOL,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,CAAEA,KAAK,CAAC,CAC3CvC,OAAO,CAACuC,KAAK,CAAC,wCAAwC,CAAC,CACvD3C,eAAe,CAAC,IAAM,CACpBgD,QAAQ,CAAC,OAAO,CAAC,CACnB,CAAC,CAAC,CACJ,CAAC,OAAS,CACRI,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIL,EAAE,EAAIE,IAAI,CAAE,CACdkB,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAE,CAACpB,EAAE,CAAEC,QAAQ,CAAEC,IAAI,CAAC,CAAC,CAExB;AACA,KAAM,CAAA8B,gBAAgB,CAAGhF,WAAW,CAAC,SAAY,CAC/C6C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CACjED,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEQ,UAAU,CAAC,CAEpD,GAAI,CACF;AACApC,SAAS,CAAC,QAAQ,CAAC,CAEnB;AACAqC,aAAa,CAAC,IAAI,CAAC,CACnBV,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC3CD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,CAElD,GAAI,CAAAmC,WAAW,CAAG/B,IAAI,CACtB,GAAI,CAAC+B,WAAW,EAAI,CAACA,WAAW,CAACZ,GAAG,CAAE,CACpC,KAAM,CAAAa,UAAU,CAAGX,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAC/C,GAAIU,UAAU,CAAE,CACd,GAAI,CACFD,WAAW,CAAGE,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC,CACtC,CAAE,MAAOtC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD3C,eAAe,CAAC,IAAM,CACpBgD,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAAC,CACF,OACF,CACF,CACF,CAEA,GAAI,CAACgC,WAAW,EAAI,CAACA,WAAW,CAACZ,GAAG,CAAE,CACpChE,OAAO,CAACuC,KAAK,CAAC,2CAA2C,CAAC,CAC1D3C,eAAe,CAAC,IAAM,CACpBgD,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAAC,CACF,OACF,CAEA,KAAM,CAAAoC,OAAO,CAAG,GAAI,CAAAN,IAAI,CAAC,CAAC,CAC1B,KAAM,CAAAO,SAAS,CAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,CAAGnB,SAAS,EAAI,IAAI,CAAC,CAE1D,GAAI,CAAAuB,cAAc,CAAG,CAAC,CACtB,KAAM,CAAAC,aAAa,CAAGhC,SAAS,CAACiC,GAAG,CAAC,CAACC,QAAQ,CAAEC,KAAK,GAAK,CACvD,KAAM,CAAAC,UAAU,CAAGhC,OAAO,CAAC+B,KAAK,CAAC,CACjC,GAAI,CAAAE,SAAS,CAAG,KAAK,CACrB,GAAI,CAAAC,mBAAmB,CAAG,EAAE,CAE5B;AACA,KAAM,CAAAC,YAAY,CAAGL,QAAQ,CAACzE,IAAI,EAAIyE,QAAQ,CAACM,UAAU,EAAI,KAAK,CAElE,GAAID,YAAY,CAACE,WAAW,CAAC,CAAC,GAAK,KAAK,EAAIF,YAAY,GAAK,SAAS,CAAE,CACtE;AACA,GAAIL,QAAQ,CAACQ,OAAO,EAAI,MAAO,CAAAR,QAAQ,CAACQ,OAAO,GAAK,QAAQ,CAAE,CAC5D;AACA,GAAIR,QAAQ,CAACS,aAAa,EAAIT,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACS,aAAa,CAAC,CAAE,CACtEL,mBAAmB,CAAGJ,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACS,aAAa,CAAC,CAC9DN,SAAS,CAAGD,UAAU,GAAKE,mBAAmB,CAChD,CACA;AAAA,IACK,IAAIJ,QAAQ,CAACU,aAAa,EAAIV,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACU,aAAa,CAAC,CAAE,CAC3EN,mBAAmB,CAAGJ,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACU,aAAa,CAAC,CAC9DP,SAAS,CAAGD,UAAU,GAAKE,mBAAmB,CAChD,CACA;AAAA,IACK,IAAIJ,QAAQ,CAACS,aAAa,CAAE,CAC/BL,mBAAmB,CAAGJ,QAAQ,CAACS,aAAa,CAC5CN,SAAS,CAAGD,UAAU,GAAKE,mBAAmB,CAChD,CACF,CAAC,IAAM,CACL;AACAA,mBAAmB,CAAGJ,QAAQ,CAACS,aAAa,EAAIT,QAAQ,CAACU,aAAa,EAAI,EAAE,CAC5EP,SAAS,CAAGD,UAAU,GAAKE,mBAAmB,CAChD,CACF,CAAC,IAAM,KAAAO,oBAAA,CACL;AACAP,mBAAmB,CAAGJ,QAAQ,CAACS,aAAa,EAAI,EAAE,CAClDN,SAAS,CAAG,CAAAD,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEK,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,MAAAD,oBAAA,CAAKP,mBAAmB,UAAAO,oBAAA,iBAAnBA,oBAAA,CAAqBJ,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,EAC5F,CAEA,GAAIT,SAAS,CAAEN,cAAc,EAAE,CAE/B,MAAO,CACLgB,UAAU,CAAEb,QAAQ,CAACvB,GAAG,cAAAqC,MAAA,CAAgBb,KAAK,CAAE,CAC/Cc,YAAY,CAAE,MAAO,CAAAf,QAAQ,CAACgB,IAAI,GAAK,QAAQ,CAAGhB,QAAQ,CAACgB,IAAI,aAAAF,MAAA,CAAeb,KAAK,CAAG,CAAC,CAAE,CACzFgB,YAAY,CAAEjB,QAAQ,CAACgB,IAAI,cAAAF,MAAA,CAAgBb,KAAK,CAAG,CAAC,CAAE,CACtDC,UAAU,CAAE,MAAO,CAAAA,UAAU,GAAK,QAAQ,CAAGA,UAAU,CAAGgB,MAAM,CAAChB,UAAU,EAAI,EAAE,CAAC,CAClFO,aAAa,CAAEL,mBAAmB,CAClCD,SAAS,CACTE,YAAY,CAAEA,YAAY,CAC1BG,OAAO,CAAER,QAAQ,CAACQ,OAAO,EAAI,IAAI,CACjCW,aAAa,CAAEnB,QAAQ,CAACoB,KAAK,EAAIpB,QAAQ,CAACmB,aAAa,EAAInB,QAAQ,CAACqB,QAAQ,EAAI,IAAI,CACpFD,KAAK,CAAEpB,QAAQ,CAACoB,KAAK,EAAIpB,QAAQ,CAACmB,aAAa,EAAInB,QAAQ,CAACqB,QAAQ,EAAI,IAC1E,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAG3B,IAAI,CAAC4B,KAAK,CAAE1B,cAAc,CAAG/B,SAAS,CAAC9B,MAAM,CAAI,GAAG,CAAC,CACxE;AACA,KAAM,CAAAwF,iBAAiB,CAAG5D,IAAI,CAAC6D,YAAY,EAAI7D,IAAI,CAAC4D,iBAAiB,EAAI,EAAE,CAC3E,KAAM,CAAAE,OAAO,CAAGJ,UAAU,EAAIE,iBAAiB,CAAG,MAAM,CAAG,MAAM,CAEjE,KAAM,CAAAG,UAAU,CAAG,CACjBC,IAAI,CAAExE,EAAE,CACRE,IAAI,CAAE+B,WAAW,CAACZ,GAAG,CACrBoD,MAAM,CAAE,CACNhC,cAAc,CACdiC,YAAY,CAAEhE,SAAS,CAAC9B,MAAM,CAAG6D,cAAc,CAC/CyB,UAAU,CACVS,KAAK,CAAET,UAAU,CACjBI,OAAO,CAAEA,OAAO,CAChBhC,SAAS,CACTsC,SAAS,CAAEtC,SAAS,CAAE;AACtBuC,MAAM,CAAEpC,cAAc,CAAG,EAAE,CAC3BqC,cAAc,CAAEpE,SAAS,CAAC9B,MAC5B,CACF,CAAC,CAED,GAAI,CACF,KAAM,CAAA6C,QAAQ,CAAG,KAAM,CAAA9D,SAAS,CAAC4G,UAAU,CAAC,CAE5C,GAAI9C,QAAQ,CAACE,OAAO,CAAE,CACpB9B,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC,CAElE;AACA,KAAM,CAAAiF,eAAe,CAAG,CACtBb,UAAU,CACVzB,cAAc,CACdqC,cAAc,CAAEpE,SAAS,CAAC9B,MAAM,CAChC0D,SAAS,CACTI,aAAa,CACbsC,MAAM,CAAEvD,QAAQ,CAACuD,MAAM,EAAI,IAAI,CAAE;AACjCC,QAAQ,CAAEzE,IAAI,CAACoD,IAAI,CACnBsB,WAAW,CAAE1E,IAAI,CAAC2E,OAAO,EAAI3E,IAAI,CAAC4E,QAAQ,CAC1ChB,iBAAiB,CAAEA,iBAAiB,CAAE;AACtCE,OAAO,CAAEA,OAAQ;AACnB,CAAC,CAED;AACA,KAAM,IAAI,CAAAe,OAAO,CAACC,OAAO,EAAI3F,UAAU,CAAC2F,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvDzF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/C7C,eAAe,CAAC,IAAM,CACpBgD,QAAQ,UAAAyD,MAAA,CAAU1D,EAAE,YAAW,CAC7BG,KAAK,CAAE4E,eACT,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,IAAM,CACLlF,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAE6B,QAAQ,CAACpE,OAAO,CAAC,CAC5D;AACAsC,UAAU,CAAC,IAAM,CACfY,aAAa,CAAC,KAAK,CAAC,CACpBlD,OAAO,CAACuC,KAAK,CAAC6B,QAAQ,CAACpE,OAAO,EAAI,uBAAuB,CAAC,CAC5D,CAAC,CAAE,IAAI,CAAC,CACR,OACF,CACF,CAAE,MAAOkI,QAAQ,CAAE,CACjB1F,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAE2F,QAAQ,CAAC,CACzD;AACA5F,UAAU,CAAC,IAAM,CACfY,aAAa,CAAC,KAAK,CAAC,CACpBlD,OAAO,CAACuC,KAAK,CAAC,qCAAqC,CAAC,CACtD,CAAC,CAAE,IAAI,CAAC,CACR,OACF,CACF,CAAE,MAAOA,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C;AACAD,UAAU,CAAC,IAAM,CACfY,aAAa,CAAC,KAAK,CAAC,CACpBlD,OAAO,CAACuC,KAAK,CAAC,uBAAuB,CAAC,CACxC,CAAC,CAAE,IAAI,CAAC,CACR,OACF,CAAC,OAAS,CACRW,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAAE,CAACW,SAAS,CAAER,SAAS,CAAEI,OAAO,CAAEd,EAAE,CAAEC,QAAQ,CAAEC,IAAI,CAAC,CAAC,CAEvD;AACAnD,SAAS,CAAC,IAAM,CACd,GAAIiE,QAAQ,EAAI,CAAC,CAAE,CACjB;AACA,OACF,CAEA,KAAM,CAAAwE,KAAK,CAAGC,WAAW,CAAC,IAAM,CAC9BxE,WAAW,CAACyE,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAC/B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMC,aAAa,CAACH,KAAK,CAAC,CACnC,CAAC,CAAE,CAACxE,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAA4E,kBAAkB,CAAIC,MAAM,EAAK,CACrC,KAAM,CAAAC,UAAU,CAAG,CAAC,GAAGhF,OAAO,CAAC,CAC/BgF,UAAU,CAAClF,eAAe,CAAC,CAAGiF,MAAM,CACpC9E,UAAU,CAAC+E,UAAU,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,GAAInF,eAAe,CAAGF,SAAS,CAAC9B,MAAM,CAAG,CAAC,CAAE,CAC1CV,SAAS,CAAC,UAAU,CAAC,CACrB2C,kBAAkB,CAACD,eAAe,CAAG,CAAC,CAAC,CACzC,CACF,CAAC,CAED,KAAM,CAAAoF,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIpF,eAAe,CAAG,CAAC,CAAE,CACvB1C,SAAS,CAAC,UAAU,CAAC,CACrB2C,kBAAkB,CAACD,eAAe,CAAG,CAAC,CAAC,CACzC,CACF,CAAC,CAED;AACA,KAAM,CAAAqF,UAAU,CAAIC,OAAO,EAAK,CAC9B,KAAM,CAAAC,OAAO,CAAG5D,IAAI,CAACC,KAAK,CAAC0D,OAAO,CAAG,EAAE,CAAC,CACxC,KAAM,CAAAE,gBAAgB,CAAGF,OAAO,CAAG,EAAE,CACrC,SAAAxC,MAAA,CAAUyC,OAAO,MAAAzC,MAAA,CAAI0C,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EACnE,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAtD,YAAY,CAAGuD,QAAQ,CAACrI,IAAI,EAAIqI,QAAQ,CAACtD,UAAU,EAAI,KAAK,CAIlE,OAAQD,YAAY,CAACE,WAAW,CAAC,CAAC,EAChC,IAAK,KAAK,CACV,IAAK,iBAAiB,CACtB,IAAK,gBAAgB,CACnB,MAAO,CAAAsD,oBAAoB,CAAC,CAAC,CAE/B,IAAK,MAAM,CACX,IAAK,mBAAmB,CACxB,IAAK,WAAW,CAChB,IAAK,MAAM,CACT,MAAO,CAAAC,oBAAoB,CAAC,CAAC,CAE/B,IAAK,OAAO,CACZ,IAAK,SAAS,CACZ,MAAO,CAAAC,mBAAmB,CAAC,CAAC,CAE9B,QACE;AACA,MAAO,CAAAF,oBAAoB,CAAC,CAAC,CACjC,CACF,CAAC,CAED;AACA,KAAM,CAAAA,oBAAoB,CAAGA,CAAA,GAAM,CACjC,GAAI,CAAArD,OAAO,CAAG,EAAE,CAEhB;AACA,GAAIvB,KAAK,CAAC+E,OAAO,CAACJ,QAAQ,CAACpD,OAAO,CAAC,CAAE,CACnCA,OAAO,CAAGoD,QAAQ,CAACpD,OAAO,CAC5B,CAAC,IAAM,IAAIoD,QAAQ,CAACpD,OAAO,EAAI,MAAO,CAAAoD,QAAQ,CAACpD,OAAO,GAAK,QAAQ,CAAE,CACnE;AACAA,OAAO,CAAGyD,MAAM,CAACC,MAAM,CAACN,QAAQ,CAACpD,OAAO,CAAC,CAC3C,CAAC,IAAM,IAAIoD,QAAQ,CAACO,OAAO,EAAIP,QAAQ,CAACQ,OAAO,CAAE,CAC/C;AACA5D,OAAO,CAAG,CAACoD,QAAQ,CAACO,OAAO,CAAEP,QAAQ,CAACQ,OAAO,CAAER,QAAQ,CAACS,OAAO,CAAET,QAAQ,CAACU,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACpG,CAEA,GAAI,CAAChE,OAAO,EAAIA,OAAO,CAACxE,MAAM,GAAK,CAAC,CAAE,CACpC;AACA,mBACEb,KAAA,QAAKsJ,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvJ,KAAA,QAAKsJ,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAChFzJ,IAAA,MAAGwJ,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,oCAAkC,CAAG,CAAC,cACjFvJ,KAAA,YAASsJ,SAAS,CAAC,MAAM,CAAAC,QAAA,eACvBzJ,IAAA,YAASwJ,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oBAAkB,CAAS,CAAC,cACxFzJ,IAAA,QAAKwJ,SAAS,CAAC,yEAAyE,CAAAC,QAAA,CACrFnF,IAAI,CAACoF,SAAS,CAACf,QAAQ,CAAE,IAAI,CAAE,CAAC,CAAC,CAC/B,CAAC,EACC,CAAC,EACP,CAAC,cAGN3I,IAAA,QAAKwJ,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CAAC,iBAAiB,CAAE,iBAAiB,CAAE,iBAAiB,CAAE,iBAAiB,CAAC,CAAC3E,GAAG,CAAC,CAAC6E,MAAM,CAAE3E,KAAK,GAAK,CACnG,KAAM,CAAA4E,YAAY,CAAG3D,MAAM,CAAC4D,YAAY,CAAC,EAAE,CAAG7E,KAAK,CAAC,CACpD,KAAM,CAAA8E,UAAU,CAAG7G,OAAO,CAACF,eAAe,CAAC,GAAK4G,MAAM,CAEtD,mBACE3J,IAAA,WAEE+J,OAAO,CAAEA,CAAA,GAAMhC,kBAAkB,CAAC4B,MAAM,CAAE,CAC1CH,SAAS,yFAAA3D,MAAA,CACPiE,UAAU,CACN,oDAAoD,CACpD,iEAAiE,CACpE,CAAAL,QAAA,cAEHvJ,KAAA,QAAKsJ,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCzJ,IAAA,QAAKwJ,SAAS,4FAAA3D,MAAA,CACZiE,UAAU,CACN,wBAAwB,CACxB,2BAA2B,CAC9B,CAAAL,QAAA,CACAG,YAAY,CACV,CAAC,cACN5J,IAAA,SAAMwJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACrEE,MAAM,CACH,CAAC,EACJ,CAAC,EAnBD3E,KAoBC,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAEA,mBACEhF,IAAA,QAAKwJ,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBlE,OAAO,CAACT,GAAG,CAAC,CAAC6E,MAAM,CAAE3E,KAAK,GAAK,CAC9B,KAAM,CAAA4E,YAAY,CAAG3D,MAAM,CAAC4D,YAAY,CAAC,EAAE,CAAG7E,KAAK,CAAC,CACpD,KAAM,CAAA8E,UAAU,CAAG7G,OAAO,CAACF,eAAe,CAAC,GAAK4G,MAAM,CAEtD,mBACE3J,IAAA,WAEE+J,OAAO,CAAEA,CAAA,GAAMhC,kBAAkB,CAAC4B,MAAM,CAAE,CAC1CH,SAAS,yFAAA3D,MAAA,CACPiE,UAAU,CACN,oDAAoD,CACpD,iEAAiE,CACpE,CAAAL,QAAA,cAEHvJ,KAAA,QAAKsJ,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCzJ,IAAA,QAAKwJ,SAAS,4FAAA3D,MAAA,CACZiE,UAAU,CACN,wBAAwB,CACxB,2BAA2B,CAC9B,CAAAL,QAAA,CACAG,YAAY,CACV,CAAC,cACN5J,IAAA,SAAMwJ,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACrE,MAAO,CAAAE,MAAM,GAAK,QAAQ,CAAGA,MAAM,CAAGrF,IAAI,CAACoF,SAAS,CAACC,MAAM,CAAC,CACzD,CAAC,EACJ,CAAC,EAnBD3E,KAoBC,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA6D,oBAAoB,CAAGA,CAAA,GAAM,CACjC,mBACE3I,KAAA,QAAKsJ,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvJ,KAAA,QAAKsJ,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DzJ,IAAA,MAAGwJ,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,cAC5EzJ,IAAA,MAAGwJ,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mCAAiC,CAAG,CAAC,EAC/D,CAAC,cACNzJ,IAAA,QAAKwJ,SAAS,CAAC,UAAU,CAAAC,QAAA,cACvBzJ,IAAA,UACEM,IAAI,CAAC,MAAM,CACX0J,KAAK,CAAE/G,OAAO,CAACF,eAAe,CAAC,EAAI,EAAG,CACtCkH,QAAQ,CAAGC,CAAC,EAAKnC,kBAAkB,CAACmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACpDI,WAAW,CAAC,0BAA0B,CACtCZ,SAAS,CAAC,mHAAmH,CAC7Ha,SAAS,MACV,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAvB,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAIH,QAAQ,CAACpD,OAAO,EAAIvB,KAAK,CAAC+E,OAAO,CAACJ,QAAQ,CAACpD,OAAO,CAAC,EAAIoD,QAAQ,CAACpD,OAAO,CAACxE,MAAM,CAAG,CAAC,CAAE,CACtF,MAAO,CAAA6H,oBAAoB,CAAC,CAAC,CAC/B,CAAC,IAAM,CACL,MAAO,CAAAC,oBAAoB,CAAC,CAAC,CAC/B,CACF,CAAC,CAED,GAAItG,OAAO,CAAE,CACX,mBACEvC,IAAA,QAAKwJ,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACzGvJ,KAAA,QAAKsJ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzJ,IAAA,QAAKwJ,SAAS,CAAC,6EAA6E,CAAM,CAAC,cACnGxJ,IAAA,MAAGwJ,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,EACzD,CAAC,CACH,CAAC,CAEV,CAEA,GAAI,CAAC9G,IAAI,EAAI,CAACE,SAAS,CAAC9B,MAAM,CAAE,CAC9B,mBACEf,IAAA,QAAKwJ,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACzGzJ,IAAA,QAAKwJ,SAAS,CAAC,yEAAyE,CAAAC,QAAA,cACtFvJ,KAAA,QAAKsJ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzJ,IAAA,OAAIwJ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,cAChFzJ,IAAA,MAAGwJ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,2CAAyC,CAAG,CAAC,cAC/EzJ,IAAA,WACE+J,OAAO,CAAEA,CAAA,GAAM3K,eAAe,CAAC,IAAMgD,QAAQ,CAAC,OAAO,CAAC,CAAE,CACxDoH,SAAS,CAAC,sFAAsF,CAAAC,QAAA,CACjG,iBAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA;AACA,GAAI,CAAC5G,SAAS,CAACE,eAAe,CAAC,CAAE,CAC/B,mBACE/C,IAAA,QAAKwJ,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACzGzJ,IAAA,QAAKwJ,SAAS,CAAC,yEAAyE,CAAAC,QAAA,cACtFvJ,KAAA,QAAKsJ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzJ,IAAA,OAAIwJ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC5EzJ,IAAA,MAAGwJ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,sCAAoC,CAAG,CAAC,cAC1EzJ,IAAA,WACE+J,OAAO,CAAEA,CAAA,GAAM3K,eAAe,CAAC,IAAMgD,QAAQ,CAAC,OAAO,CAAC,CAAE,CACxDoH,SAAS,CAAC,sFAAsF,CAAAC,QAAA,CACjG,iBAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,KAAM,CAAAd,QAAQ,CAAG9F,SAAS,CAACE,eAAe,CAAC,CAC3C,KAAM,CAAAuH,cAAc,CAAGvH,eAAe,GAAKF,SAAS,CAAC9B,MAAM,CAAG,CAAC,CAE/D;AACA,GAAI,CAAC4H,QAAQ,EAAI,MAAO,CAAAA,QAAQ,GAAK,QAAQ,CAAE,CAC7C,mBACE3I,IAAA,QAAKwJ,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACzGzJ,IAAA,QAAKwJ,SAAS,CAAC,yEAAyE,CAAAC,QAAA,cACtFvJ,KAAA,QAAKsJ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzJ,IAAA,OAAIwJ,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC/EzJ,IAAA,MAAGwJ,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,4CAA0C,CAAG,CAAC,cAChFzJ,IAAA,WACE+J,OAAO,CAAEA,CAAA,GAAM3K,eAAe,CAAC,IAAMgD,QAAQ,CAAC,OAAO,CAAC,CAAE,CACxDoH,SAAS,CAAC,sFAAsF,CAAAC,QAAA,CACjG,iBAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAIA;AACA,GAAIhH,UAAU,CAAE,CACd,mBACEvC,KAAA,CAAAE,SAAA,EAAAqJ,QAAA,eACEzJ,IAAA,UAAAyJ,QAAA,svDAwCS,CAAC,cACVzJ,IAAA,QAAKwJ,SAAS,CAAC,aAAa,CAACe,KAAK,CAAE,CAClCC,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,MAAM,CAAE,IACV,CAAE,CAAAvB,QAAA,cACAvJ,KAAA,QAAKqK,KAAK,CAAE,CACVU,UAAU,CAAE,2BAA2B,CACvCC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,WAAW,CAAG,WAAW,CAC7DC,SAAS,CAAE,QAAQ,CACnBC,SAAS,CAAE,gEAAgE,CAC3EC,cAAc,CAAE,YAAY,CAC5BC,MAAM,CAAE,oCAAoC,CAC5CC,QAAQ,CAAEjL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,OAAO,CAAG,OAAO,CACtDT,KAAK,CAAE,KAAK,CACZe,SAAS,CAAE,wBACb,CAAE,CAAAjC,QAAA,eAEAvJ,KAAA,QAAKqK,KAAK,CAAE,CACVI,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,OAAO,CAAG,OAAO,CACnDR,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,OAAO,CAAG,OAAO,CACpDO,MAAM,CAAE,kBAAkB,CAC1BV,UAAU,CAAE,gEAAgE,CAC5EC,YAAY,CAAE,KAAK,CACnBL,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBW,SAAS,CAAE,0CAA0C,CACrDJ,SAAS,CAAE,0EAA0E,CACrFd,QAAQ,CAAE,UACZ,CAAE,CAAAf,QAAA,eACAzJ,IAAA,QAAKuK,KAAK,CAAE,CACVI,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDR,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CAClDH,UAAU,CAAE,OAAO,CACnBC,YAAY,CAAE,KAAK,CACnBL,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBO,SAAS,CAAE,+BACb,CAAE,CAAA7B,QAAA,cACAzJ,IAAA,SAAMuK,KAAK,CAAE,CACXqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACpD9B,MAAM,CAAE,wCACV,CAAE,CAAAG,QAAA,CAAC,cAAE,CAAM,CAAC,CACT,CAAC,cAENzJ,IAAA,QAAKuK,KAAK,CAAE,CACVC,QAAQ,CAAE,UAAU,CACpBG,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdY,MAAM,CAAE,oCAAoC,CAC5CN,YAAY,CAAE,KAAK,CACnBQ,SAAS,CAAE,wCACb,CAAE,CAAM,CAAC,EACN,CAAC,cAGN1L,IAAA,OAAIuK,KAAK,CAAE,CACTqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACpDS,UAAU,CAAE,KAAK,CACjBZ,UAAU,CAAE,2CAA2C,CACvDa,oBAAoB,CAAE,MAAM,CAC5BC,mBAAmB,CAAE,aAAa,CAClCC,cAAc,CAAE,MAAM,CACtBL,MAAM,CAAE,YAAY,CACpBD,SAAS,CAAE,4BAA4B,CACvCO,aAAa,CAAE,QACjB,CAAE,CAAAxC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAG5BzJ,IAAA,MAAGuK,KAAK,CAAE,CACRqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACpDc,KAAK,CAAE,SAAS,CAChBP,MAAM,CAAE,YAAY,CACpBQ,UAAU,CAAE,KAAK,CACjBN,UAAU,CAAE,KACd,CAAE,CAAApC,QAAA,CAAC,yDAAuD,CAAG,CAAC,cAG9DzJ,IAAA,QAAKuK,KAAK,CAAE,CACVM,OAAO,CAAE,MAAM,CACfE,cAAc,CAAE,QAAQ,CACxBD,UAAU,CAAE,QAAQ,CACpBsB,GAAG,CAAE,KAAK,CACVC,YAAY,CAAE,MAChB,CAAE,CAAA5C,QAAA,CACC,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC3E,GAAG,CAACwH,CAAC,eACjBtM,IAAA,QAEEwJ,SAAS,CAAC,kBAAkB,CAC5Be,KAAK,CAAE,CACLI,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdK,UAAU,CAAE,2CAA2C,CACvDC,YAAY,CAAE,KAAK,CACnBI,SAAS,CAAE,oCACb,CAAE,EARGgB,CASN,CACF,CAAC,CACC,CAAC,cAGNtM,IAAA,QAAKuK,KAAK,CAAE,CACVqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACpDc,KAAK,CAAE,SAAS,CAChBL,UAAU,CAAE,KAAK,CACjBU,aAAa,CAAE,WAAW,CAC1BN,aAAa,CAAE,KACjB,CAAE,CAAAxC,QAAA,CAAC,eAEH,CAAK,CAAC,EACH,CAAC,CACH,CAAC,EACN,CAAC,CAEP,CAEA,mBACEvJ,KAAA,QACEsJ,SAAS,CAAC,oEAAoE,CAC9Ee,KAAK,CAAE,CACLY,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAC9C,CAAE,CAAA3B,QAAA,eAGFzJ,IAAA,QACEwJ,SAAS,CAAC,wDAAwD,CAClEe,KAAK,CAAE,CACL8B,YAAY,CAAE7L,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MACpD,CAAE,CAAA3B,QAAA,cAEFvJ,KAAA,QACEsJ,SAAS,CAAC,SAAS,CACnBe,KAAK,CAAE,CACLkB,QAAQ,CAAEjL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG5K,MAAM,CAAC4K,UAAU,EAAI,IAAI,CAAG,KAAK,CAAG,QAAQ,CAC1FD,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC/C,CAAE,CAAA3B,QAAA,eAEFvJ,KAAA,QAAKsJ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAE1BvJ,KAAA,QAAAuJ,QAAA,eACEzJ,IAAA,OACEwJ,SAAS,CAAC,8BAA8B,CACxCe,KAAK,CAAE,CACLqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG5K,MAAM,CAAC4K,UAAU,EAAI,IAAI,CAAG,MAAM,CAAG,MACrF,CAAE,CAAA3B,QAAA,CAED9G,IAAI,CAACoD,IAAI,CACR,CAAC,cACL7F,KAAA,MACEsJ,SAAS,CAAC,oBAAoB,CAC9Be,KAAK,CAAE,CACLqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CAAA3B,QAAA,EACH,WACU,CAAC1G,eAAe,CAAG,CAAC,CAAC,MAAI,CAACF,SAAS,CAAC9B,MAAM,EAClD,CAAC,EACD,CAAC,cAGNf,IAAA,QAAKwJ,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCvJ,KAAA,QAAKsJ,SAAS,uCAAA3D,MAAA,CACZ1C,QAAQ,EAAI,GAAG,CAAG,yBAAyB,CAAG,2BAA2B,CACxE,CACHoH,KAAK,CAAE,CACLY,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,UAAU,CAAG,WACnD,CAAE,CAAA3B,QAAA,eACAzJ,IAAA,CAACP,OAAO,EACN8K,KAAK,CAAE,CACLI,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDR,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACH,CAAC,cACFpL,IAAA,SACEwJ,SAAS,CAAC,eAAe,CACzBe,KAAK,CAAE,CACLqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CAAA3B,QAAA,CAEDrB,UAAU,CAACjF,QAAQ,CAAC,CACjB,CAAC,EACJ,CAAC,CACH,CAAC,EACH,CAAC,cAGNjD,KAAA,QAAKsJ,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvJ,KAAA,QAAKsJ,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDzJ,IAAA,SACEwJ,SAAS,CAAC,2BAA2B,CACrCe,KAAK,CAAE,CACLqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CAAA3B,QAAA,CACH,UAED,CAAM,CAAC,cACPvJ,KAAA,SACEsJ,SAAS,CAAC,yBAAyB,CACnCe,KAAK,CAAE,CACLqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CAAA3B,QAAA,EAED/E,IAAI,CAAC4B,KAAK,CAAE,CAACvD,eAAe,CAAG,CAAC,EAAIF,SAAS,CAAC9B,MAAM,CAAI,GAAG,CAAC,CAAC,GAChE,EAAM,CAAC,EACJ,CAAC,cACNf,IAAA,QACEuK,KAAK,CAAE,CACLI,KAAK,CAAE,MAAM,CACbC,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAAM,CACjDoB,eAAe,CAAE,SAAS,CAC1BtB,YAAY,CAAE,QAAQ,CACtBuB,QAAQ,CAAE,QAAQ,CAClBnB,SAAS,CAAE,iCACb,CAAE,CAAA7B,QAAA,cAEFzJ,IAAA,QACEuK,KAAK,CAAE,CACLI,KAAK,IAAA9E,MAAA,CAAM,CAAC9C,eAAe,CAAG,CAAC,EAAIF,SAAS,CAAC9B,MAAM,CAAI,GAAG,KAAG,CAC7D6J,MAAM,CAAE,MAAM,CACd4B,eAAe,CAAE,SAAS,CAC1BtB,YAAY,CAAE,QAAQ,CACtBwB,UAAU,CAAE,qBAAqB,CACjCpB,SAAS,CAAE,kCACb,CAAE,CACE,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNtL,IAAA,QACEwJ,SAAS,CAAC,SAAS,CACnBe,KAAK,CAAE,CACLkB,QAAQ,CAAEjL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG5K,MAAM,CAAC4K,UAAU,EAAI,IAAI,CAAG,KAAK,CAAG,QAAQ,CAC1FD,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,OAAO,CAAG,QAChD,CAAE,CAAA3B,QAAA,cAEFvJ,KAAA,QACEsJ,SAAS,CAAC,mFAAmF,CAC7Fe,KAAK,CAAE,CACLY,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG5K,MAAM,CAAC4K,UAAU,EAAI,IAAI,CAAG,MAAM,CAAG,MACpF,CAAE,CAAA3B,QAAA,eAGFvJ,KAAA,QACEqK,KAAK,CAAE,CACL8B,YAAY,CAAE7L,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MACpD,CAAE,CAAA3B,QAAA,eAEFzJ,IAAA,OACEwJ,SAAS,CAAC,qCAAqC,CAC/Ce,KAAK,CAAE,CACLqB,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG5K,MAAM,CAAC4K,UAAU,EAAI,IAAI,CAAG,MAAM,CAAG,MAAM,CACzFiB,YAAY,CAAE7L,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MACpD,CAAE,CAAA3B,QAAA,CAED,MAAO,CAAAd,QAAQ,CAAC5C,IAAI,GAAK,QAAQ,CAAG4C,QAAQ,CAAC5C,IAAI,CAAG,UAAU,CAC7D,CAAC,CAEJ4C,QAAQ,CAACxC,KAAK,eACbnG,IAAA,QACEwJ,SAAS,CAAC,uBAAuB,CACjCe,KAAK,CAAE,CACL8B,YAAY,CAAE7L,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACxDD,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC/C,CAAE,CAAA3B,QAAA,cAEFzJ,IAAA,QACE2M,GAAG,CAAEhE,QAAQ,CAACxC,KAAM,CACpByG,GAAG,CAAC,kBAAkB,CACtBpD,SAAS,CAAC,sDAAsD,CAChEe,KAAK,CAAE,CACLsC,SAAS,CAAErM,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,OAAO,CAAG,OAClD,CAAE,CACF0B,OAAO,CAAG5C,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACI,KAAK,CAACM,OAAO,CAAG,MAAM,CAC/B;AACA,KAAM,CAAAkC,QAAQ,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC9CF,QAAQ,CAACvD,SAAS,CAAG,gCAAgC,CACrDuD,QAAQ,CAACG,SAAS,CAAG,+BAA+B,CACpDhD,CAAC,CAACC,MAAM,CAACgD,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC,CAC3C,CAAE,CACH,CAAC,CACC,CACN,EACE,CAAC,cAGN/M,IAAA,QACEwJ,SAAS,CAAC,WAAW,CACrBe,KAAK,CAAE,CACL8B,YAAY,CAAE7L,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MACpD,CAAE,CAAA3B,QAAA,CAEDf,mBAAmB,CAAC,CAAC,CACnB,CAAC,cAGNxI,KAAA,QACEsJ,SAAS,CAAC,mBAAmB,CAC7Be,KAAK,CAAE,CACL8C,aAAa,CAAE7M,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,QAAQ,CAAG,KAAK,CAC1DL,cAAc,CAAEvK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,QAAQ,CAAG,eAAe,CACrEgB,GAAG,CAAE5L,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,GAC3C,CAAE,CAAA3B,QAAA,eAEFvJ,KAAA,WACE6J,OAAO,CAAE5B,YAAa,CACtBmF,QAAQ,CAAEvK,eAAe,GAAK,CAAE,CAChCyG,SAAS,uEAAA3D,MAAA,CACP9C,eAAe,GAAK,CAAC,CACjB,8CAA8C,CAC9C,6CAA6C,CAChD,CACHwH,KAAK,CAAE,CACLY,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,WAAW,CAAG,WAAW,CAC7DQ,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACpDT,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDL,cAAc,CAAE,QAClB,CAAE,CAAAtB,QAAA,eAEFzJ,IAAA,CAACN,WAAW,EACV6K,KAAK,CAAE,CACLI,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDR,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACH,CAAC,WAEJ,EAAQ,CAAC,CAERd,cAAc,cACbtK,IAAA,WACE+J,OAAO,CAAE5F,gBAAiB,CAC1BmJ,QAAQ,CAAE7K,UAAW,CACrB+G,SAAS,uEAAA3D,MAAA,CACPpD,UAAU,CACN,8CAA8C,CAC9C,4CAA4C,CAC/C,CACH8H,KAAK,CAAE,CACLY,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,WAAW,CAAG,WAAW,CAC7DQ,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACpDT,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDL,cAAc,CAAE,QAClB,CAAE,CAAAtB,QAAA,CAEDhH,UAAU,cACTvC,KAAA,CAAAE,SAAA,EAAAqJ,QAAA,eACEzJ,IAAA,QACEwJ,SAAS,CAAC,sEAAsE,CAChFe,KAAK,CAAE,CACLI,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDR,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACE,CAAC,gBAET,EAAE,CAAC,cAEHlL,KAAA,CAAAE,SAAA,EAAAqJ,QAAA,eACEzJ,IAAA,CAACJ,OAAO,EACN2K,KAAK,CAAE,CACLI,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDR,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACH,CAAC,cAEJ,EAAE,CACH,CACK,CAAC,cAETlL,KAAA,WACE6J,OAAO,CAAE7B,QAAS,CAClBsB,SAAS,CAAC,6GAA6G,CACvHe,KAAK,CAAE,CACLY,OAAO,CAAE3K,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,WAAW,CAAG,WAAW,CAC7DQ,QAAQ,CAAEpL,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACpDT,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDL,cAAc,CAAE,QAClB,CAAE,CAAAtB,QAAA,EACH,MAEC,cAAAzJ,IAAA,CAACL,YAAY,EACX4K,KAAK,CAAE,CACLI,KAAK,CAAEnK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDR,MAAM,CAAEpK,MAAM,CAAC4K,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACH,CAAC,EACI,CACT,EACE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}