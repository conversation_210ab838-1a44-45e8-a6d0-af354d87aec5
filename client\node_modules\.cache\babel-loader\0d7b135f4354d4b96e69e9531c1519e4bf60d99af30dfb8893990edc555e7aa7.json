{"ast": null, "code": "const {\n  default: axiosInstance\n} = require(\".\");\nexport const registerUser = async payload => {\n  try {\n    const response = await axiosInstance.post('/api/users/register', payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const sendOTP = async payload => {\n  try {\n    const response = await axiosInstance.post('/api/users/otp', payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const contactUs = async payload => {\n  try {\n    const response = await axiosInstance.post('/api/users/contact-us', payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const loginUser = async payload => {\n  try {\n    const response = await axiosInstance.post('/api/users/login', payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const getAllUsers = async () => {\n  try {\n    const response = await axiosInstance.get('/api/users/get-all-users');\n    console.log(\"data :\", response.data);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const getUserInfo = async () => {\n  try {\n    const response = await axiosInstance.post('/api/users/get-user-info');\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const updateUserInfo = async payload => {\n  try {\n    const response = await axiosInstance.post('/api/users/update-user-info', payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const updateUserPhoto = async payload => {\n  try {\n    const response = await axiosInstance.post('/api/users/update-user-photo', payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const blockUserById = async payload => {\n  try {\n    const response = await axiosInstance.patch('/api/users/block-user', payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\nexport const deleteUserById = async payload => {\n  try {\n    const response = await axiosInstance.delete('/api/users/delete-user', {\n      data: payload\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};", "map": {"version": 3, "names": ["default", "axiosInstance", "require", "registerUser", "payload", "response", "post", "data", "error", "sendOTP", "contactUs", "loginUser", "getAllUsers", "get", "console", "log", "getUserInfo", "updateUserInfo", "updateUserPhoto", "blockUserById", "patch", "deleteUserById", "delete"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/users.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\nexport const registerUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post('/api/users/register', payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n\r\n\r\nexport const sendOTP = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post('/api/users/otp', payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const contactUs = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post('/api/users/contact-us', payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n\r\nexport const loginUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post('/api/users/login', payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getAllUsers = async () => {\r\n    try {\r\n        const response = await axiosInstance.get('/api/users/get-all-users');\r\n        console.log(\"data :\", response.data);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const getUserInfo = async () => {\r\n    try {\r\n        const response = await axiosInstance.post('/api/users/get-user-info');\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const updateUserInfo = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post('/api/users/update-user-info', payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const updateUserPhoto = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post('/api/users/update-user-photo', payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const blockUserById = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.patch('/api/users/block-user', payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\nexport const deleteUserById = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.delete('/api/users/delete-user', { data: payload });\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}"], "mappings": "AAAA,MAAM;EAAEA,OAAO,EAAEC;AAAc,CAAC,GAAGC,OAAO,CAAC,GAAG,CAAC;AAE/C,OAAO,MAAMC,YAAY,GAAG,MAAOC,OAAO,IAAK;EAC3C,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,qBAAqB,EAAEF,OAAO,CAAC;IACzE,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAID,OAAO,MAAME,OAAO,GAAG,MAAOL,OAAO,IAAK;EACtC,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,gBAAgB,EAAEF,OAAO,CAAC;IACpE,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMG,SAAS,GAAG,MAAON,OAAO,IAAK;EACxC,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,uBAAuB,EAAEF,OAAO,CAAC;IAC3E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAGD,OAAO,MAAMI,SAAS,GAAG,MAAOP,OAAO,IAAK;EACxC,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,kBAAkB,EAAEF,OAAO,CAAC;IACtE,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMK,WAAW,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACA,MAAMP,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAC,0BAA0B,CAAC;IACpEC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEV,QAAQ,CAACE,IAAI,CAAC;IACpC,OAAOF,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMS,WAAW,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACA,MAAMX,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,0BAA0B,CAAC;IACrE,OAAOD,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMU,cAAc,GAAG,MAAOb,OAAO,IAAK;EAC7C,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,6BAA6B,EAAEF,OAAO,CAAC;IACjF,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMW,eAAe,GAAG,MAAOd,OAAO,IAAK;EAC9C,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,8BAA8B,EAAEF,OAAO,CAAC;IAClF,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMY,aAAa,GAAG,MAAOf,OAAO,IAAK;EAC5C,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACmB,KAAK,CAAC,uBAAuB,EAAEhB,OAAO,CAAC;IAC5E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;AAED,OAAO,MAAMc,cAAc,GAAG,MAAOjB,OAAO,IAAK;EAC7C,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACqB,MAAM,CAAC,wBAAwB,EAAE;MAAEf,IAAI,EAAEH;IAAQ,CAAC,CAAC;IACxF,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}