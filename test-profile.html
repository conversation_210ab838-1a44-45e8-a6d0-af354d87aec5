<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainWave Profile Test</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #4f46e5;
            margin-bottom: 10px;
        }
        .profile-section {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4f46e5;
        }
        .btn {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: 600;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .api-test {
            background: #f1f5f9;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }
        .api-test h3 {
            color: #1e293b;
            margin-bottom: 15px;
        }
        .test-btn {
            background: #059669;
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 BrainWave Profile Test</h1>
            <p>Test the profile functionality with demo server</p>
        </div>

        <div class="profile-section">
            <h2>👤 User Profile</h2>
            <form id="profileForm">
                <div class="form-group">
                    <label for="firstName">First Name</label>
                    <input type="text" id="firstName" name="firstName" value="Demo">
                </div>
                <div class="form-group">
                    <label for="lastName">Last Name</label>
                    <input type="text" id="lastName" name="lastName" value="User">
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="phoneNumber">Phone Number</label>
                    <input type="tel" id="phoneNumber" name="phoneNumber" value="0712345678">
                </div>
                <div class="form-group">
                    <label for="level">Level</label>
                    <select id="level" name="level">
                        <option value="Primary">Primary</option>
                        <option value="Secondary">Secondary</option>
                        <option value="Advance">Advance</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="class">Class</label>
                    <select id="class" name="class">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5" selected>5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                    </select>
                </div>
                <button type="submit" class="btn">💾 Update Profile</button>
            </form>
        </div>

        <div id="status"></div>

        <div class="api-test">
            <h3>🔧 API Tests</h3>
            <button class="btn test-btn" onclick="testLogin()">🔐 Test Login</button>
            <button class="btn test-btn" onclick="testGetUserInfo()">👤 Get User Info</button>
            <button class="btn test-btn" onclick="testGetPlans()">📋 Get Plans</button>
            <button class="btn test-btn" onclick="testPayment()">💳 Test Payment</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';

        // Handle profile form submission
        document.getElementById('profileForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const profileData = Object.fromEntries(formData);
            
            try {
                const response = await fetch(`${API_BASE}/api/users/update-user-info`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(profileData)
                });
                
                const result = await response.json();
                showStatus(result.success ? 'success' : 'error', result.message);
            } catch (error) {
                showStatus('error', 'Failed to update profile: ' + error.message);
            }
        });

        // Test functions
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/api/users/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email: '<EMAIL>', password: 'test123' })
                });
                const result = await response.json();
                showStatus(result.success ? 'success' : 'error', `Login: ${result.message}`);
            } catch (error) {
                showStatus('error', 'Login test failed: ' + error.message);
            }
        }

        async function testGetUserInfo() {
            try {
                const response = await fetch(`${API_BASE}/api/users/get-user-info`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ userId: 'demo-user-id' })
                });
                const result = await response.json();
                showStatus(result.success ? 'success' : 'error', `User Info: ${result.message}`);
            } catch (error) {
                showStatus('error', 'Get user info failed: ' + error.message);
            }
        }

        async function testGetPlans() {
            try {
                const response = await fetch(`${API_BASE}/api/plans`);
                const result = await response.json();
                showStatus('success', `Plans: Found ${result.length} plans`);
            } catch (error) {
                showStatus('error', 'Get plans failed: ' + error.message);
            }
        }

        async function testPayment() {
            try {
                const response = await fetch(`${API_BASE}/api/payment/create-invoice`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        plan: { _id: 'test-plan', discountedPrice: 13000 },
                        userId: 'demo-user-id'
                    })
                });
                const result = await response.json();
                showStatus(result.success ? 'success' : 'error', `Payment: ${result.message}`);
            } catch (error) {
                showStatus('error', 'Payment test failed: ' + error.message);
            }
        }

        function showStatus(type, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusDiv.textContent = '';
                statusDiv.className = '';
            }, 5000);
        }

        // Test server connection on load
        window.addEventListener('load', () => {
            fetch(`${API_BASE}/api/health`)
                .then(response => response.json())
                .then(result => {
                    showStatus('success', '✅ Server connected successfully!');
                })
                .catch(error => {
                    showStatus('error', '❌ Server connection failed: ' + error.message);
                });
        });
    </script>
</body>
</html>
