/* Modern Premium Subscription Modal */
.subscription-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.subscription-modal {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
  max-width: 1000px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modal-content {
  padding: 2rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

/* Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.plan-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.plan-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-8px);
  border-color: #667eea;
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
}

.plan-card:hover::before {
  transform: scaleX(1);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.plan-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.plan-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.plan-price {
  text-align: center;
  margin-bottom: 1.5rem;
}

.price-amount {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.price-original {
  display: block;
  font-size: 1rem;
  color: #9ca3af;
  text-decoration: line-through;
  margin-bottom: 0.25rem;
}

.price-period {
  display: block;
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

.plan-features {
  margin-bottom: 1.5rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.feature-icon {
  color: #10b981;
  font-weight: 700;
  font-size: 1rem;
}

.feature-text {
  color: #374151;
  font-size: 0.9rem;
  font-weight: 500;
}

.select-plan-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.select-plan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Payment Step */
.payment-step {
  max-width: 500px;
  margin: 0 auto;
}

.selected-plan-summary {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  margin-bottom: 2rem;
  border: 1px solid #bfdbfe;
}

.selected-plan-summary h3 {
  color: #1e40af;
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.plan-price-summary {
  color: #1e40af;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.payment-info {
  margin-bottom: 2rem;
}

.phone-section {
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.info-item .info-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.phone-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  justify-content: space-between;
}

.phone-edit {
  flex: 1;
}

.phone-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 0.75rem;
  transition: border-color 0.3s ease;
}

.phone-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.phone-input.valid {
  border-color: #10b981;
}

.phone-input.invalid {
  border-color: #ef4444;
}

.phone-validation {
  margin-bottom: 0.5rem;
}

.validation-message {
  font-size: 0.8rem;
  font-weight: 500;
}

.validation-message.valid {
  color: #10b981;
}

.validation-message.invalid {
  color: #ef4444;
}

.phone-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-phone-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.edit-phone-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.save-phone-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.save-phone-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.save-phone-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.cancel-phone-btn {
  background: #f3f4f6;
  color: #374151;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.cancel-phone-btn:hover {
  background: #e5e7eb;
}

.phone-note {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #bfdbfe;
  margin-top: 0.5rem;
}

.phone-note small {
  color: #1e40af;
  font-weight: 500;
  line-height: 1.4;
}

.info-label {
  font-weight: 600;
  color: #374151;
}

.info-value {
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-value.updated {
  color: #10b981;
  font-weight: 600;
}

.updated-indicator {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  animation: fadeInScale 0.5s ease-out;
}

.payment-actions {
  display: flex;
  gap: 1rem;
}

.back-btn {
  flex: 1;
  background: #f3f4f6;
  color: #374151;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #e5e7eb;
}

.pay-btn {
  flex: 2;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.pay-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.pay-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Success Step */
.success-step {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.success-animation {
  margin-bottom: 2rem;
}

.pulse-circle {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  animation: pulse 2s infinite;
}

.phone-icon {
  font-size: 3rem;
}

.success-step h3 {
  color: #1f2937;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.success-step p {
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.payment-steps {
  margin-bottom: 2rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.step-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.step-text {
  color: #374151;
  font-weight: 500;
}

.success-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.check-status-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  font-size: 0.9rem;
}

.check-status-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.done-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.done-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 3rem;
}

.spinner, .btn-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(50px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .subscription-modal {
    width: 98%;
    margin: 1%;
  }

  .modal-header {
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.4rem;
  }

  .modal-content {
    padding: 1.5rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .payment-actions {
    flex-direction: column;
  }

  .price-amount {
    font-size: 1.6rem;
  }

  .success-actions {
    flex-direction: column;
  }

  .check-status-btn,
  .done-btn {
    width: 100%;
  }

  .phone-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .phone-actions {
    width: 100%;
  }

  .save-phone-btn,
  .cancel-phone-btn {
    flex: 1;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

/* Simplified Phone Display Styles */
.phone-display-simple {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.valid-phone {
  color: #059669;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.invalid-phone-warning {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.invalid-phone {
  color: #dc2626;
  font-weight: 500;
}

.update-phone-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.update-phone-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.payment-note {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

.payment-note p {
  color: #1e40af;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}
