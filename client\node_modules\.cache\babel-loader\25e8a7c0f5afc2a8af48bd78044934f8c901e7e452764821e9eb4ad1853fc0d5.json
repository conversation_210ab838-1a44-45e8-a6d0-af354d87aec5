{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Plans\\\\Plans.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { message } from \"antd\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport { addPayment, checkPaymentStatus } from \"../../../apicalls/payment\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport WaitingModal from \"./components/WaitingModal\";\nimport ConfirmModal from \"./components/ConfirmModal\";\nimport \"./Plans.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Plans = () => {\n  _s();\n  var _subscriptionData$pla, _subscriptionData$pla2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [selectedPlanId, setSelectedPlanId] = useState(null);\n  const [showWaitingModal, setShowWaitingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [transactionData, setTransactionData] = useState(null);\n  const [paymentOrderId, setPaymentOrderId] = useState(null);\n  const [paymentStatusInterval, setPaymentStatusInterval] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchPlans();\n  }, []);\n\n  // Cleanup interval on component unmount\n  useEffect(() => {\n    return () => {\n      if (paymentStatusInterval) {\n        clearInterval(paymentStatusInterval);\n      }\n    };\n  }, [paymentStatusInterval]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error(\"Error fetching plans:\", error);\n      setError(\"Failed to load plans. Please try again.\");\n      setPlans([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const hasActiveSubscription = () => {\n    if (!subscriptionData) return false;\n    if (subscriptionData.paymentStatus === \"paid\") {\n      if (subscriptionData.status === \"active\") return true;\n      if (subscriptionData.endDate) {\n        const endDate = new Date(subscriptionData.endDate);\n        const now = new Date();\n        return endDate > now;\n      }\n    }\n    return false;\n  };\n  const isActive = hasActiveSubscription();\n\n  // Function to check payment status periodically\n  const checkPaymentConfirmation = async (orderId, planData) => {\n    let attempts = 0;\n    const maxAttempts = 60; // Check for 5 minutes (60 attempts * 5 seconds)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        // Check if response has the expected structure\n        if (response && !response.error) {\n          var _response$plan;\n          console.log('📋 Payment details:', {\n            paymentStatus: response.paymentStatus,\n            status: response.status,\n            plan: ((_response$plan = response.plan) === null || _response$plan === void 0 ? void 0 : _response$plan.title) || 'No plan'\n          });\n          if (response.paymentStatus === 'paid' && response.status === 'active') {\n            // Payment confirmed! Show success modal\n            console.log('✅ Payment confirmed! Showing success modal...');\n            setTransactionData({\n              amount: planData.discountedPrice,\n              planTitle: planData.title,\n              orderId: orderId,\n              status: 'success'\n            });\n            setShowWaitingModal(false);\n            setShowSuccessModal(true);\n\n            // Clear the interval\n            if (paymentStatusInterval) {\n              clearInterval(paymentStatusInterval);\n              setPaymentStatusInterval(null);\n            }\n            return true; // Payment confirmed\n          } else {\n            console.log('⏳ Payment not yet confirmed:', {\n              paymentStatus: response.paymentStatus,\n              status: response.status\n            });\n          }\n        } else {\n          console.log('❌ No subscription found or error:', response.error || 'Unknown error');\n        }\n        if (attempts >= maxAttempts) {\n          // Timeout - stop checking but keep waiting modal open\n          console.log('⏰ Payment status check timeout');\n          if (paymentStatusInterval) {\n            clearInterval(paymentStatusInterval);\n            setPaymentStatusInterval(null);\n          }\n\n          // Show message but keep waiting modal open\n          message.info(\"Payment is still processing. Please wait for SMS confirmation or refresh the page later.\");\n          return false;\n        }\n      } catch (error) {\n        console.error('Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          if (paymentStatusInterval) {\n            clearInterval(paymentStatusInterval);\n            setPaymentStatusInterval(null);\n          }\n        }\n      }\n      return false; // Continue checking\n    };\n\n    // Start checking immediately\n    const confirmed = await checkStatus();\n    if (confirmed) return;\n\n    // Set up interval to check every 5 seconds\n    const interval = setInterval(async () => {\n      const confirmed = await checkStatus();\n      if (confirmed) {\n        clearInterval(interval);\n      }\n    }, 5000);\n    setPaymentStatusInterval(interval);\n  };\n  const handlePlanSelect = async plan => {\n    if (!plan || paymentLoading) return;\n    if (!(user !== null && user !== void 0 && user.phoneNumber)) {\n      message.error(\"Please update your phone number in profile to proceed with payment.\");\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      setSelectedPlanId(plan._id);\n      setShowWaitingModal(true); // Show beautiful waiting modal\n\n      const paymentData = {\n        plan: plan,\n        // Send the complete plan object\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        // Payment initiated successfully - start checking for confirmation\n        setPaymentOrderId(response.order_id);\n        message.success(\"Payment initiated successfully! Please check your phone for SMS confirmation.\");\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id, plan);\n      } else {\n        throw new Error(response.message || \"Payment failed\");\n      }\n    } catch (error) {\n      console.error(\"Payment error:\", error);\n      setShowWaitingModal(false); // Hide waiting modal on error\n      message.error(error.message || \"Payment failed. Please try again.\");\n    } finally {\n      setPaymentLoading(false);\n      setSelectedPlanId(null);\n    }\n  };\n  const formatDate = dateString => {\n    if (!dateString) return \"Not available\";\n    try {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    } catch {\n      return \"Invalid date\";\n    }\n  };\n  const getDaysRemaining = endDate => {\n    if (!endDate) return 0;\n    try {\n      const end = new Date(endDate);\n      const now = new Date();\n      const diffTime = end - now;\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      return Math.max(0, diffDays);\n    } catch {\n      return 0;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Loading Plans...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please wait while we fetch your subscription options\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"retry-button\",\n          onClick: fetchPlans,\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"plans-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"page-title\",\n        children: \"Your Learning Journey\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"page-subtitle\",\n        children: \"Choose the perfect plan to unlock your potential\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }, this), isActive && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"active-subscription\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-badge\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 25\n        }, this), \"Active Subscription\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"subscription-title\",\n          children: (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$pla = subscriptionData.plan) === null || _subscriptionData$pla === void 0 ? void 0 : _subscriptionData$pla.title) || \"Premium Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83D\\uDCC5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.startDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\u23F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Expires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Days Left\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value highlight\",\n                children: [getDaysRemaining(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate), \" days\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83D\\uDC8E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Plan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$pla2 = subscriptionData.plan) === null || _subscriptionData$pla2 === void 0 ? void 0 : _subscriptionData$pla2.title) || \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"benefits-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Your Benefits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefits-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83D\\uDCDA Unlimited Quiz Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83C\\uDFAF Progress Tracking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83C\\uDFC6 Achievement System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83D\\uDE80 AI Study Assistant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"primary-button\",\n            onClick: () => navigate('/user/hub'),\n            children: \"Continue Learning \\uD83C\\uDF93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"secondary-button\",\n            onClick: () => navigate('/user/profile'),\n            children: \"Manage Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 17\n    }, this), !isActive && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Choose Your Plan\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 21\n      }, this), plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-icon\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Plans Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please check back later for subscription options.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"plans-grid\",\n        children: plans.map(plan => {\n          var _plan$title, _plan$discountedPrice, _plan$features, _plan$title2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"plan-title\",\n                children: plan.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 41\n              }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-badge\",\n                children: \"\\uD83D\\uDE80 Quick Start\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-price\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-main\",\n                children: [((_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()) || '0', \" TZS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 41\n              }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-old\",\n                children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-period\",\n                children: plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-features\",\n              children: ((_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-check\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-text\",\n                  children: feature\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 49\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 45\n              }, this))) || /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-check\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-text\",\n                  children: \"Premium access included\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `plan-button ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`,\n              onClick: () => handlePlanSelect(plan),\n              disabled: paymentLoading,\n              children: paymentLoading && selectedPlanId === plan._id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"button-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 49\n                }, this), \"Processing...\"]\n              }, void 0, true) : (_plan$title2 = plan.title) !== null && _plan$title2 !== void 0 && _plan$title2.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 37\n            }, this)]\n          }, plan._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(WaitingModal, {\n      isOpen: showWaitingModal,\n      onClose: () => {\n        setShowWaitingModal(false);\n        // Clean up payment status checking\n        if (paymentStatusInterval) {\n          clearInterval(paymentStatusInterval);\n          setPaymentStatusInterval(null);\n        }\n        setPaymentLoading(false);\n        setSelectedPlanId(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: showSuccessModal,\n      onClose: () => {\n        setShowSuccessModal(false);\n        // Optionally redirect to dashboard or refresh subscription data\n        window.location.reload(); // Refresh to show updated subscription\n      },\n\n      transaction: transactionData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 9\n  }, this);\n};\n_s(Plans, \"pfTe3gWAKwLw2WqM4cNTgfEprgQ=\", false, function () {\n  return [useSelector, useSelector, useDispatch, useNavigate];\n});\n_c = Plans;\nexport default Plans;\nvar _c;\n$RefreshReg$(_c, \"Plans\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "useDispatch", "useNavigate", "message", "getPlans", "addPayment", "checkPaymentStatus", "HideLoading", "ShowLoading", "WaitingModal", "ConfirmModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Plans", "_s", "_subscriptionData$pla", "_subscriptionData$pla2", "plans", "setPlans", "loading", "setLoading", "error", "setError", "paymentLoading", "setPaymentLoading", "selectedPlanId", "setSelectedPlanId", "showWaitingModal", "setShowWaitingModal", "showSuccessModal", "setShowSuccessModal", "transactionData", "setTransactionData", "paymentOrderId", "setPaymentOrderId", "paymentStatusInterval", "setPaymentStatusInterval", "user", "state", "subscriptionData", "subscription", "dispatch", "navigate", "fetchPlans", "clearInterval", "response", "Array", "isArray", "console", "hasActiveSubscription", "paymentStatus", "status", "endDate", "Date", "now", "isActive", "checkPaymentConfirmation", "orderId", "planData", "attempts", "maxAttempts", "checkStatus", "log", "_response$plan", "plan", "title", "amount", "discountedPrice", "planTitle", "info", "confirmed", "interval", "setInterval", "handlePlanSelect", "phoneNumber", "_user$name", "_id", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "success", "order_id", "Error", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "end", "diffTime", "diffDays", "Math", "ceil", "max", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "startDate", "length", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$title2", "includes", "toLocaleString", "actualPrice", "duration", "features", "feature", "index", "disabled", "isOpen", "onClose", "window", "location", "reload", "transaction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Plans/Plans.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { message } from \"antd\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport { addPayment, checkPaymentStatus } from \"../../../apicalls/payment\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport WaitingModal from \"./components/WaitingModal\";\nimport ConfirmModal from \"./components/ConfirmModal\";\nimport \"./Plans.css\";\n\nconst Plans = () => {\n    const [plans, setPlans] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [paymentLoading, setPaymentLoading] = useState(false);\n    const [selectedPlanId, setSelectedPlanId] = useState(null);\n    const [showWaitingModal, setShowWaitingModal] = useState(false);\n    const [showSuccessModal, setShowSuccessModal] = useState(false);\n    const [transactionData, setTransactionData] = useState(null);\n    const [paymentOrderId, setPaymentOrderId] = useState(null);\n    const [paymentStatusInterval, setPaymentStatusInterval] = useState(null);\n    \n    const { user } = useSelector((state) => state.user);\n    const { subscriptionData } = useSelector((state) => state.subscription);\n    const dispatch = useDispatch();\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchPlans();\n    }, []);\n\n    // Cleanup interval on component unmount\n    useEffect(() => {\n        return () => {\n            if (paymentStatusInterval) {\n                clearInterval(paymentStatusInterval);\n            }\n        };\n    }, [paymentStatusInterval]);\n\n    const fetchPlans = async () => {\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await getPlans();\n            setPlans(Array.isArray(response) ? response : []);\n        } catch (error) {\n            console.error(\"Error fetching plans:\", error);\n            setError(\"Failed to load plans. Please try again.\");\n            setPlans([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const hasActiveSubscription = () => {\n        if (!subscriptionData) return false;\n        \n        if (subscriptionData.paymentStatus === \"paid\") {\n            if (subscriptionData.status === \"active\") return true;\n            \n            if (subscriptionData.endDate) {\n                const endDate = new Date(subscriptionData.endDate);\n                const now = new Date();\n                return endDate > now;\n            }\n        }\n        return false;\n    };\n\n    const isActive = hasActiveSubscription();\n\n    // Function to check payment status periodically\n    const checkPaymentConfirmation = async (orderId, planData) => {\n        let attempts = 0;\n        const maxAttempts = 60; // Check for 5 minutes (60 attempts * 5 seconds)\n\n        const checkStatus = async () => {\n            try {\n                attempts++;\n                console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n                const response = await checkPaymentStatus();\n                console.log('📥 Payment status response:', response);\n\n                // Check if response has the expected structure\n                if (response && !response.error) {\n                    console.log('📋 Payment details:', {\n                        paymentStatus: response.paymentStatus,\n                        status: response.status,\n                        plan: response.plan?.title || 'No plan'\n                    });\n\n                    if (response.paymentStatus === 'paid' && response.status === 'active') {\n                        // Payment confirmed! Show success modal\n                        console.log('✅ Payment confirmed! Showing success modal...');\n\n                        setTransactionData({\n                            amount: planData.discountedPrice,\n                            planTitle: planData.title,\n                            orderId: orderId,\n                            status: 'success'\n                        });\n\n                        setShowWaitingModal(false);\n                        setShowSuccessModal(true);\n\n                        // Clear the interval\n                        if (paymentStatusInterval) {\n                            clearInterval(paymentStatusInterval);\n                            setPaymentStatusInterval(null);\n                        }\n\n                        return true; // Payment confirmed\n                    } else {\n                        console.log('⏳ Payment not yet confirmed:', {\n                            paymentStatus: response.paymentStatus,\n                            status: response.status\n                        });\n                    }\n                } else {\n                    console.log('❌ No subscription found or error:', response.error || 'Unknown error');\n                }\n\n                if (attempts >= maxAttempts) {\n                    // Timeout - stop checking but keep waiting modal open\n                    console.log('⏰ Payment status check timeout');\n                    if (paymentStatusInterval) {\n                        clearInterval(paymentStatusInterval);\n                        setPaymentStatusInterval(null);\n                    }\n\n                    // Show message but keep waiting modal open\n                    message.info(\"Payment is still processing. Please wait for SMS confirmation or refresh the page later.\");\n                    return false;\n                }\n\n            } catch (error) {\n                console.error('Error checking payment status:', error);\n\n                if (attempts >= maxAttempts) {\n                    if (paymentStatusInterval) {\n                        clearInterval(paymentStatusInterval);\n                        setPaymentStatusInterval(null);\n                    }\n                }\n            }\n\n            return false; // Continue checking\n        };\n\n        // Start checking immediately\n        const confirmed = await checkStatus();\n        if (confirmed) return;\n\n        // Set up interval to check every 5 seconds\n        const interval = setInterval(async () => {\n            const confirmed = await checkStatus();\n            if (confirmed) {\n                clearInterval(interval);\n            }\n        }, 5000);\n\n        setPaymentStatusInterval(interval);\n    };\n\n    const handlePlanSelect = async (plan) => {\n        if (!plan || paymentLoading) return;\n        \n        if (!user?.phoneNumber) {\n            message.error(\"Please update your phone number in profile to proceed with payment.\");\n            return;\n        }\n\n        try {\n            setPaymentLoading(true);\n            setSelectedPlanId(plan._id);\n            setShowWaitingModal(true); // Show beautiful waiting modal\n\n            const paymentData = {\n                plan: plan,  // Send the complete plan object\n                userId: user._id,\n                userPhone: user.phoneNumber,\n                userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n            };\n\n            const response = await addPayment(paymentData);\n\n            if (response.success) {\n                // Payment initiated successfully - start checking for confirmation\n                setPaymentOrderId(response.order_id);\n\n                message.success(\"Payment initiated successfully! Please check your phone for SMS confirmation.\");\n\n                // Start checking payment status\n                checkPaymentConfirmation(response.order_id, plan);\n\n            } else {\n                throw new Error(response.message || \"Payment failed\");\n            }\n\n        } catch (error) {\n            console.error(\"Payment error:\", error);\n            setShowWaitingModal(false); // Hide waiting modal on error\n            message.error(error.message || \"Payment failed. Please try again.\");\n        } finally {\n            setPaymentLoading(false);\n            setSelectedPlanId(null);\n        }\n    };\n\n    const formatDate = (dateString) => {\n        if (!dateString) return \"Not available\";\n        try {\n            return new Date(dateString).toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            });\n        } catch {\n            return \"Invalid date\";\n        }\n    };\n\n    const getDaysRemaining = (endDate) => {\n        if (!endDate) return 0;\n        try {\n            const end = new Date(endDate);\n            const now = new Date();\n            const diffTime = end - now;\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            return Math.max(0, diffDays);\n        } catch {\n            return 0;\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"plans-page\">\n                <div className=\"loading-container\">\n                    <div className=\"spinner\"></div>\n                    <h3>Loading Plans...</h3>\n                    <p>Please wait while we fetch your subscription options</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"plans-page\">\n                <div className=\"error-container\">\n                    <div className=\"error-icon\">⚠️</div>\n                    <h3>Something went wrong</h3>\n                    <p>{error}</p>\n                    <button className=\"retry-button\" onClick={fetchPlans}>\n                        Try Again\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"plans-page\">\n            <div className=\"page-header\">\n                <h1 className=\"page-title\">Your Learning Journey</h1>\n                <p className=\"page-subtitle\">Choose the perfect plan to unlock your potential</p>\n            </div>\n\n            {isActive && (\n                <div className=\"active-subscription\">\n                    <div className=\"subscription-badge\">\n                        <span className=\"badge-dot\"></span>\n                        Active Subscription\n                    </div>\n                    \n                    <div className=\"subscription-content\">\n                        <h2 className=\"subscription-title\">\n                            {subscriptionData?.plan?.title || \"Premium Plan\"}\n                        </h2>\n                        \n                        <div className=\"subscription-grid\">\n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">📅</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Started</span>\n                                    <span className=\"info-value\">{formatDate(subscriptionData?.startDate)}</span>\n                                </div>\n                            </div>\n                            \n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">⏰</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Expires</span>\n                                    <span className=\"info-value\">{formatDate(subscriptionData?.endDate)}</span>\n                                </div>\n                            </div>\n                            \n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">🎯</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Days Left</span>\n                                    <span className=\"info-value highlight\">\n                                        {getDaysRemaining(subscriptionData?.endDate)} days\n                                    </span>\n                                </div>\n                            </div>\n                            \n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">💎</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Plan Type</span>\n                                    <span className=\"info-value\">{subscriptionData?.plan?.title || \"Premium\"}</span>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"benefits-section\">\n                            <h3>Your Benefits</h3>\n                            <div className=\"benefits-list\">\n                                <div className=\"benefit\">📚 Unlimited Quiz Access</div>\n                                <div className=\"benefit\">🎯 Progress Tracking</div>\n                                <div className=\"benefit\">🏆 Achievement System</div>\n                                <div className=\"benefit\">🚀 AI Study Assistant</div>\n                            </div>\n                        </div>\n\n                        <div className=\"action-buttons\">\n                            <button \n                                className=\"primary-button\"\n                                onClick={() => navigate('/user/hub')}\n                            >\n                                Continue Learning 🎓\n                            </button>\n                            <button \n                                className=\"secondary-button\"\n                                onClick={() => navigate('/user/profile')}\n                            >\n                                Manage Account\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {!isActive && (\n                <div className=\"plans-section\">\n                    <h2 className=\"section-title\">Choose Your Plan</h2>\n                    \n                    {plans.length === 0 ? (\n                        <div className=\"no-plans\">\n                            <div className=\"no-plans-icon\">📋</div>\n                            <h3>No Plans Available</h3>\n                            <p>Please check back later for subscription options.</p>\n                        </div>\n                    ) : (\n                        <div className=\"plans-grid\">\n                            {plans.map((plan) => (\n                                <div key={plan._id} className=\"plan-card\">\n                                    <div className=\"plan-header\">\n                                        <h3 className=\"plan-title\">{plan.title}</h3>\n                                        {plan.title?.toLowerCase().includes('glimp') && (\n                                            <div className=\"plan-badge\">🚀 Quick Start</div>\n                                        )}\n                                    </div>\n\n                                    <div className=\"plan-price\">\n                                        <div className=\"price-main\">\n                                            {plan.discountedPrice?.toLocaleString() || '0'} TZS\n                                        </div>\n                                        {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                                            <div className=\"price-old\">\n                                                {plan.actualPrice.toLocaleString()} TZS\n                                            </div>\n                                        )}\n                                        <div className=\"price-period\">\n                                            {plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'}\n                                        </div>\n                                    </div>\n\n                                    <div className=\"plan-features\">\n                                        {plan.features?.map((feature, index) => (\n                                            <div key={index} className=\"feature\">\n                                                <span className=\"feature-check\">✓</span>\n                                                <span className=\"feature-text\">{feature}</span>\n                                            </div>\n                                        )) || (\n                                            <div className=\"feature\">\n                                                <span className=\"feature-check\">✓</span>\n                                                <span className=\"feature-text\">Premium access included</span>\n                                            </div>\n                                        )}\n                                    </div>\n\n                                    <button\n                                        className={`plan-button ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`}\n                                        onClick={() => handlePlanSelect(plan)}\n                                        disabled={paymentLoading}\n                                    >\n                                        {paymentLoading && selectedPlanId === plan._id ? (\n                                            <>\n                                                <span className=\"button-spinner\"></span>\n                                                Processing...\n                                            </>\n                                        ) : (\n                                            plan.title?.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'\n                                        )}\n                                    </button>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n            )}\n\n            {/* Beautiful Payment Modals */}\n            <WaitingModal\n                isOpen={showWaitingModal}\n                onClose={() => {\n                    setShowWaitingModal(false);\n                    // Clean up payment status checking\n                    if (paymentStatusInterval) {\n                        clearInterval(paymentStatusInterval);\n                        setPaymentStatusInterval(null);\n                    }\n                    setPaymentLoading(false);\n                    setSelectedPlanId(null);\n                }}\n            />\n\n            <ConfirmModal\n                isOpen={showSuccessModal}\n                onClose={() => {\n                    setShowSuccessModal(false);\n                    // Optionally redirect to dashboard or refresh subscription data\n                    window.location.reload(); // Refresh to show updated subscription\n                }}\n                transaction={transactionData}\n            />\n        </div>\n    );\n};\n\nexport default Plans;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAChB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAExE,MAAM;IAAEwC;EAAK,CAAC,GAAGvC,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGzC,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAE9BJ,SAAS,CAAC,MAAM;IACZ+C,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/C,SAAS,CAAC,MAAM;IACZ,OAAO,MAAM;MACT,IAAIuC,qBAAqB,EAAE;QACvBS,aAAa,CAACT,qBAAqB,CAAC;MACxC;IACJ,CAAC;EACL,CAAC,EAAE,CAACA,qBAAqB,CAAC,CAAC;EAE3B,MAAMQ,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACAvB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMuB,QAAQ,GAAG,MAAM3C,QAAQ,CAAC,CAAC;MACjCgB,QAAQ,CAAC4B,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACZ2B,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,yCAAyC,CAAC;MACnDJ,QAAQ,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM6B,qBAAqB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACV,gBAAgB,EAAE,OAAO,KAAK;IAEnC,IAAIA,gBAAgB,CAACW,aAAa,KAAK,MAAM,EAAE;MAC3C,IAAIX,gBAAgB,CAACY,MAAM,KAAK,QAAQ,EAAE,OAAO,IAAI;MAErD,IAAIZ,gBAAgB,CAACa,OAAO,EAAE;QAC1B,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAACd,gBAAgB,CAACa,OAAO,CAAC;QAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,OAAOD,OAAO,GAAGE,GAAG;MACxB;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EAED,MAAMC,QAAQ,GAAGN,qBAAqB,CAAC,CAAC;;EAExC;EACA,MAAMO,wBAAwB,GAAG,MAAAA,CAAOC,OAAO,EAAEC,QAAQ,KAAK;IAC1D,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;;IAExB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACAF,QAAQ,EAAE;QACVX,OAAO,CAACc,GAAG,CAAE,yCAAwCH,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAMf,QAAQ,GAAG,MAAMzC,kBAAkB,CAAC,CAAC;QAC3C4C,OAAO,CAACc,GAAG,CAAC,6BAA6B,EAAEjB,QAAQ,CAAC;;QAEpD;QACA,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACxB,KAAK,EAAE;UAAA,IAAA0C,cAAA;UAC7Bf,OAAO,CAACc,GAAG,CAAC,qBAAqB,EAAE;YAC/BZ,aAAa,EAAEL,QAAQ,CAACK,aAAa;YACrCC,MAAM,EAAEN,QAAQ,CAACM,MAAM;YACvBa,IAAI,EAAE,EAAAD,cAAA,GAAAlB,QAAQ,CAACmB,IAAI,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,KAAK,KAAI;UAClC,CAAC,CAAC;UAEF,IAAIpB,QAAQ,CAACK,aAAa,KAAK,MAAM,IAAIL,QAAQ,CAACM,MAAM,KAAK,QAAQ,EAAE;YACnE;YACAH,OAAO,CAACc,GAAG,CAAC,+CAA+C,CAAC;YAE5D9B,kBAAkB,CAAC;cACfkC,MAAM,EAAER,QAAQ,CAACS,eAAe;cAChCC,SAAS,EAAEV,QAAQ,CAACO,KAAK;cACzBR,OAAO,EAAEA,OAAO;cAChBN,MAAM,EAAE;YACZ,CAAC,CAAC;YAEFvB,mBAAmB,CAAC,KAAK,CAAC;YAC1BE,mBAAmB,CAAC,IAAI,CAAC;;YAEzB;YACA,IAAIK,qBAAqB,EAAE;cACvBS,aAAa,CAACT,qBAAqB,CAAC;cACpCC,wBAAwB,CAAC,IAAI,CAAC;YAClC;YAEA,OAAO,IAAI,CAAC,CAAC;UACjB,CAAC,MAAM;YACHY,OAAO,CAACc,GAAG,CAAC,8BAA8B,EAAE;cACxCZ,aAAa,EAAEL,QAAQ,CAACK,aAAa;cACrCC,MAAM,EAAEN,QAAQ,CAACM;YACrB,CAAC,CAAC;UACN;QACJ,CAAC,MAAM;UACHH,OAAO,CAACc,GAAG,CAAC,mCAAmC,EAAEjB,QAAQ,CAACxB,KAAK,IAAI,eAAe,CAAC;QACvF;QAEA,IAAIsC,QAAQ,IAAIC,WAAW,EAAE;UACzB;UACAZ,OAAO,CAACc,GAAG,CAAC,gCAAgC,CAAC;UAC7C,IAAI3B,qBAAqB,EAAE;YACvBS,aAAa,CAACT,qBAAqB,CAAC;YACpCC,wBAAwB,CAAC,IAAI,CAAC;UAClC;;UAEA;UACAnC,OAAO,CAACoE,IAAI,CAAC,0FAA0F,CAAC;UACxG,OAAO,KAAK;QAChB;MAEJ,CAAC,CAAC,OAAOhD,KAAK,EAAE;QACZ2B,OAAO,CAAC3B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QAEtD,IAAIsC,QAAQ,IAAIC,WAAW,EAAE;UACzB,IAAIzB,qBAAqB,EAAE;YACvBS,aAAa,CAACT,qBAAqB,CAAC;YACpCC,wBAAwB,CAAC,IAAI,CAAC;UAClC;QACJ;MACJ;MAEA,OAAO,KAAK,CAAC,CAAC;IAClB,CAAC;;IAED;IACA,MAAMkC,SAAS,GAAG,MAAMT,WAAW,CAAC,CAAC;IACrC,IAAIS,SAAS,EAAE;;IAEf;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,YAAY;MACrC,MAAMF,SAAS,GAAG,MAAMT,WAAW,CAAC,CAAC;MACrC,IAAIS,SAAS,EAAE;QACX1B,aAAa,CAAC2B,QAAQ,CAAC;MAC3B;IACJ,CAAC,EAAE,IAAI,CAAC;IAERnC,wBAAwB,CAACmC,QAAQ,CAAC;EACtC,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAOT,IAAI,IAAK;IACrC,IAAI,CAACA,IAAI,IAAIzC,cAAc,EAAE;IAE7B,IAAI,EAACc,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqC,WAAW,GAAE;MACpBzE,OAAO,CAACoB,KAAK,CAAC,qEAAqE,CAAC;MACpF;IACJ;IAEA,IAAI;MAAA,IAAAsD,UAAA;MACAnD,iBAAiB,CAAC,IAAI,CAAC;MACvBE,iBAAiB,CAACsC,IAAI,CAACY,GAAG,CAAC;MAC3BhD,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE3B,MAAMiD,WAAW,GAAG;QAChBb,IAAI,EAAEA,IAAI;QAAG;QACbc,MAAM,EAAEzC,IAAI,CAACuC,GAAG;QAChBG,SAAS,EAAE1C,IAAI,CAACqC,WAAW;QAC3BM,SAAS,EAAE3C,IAAI,CAAC4C,KAAK,IAAK,IAAAN,UAAA,GAAEtC,IAAI,CAAC6C,IAAI,cAAAP,UAAA,uBAATA,UAAA,CAAWQ,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC7E,CAAC;MAED,MAAMvC,QAAQ,GAAG,MAAM1C,UAAU,CAAC0E,WAAW,CAAC;MAE9C,IAAIhC,QAAQ,CAACwC,OAAO,EAAE;QAClB;QACAnD,iBAAiB,CAACW,QAAQ,CAACyC,QAAQ,CAAC;QAEpCrF,OAAO,CAACoF,OAAO,CAAC,+EAA+E,CAAC;;QAEhG;QACA7B,wBAAwB,CAACX,QAAQ,CAACyC,QAAQ,EAAEtB,IAAI,CAAC;MAErD,CAAC,MAAM;QACH,MAAM,IAAIuB,KAAK,CAAC1C,QAAQ,CAAC5C,OAAO,IAAI,gBAAgB,CAAC;MACzD;IAEJ,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACZ2B,OAAO,CAAC3B,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCO,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC5B3B,OAAO,CAACoB,KAAK,CAACA,KAAK,CAACpB,OAAO,IAAI,mCAAmC,CAAC;IACvE,CAAC,SAAS;MACNuB,iBAAiB,CAAC,KAAK,CAAC;MACxBE,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAM8D,UAAU,GAAIC,UAAU,IAAK;IAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,eAAe;IACvC,IAAI;MACA,OAAO,IAAIpC,IAAI,CAACoC,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACpDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACT,CAAC,CAAC;IACN,CAAC,CAAC,MAAM;MACJ,OAAO,cAAc;IACzB;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAI1C,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IACtB,IAAI;MACA,MAAM2C,GAAG,GAAG,IAAI1C,IAAI,CAACD,OAAO,CAAC;MAC7B,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAM2C,QAAQ,GAAGD,GAAG,GAAGzC,GAAG;MAC1B,MAAM2C,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;IAChC,CAAC,CAAC,MAAM;MACJ,OAAO,CAAC;IACZ;EACJ,CAAC;EAED,IAAI9E,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK2F,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvB5F,OAAA;QAAK2F,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9B5F,OAAA;UAAK2F,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/BhG,OAAA;UAAA4F,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBhG,OAAA;UAAA4F,QAAA,EAAG;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIrF,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK2F,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvB5F,OAAA;QAAK2F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5B5F,OAAA;UAAK2F,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpChG,OAAA;UAAA4F,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BhG,OAAA;UAAA4F,QAAA,EAAIjF;QAAK;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdhG,OAAA;UAAQ2F,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEhE,UAAW;UAAA2D,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIhG,OAAA;IAAK2F,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACvB5F,OAAA;MAAK2F,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxB5F,OAAA;QAAI2F,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrDhG,OAAA;QAAG2F,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChF,CAAC,EAELnD,QAAQ,iBACL7C,OAAA;MAAK2F,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChC5F,OAAA;QAAK2F,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC/B5F,OAAA;UAAM2F,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,uBAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAENhG,OAAA;QAAK2F,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjC5F,OAAA;UAAI2F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC7B,CAAA/D,gBAAgB,aAAhBA,gBAAgB,wBAAAxB,qBAAA,GAAhBwB,gBAAgB,CAAEyB,IAAI,cAAAjD,qBAAA,uBAAtBA,qBAAA,CAAwBkD,KAAK,KAAI;QAAc;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAELhG,OAAA;UAAK2F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9B5F,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5F,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5F,OAAA;gBAAM2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3ChG,OAAA;gBAAM2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEd,UAAU,CAACjD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqE,SAAS;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5F,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClChG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5F,OAAA;gBAAM2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3ChG,OAAA;gBAAM2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEd,UAAU,CAACjD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEa,OAAO;cAAC;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5F,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5F,OAAA;gBAAM2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ChG,OAAA;gBAAM2F,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,GACjCR,gBAAgB,CAACvD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEa,OAAO,CAAC,EAAC,OACjD;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhG,OAAA;YAAK2F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5F,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChG,OAAA;cAAK2F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5F,OAAA;gBAAM2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ChG,OAAA;gBAAM2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE,CAAA/D,gBAAgB,aAAhBA,gBAAgB,wBAAAvB,sBAAA,GAAhBuB,gBAAgB,CAAEyB,IAAI,cAAAhD,sBAAA,uBAAtBA,sBAAA,CAAwBiD,KAAK,KAAI;cAAS;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhG,OAAA;UAAK2F,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7B5F,OAAA;YAAA4F,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBhG,OAAA;YAAK2F,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B5F,OAAA;cAAK2F,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDhG,OAAA;cAAK2F,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDhG,OAAA;cAAK2F,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpDhG,OAAA;cAAK2F,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhG,OAAA;UAAK2F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3B5F,OAAA;YACI2F,SAAS,EAAC,gBAAgB;YAC1BM,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,WAAW,CAAE;YAAA4D,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA;YACI2F,SAAS,EAAC,kBAAkB;YAC5BM,OAAO,EAAEA,CAAA,KAAMjE,QAAQ,CAAC,eAAe,CAAE;YAAA4D,QAAA,EAC5C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEA,CAACnD,QAAQ,iBACN7C,OAAA;MAAK2F,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B5F,OAAA;QAAI2F,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAElDzF,KAAK,CAAC4F,MAAM,KAAK,CAAC,gBACfnG,OAAA;QAAK2F,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrB5F,OAAA;UAAK2F,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvChG,OAAA;UAAA4F,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BhG,OAAA;UAAA4F,QAAA,EAAG;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,gBAENhG,OAAA;QAAK2F,SAAS,EAAC,YAAY;QAAAC,QAAA,EACtBrF,KAAK,CAAC6F,GAAG,CAAE9C,IAAI;UAAA,IAAA+C,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,YAAA;UAAA,oBACZxG,OAAA;YAAoB2F,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACrC5F,OAAA;cAAK2F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB5F,OAAA;gBAAI2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEtC,IAAI,CAACC;cAAK;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAC3C,EAAAK,WAAA,GAAA/C,IAAI,CAACC,KAAK,cAAA8C,WAAA,uBAAVA,WAAA,CAAY3B,WAAW,CAAC,CAAC,CAAC+B,QAAQ,CAAC,OAAO,CAAC,kBACxCzG,OAAA;gBAAK2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAClD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAENhG,OAAA;cAAK2F,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB5F,OAAA;gBAAK2F,SAAS,EAAC,YAAY;gBAAAC,QAAA,GACtB,EAAAU,qBAAA,GAAAhD,IAAI,CAACG,eAAe,cAAA6C,qBAAA,uBAApBA,qBAAA,CAAsBI,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,MACnD;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACL1C,IAAI,CAACqD,WAAW,IAAIrD,IAAI,CAACqD,WAAW,KAAKrD,IAAI,CAACG,eAAe,iBAC1DzD,OAAA;gBAAK2F,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACrBtC,IAAI,CAACqD,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MACvC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACR,eACDhG,OAAA;gBAAK2F,SAAS,EAAC,cAAc;gBAAAC,QAAA,EACxBtC,IAAI,CAACsD,QAAQ,GAAI,GAAEtD,IAAI,CAACsD,QAAS,SAAQtD,IAAI,CAACsD,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG,EAAC,GAAG;cAAU;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhG,OAAA;cAAK2F,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzB,EAAAW,cAAA,GAAAjD,IAAI,CAACuD,QAAQ,cAAAN,cAAA,uBAAbA,cAAA,CAAeH,GAAG,CAAC,CAACU,OAAO,EAAEC,KAAK,kBAC/B/G,OAAA;gBAAiB2F,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAChC5F,OAAA;kBAAM2F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxChG,OAAA;kBAAM2F,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEkB;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFzCe,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACR,CAAC,kBACEhG,OAAA;gBAAK2F,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpB5F,OAAA;kBAAM2F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxChG,OAAA;kBAAM2F,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAENhG,OAAA;cACI2F,SAAS,EAAG,eAAc9E,cAAc,IAAIE,cAAc,KAAKuC,IAAI,CAACY,GAAG,GAAG,SAAS,GAAG,EAAG,EAAE;cAC3F+B,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAACT,IAAI,CAAE;cACtC0D,QAAQ,EAAEnG,cAAe;cAAA+E,QAAA,EAExB/E,cAAc,IAAIE,cAAc,KAAKuC,IAAI,CAACY,GAAG,gBAC1ClE,OAAA,CAAAE,SAAA;gBAAA0F,QAAA,gBACI5F,OAAA;kBAAM2F,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAE5C;cAAA,eAAE,CAAC,GAEH,CAAAQ,YAAA,GAAAlD,IAAI,CAACC,KAAK,cAAAiD,YAAA,eAAVA,YAAA,CAAY9B,WAAW,CAAC,CAAC,CAAC+B,QAAQ,CAAC,OAAO,CAAC,GAAG,gBAAgB,GAAG;YACpE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAjDH1C,IAAI,CAACY,GAAG;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkDb,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,eAGDhG,OAAA,CAACH,YAAY;MACToH,MAAM,EAAEhG,gBAAiB;MACzBiG,OAAO,EAAEA,CAAA,KAAM;QACXhG,mBAAmB,CAAC,KAAK,CAAC;QAC1B;QACA,IAAIO,qBAAqB,EAAE;UACvBS,aAAa,CAACT,qBAAqB,CAAC;UACpCC,wBAAwB,CAAC,IAAI,CAAC;QAClC;QACAZ,iBAAiB,CAAC,KAAK,CAAC;QACxBE,iBAAiB,CAAC,IAAI,CAAC;MAC3B;IAAE;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEFhG,OAAA,CAACF,YAAY;MACTmH,MAAM,EAAE9F,gBAAiB;MACzB+F,OAAO,EAAEA,CAAA,KAAM;QACX9F,mBAAmB,CAAC,KAAK,CAAC;QAC1B;QACA+F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAE;;MACFC,WAAW,EAAEjG;IAAgB;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC5F,EAAA,CAjbID,KAAK;EAAA,QAYUf,WAAW,EACCA,WAAW,EACvBC,WAAW,EACXC,WAAW;AAAA;AAAAiI,EAAA,GAf1BpH,KAAK;AAmbX,eAAeA,KAAK;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}