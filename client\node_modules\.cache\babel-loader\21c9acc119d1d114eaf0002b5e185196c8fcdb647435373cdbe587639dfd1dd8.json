{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Profile\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./index.css\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { getUserInfo, updateUserInfo, updateUserPhoto, sendOTP } from \"../../../apicalls/users\";\nimport { Form, message, Modal, Input, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _userRankingStats$use, _userRankingStats$use2, _userRankingStats$use3, _userRankingStats$use4, _userRankingStats$use5;\n  const [userDetails, setUserDetails] = useState(null);\n  const [rankingData, setRankingData] = useState(null);\n  const [userRanking, setUserRanking] = useState(null);\n  const [userRankingStats, setUserRankingStats] = useState(null);\n  const [edit, setEdit] = useState(false);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    school: \"\",\n    level: \"\",\n    class_: \"\",\n    phoneNumber: \"\"\n  });\n  const [profileImage, setProfileImage] = useState(null);\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\n  const dispatch = useDispatch();\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      message.error(error.message);\n      dispatch(HideLoading());\n    }\n  };\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userDetails._id));\n    setUserRanking(Ranking);\n  };\n\n  // Fetch user ranking data from the ranking system\n  const fetchUserRankingData = async () => {\n    if (!(userDetails !== null && userDetails !== void 0 && userDetails._id)) return;\n    try {\n      dispatch(ShowLoading());\n\n      // Get user's ranking position and nearby users\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\n      if (rankingResponse.success) {\n        setUserRankingStats(rankingResponse.data);\n      }\n\n      // Also get the full leaderboard to find user's position\n      const leaderboardResponse = await getXPLeaderboard({\n        limit: 1000,\n        levelFilter: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'all'\n      });\n      if (leaderboardResponse.success) {\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\n        if (userIndex >= 0) {\n          const userWithRank = {\n            ...leaderboardResponse.data[userIndex],\n            rank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length\n          };\n          setUserRankingStats(prev => ({\n            ...prev,\n            userRank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length,\n            user: userWithRank\n          }));\n        }\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Error fetching ranking data:', error);\n    }\n  };\n  useEffect(() => {\n    if (rankingData && userDetails) {\n      getUserStats();\n    }\n  }, [rankingData, userDetails]);\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        setUserDetails(response.data);\n        setFormData({\n          name: response.data.name || \"\",\n          email: response.data.email || \"\",\n          school: response.data.school || \"\",\n          class_: response.data.class || \"\",\n          level: response.data.level || \"\",\n          phoneNumber: response.data.phoneNumber || \"\"\n        });\n        if (response.data.profileImage) {\n          setProfileImage(response.data.profileImage);\n        }\n        fetchReports();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === \"phoneNumber\" && value.length > 10) return;\n    if (name === \"level\" && value !== (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) && value !== \"\") {\n      setPendingLevelChange(value);\n      setShowLevelChangeModal(true);\n      return;\n    }\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n      ...(name === \"level\" ? {\n        class_: \"\"\n      } : {})\n    }));\n  };\n  const discardChanges = () => {\n    setFormData({\n      name: userDetails.name,\n      email: userDetails.email,\n      school: userDetails.school,\n      class_: userDetails.class,\n      level: userDetails.level,\n      phoneNumber: userDetails.phoneNumber\n    });\n    setEdit(false);\n  };\n  const sendOTPRequest = async email => {\n    dispatch(ShowLoading());\n    try {\n      const response = await sendOTP({\n        email\n      });\n      if (response.success) {\n        message.success(\"Please verify new email!\");\n        setEdit(false);\n        setServerGeneratedOTP(response.data);\n      } else {\n        message.error(response.message);\n        discardChanges();\n      }\n    } catch (error) {\n      message.error(error.message);\n      discardChanges();\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleUpdate = async ({\n    skipOTP\n  } = {}) => {\n    console.log('🔍 Current formData:', formData);\n    console.log('🔍 Current userDetails:', userDetails);\n\n    // Validation\n    if (!formData.name || formData.name.trim() === \"\") {\n      console.log('❌ Validation failed: name is empty');\n      return message.error(\"Please enter your name.\");\n    }\n    if (!formData.class_ || formData.class_.trim() === \"\") {\n      console.log('❌ Validation failed: class is empty');\n      return message.error(\"Please select a class.\");\n    }\n    if (!formData.level || formData.level.trim() === \"\") {\n      console.log('❌ Validation failed: level is empty');\n      return message.error(\"Please select a level.\");\n    }\n    if (!formData.email || formData.email.trim() === \"\") {\n      console.log('❌ Validation failed: email is empty');\n      console.log('Email value:', `\"${formData.email}\"`);\n      return message.error(\"Please enter your email.\");\n    }\n\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(formData.email)) {\n      return message.error(\"Please enter a valid email address.\");\n    }\n    if (!skipOTP && formData.email !== userDetails.email) {\n      return sendOTPRequest(formData.email);\n    }\n    dispatch(ShowLoading());\n    try {\n      // Ensure email is set (fallback to userDetails.email if formData.email is empty)\n      const updatePayload = {\n        ...formData,\n        email: formData.email || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || \"\",\n        userId: userDetails._id\n      };\n      console.log('📤 Sending update data:', updatePayload);\n      const response = await updateUserInfo(updatePayload);\n      console.log('📥 Server response:', response);\n      if (response.success) {\n        message.success(response.message);\n        setEdit(false);\n        setServerGeneratedOTP(null);\n        getUserData();\n        if (response.levelChanged) {\n          setTimeout(() => window.location.reload(), 2000);\n        }\n      } else {\n        console.error('❌ Update failed:', response);\n        message.error(response.message || \"Failed to update profile. Please try again.\");\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Update error:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || \"An unexpected error occurred.\";\n      message.error(`Update failed: ${errorMessage}`);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleLevelChangeConfirm = () => {\n    setFormData(prev => ({\n      ...prev,\n      level: pendingLevelChange,\n      class_: \"\"\n    }));\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleLevelChangeCancel = () => {\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleImageChange = async e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        message.error('Please select a valid image file');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        message.error('Image size should be less than 5MB');\n        return;\n      }\n      setProfileImage(file);\n\n      // Show preview\n      const reader = new FileReader();\n      reader.onloadend = () => setImagePreview(reader.result);\n      reader.readAsDataURL(file);\n\n      // Auto-upload the image\n      const data = new FormData();\n      data.append(\"profileImage\", file);\n      dispatch(ShowLoading());\n      try {\n        const response = await updateUserPhoto(data);\n        dispatch(HideLoading());\n        if (response.success) {\n          message.success(\"Profile picture updated successfully!\");\n          getUserData(); // Refresh user data to show new image\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message || \"Failed to update profile picture\");\n      }\n    }\n  };\n  const handleImageUpload = async () => {\n    const data = new FormData();\n    data.append(\"profileImage\", profileImage);\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserPhoto(data);\n      if (response.success) {\n        message.success(\"Photo updated successfully!\");\n        getUserData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const verifyUser = async values => {\n    if (values.otp === serverGeneratedOTP) {\n      handleUpdate({\n        skipOTP: true\n      });\n    } else {\n      message.error(\"Invalid OTP\");\n    }\n  };\n\n  // Load user data on component mount\n  useEffect(() => {\n    getUserData();\n  }, []);\n\n  // Load ranking data when user details are available\n  useEffect(() => {\n    if (userDetails) {\n      fetchUserRankingData();\n    }\n  }, [userDetails]);\n\n  // Ensure formData is synchronized with userDetails\n  useEffect(() => {\n    if (userDetails) {\n      setFormData({\n        name: userDetails.name || \"\",\n        email: userDetails.email || \"\",\n        school: userDetails.school || \"\",\n        class_: userDetails.class || \"\",\n        level: userDetails.level || \"\",\n        phoneNumber: userDetails.phoneNumber || \"\"\n      });\n    }\n  }, [userDetails]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold text-gray-900 mb-2\",\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your account settings and preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mt-8 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: userDetails,\n                size: \"3xl\",\n                showOnlineStatus: true,\n                onClick: () => document.getElementById('profileImageInput').click(),\n                className: \"hover:scale-105 transition-transform duration-200\",\n                style: {\n                  width: '120px',\n                  height: '120px',\n                  border: '4px solid #BFDBFE',\n                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\",\n                onClick: () => document.getElementById('profileImageInput').click(),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 13a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"profileImageInput\",\n                type: \"file\",\n                accept: \"image/*\",\n                className: \"hidden\",\n                onChange: handleImageChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-blue-600 font-medium\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-green-600 font-medium\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900 truncate max-w-[150px]\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || '<EMAIL>'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-purple-600 font-medium\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this), userRankingStats && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-yellow-600 font-medium\",\n                    children: \"Rank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [\"#\", userRankingStats.userRank || 'N/A', userRankingStats.totalUsers && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"/\", userRankingStats.totalUsers]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-orange-600 font-medium\",\n                    children: \"Total XP\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use = userRankingStats.user) === null || _userRankingStats$use === void 0 ? void 0 : (_userRankingStats$use2 = _userRankingStats$use.totalXP) === null || _userRankingStats$use2 === void 0 ? void 0 : _userRankingStats$use2.toLocaleString()) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-indigo-600 font-medium\",\n                    children: \"Avg Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [((_userRankingStats$use3 = userRankingStats.user) === null || _userRankingStats$use3 === void 0 ? void 0 : _userRankingStats$use3.averageScore) || '0', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-pink-600 font-medium\",\n                    children: \"Quizzes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use4 = userRankingStats.user) === null || _userRankingStats$use4 === void 0 ? void 0 : _userRankingStats$use4.totalQuizzesTaken) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-teal-600 font-medium\",\n                    children: \"Streak\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use5 = userRankingStats.user) === null || _userRankingStats$use5 === void 0 ? void 0 : _userRankingStats$use5.currentStreak) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), !edit ?\n            /*#__PURE__*/\n            // View Mode\n            _jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // Edit Mode\n            _jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter your name\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    name: \"email\",\n                    value: formData.email,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter your email\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"school\",\n                    value: formData.school,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter your school\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 570,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"level\",\n                    value: formData.level,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Level\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Primary\",\n                      children: \"Primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 579,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Secondary\",\n                      children: \"Secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 580,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Advance\",\n                      children: \"Advance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 581,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"class_\",\n                    value: formData.class_,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Class\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 25\n                    }, this), formData.level === \"Primary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"1\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"2\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 597,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"3\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"4\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 599,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"5\",\n                        children: \"5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"6\",\n                        children: \"6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"7\",\n                        children: \"7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 602,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), formData.level === \"Secondary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"1\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 607,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"2\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"3\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"4\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), formData.level === \"Advance\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"5\",\n                        children: \"5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"6\",\n                        children: \"6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 616,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"phoneNumber\",\n                    value: formData.phoneNumber,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter phone number\",\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 flex justify-center gap-4\",\n              children: !edit ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  // Ensure formData is properly initialized with current user data\n                  setFormData({\n                    name: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || \"\",\n                    email: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || \"\",\n                    school: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || \"\",\n                    class_: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || \"\",\n                    level: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || \"\",\n                    phoneNumber: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || \"\"\n                  });\n                  setEdit(true);\n                },\n                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\",\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: discardChanges,\n                  className: \"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium\",\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleUpdate,\n                  className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\",\n                  children: \"Save Changes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    console.log('🔍 Debug - Current formData:', formData);\n                    console.log('🔍 Debug - Current userDetails:', userDetails);\n                    alert(`FormData: ${JSON.stringify(formData, null, 2)}`);\n                  },\n                  className: \"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm\",\n                  children: \"Debug\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      id: \"profileImageInput\",\n      accept: \"image/*\",\n      onChange: handleImageChange,\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 691,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Confirm Level Change\",\n      open: showLevelChangeModal,\n      onOk: handleLevelChangeConfirm,\n      onCancel: () => {\n        setShowLevelChangeModal(false);\n        setPendingLevelChange(null);\n      },\n      okText: \"Confirm\",\n      cancelText: \"Cancel\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Are you sure you want to change your level to \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: pendingLevelChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 57\n        }, this), \"?\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 711,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-orange-600 text-sm mt-2\",\n        children: \"Note: Changing your level will reset your class selection and you'll only have access to content for the new level.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 7\n    }, this), serverGeneratedOTP && /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Verify Email Change\",\n      open: !!serverGeneratedOTP,\n      onOk: () => {\n        const enteredOTP = document.getElementById('otpInput').value;\n        if (enteredOTP === serverGeneratedOTP.toString()) {\n          handleUpdate({\n            skipOTP: true\n          });\n        } else {\n          message.error(\"Invalid OTP. Please try again.\");\n        }\n      },\n      onCancel: () => {\n        setServerGeneratedOTP(null);\n        discardChanges();\n      },\n      okText: \"Verify\",\n      cancelText: \"Cancel\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please enter the OTP sent to your new email address:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          id: \"otpInput\",\n          placeholder: \"Enter OTP\",\n          maxLength: 6,\n          className: \"text-center text-lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Check your email for the verification code.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 377,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"dhYp44k6pfWzzlwWyXBQYHs3eRE=\", false, function () {\n  return [useDispatch];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Page<PERSON><PERSON>le", "getUserInfo", "updateUserInfo", "updateUserPhoto", "sendOTP", "Form", "message", "Modal", "Input", "<PERSON><PERSON>", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsForRanking", "getUserRanking", "getXPLeaderboard", "ProfilePicture", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Profile", "_s", "_userRankingStats$use", "_userRankingStats$use2", "_userRankingStats$use3", "_userRankingStats$use4", "_userRankingStats$use5", "userDetails", "setUserDetails", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userRankingStats", "setUserRankingStats", "edit", "setEdit", "imagePreview", "setImagePreview", "formData", "setFormData", "name", "email", "school", "level", "class_", "phoneNumber", "profileImage", "setProfileImage", "serverGeneratedOTP", "setServerGeneratedOTP", "showLevelChangeModal", "setShowLevelChangeModal", "pendingLevelChange", "setPendingLevelChange", "dispatch", "fetchReports", "response", "success", "data", "error", "getUserStats", "Ranking", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "fetchUserRankingData", "rankingResponse", "leaderboardResponse", "limit", "levelFilter", "userIndex", "findIndex", "userWithRank", "rank", "totalUsers", "length", "prev", "userRank", "console", "getUserData", "class", "localStorage", "getItem", "handleChange", "e", "value", "target", "discardChanges", "sendOTPRequest", "handleUpdate", "skipOTP", "log", "trim", "emailRegex", "test", "updatePayload", "levelChanged", "setTimeout", "window", "location", "reload", "_error$response", "_error$response$data", "errorMessage", "handleLevelChangeConfirm", "handleLevelChangeCancel", "handleImageChange", "file", "files", "type", "startsWith", "size", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "FormData", "append", "handleImageUpload", "verifyUser", "values", "otp", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showOnlineStatus", "onClick", "document", "getElementById", "click", "style", "width", "height", "border", "boxShadow", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "id", "accept", "onChange", "totalXP", "toLocaleString", "averageScore", "totalQuizzesTaken", "currentStreak", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "alert", "JSON", "stringify", "display", "title", "open", "onOk", "onCancel", "okText", "cancelText", "enteredOTP", "toString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Profile/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n  sendOTP,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [userRankingStats, setUserRankingStats] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  // Fetch user ranking data from the ranking system\r\n  const fetchUserRankingData = async () => {\r\n    if (!userDetails?._id) return;\r\n\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user's ranking position and nearby users\r\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\r\n\r\n      if (rankingResponse.success) {\r\n        setUserRankingStats(rankingResponse.data);\r\n      }\r\n\r\n      // Also get the full leaderboard to find user's position\r\n      const leaderboardResponse = await getXPLeaderboard({\r\n        limit: 1000,\r\n        levelFilter: userDetails?.level || 'all'\r\n      });\r\n\r\n      if (leaderboardResponse.success) {\r\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\r\n        if (userIndex >= 0) {\r\n          const userWithRank = {\r\n            ...leaderboardResponse.data[userIndex],\r\n            rank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length\r\n          };\r\n          setUserRankingStats(prev => ({\r\n            ...prev,\r\n            userRank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length,\r\n            user: userWithRank\r\n          }));\r\n        }\r\n      }\r\n\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Error fetching ranking data:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n  const sendOTPRequest = async (email) => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await sendOTP({ email });\r\n      if (response.success) {\r\n        message.success(\"Please verify new email!\");\r\n        setEdit(false);\r\n        setServerGeneratedOTP(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n        discardChanges();\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      discardChanges();\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    console.log('🔍 Current formData:', formData);\r\n    console.log('🔍 Current userDetails:', userDetails);\r\n\r\n    // Validation\r\n    if (!formData.name || formData.name.trim() === \"\") {\r\n      console.log('❌ Validation failed: name is empty');\r\n      return message.error(\"Please enter your name.\");\r\n    }\r\n    if (!formData.class_ || formData.class_.trim() === \"\") {\r\n      console.log('❌ Validation failed: class is empty');\r\n      return message.error(\"Please select a class.\");\r\n    }\r\n    if (!formData.level || formData.level.trim() === \"\") {\r\n      console.log('❌ Validation failed: level is empty');\r\n      return message.error(\"Please select a level.\");\r\n    }\r\n    if (!formData.email || formData.email.trim() === \"\") {\r\n      console.log('❌ Validation failed: email is empty');\r\n      console.log('Email value:', `\"${formData.email}\"`);\r\n      return message.error(\"Please enter your email.\");\r\n    }\r\n\r\n    // Email validation\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(formData.email)) {\r\n      return message.error(\"Please enter a valid email address.\");\r\n    }\r\n\r\n    if (\r\n      !skipOTP &&\r\n      formData.email !== userDetails.email\r\n    ) {\r\n      return sendOTPRequest(formData.email);\r\n    }\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      // Ensure email is set (fallback to userDetails.email if formData.email is empty)\r\n      const updatePayload = {\r\n        ...formData,\r\n        email: formData.email || userDetails?.email || \"\",\r\n        userId: userDetails._id,\r\n      };\r\n\r\n      console.log('📤 Sending update data:', updatePayload);\r\n\r\n      const response = await updateUserInfo(updatePayload);\r\n\r\n      console.log('📥 Server response:', response);\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        setServerGeneratedOTP(null);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        console.error('❌ Update failed:', response);\r\n        message.error(response.message || \"Failed to update profile. Please try again.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Update error:', error);\r\n      const errorMessage = error.response?.data?.message || error.message || \"An unexpected error occurred.\";\r\n      message.error(`Update failed: ${errorMessage}`);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('Please select a valid image file');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        message.error('Image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setProfileImage(file);\r\n\r\n      // Show preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n\r\n      // Auto-upload the image\r\n      const data = new FormData();\r\n      data.append(\"profileImage\", file);\r\n      dispatch(ShowLoading());\r\n\r\n      try {\r\n        const response = await updateUserPhoto(data);\r\n        dispatch(HideLoading());\r\n        if (response.success) {\r\n          message.success(\"Profile picture updated successfully!\");\r\n          getUserData(); // Refresh user data to show new image\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message || \"Failed to update profile picture\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === serverGeneratedOTP) {\r\n      handleUpdate({ skipOTP: true });\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  // Load user data on component mount\r\n  useEffect(() => {\r\n    getUserData();\r\n  }, []);\r\n\r\n  // Load ranking data when user details are available\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      fetchUserRankingData();\r\n    }\r\n  }, [userDetails]);\r\n\r\n  // Ensure formData is synchronized with userDetails\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      setFormData({\r\n        name: userDetails.name || \"\",\r\n        email: userDetails.email || \"\",\r\n        school: userDetails.school || \"\",\r\n        class_: userDetails.class || \"\",\r\n        level: userDetails.level || \"\",\r\n        phoneNumber: userDetails.phoneNumber || \"\",\r\n      });\r\n    }\r\n  }, [userDetails]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Profile</h1>\r\n            <p className=\"text-gray-600\">Manage your account settings and preferences</p>\r\n\r\n            {/* Profile Picture with Online Status - Centered Below Header */}\r\n            <div className=\"relative mt-8 flex justify-center\">\r\n              <div className=\"relative\">\r\n                <ProfilePicture\r\n                  user={userDetails}\r\n                  size=\"3xl\"\r\n                  showOnlineStatus={true}\r\n                  onClick={() => document.getElementById('profileImageInput').click()}\r\n                  className=\"hover:scale-105 transition-transform duration-200\"\r\n                  style={{\r\n                    width: '120px',\r\n                    height: '120px',\r\n                    border: '4px solid #BFDBFE',\r\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\r\n                  }}\r\n                />\r\n\r\n                {/* Camera Icon Overlay */}\r\n                <div className=\"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\"\r\n                     onClick={() => document.getElementById('profileImageInput').click()}>\r\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  </svg>\r\n                </div>\r\n\r\n                {/* Hidden File Input */}\r\n                <input\r\n                  id=\"profileImageInput\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleImageChange}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Profile Content */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n            <div className=\"p-8\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                {/* User Info - Horizontal Layout */}\r\n                <div className=\"flex flex-wrap justify-center gap-4 text-center mb-6\">\r\n                  <div className=\"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-blue-600 font-medium\">Name</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.name || 'User'}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-green-600 font-medium\">Email</p>\r\n                    <p className=\"text-lg font-bold text-gray-900 truncate max-w-[150px]\">{userDetails?.email || '<EMAIL>'}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-purple-600 font-medium\">Class</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.class || 'N/A'}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Ranking Stats - Horizontal Layout */}\r\n                {userRankingStats && (\r\n                  <div className=\"flex flex-wrap justify-center gap-4 text-center\">\r\n                    <div className=\"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-yellow-600 font-medium\">Rank</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        #{userRankingStats.userRank || 'N/A'}\r\n                        {userRankingStats.totalUsers && (\r\n                          <span className=\"text-sm text-gray-500\">/{userRankingStats.totalUsers}</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-orange-600 font-medium\">Total XP</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalXP?.toLocaleString() || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-indigo-600 font-medium\">Avg Score</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.averageScore || '0'}%\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-pink-600 font-medium\">Quizzes</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalQuizzesTaken || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-teal-600 font-medium\">Streak</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.currentStreak || '0'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Profile Details */}\r\n              {!edit ? (\r\n                // View Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.name || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.email || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.school || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.level || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.class || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.phoneNumber || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                // Edit Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name *</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"name\"\r\n                        value={formData.name}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your name\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email *</label>\r\n                      <input\r\n                        type=\"email\"\r\n                        name=\"email\"\r\n                        value={formData.email}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your email\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"school\"\r\n                        value={formData.school}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your school\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level *</label>\r\n                      <select\r\n                        name=\"level\"\r\n                        value={formData.level}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Level</option>\r\n                        <option value=\"Primary\">Primary</option>\r\n                        <option value=\"Secondary\">Secondary</option>\r\n                        <option value=\"Advance\">Advance</option>\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class *</label>\r\n                      <select\r\n                        name=\"class_\"\r\n                        value={formData.class_}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Class</option>\r\n                        {formData.level === \"Primary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                            <option value=\"7\">7</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Secondary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Advance\" && (\r\n                          <>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                          </>\r\n                        )}\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <input\r\n                        type=\"tel\"\r\n                        name=\"phoneNumber\"\r\n                        value={formData.phoneNumber}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter phone number\"\r\n                        maxLength=\"10\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"mt-8 flex justify-center gap-4\">\r\n                {!edit ? (\r\n                  <button\r\n                    onClick={() => {\r\n                      // Ensure formData is properly initialized with current user data\r\n                      setFormData({\r\n                        name: userDetails?.name || \"\",\r\n                        email: userDetails?.email || \"\",\r\n                        school: userDetails?.school || \"\",\r\n                        class_: userDetails?.class || \"\",\r\n                        level: userDetails?.level || \"\",\r\n                        phoneNumber: userDetails?.phoneNumber || \"\",\r\n                      });\r\n                      setEdit(true);\r\n                    }}\r\n                    className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\"\r\n                  >\r\n                    Edit Profile\r\n                  </button>\r\n                ) : (\r\n                  <>\r\n                    <button\r\n                      onClick={discardChanges}\r\n                      className=\"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                    <button\r\n                      onClick={handleUpdate}\r\n                      className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Save Changes\r\n                    </button>\r\n                    {/* Debug button - remove in production */}\r\n                    <button\r\n                      onClick={() => {\r\n                        console.log('🔍 Debug - Current formData:', formData);\r\n                        console.log('🔍 Debug - Current userDetails:', userDetails);\r\n                        alert(`FormData: ${JSON.stringify(formData, null, 2)}`);\r\n                      }}\r\n                      className=\"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm\"\r\n                    >\r\n                      Debug\r\n                    </button>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hidden file input for profile image upload */}\r\n      <input\r\n        type=\"file\"\r\n        id=\"profileImageInput\"\r\n        accept=\"image/*\"\r\n        onChange={handleImageChange}\r\n        style={{ display: 'none' }}\r\n      />\r\n\r\n      {/* Level Change Confirmation Modal */}\r\n      <Modal\r\n        title=\"Confirm Level Change\"\r\n        open={showLevelChangeModal}\r\n        onOk={handleLevelChangeConfirm}\r\n        onCancel={() => {\r\n          setShowLevelChangeModal(false);\r\n          setPendingLevelChange(null);\r\n        }}\r\n        okText=\"Confirm\"\r\n        cancelText=\"Cancel\"\r\n      >\r\n        <p>\r\n          Are you sure you want to change your level to <strong>{pendingLevelChange}</strong>?\r\n        </p>\r\n        <p className=\"text-orange-600 text-sm mt-2\">\r\n          Note: Changing your level will reset your class selection and you'll only have access to content for the new level.\r\n        </p>\r\n      </Modal>\r\n\r\n      {/* OTP Verification Modal */}\r\n      {serverGeneratedOTP && (\r\n        <Modal\r\n          title=\"Verify Email Change\"\r\n          open={!!serverGeneratedOTP}\r\n          onOk={() => {\r\n            const enteredOTP = document.getElementById('otpInput').value;\r\n            if (enteredOTP === serverGeneratedOTP.toString()) {\r\n              handleUpdate({ skipOTP: true });\r\n            } else {\r\n              message.error(\"Invalid OTP. Please try again.\");\r\n            }\r\n          }}\r\n          onCancel={() => {\r\n            setServerGeneratedOTP(null);\r\n            discardChanges();\r\n          }}\r\n          okText=\"Verify\"\r\n          cancelText=\"Cancel\"\r\n        >\r\n          <div className=\"space-y-4\">\r\n            <p>Please enter the OTP sent to your new email address:</p>\r\n            <Input\r\n              id=\"otpInput\"\r\n              placeholder=\"Enter OTP\"\r\n              maxLength={6}\r\n              className=\"text-center text-lg\"\r\n            />\r\n            <p className=\"text-sm text-gray-500\">\r\n              Check your email for the verification code.\r\n            </p>\r\n          </div>\r\n        </Modal>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SACEC,WAAW,EACXC,cAAc,EACdC,eAAe,EACfC,OAAO,QACF,yBAAyB;AAChC,SAASC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,2BAA2B;AACrG,OAAOC,cAAc,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqC,IAAI,EAAEC,OAAO,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC;IACvC2C,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACqD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACuD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAMyD,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAE9B,MAAM+C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM7C,uBAAuB,CAAC,CAAC;MAChD,IAAI6C,QAAQ,CAACC,OAAO,EAAE;QACpB5B,cAAc,CAAC2B,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLtD,OAAO,CAACuD,KAAK,CAACH,QAAQ,CAACpD,OAAO,CAAC;MACjC;MACAkD,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;MAC5BkD,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMmD,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAGjC,WAAW,CACxBkC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACrBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACnB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC3C,WAAW,CAAC4C,GAAG,CAAC,CAAC;IAC/DvC,cAAc,CAAC8B,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,EAAC7C,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE4C,GAAG,GAAE;IAEvB,IAAI;MACFhB,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAM8D,eAAe,GAAG,MAAM5D,cAAc,CAACc,WAAW,CAAC4C,GAAG,EAAE,CAAC,CAAC;MAEhE,IAAIE,eAAe,CAACf,OAAO,EAAE;QAC3BxB,mBAAmB,CAACuC,eAAe,CAACd,IAAI,CAAC;MAC3C;;MAEA;MACA,MAAMe,mBAAmB,GAAG,MAAM5D,gBAAgB,CAAC;QACjD6D,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,CAAAjD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;MACrC,CAAC,CAAC;MAEF,IAAI8B,mBAAmB,CAAChB,OAAO,EAAE;QAC/B,MAAMmB,SAAS,GAAGH,mBAAmB,CAACf,IAAI,CAACmB,SAAS,CAACd,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK5C,WAAW,CAAC4C,GAAG,CAAC;QAC1F,IAAIM,SAAS,IAAI,CAAC,EAAE;UAClB,MAAME,YAAY,GAAG;YACnB,GAAGL,mBAAmB,CAACf,IAAI,CAACkB,SAAS,CAAC;YACtCG,IAAI,EAAEH,SAAS,GAAG,CAAC;YACnBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB;UACvC,CAAC;UACDhD,mBAAmB,CAACiD,IAAI,KAAK;YAC3B,GAAGA,IAAI;YACPC,QAAQ,EAAEP,SAAS,GAAG,CAAC;YACvBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB,MAAM;YAC3ClB,IAAI,EAAEe;UACR,CAAC,CAAC,CAAC;QACL;MACF;MAEAxB,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACdL,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;MACvB2E,OAAO,CAACzB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED/D,SAAS,CAAC,MAAM;IACd,IAAIgC,WAAW,IAAIF,WAAW,EAAE;MAC9BkC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAChC,WAAW,EAAEF,WAAW,CAAC,CAAC;EAE9B,MAAM2D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B/B,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAMzD,WAAW,CAAC,CAAC;MACpC,IAAIyD,QAAQ,CAACC,OAAO,EAAE;QACpB9B,cAAc,CAAC6B,QAAQ,CAACE,IAAI,CAAC;QAC7BnB,WAAW,CAAC;UACVC,IAAI,EAAEgB,QAAQ,CAACE,IAAI,CAAClB,IAAI,IAAI,EAAE;UAC9BC,KAAK,EAAEe,QAAQ,CAACE,IAAI,CAACjB,KAAK,IAAI,EAAE;UAChCC,MAAM,EAAEc,QAAQ,CAACE,IAAI,CAAChB,MAAM,IAAI,EAAE;UAClCE,MAAM,EAAEY,QAAQ,CAACE,IAAI,CAAC4B,KAAK,IAAI,EAAE;UACjC3C,KAAK,EAAEa,QAAQ,CAACE,IAAI,CAACf,KAAK,IAAI,EAAE;UAChCE,WAAW,EAAEW,QAAQ,CAACE,IAAI,CAACb,WAAW,IAAI;QAC5C,CAAC,CAAC;QACF,IAAIW,QAAQ,CAACE,IAAI,CAACZ,YAAY,EAAE;UAC9BC,eAAe,CAACS,QAAQ,CAACE,IAAI,CAACZ,YAAY,CAAC;QAC7C;QACAS,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLnD,OAAO,CAACuD,KAAK,CAACH,QAAQ,CAACpD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRkD,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd,IAAI2F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElD,IAAI;MAAEmD;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAIpD,IAAI,KAAK,aAAa,IAAImD,KAAK,CAACV,MAAM,GAAG,EAAE,EAAE;IACjD,IAAIzC,IAAI,KAAK,OAAO,IAAImD,KAAK,MAAKjE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAIgD,KAAK,KAAK,EAAE,EAAE;MACpEtC,qBAAqB,CAACsC,KAAK,CAAC;MAC5BxC,uBAAuB,CAAC,IAAI,CAAC;MAC7B;IACF;IACAZ,WAAW,CAAE2C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAAC1C,IAAI,GAAGmD,KAAK;MACb,IAAInD,IAAI,KAAK,OAAO,GAAG;QAAEI,MAAM,EAAE;MAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiD,cAAc,GAAGA,CAAA,KAAM;IAC3BtD,WAAW,CAAC;MACVC,IAAI,EAAEd,WAAW,CAACc,IAAI;MACtBC,KAAK,EAAEf,WAAW,CAACe,KAAK;MACxBC,MAAM,EAAEhB,WAAW,CAACgB,MAAM;MAC1BE,MAAM,EAAElB,WAAW,CAAC4D,KAAK;MACzB3C,KAAK,EAAEjB,WAAW,CAACiB,KAAK;MACxBE,WAAW,EAAEnB,WAAW,CAACmB;IAC3B,CAAC,CAAC;IACFV,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,MAAM2D,cAAc,GAAG,MAAOrD,KAAK,IAAK;IACtCa,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAMtD,OAAO,CAAC;QAAEuC;MAAM,CAAC,CAAC;MACzC,IAAIe,QAAQ,CAACC,OAAO,EAAE;QACpBrD,OAAO,CAACqD,OAAO,CAAC,0BAA0B,CAAC;QAC3CtB,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAACO,QAAQ,CAACE,IAAI,CAAC;MACtC,CAAC,MAAM;QACLtD,OAAO,CAACuD,KAAK,CAACH,QAAQ,CAACpD,OAAO,CAAC;QAC/ByF,cAAc,CAAC,CAAC;MAClB;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;MAC5ByF,cAAc,CAAC,CAAC;IAClB,CAAC,SAAS;MACRvC,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMsF,YAAY,GAAG,MAAAA,CAAO;IAAEC;EAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;IAC/CZ,OAAO,CAACa,GAAG,CAAC,sBAAsB,EAAE3D,QAAQ,CAAC;IAC7C8C,OAAO,CAACa,GAAG,CAAC,yBAAyB,EAAEvE,WAAW,CAAC;;IAEnD;IACA,IAAI,CAACY,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC0D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjDd,OAAO,CAACa,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAO7F,OAAO,CAACuD,KAAK,CAAC,yBAAyB,CAAC;IACjD;IACA,IAAI,CAACrB,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAACM,MAAM,CAACsD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrDd,OAAO,CAACa,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO7F,OAAO,CAACuD,KAAK,CAAC,wBAAwB,CAAC;IAChD;IACA,IAAI,CAACrB,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACuD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnDd,OAAO,CAACa,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO7F,OAAO,CAACuD,KAAK,CAAC,wBAAwB,CAAC;IAChD;IACA,IAAI,CAACrB,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAACyD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnDd,OAAO,CAACa,GAAG,CAAC,qCAAqC,CAAC;MAClDb,OAAO,CAACa,GAAG,CAAC,cAAc,EAAG,IAAG3D,QAAQ,CAACG,KAAM,GAAE,CAAC;MAClD,OAAOrC,OAAO,CAACuD,KAAK,CAAC,0BAA0B,CAAC;IAClD;;IAEA;IACA,MAAMwC,UAAU,GAAG,4BAA4B;IAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC9D,QAAQ,CAACG,KAAK,CAAC,EAAE;MACpC,OAAOrC,OAAO,CAACuD,KAAK,CAAC,qCAAqC,CAAC;IAC7D;IAEA,IACE,CAACqC,OAAO,IACR1D,QAAQ,CAACG,KAAK,KAAKf,WAAW,CAACe,KAAK,EACpC;MACA,OAAOqD,cAAc,CAACxD,QAAQ,CAACG,KAAK,CAAC;IACvC;IAEAa,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF;MACA,MAAM2F,aAAa,GAAG;QACpB,GAAG/D,QAAQ;QACXG,KAAK,EAAEH,QAAQ,CAACG,KAAK,KAAIf,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI,EAAE;QACjD2B,MAAM,EAAE1C,WAAW,CAAC4C;MACtB,CAAC;MAEDc,OAAO,CAACa,GAAG,CAAC,yBAAyB,EAAEI,aAAa,CAAC;MAErD,MAAM7C,QAAQ,GAAG,MAAMxD,cAAc,CAACqG,aAAa,CAAC;MAEpDjB,OAAO,CAACa,GAAG,CAAC,qBAAqB,EAAEzC,QAAQ,CAAC;MAE5C,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBrD,OAAO,CAACqD,OAAO,CAACD,QAAQ,CAACpD,OAAO,CAAC;QACjC+B,OAAO,CAAC,KAAK,CAAC;QACdc,qBAAqB,CAAC,IAAI,CAAC;QAC3BoC,WAAW,CAAC,CAAC;QACb,IAAI7B,QAAQ,CAAC8C,YAAY,EAAE;UACzBC,UAAU,CAAC,MAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,MAAM;QACLtB,OAAO,CAACzB,KAAK,CAAC,kBAAkB,EAAEH,QAAQ,CAAC;QAC3CpD,OAAO,CAACuD,KAAK,CAACH,QAAQ,CAACpD,OAAO,IAAI,6CAA6C,CAAC;MAClF;IACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;MAAA,IAAAgD,eAAA,EAAAC,oBAAA;MACdxB,OAAO,CAACzB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,MAAMkD,YAAY,GAAG,EAAAF,eAAA,GAAAhD,KAAK,CAACH,QAAQ,cAAAmD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBjD,IAAI,cAAAkD,oBAAA,uBAApBA,oBAAA,CAAsBxG,OAAO,KAAIuD,KAAK,CAACvD,OAAO,IAAI,+BAA+B;MACtGA,OAAO,CAACuD,KAAK,CAAE,kBAAiBkD,YAAa,EAAC,CAAC;IACjD,CAAC,SAAS;MACRvD,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMqG,wBAAwB,GAAGA,CAAA,KAAM;IACrCvE,WAAW,CAAE2C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACPvC,KAAK,EAAES,kBAAkB;MACzBR,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHO,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM0D,uBAAuB,GAAGA,CAAA,KAAM;IACpC5D,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM2D,iBAAiB,GAAG,MAAOtB,CAAC,IAAK;IACrC,MAAMuB,IAAI,GAAGvB,CAAC,CAACE,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnChH,OAAO,CAACuD,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACF;;MAEA;MACA,IAAIsD,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/BjH,OAAO,CAACuD,KAAK,CAAC,oCAAoC,CAAC;QACnD;MACF;MAEAZ,eAAe,CAACkE,IAAI,CAAC;;MAErB;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMnF,eAAe,CAACiF,MAAM,CAACG,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;;MAE1B;MACA,MAAMvD,IAAI,GAAG,IAAIiE,QAAQ,CAAC,CAAC;MAC3BjE,IAAI,CAACkE,MAAM,CAAC,cAAc,EAAEX,IAAI,CAAC;MACjC3D,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI;QACF,MAAM8C,QAAQ,GAAG,MAAMvD,eAAe,CAACyD,IAAI,CAAC;QAC5CJ,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;QACvB,IAAI+C,QAAQ,CAACC,OAAO,EAAE;UACpBrD,OAAO,CAACqD,OAAO,CAAC,uCAAuC,CAAC;UACxD4B,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UACLjF,OAAO,CAACuD,KAAK,CAACH,QAAQ,CAACpD,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;QACdL,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;QACvBL,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,IAAI,kCAAkC,CAAC;MACpE;IACF;EACF,CAAC;EAED,MAAMyH,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMnE,IAAI,GAAG,IAAIiE,QAAQ,CAAC,CAAC;IAC3BjE,IAAI,CAACkE,MAAM,CAAC,cAAc,EAAE9E,YAAY,CAAC;IACzCQ,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAMvD,eAAe,CAACyD,IAAI,CAAC;MAC5C,IAAIF,QAAQ,CAACC,OAAO,EAAE;QACpBrD,OAAO,CAACqD,OAAO,CAAC,6BAA6B,CAAC;QAC9C4B,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLjF,OAAO,CAACuD,KAAK,CAACH,QAAQ,CAACpD,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRkD,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMqH,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAIA,MAAM,CAACC,GAAG,KAAKhF,kBAAkB,EAAE;MACrC+C,YAAY,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACL5F,OAAO,CAACuD,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;;EAED;EACA/D,SAAS,CAAC,MAAM;IACdyF,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzF,SAAS,CAAC,MAAM;IACd,IAAI8B,WAAW,EAAE;MACf6C,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC7C,WAAW,CAAC,CAAC;;EAEjB;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI8B,WAAW,EAAE;MACfa,WAAW,CAAC;QACVC,IAAI,EAAEd,WAAW,CAACc,IAAI,IAAI,EAAE;QAC5BC,KAAK,EAAEf,WAAW,CAACe,KAAK,IAAI,EAAE;QAC9BC,MAAM,EAAEhB,WAAW,CAACgB,MAAM,IAAI,EAAE;QAChCE,MAAM,EAAElB,WAAW,CAAC4D,KAAK,IAAI,EAAE;QAC/B3C,KAAK,EAAEjB,WAAW,CAACiB,KAAK,IAAI,EAAE;QAC9BE,WAAW,EAAEnB,WAAW,CAACmB,WAAW,IAAI;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACnB,WAAW,CAAC,CAAC;EAEjB,oBACEV,OAAA;IAAKiH,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjFlH,OAAA;MAAKiH,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1ClH,OAAA;QAAKiH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhClH,OAAA;UAAKiH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BlH,OAAA;YAAIiH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEtH,OAAA;YAAGiH,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAG7EtH,OAAA;YAAKiH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDlH,OAAA;cAAKiH,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBlH,OAAA,CAACF,cAAc;gBACbiD,IAAI,EAAErC,WAAY;gBAClB2F,IAAI,EAAC,KAAK;gBACVkB,gBAAgB,EAAE,IAAK;gBACvBC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;gBACpEV,SAAS,EAAC,mDAAmD;gBAC7DW,KAAK,EAAE;kBACLC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfC,MAAM,EAAE,mBAAmB;kBAC3BC,SAAS,EAAE;gBACb;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFtH,OAAA;gBAAKiH,SAAS,EAAC,kIAAkI;gBAC5IO,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;gBAAAT,QAAA,eACvElH,OAAA;kBAAKiH,SAAS,EAAC,oBAAoB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,gBACvFlH,OAAA;oBAAMoI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1OtH,OAAA;oBAAMoI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtH,OAAA;gBACEwI,EAAE,EAAC,mBAAmB;gBACtBrC,IAAI,EAAC,MAAM;gBACXsC,MAAM,EAAC,SAAS;gBAChBxB,SAAS,EAAC,QAAQ;gBAClByB,QAAQ,EAAE1C;cAAkB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtH,OAAA;UAAKiH,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7DlH,OAAA;YAAKiH,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBlH,OAAA;cAAKiH,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAE9ClH,OAAA;gBAAKiH,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnElH,OAAA;kBAAKiH,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnFlH,OAAA;oBAAGiH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzDtH,OAAA;oBAAGiH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAM;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACNtH,OAAA;kBAAKiH,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBACrFlH,OAAA;oBAAGiH,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3DtH,OAAA;oBAAGiH,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,EAAE,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI;kBAAkB;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC,eACNtH,OAAA;kBAAKiH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFlH,OAAA;oBAAGiH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5DtH,OAAA;oBAAGiH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI;kBAAK;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLtG,gBAAgB,iBACfhB,OAAA;gBAAKiH,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC9DlH,OAAA;kBAAKiH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFlH,OAAA;oBAAGiH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3DtH,OAAA;oBAAGiH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAAC,GAC5C,EAAClG,gBAAgB,CAACmD,QAAQ,IAAI,KAAK,EACnCnD,gBAAgB,CAACgD,UAAU,iBAC1BhE,OAAA;sBAAMiH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAAClG,gBAAgB,CAACgD,UAAU;oBAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAC7E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtH,OAAA;kBAAKiH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFlH,OAAA;oBAAGiH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/DtH,OAAA;oBAAGiH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAA7G,qBAAA,GAAAW,gBAAgB,CAAC+B,IAAI,cAAA1C,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBsI,OAAO,cAAArI,sBAAA,uBAA9BA,sBAAA,CAAgCsI,cAAc,CAAC,CAAC,KAAI;kBAAG;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtH,OAAA;kBAAKiH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFlH,OAAA;oBAAGiH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChEtH,OAAA;oBAAGiH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAC3C,EAAA3G,sBAAA,GAAAS,gBAAgB,CAAC+B,IAAI,cAAAxC,sBAAA,uBAArBA,sBAAA,CAAuBsI,YAAY,KAAI,GAAG,EAAC,GAC9C;kBAAA;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtH,OAAA;kBAAKiH,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnFlH,OAAA;oBAAGiH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5DtH,OAAA;oBAAGiH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAA1G,sBAAA,GAAAQ,gBAAgB,CAAC+B,IAAI,cAAAvC,sBAAA,uBAArBA,sBAAA,CAAuBsI,iBAAiB,KAAI;kBAAG;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtH,OAAA;kBAAKiH,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnFlH,OAAA;oBAAGiH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3DtH,OAAA;oBAAGiH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAAzG,sBAAA,GAAAO,gBAAgB,CAAC+B,IAAI,cAAAtC,sBAAA,uBAArBA,sBAAA,CAAuBsI,aAAa,KAAI;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAACpG,IAAI;YAAA;YACJ;YACAlB,OAAA;cAAKiH,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlH,OAAA;gBAAKiH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5EtH,OAAA;oBAAKiH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAc;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7EtH,OAAA;oBAAKiH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI;kBAAc;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9EtH,OAAA;oBAAKiH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI;kBAAc;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtH,OAAA;gBAAKiH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7EtH,OAAA;oBAAKiH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;kBAAc;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7EtH,OAAA;oBAAKiH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI;kBAAc;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFtH,OAAA;oBAAKiH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAxG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,WAAW,KAAI;kBAAc;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACAtH,OAAA;cAAKiH,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlH,OAAA;gBAAKiH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9EtH,OAAA;oBACEmG,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,MAAM;oBACXmD,KAAK,EAAErD,QAAQ,CAACE,IAAK;oBACrBkH,QAAQ,EAAEjE,YAAa;oBACvBwC,SAAS,EAAC,uHAAuH;oBACjI+B,WAAW,EAAC,iBAAiB;oBAC7BC,QAAQ;kBAAA;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EtH,OAAA;oBACEmG,IAAI,EAAC,OAAO;oBACZ3E,IAAI,EAAC,OAAO;oBACZmD,KAAK,EAAErD,QAAQ,CAACG,KAAM;oBACtBiH,QAAQ,EAAEjE,YAAa;oBACvBwC,SAAS,EAAC,uHAAuH;oBACjI+B,WAAW,EAAC,kBAAkB;oBAC9BC,QAAQ;kBAAA;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9EtH,OAAA;oBACEmG,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,QAAQ;oBACbmD,KAAK,EAAErD,QAAQ,CAACI,MAAO;oBACvBgH,QAAQ,EAAEjE,YAAa;oBACvBwC,SAAS,EAAC,uHAAuH;oBACjI+B,WAAW,EAAC;kBAAmB;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtH,OAAA;gBAAKiH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EtH,OAAA;oBACEwB,IAAI,EAAC,OAAO;oBACZmD,KAAK,EAAErD,QAAQ,CAACK,KAAM;oBACtB+G,QAAQ,EAAEjE,YAAa;oBACvBwC,SAAS,EAAC,uHAAuH;oBACjIgC,QAAQ;oBAAA/B,QAAA,gBAERlH,OAAA;sBAAQ2E,KAAK,EAAC,EAAE;sBAAAuC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtH,OAAA;sBAAQ2E,KAAK,EAAC,SAAS;sBAAAuC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxCtH,OAAA;sBAAQ2E,KAAK,EAAC,WAAW;sBAAAuC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5CtH,OAAA;sBAAQ2E,KAAK,EAAC,SAAS;sBAAAuC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/EtH,OAAA;oBACEwB,IAAI,EAAC,QAAQ;oBACbmD,KAAK,EAAErD,QAAQ,CAACM,MAAO;oBACvB8G,QAAQ,EAAEjE,YAAa;oBACvBwC,SAAS,EAAC,uHAAuH;oBACjIgC,QAAQ;oBAAA/B,QAAA,gBAERlH,OAAA;sBAAQ2E,KAAK,EAAC,EAAE;sBAAAuC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACrChG,QAAQ,CAACK,KAAK,KAAK,SAAS,iBAC3B3B,OAAA,CAAAE,SAAA;sBAAAgH,QAAA,gBACElH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH,EACAhG,QAAQ,CAACK,KAAK,KAAK,WAAW,iBAC7B3B,OAAA,CAAAE,SAAA;sBAAAgH,QAAA,gBACElH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH,EACAhG,QAAQ,CAACK,KAAK,KAAK,SAAS,iBAC3B3B,OAAA,CAAAE,SAAA;sBAAAgH,QAAA,gBACElH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5BtH,OAAA;wBAAQ2E,KAAK,EAAC,GAAG;wBAAAuC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtH,OAAA;kBAAAkH,QAAA,gBACElH,OAAA;oBAAOiH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpFtH,OAAA;oBACEmG,IAAI,EAAC,KAAK;oBACV3E,IAAI,EAAC,aAAa;oBAClBmD,KAAK,EAAErD,QAAQ,CAACO,WAAY;oBAC5B6G,QAAQ,EAAEjE,YAAa;oBACvBwC,SAAS,EAAC,uHAAuH;oBACjI+B,WAAW,EAAC,oBAAoB;oBAChCE,SAAS,EAAC;kBAAI;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAGDtH,OAAA;cAAKiH,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAC5C,CAAChG,IAAI,gBACJlB,OAAA;gBACEwH,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACAjG,WAAW,CAAC;oBACVC,IAAI,EAAE,CAAAd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI,EAAE;oBAC7BC,KAAK,EAAE,CAAAf,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI,EAAE;oBAC/BC,MAAM,EAAE,CAAAhB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI,EAAE;oBACjCE,MAAM,EAAE,CAAAlB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D,KAAK,KAAI,EAAE;oBAChC3C,KAAK,EAAE,CAAAjB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI,EAAE;oBAC/BE,WAAW,EAAE,CAAAnB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,WAAW,KAAI;kBAC3C,CAAC,CAAC;kBACFV,OAAO,CAAC,IAAI,CAAC;gBACf,CAAE;gBACF8F,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EACrH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gBAETtH,OAAA,CAAAE,SAAA;gBAAAgH,QAAA,gBACElH,OAAA;kBACEwH,OAAO,EAAE3C,cAAe;kBACxBoC,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,EACrH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtH,OAAA;kBACEwH,OAAO,EAAEzC,YAAa;kBACtBkC,SAAS,EAAC,4GAA4G;kBAAAC,QAAA,EACvH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAETtH,OAAA;kBACEwH,OAAO,EAAEA,CAAA,KAAM;oBACbpD,OAAO,CAACa,GAAG,CAAC,8BAA8B,EAAE3D,QAAQ,CAAC;oBACrD8C,OAAO,CAACa,GAAG,CAAC,iCAAiC,EAAEvE,WAAW,CAAC;oBAC3DyI,KAAK,CAAE,aAAYC,IAAI,CAACC,SAAS,CAAC/H,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;kBACzD,CAAE;kBACF2F,SAAS,EAAC,kHAAkH;kBAAAC,QAAA,EAC7H;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtH,OAAA;MACEmG,IAAI,EAAC,MAAM;MACXqC,EAAE,EAAC,mBAAmB;MACtBC,MAAM,EAAC,SAAS;MAChBC,QAAQ,EAAE1C,iBAAkB;MAC5B4B,KAAK,EAAE;QAAE0B,OAAO,EAAE;MAAO;IAAE;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGFtH,OAAA,CAACX,KAAK;MACJkK,KAAK,EAAC,sBAAsB;MAC5BC,IAAI,EAAEtH,oBAAqB;MAC3BuH,IAAI,EAAE3D,wBAAyB;MAC/B4D,QAAQ,EAAEA,CAAA,KAAM;QACdvH,uBAAuB,CAAC,KAAK,CAAC;QAC9BE,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MACFsH,MAAM,EAAC,SAAS;MAChBC,UAAU,EAAC,QAAQ;MAAA1C,QAAA,gBAEnBlH,OAAA;QAAAkH,QAAA,GAAG,gDAC6C,eAAAlH,OAAA;UAAAkH,QAAA,EAAS9E;QAAkB;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,KACrF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJtH,OAAA;QAAGiH,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAAC;MAE5C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGPtF,kBAAkB,iBACjBhC,OAAA,CAACX,KAAK;MACJkK,KAAK,EAAC,qBAAqB;MAC3BC,IAAI,EAAE,CAAC,CAACxH,kBAAmB;MAC3ByH,IAAI,EAAEA,CAAA,KAAM;QACV,MAAMI,UAAU,GAAGpC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,CAAC/C,KAAK;QAC5D,IAAIkF,UAAU,KAAK7H,kBAAkB,CAAC8H,QAAQ,CAAC,CAAC,EAAE;UAChD/E,YAAY,CAAC;YAAEC,OAAO,EAAE;UAAK,CAAC,CAAC;QACjC,CAAC,MAAM;UACL5F,OAAO,CAACuD,KAAK,CAAC,gCAAgC,CAAC;QACjD;MACF,CAAE;MACF+G,QAAQ,EAAEA,CAAA,KAAM;QACdzH,qBAAqB,CAAC,IAAI,CAAC;QAC3B4C,cAAc,CAAC,CAAC;MAClB,CAAE;MACF8E,MAAM,EAAC,QAAQ;MACfC,UAAU,EAAC,QAAQ;MAAA1C,QAAA,eAEnBlH,OAAA;QAAKiH,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBlH,OAAA;UAAAkH,QAAA,EAAG;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3DtH,OAAA,CAACV,KAAK;UACJkJ,EAAE,EAAC,UAAU;UACbQ,WAAW,EAAC,WAAW;UACvBE,SAAS,EAAE,CAAE;UACbjC,SAAS,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACFtH,OAAA;UAAGiH,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClH,EAAA,CAnuBID,OAAO;EAAA,QAmBMX,WAAW;AAAA;AAAAuK,EAAA,GAnBxB5J,OAAO;AAquBb,eAAeA,OAAO;AAAC,IAAA4J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}