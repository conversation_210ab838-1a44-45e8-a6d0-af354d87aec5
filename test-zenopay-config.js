const axios = require('axios');
const qs = require('qs');
require('dotenv').config({ path: './server/.env' });

const testZenoPayConfig = async () => {
  console.log('🔍 Testing ZenoPay Configuration...\n');

  // Check current configuration
  console.log('📋 Current ZenoPay Configuration:');
  console.log('- Account ID:', process.env.ZENOPAY_ACCOUNT_ID);
  console.log('- Secret Key:', process.env.ZENOPAY_SECRET_KEY);
  console.log('- API Key:', process.env.ZENOPAY_API_KEY ? '***' + process.env.ZENOPAY_API_KEY.slice(-4) : 'NOT_SET');
  console.log('- Webhook URL:', process.env.ZENOPAY_WEBHOOK_URL);
  console.log('- Environment:', process.env.ZENOPAY_ENVIRONMENT);

  // Check for configuration issues
  console.log('\n🔍 Configuration Analysis:');
  
  if (process.env.ZENOPAY_SECRET_KEY === 'your_secret_key_here') {
    console.log('❌ CRITICAL: Secret key is still placeholder value!');
    console.log('   This will cause all payments to fail authentication.');
  }
  
  if (process.env.ZENOPAY_WEBHOOK_URL?.includes('webhook.site')) {
    console.log('❌ CRITICAL: Webhook URL points to test site!');
    console.log('   Payments will succeed but our server won\'t be notified.');
  }
  
  if (!process.env.ZENOPAY_ACCOUNT_ID || !process.env.ZENOPAY_API_KEY) {
    console.log('❌ CRITICAL: Missing required ZenoPay credentials!');
  }

  // Test API call with current configuration
  console.log('\n🧪 Testing ZenoPay API call...');
  
  const testData = {
    buyer_name: 'Test User',
    buyer_phone: '**********',
    buyer_email: '<EMAIL>',
    amount: 1000, // Small test amount
    account_id: process.env.ZENOPAY_ACCOUNT_ID,
    secret_key: process.env.ZENOPAY_SECRET_KEY,
    api_key: process.env.ZENOPAY_API_KEY,
    webhook_url: process.env.ZENOPAY_WEBHOOK_URL,
  };

  try {
    const formattedData = qs.stringify(testData);
    
    const response = await axios.post('https://api.zeno.africa', formattedData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 30000
    });

    console.log('✅ ZenoPay API Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

    if (response.data.status === 'success') {
      console.log('\n🎉 SUCCESS! ZenoPay API is working correctly.');
      console.log('📱 Test SMS should be sent to: **********');
      
      console.log('\n⚠️ However, webhook issues remain:');
      if (process.env.ZENOPAY_WEBHOOK_URL?.includes('webhook.site')) {
        console.log('- Webhook URL needs to point to your actual server');
        console.log('- Suggested: http://localhost:5000/api/payment/webhook (for local testing)');
        console.log('- Production: https://yourdomain.com/api/payment/webhook');
      }
    } else {
      console.log('\n⚠️ ZenoPay returned an error:', response.data.message || 'Unknown error');
    }

  } catch (error) {
    console.error('\n❌ ZenoPay API Test Failed:');
    console.error('Error:', error.message);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      
      // Analyze common errors
      if (error.response.status === 401) {
        console.log('\n🔍 Analysis: Authentication failed');
        console.log('   - Check if ZENOPAY_SECRET_KEY is correct');
        console.log('   - Verify ZENOPAY_API_KEY is valid');
        console.log('   - Ensure ZENOPAY_ACCOUNT_ID is correct');
      } else if (error.response.status === 400) {
        console.log('\n🔍 Analysis: Bad request');
        console.log('   - Check if all required fields are provided');
        console.log('   - Verify phone number format');
        console.log('   - Check amount format');
      }
    }
  }

  // Provide fix recommendations
  console.log('\n💡 Recommendations to Fix Payment Issues:');
  console.log('1. 🔑 Update ZENOPAY_SECRET_KEY with actual secret key from ZenoPay');
  console.log('2. 🌐 Update ZENOPAY_WEBHOOK_URL to point to your server:');
  console.log('   - Local: http://localhost:5000/api/payment/webhook');
  console.log('   - Production: https://yourdomain.com/api/payment/webhook');
  console.log('3. 🔄 Restart the server after updating .env file');
  console.log('4. 🧪 Test payment flow again');
  
  console.log('\n📝 Current webhook URL should be changed from:');
  console.log(`   ${process.env.ZENOPAY_WEBHOOK_URL}`);
  console.log('   to:');
  console.log('   http://localhost:5000/api/payment/webhook (for local testing)');
};

testZenoPayConfig();
