{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{startTransition,useEffect,useState}from'react';import{useNavigate,useLocation,useParams}from'react-router-dom';import{useSelector}from'react-redux';import{TbTrophy,TbCheck,TbX,TbHome,TbStar,TbBrain,TbChartBar}from'react-icons/tb';import{chatWithChatGPTToExplainAns}from'../../../apicalls/chat';import ContentRenderer from'../../../components/ContentRenderer';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";const QuizResult=()=>{const navigate=useNavigate();const location=useLocation();const{id}=useParams();const{user}=useSelector(state=>state.user);// Get result data from navigation state first\nconst resultData=location.state||{percentage:0,correctAnswers:0,totalQuestions:0,timeTaken:0,resultDetails:[],xpData:null,quizName:'Quiz',quizSubject:'General',passingPercentage:60,verdict:'Fail'};const{percentage,correctAnswers,totalQuestions,timeTaken,xpData,quizName,quizSubject,passingPercentage,verdict,resultDetails}=resultData;const isPassed=verdict==='Pass'||percentage>=(passingPercentage||60);const[confetti,setConfetti]=useState([]);const[explanations,setExplanations]=useState({});const[loadingExplanations,setLoadingExplanations]=useState({});const[isFlashing,setIsFlashing]=useState(false);// Play sound and trigger animations when component loads\nuseEffect(()=>{// Play enhanced sound effects and trigger flash animation\nconst playSound=()=>{// Trigger premium flash animation\nsetIsFlashing(true);setTimeout(()=>setIsFlashing(false),3200);// Flash for 3.2 seconds (2 cycles of 0.8s each)\ntry{if(isPassed){// Success sound with clapping\nconst audioContext=new(window.AudioContext||window.webkitAudioContext)();// Create a success melody\nconst playTone=function(frequency,startTime,duration){let type=arguments.length>3&&arguments[3]!==undefined?arguments[3]:'sine';const oscillator=audioContext.createOscillator();const gainNode=audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(audioContext.destination);oscillator.frequency.setValueAtTime(frequency,startTime);oscillator.type=type;gainNode.gain.setValueAtTime(0,startTime);gainNode.gain.linearRampToValueAtTime(0.1,startTime+0.01);gainNode.gain.exponentialRampToValueAtTime(0.01,startTime+duration);oscillator.start(startTime);oscillator.stop(startTime+duration);};// Create clapping sound effect\nconst createClap=startTime=>{const noise=audioContext.createBufferSource();const buffer=audioContext.createBuffer(1,audioContext.sampleRate*0.1,audioContext.sampleRate);const data=buffer.getChannelData(0);// Generate white noise for clap\nfor(let i=0;i<data.length;i++){data[i]=Math.random()*2-1;}noise.buffer=buffer;const filter=audioContext.createBiquadFilter();filter.type='highpass';filter.frequency.setValueAtTime(1000,startTime);const gainNode=audioContext.createGain();gainNode.gain.setValueAtTime(0,startTime);gainNode.gain.linearRampToValueAtTime(0.3,startTime+0.01);gainNode.gain.exponentialRampToValueAtTime(0.01,startTime+0.1);noise.connect(filter);filter.connect(gainNode);gainNode.connect(audioContext.destination);noise.start(startTime);noise.stop(startTime+0.1);};const now=audioContext.currentTime;// Play success melody: C-E-G-C (major chord progression)\nplayTone(523.25,now,0.2);// C5\nplayTone(659.25,now+0.1,0.2);// E5\nplayTone(783.99,now+0.2,0.2);// G5\nplayTone(1046.5,now+0.3,0.4);// C6\n// Add clapping sounds\ncreateClap(now+0.5);createClap(now+0.7);createClap(now+0.9);createClap(now+1.1);}else{// Fail sound - create a gentle, encouraging tone\nconst audioContext=new(window.AudioContext||window.webkitAudioContext)();const playTone=(frequency,startTime,duration)=>{const oscillator=audioContext.createOscillator();const gainNode=audioContext.createGain();oscillator.connect(gainNode);gainNode.connect(audioContext.destination);oscillator.frequency.setValueAtTime(frequency,startTime);oscillator.type='sine';gainNode.gain.setValueAtTime(0,startTime);gainNode.gain.linearRampToValueAtTime(0.08,startTime+0.01);gainNode.gain.exponentialRampToValueAtTime(0.01,startTime+duration);oscillator.start(startTime);oscillator.stop(startTime+duration);};// Play gentle fail tone: A-F (not harsh, encouraging)\nconst now=audioContext.currentTime;playTone(440,now,0.3);// A4\nplayTone(349.23,now+0.2,0.4);// F4\n}}catch(error){console.log('Audio not supported');}};// Generate premium animations based on pass/fail\nif(isPassed){// Premium confetti explosion\nconst premiumConfetti=[];// Main confetti burst (200 pieces)\nfor(let i=0;i<200;i++){const colors=['#FFD700','#FFA500','#FF6B6B','#4ECDC4','#45B7D1','#96CEB4','#FF69B4','#32CD32','#FF4500','#9370DB','#00CED1','#FF1493','#00FF7F','#FF8C00','#DA70D6'];premiumConfetti.push({id:\"confetti_\".concat(i),left:20+Math.random()*60,// More centered\ndelay:Math.random()*2,duration:4+Math.random()*3,color:colors[Math.floor(Math.random()*colors.length)],shape:['circle','square','triangle'][Math.floor(Math.random()*3)],size:3+Math.random()*6,type:'confetti',randomX:(Math.random()-0.5)*200// For burst effect\n});}// Premium sparkles (50 pieces)\nfor(let i=0;i<50;i++){premiumConfetti.push({id:\"sparkle_\".concat(i),left:Math.random()*100,delay:Math.random()*3,duration:2+Math.random()*2,color:['#FFD700','#FFFFFF','#FFF700','#FFFF00'][Math.floor(Math.random()*4)],size:1+Math.random()*3,type:'sparkle'});}// Burst confetti (100 pieces from center)\nfor(let i=0;i<100;i++){premiumConfetti.push({id:\"burst_\".concat(i),left:45+Math.random()*10,// Center burst\ndelay:Math.random()*0.5,duration:3+Math.random()*2,color:['#FFD700','#FF6B6B','#4ECDC4','#FF69B4'][Math.floor(Math.random()*4)],shape:'circle',size:2+Math.random()*4,type:'burst',randomX:(Math.random()-0.5)*300});}setConfetti(premiumConfetti);// Remove all animations after 10 seconds\nsetTimeout(()=>setConfetti([]),10000);}else{// Premium motivational elements\nconst motivationalElements=[];const motivationalEmojis=['💪','🌟','📚','🎯','🚀','💡','⭐','✨','🔥','💎'];for(let i=0;i<30;i++){motivationalElements.push({id:\"motivate_\".concat(i),left:Math.random()*100,delay:Math.random()*4,duration:5+Math.random()*3,emoji:motivationalEmojis[Math.floor(Math.random()*motivationalEmojis.length)],isMotivational:true,size:2+Math.random()*2});}setConfetti(motivationalElements);// Remove motivational elements after 8 seconds\nsetTimeout(()=>setConfetti([]),8000);}playSound();},[isPassed]);// Function to fetch explanation\nconst fetchExplanation=async(questionIndex,detail)=>{const questionKey=\"question_\".concat(questionIndex);// Don't fetch if already loading or already have explanation\nif(loadingExplanations[questionKey]||explanations[questionKey]){return;}try{setLoadingExplanations(prev=>_objectSpread(_objectSpread({},prev),{},{[questionKey]:true}));const response=await chatWithChatGPTToExplainAns({question:detail.questionText||detail.questionName,expectedAnswer:detail.correctAnswer,userAnswer:detail.userAnswer,imageUrl:detail.questionImage||detail.image||detail.imageUrl||null});if(response.success){setExplanations(prev=>_objectSpread(_objectSpread({},prev),{},{[questionKey]:response.explanation}));}else{console.error('Failed to fetch explanation:',response.error);setExplanations(prev=>_objectSpread(_objectSpread({},prev),{},{[questionKey]:'Sorry, we could not generate an explanation at this time. Please try again later.'}));}}catch(error){console.error('Error fetching explanation:',error);setExplanations(prev=>_objectSpread(_objectSpread({},prev),{},{[questionKey]:'Sorry, we could not generate an explanation at this time. Please try again later.'}));}finally{setLoadingExplanations(prev=>_objectSpread(_objectSpread({},prev),{},{[questionKey]:false}));}};const handleBackToQuizzes=()=>{console.log('🏠 Navigating to quiz listing...');startTransition(()=>{navigate('/user/quiz');});};const handleRetakeQuiz=()=>{console.log('🔄 Retaking quiz with ID:',id);if(id){startTransition(()=>{navigate(\"/quiz/\".concat(id,\"/play\"));});}else{console.log('❌ No quiz ID available, going to quiz listing');startTransition(()=>{navigate('/user/quiz');});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen flex items-center justify-center relative overflow-hidden \".concat(isPassed?'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100':'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100',\" \").concat(isFlashing?isPassed?'flash-green':'flash-red':''),style:{padding:window.innerWidth<=768?'8px':window.innerWidth<=1024?'16px':'24px'},children:[confetti.map(piece=>{if(piece.isMotivational){return/*#__PURE__*/_jsx(\"div\",{className:\"absolute opacity-90\",style:{left:\"\".concat(piece.left,\"%\"),top:\"\".concat(20+Math.random()*60,\"%\"),fontSize:\"\".concat(piece.size||2,\"rem\"),animation:\"heart-beat \".concat(piece.duration,\"s ease-in-out \").concat(piece.delay,\"s infinite\"),zIndex:100},children:piece.emoji},piece.id);}if(piece.type==='sparkle'){return/*#__PURE__*/_jsx(\"div\",{className:\"absolute\",style:{left:\"\".concat(piece.left,\"%\"),width:\"\".concat(piece.size,\"px\"),height:\"\".concat(piece.size,\"px\"),background:\"radial-gradient(circle, \".concat(piece.color,\", transparent)\"),borderRadius:'50%',animation:\"premium-sparkle \".concat(piece.duration,\"s ease-in-out \").concat(piece.delay,\"s infinite\"),top:\"\".concat(Math.random()*100,\"%\"),boxShadow:\"0 0 \".concat(piece.size*2,\"px \").concat(piece.color),zIndex:100}},piece.id);}if(piece.type==='burst'){return/*#__PURE__*/_jsx(\"div\",{className:\"absolute opacity-90\",style:{left:\"\".concat(piece.left,\"%\"),width:\"\".concat(piece.size,\"px\"),height:\"\".concat(piece.size,\"px\"),backgroundColor:piece.color,borderRadius:piece.shape==='circle'?'50%':piece.shape==='triangle'?'0':'0%',clipPath:piece.shape==='triangle'?'polygon(50% 0%, 0% 100%, 100% 100%)':'none',animation:\"confetti-burst \".concat(piece.duration,\"s ease-out \").concat(piece.delay,\"s forwards\"),top:'40%','--random-x':\"\".concat(piece.randomX,\"px\"),boxShadow:\"0 0 \".concat(piece.size,\"px \").concat(piece.color,\"40\"),zIndex:100}},piece.id);}// Regular premium confetti\nreturn/*#__PURE__*/_jsx(\"div\",{className:\"absolute opacity-90\",style:{left:\"\".concat(piece.left,\"%\"),width:\"\".concat(piece.size,\"px\"),height:\"\".concat(piece.size,\"px\"),backgroundColor:piece.color,borderRadius:piece.shape==='circle'?'50%':piece.shape==='triangle'?'0':'0%',clipPath:piece.shape==='triangle'?'polygon(50% 0%, 0% 100%, 100% 100%)':'none',animation:\"confetti-fall \".concat(piece.duration,\"s ease-out \").concat(piece.delay,\"s forwards\"),top:'-20px',boxShadow:\"0 0 \".concat(piece.size,\"px \").concat(piece.color,\"60\"),border:\"1px solid \".concat(piece.color),background:\"linear-gradient(45deg, \".concat(piece.color,\", \").concat(piece.color,\"80)\"),zIndex:100}},piece.id);}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        @keyframes confetti-fall {\\n          0% {\\n            transform: translateY(-20px) rotate(0deg) scale(0);\\n            opacity: 0;\\n          }\\n          10% {\\n            opacity: 1;\\n            transform: translateY(0px) rotate(36deg) scale(1);\\n          }\\n          100% {\\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\\n            opacity: 0;\\n          }\\n        }\\n\\n        @keyframes confetti-burst {\\n          0% {\\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\\n            opacity: 0;\\n          }\\n          15% {\\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\\n            opacity: 1;\\n          }\\n          100% {\\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\\n            opacity: 0;\\n          }\\n        }\\n\\n        @keyframes premium-sparkle {\\n          0%, 100% {\\n            transform: scale(0) rotate(0deg);\\n            opacity: 0;\\n          }\\n          50% {\\n            transform: scale(1.5) rotate(180deg);\\n            opacity: 1;\\n          }\\n        }\\n\\n\\n\\n        @keyframes heart-beat {\\n          0%, 100% { transform: scale(1); }\\n          50% { transform: scale(1.3); }\\n        }\\n\\n        @keyframes text-bounce {\\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\\n          40% { transform: translateY(-20px) scale(1.1); }\\n          60% { transform: translateY(-10px) scale(1.05); }\\n        }\\n\\n        @keyframes text-glow {\\n          0%, 100% {\\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\\n            transform: scale(1);\\n          }\\n          50% {\\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\\n            transform: scale(1.05);\\n          }\\n        }\\n\\n        @keyframes rainbow-text {\\n          0% {\\n            color: #FF6B6B;\\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\\n          }\\n          16% {\\n            color: #FFD93D;\\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\\n          }\\n          33% {\\n            color: #6BCF7F;\\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\\n          }\\n          50% {\\n            color: #4D96FF;\\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\\n          }\\n          66% {\\n            color: #9B59B6;\\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\\n          }\\n          83% {\\n            color: #FF69B4;\\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\\n          }\\n          100% {\\n            color: #FF6B6B;\\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\\n          }\\n        }\\n\\n        @keyframes shake-celebrate {\\n          0%, 100% { transform: translateX(0) rotate(0deg); }\\n          10% { transform: translateX(-5px) rotate(-1deg); }\\n          20% { transform: translateX(5px) rotate(1deg); }\\n          30% { transform: translateX(-5px) rotate(-1deg); }\\n          40% { transform: translateX(5px) rotate(1deg); }\\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\\n          60% { transform: translateX(3px) rotate(0.5deg); }\\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\\n          80% { transform: translateX(2px) rotate(0.5deg); }\\n          90% { transform: translateX(-1px) rotate(0deg); }\\n        }\\n\\n        @keyframes zoom-in-out {\\n          0%, 100% { transform: scale(1); }\\n          50% { transform: scale(1.2); }\\n        }\\n\\n        .animate-text-bounce {\\n          animation: text-bounce 2s ease-in-out infinite;\\n        }\\n\\n        .animate-text-glow {\\n          animation: text-glow 2s ease-in-out infinite;\\n        }\\n\\n        .animate-rainbow {\\n          animation: rainbow-text 3s linear infinite;\\n        }\\n\\n        .animate-shake-celebrate {\\n          animation: shake-celebrate 1s ease-in-out infinite;\\n        }\\n\\n        .animate-zoom {\\n          animation: zoom-in-out 2s ease-in-out infinite;\\n        }\\n\\n        @keyframes elegant-scale {\\n          0%, 100% {\\n            transform: scale(1) rotateY(0deg);\\n            text-shadow: 0 0 20px currentColor;\\n          }\\n          50% {\\n            transform: scale(1.1) rotateY(5deg);\\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\\n          }\\n        }\\n\\n        @keyframes premium-pulse {\\n          0%, 100% {\\n            transform: scale(1);\\n            opacity: 1;\\n          }\\n          50% {\\n            transform: scale(1.05);\\n            opacity: 0.9;\\n          }\\n        }\\n\\n        @keyframes smooth-glow {\\n          0%, 100% {\\n            text-shadow: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;\\n            filter: brightness(1) saturate(1.2);\\n          }\\n          50% {\\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;\\n            filter: brightness(1.3) saturate(1.5);\\n          }\\n        }\\n\\n        @keyframes rainbow-glow {\\n          0% {\\n            color: #FF6B6B;\\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n          16% {\\n            color: #FFD93D;\\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D, 0 0 60px #FFD93D;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n          33% {\\n            color: #6BCF7F;\\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F, 0 0 60px #6BCF7F;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n          50% {\\n            color: #4D96FF;\\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF, 0 0 60px #4D96FF;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n          66% {\\n            color: #9B59B6;\\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6, 0 0 60px #9B59B6;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n          83% {\\n            color: #FF69B4;\\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4, 0 0 60px #FF69B4;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n          100% {\\n            color: #FF6B6B;\\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n        }\\n\\n        @keyframes celebration-bounce {\\n          0%, 20%, 50%, 80%, 100% {\\n            transform: translateY(0) scale(1);\\n          }\\n          40% {\\n            transform: translateY(-15px) scale(1.05);\\n          }\\n          60% {\\n            transform: translateY(-8px) scale(1.02);\\n          }\\n        }\\n\\n        .animate-elegant {\\n          animation: elegant-scale 3s ease-in-out infinite;\\n        }\\n\\n        .animate-premium-pulse {\\n          animation: premium-pulse 2s ease-in-out infinite;\\n        }\\n\\n        .animate-smooth-glow {\\n          animation: smooth-glow 2.5s ease-in-out infinite;\\n        }\\n\\n        .animate-celebration {\\n          animation: celebration-bounce 2s ease-in-out infinite;\\n        }\\n\\n        .animate-rainbow {\\n          animation: rainbow-text 3s linear infinite;\\n        }\\n\\n        .animate-rainbow-glow {\\n          animation: rainbow-glow 3s linear infinite;\\n        }\\n\\n        @keyframes red-glow {\\n          0%, 100% {\\n            color: #EF4444;\\n            text-shadow: 0 0 20px #EF4444, 0 0 40px #EF4444, 0 0 60px #EF4444;\\n            filter: brightness(1.2) saturate(1.3);\\n          }\\n          25% {\\n            color: #DC2626;\\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\\n            filter: brightness(1.3) saturate(1.4);\\n          }\\n          50% {\\n            color: #B91C1C;\\n            text-shadow: 0 0 30px #B91C1C, 0 0 60px #B91C1C, 0 0 90px #B91C1C;\\n            filter: brightness(1.4) saturate(1.5);\\n          }\\n          75% {\\n            color: #DC2626;\\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\\n            filter: brightness(1.3) saturate(1.4);\\n          }\\n        }\\n\\n        .animate-red-glow {\\n          animation: red-glow 2.5s ease-in-out infinite;\\n        }\\n\\n        @keyframes premium-green-flash {\\n          0% {\\n            background: linear-gradient(45deg, transparent, transparent);\\n            box-shadow: none;\\n          }\\n          25% {\\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\\n          }\\n          50% {\\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\\n          }\\n          75% {\\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\\n          }\\n          100% {\\n            background: linear-gradient(45deg, transparent, transparent);\\n            box-shadow: none;\\n          }\\n        }\\n\\n        @keyframes premium-red-flash {\\n          0% {\\n            background: linear-gradient(45deg, transparent, transparent);\\n            box-shadow: none;\\n          }\\n          25% {\\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\\n          }\\n          50% {\\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\\n          }\\n          75% {\\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\\n          }\\n          100% {\\n            background: linear-gradient(45deg, transparent, transparent);\\n            box-shadow: none;\\n          }\\n        }\\n\\n        .flash-green {\\n          animation: premium-green-flash 0.8s ease-in-out 2;\\n        }\\n\\n        .flash-red {\\n          animation: premium-red-flash 0.8s ease-in-out 2;\\n        }\\n      \"}),isFlashing&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 pointer-events-none\",style:{background:isPassed?'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)':'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',animation:isPassed?'premium-green-flash 0.8s ease-in-out infinite':'premium-red-flash 0.8s ease-in-out infinite',zIndex:5}}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-2xl shadow-2xl border-2 w-full relative \".concat(isPassed?'border-green-400 shadow-green-200':'border-red-400 shadow-red-200',\" \").concat(isFlashing?'shadow-3xl':''),style:{background:isFlashing?isPassed?'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))':'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))':'white',boxShadow:isFlashing?isPassed?'0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)':'0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)':'0 25px 50px rgba(0,0,0,0.15)',zIndex:10,padding:window.innerWidth<=768?'16px':window.innerWidth<=1024?'24px':'32px',maxWidth:window.innerWidth<=768?'100%':window.innerWidth<=1024?'90%':'800px'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",style:{marginBottom:window.innerWidth<=768?'16px':'32px'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-flex items-center justify-center rounded-full mb-4 relative \".concat(isPassed?'bg-gradient-to-br from-green-100 to-emerald-100':'bg-gradient-to-br from-red-100 to-pink-100'),style:{width:window.innerWidth<=768?'60px':'96px',height:window.innerWidth<=768?'60px':'96px'},children:/*#__PURE__*/_jsx(TbTrophy,{className:\"\".concat(isPassed?'text-yellow-500':'text-gray-500'),style:{width:window.innerWidth<=768?'30px':'48px',height:window.innerWidth<=768?'30px':'48px'}})}),/*#__PURE__*/_jsx(\"h1\",{className:\"font-bold mb-4 \".concat(isPassed?'text-green-600 animate-elegant animate-smooth-glow':'animate-premium-pulse animate-red-glow'),style:{fontSize:window.innerWidth<=768?'24px':window.innerWidth<=1024?'36px':'48px'},children:isPassed?/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center justify-center\",style:{gap:window.innerWidth<=768?'8px':'16px',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(\"span\",{className:\"animate-celebration\",style:{fontSize:window.innerWidth<=768?'32px':'56px'},children:\"\\uD83C\\uDF89\"}),/*#__PURE__*/_jsx(\"span\",{className:\"animate-rainbow-glow animate-elegant\",children:\"Congratulations!\"}),/*#__PURE__*/_jsx(\"span\",{className:\"animate-celebration\",style:{fontSize:window.innerWidth<=768?'32px':'56px'},children:\"\\uD83C\\uDF89\"})]}):/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center justify-center\",style:{gap:window.innerWidth<=768?'8px':'16px',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(\"span\",{className:\"animate-premium-pulse\",style:{fontSize:window.innerWidth<=768?'32px':'56px'},children:\"\\uD83D\\uDCAA\"}),/*#__PURE__*/_jsx(\"span\",{className:\"animate-red-glow animate-elegant\",children:\"Keep Going!\"}),/*#__PURE__*/_jsx(\"span\",{className:\"animate-premium-pulse\",style:{fontSize:window.innerWidth<=768?'32px':'56px'},children:\"\\uD83D\\uDCAA\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold mb-4 \".concat(isPassed?'animate-celebration animate-rainbow-glow':'animate-premium-pulse animate-red-glow'),children:isPassed?/*#__PURE__*/_jsx(\"span\",{className:\"animate-elegant animate-rainbow-glow\",children:\"\\u2728 You Passed! \\u2728\"}):/*#__PURE__*/_jsx(\"span\",{className:\"animate-red-glow animate-elegant\",children:\"\\uD83C\\uDF1F You Can Do It! \\uD83C\\uDF1F\"})}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600 mt-2 text-lg\",children:[\"\\uD83D\\uDCDA \",quizName,\" - \",quizSubject]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center mb-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"inline-block px-8 py-4 rounded-2xl \".concat(isPassed?'bg-green-50 border-2 border-green-200':'bg-red-50 border-2 border-red-200'),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-5xl font-bold mb-2 \".concat(isPassed?'text-green-600':'text-red-600'),children:[percentage,\"%\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-600\",children:\"Your Score\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 mb-2 justify-center items-center\",style:{flexWrap:'wrap'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'#ffffff',padding:'4px 8px',border:'1px solid #e5e7eb',borderRadius:'6px',display:'flex',alignItems:'center',gap:'8px',fontSize:'12px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'2px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'10px'},children:\"\\u2705\"}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'14px',fontWeight:'bold',color:'#16a34a'},children:correctAnswers})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'2px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'10px'},children:\"\\u274C\"}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'14px',fontWeight:'bold',color:'#dc2626'},children:totalQuestions-correctAnswers})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'#ffffff',padding:'4px 8px',border:'1px solid #e5e7eb',borderRadius:'6px',display:'flex',alignItems:'center',gap:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'10px'},children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'14px',fontWeight:'bold',color:isPassed?'#16a34a':'#dc2626'},children:[percentage,\"%\"]})]}),timeTaken&&timeTaken>0&&/*#__PURE__*/_jsxs(\"div\",{style:{backgroundColor:'#ffffff',padding:'4px 8px',border:'1px solid #e5e7eb',borderRadius:'6px',display:'flex',alignItems:'center',gap:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'10px'},children:\"\\u23F1\\uFE0F\"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'14px',fontWeight:'bold',color:'#2563eb'},children:[Math.floor(timeTaken/60),\":\",(timeTaken%60).toString().padStart(2,'0')]})]})]}),xpData&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbStar,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-purple-600 font-bold text-xl\",children:[\"+\",xpData.xpAwarded||0,\" XP\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:\"Earned\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-indigo-600\",children:(((user===null||user===void 0?void 0:user.totalXP)||0)+(xpData.xpAwarded||0)).toLocaleString()}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-600\",children:[\"Total XP \\u2022 Level \",(user===null||user===void 0?void 0:user.currentLevel)||1]})]})]})}),!xpData&&user&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbStar,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:\"Your Progress\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-indigo-600\",children:(user.totalXP||0).toLocaleString()}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-600\",children:[\"Total XP \\u2022 Level \",user.currentLevel||1]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbBrain,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\uD83D\\uDCDA Learning Summary\"})]})}),resultDetails&&resultDetails.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbChartBar,{className:\"w-6 h-6 text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:\"\\uD83D\\uDCCB Question by Question Review\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4 max-h-96 overflow-y-auto\",children:resultDetails.map((detail,index)=>{// Debug: Log question data to see what's available\nconsole.log(\"Question \".concat(index+1,\" data:\"),detail);return/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl \".concat(detail.isCorrect?'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300':'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'),style:{boxShadow:detail.isCorrect?'0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)':'0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-4 \".concat(detail.isCorrect?'bg-green-300 border-b-4 border-green-500':'bg-red-300 border-b-4 border-red-500'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-full flex items-center justify-center font-bold \".concat(detail.isCorrect?'bg-green-500 text-white shadow-lg':'bg-red-500 text-white shadow-lg'),children:detail.isCorrect?/*#__PURE__*/_jsx(TbCheck,{className:\"w-5 h-5\"}):/*#__PURE__*/_jsx(TbX,{className:\"w-5 h-5\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-bold text-gray-900 text-lg\",children:[\"Question \",index+1]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center gap-2 mt-1\",children:/*#__PURE__*/_jsx(\"span\",{className:\"px-3 py-1 rounded-full text-xs font-bold \".concat(detail.isCorrect?'bg-green-500 text-white':'bg-red-500 text-white'),children:detail.isCorrect?'✅ CORRECT':'❌ WRONG'})})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 bg-white p-3 rounded-lg border\",children:detail.questionText||detail.questionName})}),(detail.questionType==='image'||detail.questionImage||detail.image||detail.imageUrl)&&(detail.questionImage||detail.image||detail.imageUrl)&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 rounded-lg border-2 \".concat(detail.isCorrect?'bg-green-50 border-green-200':'bg-red-50 border-red-200'),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 mb-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-gray-700\",children:\"\\uD83D\\uDCF7 Question Image:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"px-2 py-1 rounded-full text-xs font-bold \".concat(detail.isCorrect?'bg-green-500 text-white':'bg-red-500 text-white'),children:detail.isCorrect?'✅ CORRECT':'❌ WRONG'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-2 rounded-lg border\",children:[/*#__PURE__*/_jsx(\"img\",{src:detail.questionImage||detail.image||detail.imageUrl,alt:\"Question Image\",className:\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\",style:{maxHeight:'300px'},onError:e=>{e.target.style.display='none';e.target.nextSibling.style.display='block';}}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\",style:{display:'none'},children:\"\\uD83D\\uDCF7 Image could not be loaded\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded-lg border-4 \".concat(detail.isCorrect?'bg-green-50 border-green-500':'bg-red-50 border-red-500'),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 rounded-full flex items-center justify-center \".concat(detail.isCorrect?'bg-green-500':'bg-red-500'),children:detail.isCorrect?/*#__PURE__*/_jsx(TbCheck,{className:\"w-4 h-4 text-white\"}):/*#__PURE__*/_jsx(TbX,{className:\"w-4 h-4 text-white\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-gray-700\",children:\"Your Answer:\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 rounded-lg font-bold text-lg \".concat(detail.isCorrect?'bg-green-100 text-green-700 border border-green-200':'bg-red-100 text-red-700 border border-red-200'),children:detail.userAnswer||'No answer provided'})]}),!detail.isCorrect&&/*#__PURE__*/_jsxs(\"div\",{className:\"bg-green-50 p-4 rounded-lg border-4 border-green-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbCheck,{className:\"w-4 h-4 text-white\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-gray-700\",children:\"Correct Answer:\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\",children:detail.correctAnswer})]}),!detail.isCorrect&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 \".concat(loadingExplanations[\"question_\".concat(index)]?'bg-gray-400 cursor-not-allowed':'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'),onClick:()=>fetchExplanation(index,detail),disabled:loadingExplanations[\"question_\".concat(index)],children:loadingExplanations[\"question_\".concat(index)]?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"}),\"Getting Explanation...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TbBrain,{className:\"w-5 h-5\"}),\"Get Explanation\"]})}),explanations[\"question_\".concat(index)]&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 mb-3\",children:[/*#__PURE__*/_jsx(TbBrain,{className:\"w-5 h-5 text-blue-600\"}),/*#__PURE__*/_jsx(\"h6\",{className:\"font-bold text-blue-800\",children:\"\\uD83D\\uDCA1 Mathematical Solution:\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-blue-700 leading-relaxed\",children:/*#__PURE__*/_jsx(ContentRenderer,{text:explanations[\"question_\".concat(index)]})})]})]})]})]})]},detail.questionId||index);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-4\",style:{flexDirection:window.innerWidth<=768?'column':'row'},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:e=>{e.preventDefault();console.log('🔥 More Quizzes button clicked!');handleBackToQuizzes();},className:\"flex-1 flex items-center justify-center gap-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",style:{padding:window.innerWidth<=768?'12px 16px':'12px 24px',fontSize:window.innerWidth<=768?'14px':'16px'},type:\"button\",children:[/*#__PURE__*/_jsx(TbHome,{style:{width:window.innerWidth<=768?'16px':'20px',height:window.innerWidth<=768?'16px':'20px'}}),\"More Quizzes\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:e=>{e.preventDefault();console.log('🔥 Retake Quiz button clicked!');handleRetakeQuiz();},className:\"flex-1 flex items-center justify-center gap-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",style:{padding:window.innerWidth<=768?'12px 16px':'12px 24px',fontSize:window.innerWidth<=768?'14px':'16px'},type:\"button\",children:[/*#__PURE__*/_jsx(TbTrophy,{style:{width:window.innerWidth<=768?'16px':'20px',height:window.innerWidth<=768?'16px':'20px'}}),\"Retake Quiz\"]})]})]})]});};export default QuizResult;", "map": {"version": 3, "names": ["React", "startTransition", "useEffect", "useState", "useNavigate", "useLocation", "useParams", "useSelector", "TbTrophy", "TbCheck", "TbX", "TbHome", "TbStar", "TbBrain", "TbChartBar", "chatWithChatGPTToExplainAns", "Content<PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "QuizResult", "navigate", "location", "id", "user", "state", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "confetti", "set<PERSON>on<PERSON>tti", "explanations", "setExplanations", "loadingExplanations", "setLoadingExplanations", "isFlashing", "setIsFlashing", "playSound", "setTimeout", "audioContext", "window", "AudioContext", "webkitAudioContext", "playTone", "frequency", "startTime", "duration", "type", "arguments", "length", "undefined", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createClap", "noise", "createBufferSource", "buffer", "createBuffer", "sampleRate", "data", "getChannelData", "i", "Math", "random", "filter", "createBiquadFilter", "now", "currentTime", "error", "console", "log", "premiumConfetti", "colors", "push", "concat", "left", "delay", "color", "floor", "shape", "size", "randomX", "motivationalElements", "motivationalEmojis", "emoji", "isMotivational", "fetchExplanation", "questionIndex", "detail", "<PERSON><PERSON><PERSON>", "prev", "_objectSpread", "response", "question", "questionText", "questionName", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "userAnswer", "imageUrl", "questionImage", "image", "success", "explanation", "handleBackToQuizzes", "handleRetakeQuiz", "className", "style", "padding", "innerWidth", "children", "map", "piece", "top", "fontSize", "animation", "zIndex", "width", "height", "background", "borderRadius", "boxShadow", "backgroundColor", "clipPath", "border", "max<PERSON><PERSON><PERSON>", "marginBottom", "gap", "flexWrap", "display", "alignItems", "fontWeight", "toString", "padStart", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "index", "isCorrect", "questionType", "src", "alt", "maxHeight", "onError", "e", "target", "nextS<PERSON>ling", "onClick", "disabled", "text", "questionId", "flexDirection", "preventDefault"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport ContentRenderer from '../../../components/ContentRenderer';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = (startTime) => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n\n            noise.buffer = buffer;\n\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = [\n          '#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1',\n          '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB',\n          '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'\n        ];\n\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60, // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10, // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n\n    try {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: true }));\n\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: false }));\n    }\n  };\n\n\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center relative overflow-hidden ${\n      isPassed\n        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100'\n        : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'\n    } ${isFlashing ? (isPassed ? 'flash-green' : 'flash-red') : ''}`}\n    style={{\n      padding: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '24px'\n    }}>\n\n      {/* Premium Confetti System */}\n      {confetti.map((piece) => {\n        if (piece.isMotivational) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                top: `${20 + Math.random() * 60}%`,\n                fontSize: `${piece.size || 2}rem`,\n                animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                zIndex: 100\n              }}\n            >\n              {piece.emoji}\n            </div>\n          );\n        }\n\n        if (piece.type === 'sparkle') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                background: `radial-gradient(circle, ${piece.color}, transparent)`,\n                borderRadius: '50%',\n                animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                top: `${Math.random() * 100}%`,\n                boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        if (piece.type === 'burst') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                backgroundColor: piece.color,\n                borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n                clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n                animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n                top: '40%',\n                '--random-x': `${piece.randomX}px`,\n                boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        // Regular premium confetti\n        return (\n          <div\n            key={piece.id}\n            className=\"absolute opacity-90\"\n            style={{\n              left: `${piece.left}%`,\n              width: `${piece.size}px`,\n              height: `${piece.size}px`,\n              backgroundColor: piece.color,\n              borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n              clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n              animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n              top: '-20px',\n              boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n              border: `1px solid ${piece.color}`,\n              background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n              zIndex: 100\n            }}\n          />\n        );\n      })}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;\n            filter: brightness(1) saturate(1.2);\n          }\n          50% {\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;\n            filter: brightness(1.3) saturate(1.5);\n          }\n        }\n\n        @keyframes rainbow-glow {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D, 0 0 60px #FFD93D;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F, 0 0 60px #6BCF7F;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF, 0 0 60px #4D96FF;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6, 0 0 60px #9B59B6;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4, 0 0 60px #FF69B4;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-rainbow-glow {\n          animation: rainbow-glow 3s linear infinite;\n        }\n\n        @keyframes red-glow {\n          0%, 100% {\n            color: #EF4444;\n            text-shadow: 0 0 20px #EF4444, 0 0 40px #EF4444, 0 0 60px #EF4444;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          25% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n          50% {\n            color: #B91C1C;\n            text-shadow: 0 0 30px #B91C1C, 0 0 60px #B91C1C, 0 0 90px #B91C1C;\n            filter: brightness(1.4) saturate(1.5);\n          }\n          75% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n        }\n\n        .animate-red-glow {\n          animation: red-glow 2.5s ease-in-out infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `}</style>\n\n      {/* Premium Overlay Effect */}\n      {isFlashing && (\n        <div\n          className=\"fixed inset-0 pointer-events-none\"\n          style={{\n            background: isPassed\n              ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)'\n              : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n            animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n            zIndex: 5\n          }}\n        />\n      )}\n\n      <div className={`bg-white rounded-2xl shadow-2xl border-2 w-full relative ${\n        isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'\n      } ${isFlashing ? 'shadow-3xl' : ''}`}\n      style={{\n        background: isFlashing\n          ? (isPassed\n              ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))'\n              : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))')\n          : 'white',\n        boxShadow: isFlashing\n          ? (isPassed\n              ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)'\n              : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)')\n          : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10,\n        padding: window.innerWidth <= 768 ? '16px' : window.innerWidth <= 1024 ? '24px' : '32px',\n        maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '800px'\n      }}>\n        {/* Header */}\n        <div\n          className=\"text-center\"\n          style={{ marginBottom: window.innerWidth <= 768 ? '16px' : '32px' }}\n        >\n          <div\n            className={`inline-flex items-center justify-center rounded-full mb-4 relative ${\n              isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n            }`}\n            style={{\n              width: window.innerWidth <= 768 ? '60px' : '96px',\n              height: window.innerWidth <= 768 ? '60px' : '96px'\n            }}\n          >\n            <TbTrophy\n              className={`${isPassed ? 'text-yellow-500' : 'text-gray-500'}`}\n              style={{\n                width: window.innerWidth <= 768 ? '30px' : '48px',\n                height: window.innerWidth <= 768 ? '30px' : '48px'\n              }}\n            />\n          </div>\n\n          <h1\n            className={`font-bold mb-4 ${\n              isPassed\n                ? 'text-green-600 animate-elegant animate-smooth-glow'\n                : 'animate-premium-pulse animate-red-glow'\n            }`}\n            style={{\n              fontSize: window.innerWidth <= 768 ? '24px' : window.innerWidth <= 1024 ? '36px' : '48px'\n            }}\n          >\n            {isPassed ? (\n              <span\n                className=\"flex items-center justify-center\"\n                style={{ gap: window.innerWidth <= 768 ? '8px' : '16px', flexWrap: 'wrap' }}\n              >\n                <span\n                  className=\"animate-celebration\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >🎉</span>\n                <span className=\"animate-rainbow-glow animate-elegant\">Congratulations!</span>\n                <span\n                  className=\"animate-celebration\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >🎉</span>\n              </span>\n            ) : (\n              <span\n                className=\"flex items-center justify-center\"\n                style={{ gap: window.innerWidth <= 768 ? '8px' : '16px', flexWrap: 'wrap' }}\n              >\n                <span\n                  className=\"animate-premium-pulse\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >💪</span>\n                <span className=\"animate-red-glow animate-elegant\">Keep Going!</span>\n                <span\n                  className=\"animate-premium-pulse\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >💪</span>\n              </span>\n            )}\n          </h1>\n\n          <div className={`text-3xl font-bold mb-4 ${\n            isPassed\n              ? 'animate-celebration animate-rainbow-glow'\n              : 'animate-premium-pulse animate-red-glow'\n          }`}>\n            {isPassed ? (\n              <span className=\"animate-elegant animate-rainbow-glow\">✨ You Passed! ✨</span>\n            ) : (\n              <span className=\"animate-red-glow animate-elegant\">🌟 You Can Do It! 🌟</span>\n            )}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Horizontal Compact Results */}\n        <div\n          className=\"flex gap-2 mb-2 justify-center items-center\"\n          style={{ flexWrap: 'wrap' }}\n        >\n          {/* Correct and Wrong - Horizontal */}\n          <div\n            style={{\n              backgroundColor: '#ffffff',\n              padding: '4px 8px',\n              border: '1px solid #e5e7eb',\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px'\n            }}\n          >\n            <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>\n              <span style={{ fontSize: '10px' }}>✅</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#16a34a' }}>{correctAnswers}</span>\n            </div>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>\n              <span style={{ fontSize: '10px' }}>❌</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#dc2626' }}>{totalQuestions - correctAnswers}</span>\n            </div>\n          </div>\n\n          {/* Score */}\n          <div\n            style={{\n              backgroundColor: '#ffffff',\n              padding: '4px 8px',\n              border: '1px solid #e5e7eb',\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              fontSize: '12px'\n            }}\n          >\n            <span style={{ fontSize: '10px' }}>📊</span>\n            <span style={{ fontSize: '14px', fontWeight: 'bold', color: isPassed ? '#16a34a' : '#dc2626' }}>{percentage}%</span>\n          </div>\n\n          {/* Time - Only if available */}\n          {timeTaken && timeTaken > 0 && (\n            <div\n              style={{\n                backgroundColor: '#ffffff',\n                padding: '4px 8px',\n                border: '1px solid #e5e7eb',\n                borderRadius: '6px',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                fontSize: '12px'\n              }}\n            >\n              <span style={{ fontSize: '10px' }}>⏱️</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#2563eb' }}>\n                {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => {\n                // Debug: Log question data to see what's available\n                console.log(`Question ${index + 1} data:`, detail);\n                return (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                    detail.isCorrect\n                      ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300'\n                      : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'\n                  }`}\n                  style={{\n                    boxShadow: detail.isCorrect\n                      ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)'\n                      : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n                  }}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-300 border-b-4 border-green-500'\n                      : 'bg-red-300 border-b-4 border-red-500'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image || detail.imageUrl}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div className={`p-4 rounded-lg border-4 ${\n                        detail.isCorrect\n                          ? 'bg-green-50 border-green-500'\n                          : 'bg-red-50 border-red-500'\n                      }`}>\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-4 border-green-500\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${\n                              loadingExplanations[`question_${index}`]\n                                ? 'bg-gray-400 cursor-not-allowed'\n                                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'\n                            }`}\n                            onClick={() => fetchExplanation(index, detail)}\n                            disabled={loadingExplanations[`question_${index}`]}\n                          >\n                            {loadingExplanations[`question_${index}`] ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                Getting Explanation...\n                              </>\n                            ) : (\n                              <>\n                                <TbBrain className=\"w-5 h-5\" />\n                                Get Explanation\n                              </>\n                            )}\n                          </button>\n\n                          {/* Explanation Display with Math Support */}\n                          {explanations[`question_${index}`] && (\n                            <div className=\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\">\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <TbBrain className=\"w-5 h-5 text-blue-600\" />\n                                <h6 className=\"font-bold text-blue-800\">💡 Mathematical Solution:</h6>\n                              </div>\n                              <div className=\"text-blue-700 leading-relaxed\">\n                                <ContentRenderer text={explanations[`question_${index}`]} />\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                );\n              })}\n            </div>\n\n\n          </div>\n        )}\n\n        {/* Actions */}\n        <div\n          className=\"flex gap-4\"\n          style={{\n            flexDirection: window.innerWidth <= 768 ? 'column' : 'row'\n          }}\n        >\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            style={{\n              padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n            }}\n            type=\"button\"\n          >\n            <TbHome\n              style={{\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }}\n            />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            style={{\n              padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n            }}\n            type=\"button\"\n          >\n            <TbTrophy\n              style={{\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }}\n            />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": "+HAAA,MAAO,CAAAA,KAAK,EAAIC,eAAe,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CACnE,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,QAAQ,CAAEC,OAAO,CAAEC,GAAG,CAAEC,MAAM,CAAEC,MAAM,CAAEC,OAAO,CAAEC,UAAU,KAAQ,gBAAgB,CAC5F,OAASC,2BAA2B,KAAQ,wBAAwB,CACpE,MAAO,CAAAC,eAAe,KAAM,qCAAqC,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,gCAAAC,QAAA,IAAAC,SAAA,yBAElE,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAC,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqB,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEqB,EAAG,CAAC,CAAGpB,SAAS,CAAC,CAAC,CAC1B,KAAM,CAAEqB,IAAK,CAAC,CAAGpB,WAAW,CAAEqB,KAAK,EAAKA,KAAK,CAACD,IAAI,CAAC,CACnD;AACA,KAAM,CAAAE,UAAU,CAAGJ,QAAQ,CAACG,KAAK,EAAI,CACnCE,UAAU,CAAE,CAAC,CACbC,cAAc,CAAE,CAAC,CACjBC,cAAc,CAAE,CAAC,CACjBC,SAAS,CAAE,CAAC,CACZC,aAAa,CAAE,EAAE,CACjBC,MAAM,CAAE,IAAI,CACZC,QAAQ,CAAE,MAAM,CAChBC,WAAW,CAAE,SAAS,CACtBC,iBAAiB,CAAE,EAAE,CACrBC,OAAO,CAAE,MACX,CAAC,CAED,KAAM,CACJT,UAAU,CACVC,cAAc,CACdC,cAAc,CACdC,SAAS,CACTE,MAAM,CACNC,QAAQ,CACRC,WAAW,CACXC,iBAAiB,CACjBC,OAAO,CACPL,aACF,CAAC,CAAGL,UAAU,CACd,KAAM,CAAAW,QAAQ,CAAGD,OAAO,GAAK,MAAM,EAAIT,UAAU,GAAKQ,iBAAiB,EAAI,EAAE,CAAC,CAE9E,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACwC,YAAY,CAAEC,eAAe,CAAC,CAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC,CACpD,KAAM,CAAC0C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAClE,KAAM,CAAC4C,UAAU,CAAEC,aAAa,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACAD,SAAS,CAAC,IAAM,CAEd;AACA,KAAM,CAAA+C,SAAS,CAAGA,CAAA,GAAM,CACtB;AACAD,aAAa,CAAC,IAAI,CAAC,CACnBE,UAAU,CAAC,IAAMF,aAAa,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAAE;AAE9C,GAAI,CACF,GAAIR,QAAQ,CAAE,CACZ;AACA,KAAM,CAAAW,YAAY,CAAG,IAAKC,MAAM,CAACC,YAAY,EAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC,CAE7E;AACA,KAAM,CAAAC,QAAQ,CAAG,QAAAA,CAACC,SAAS,CAAEC,SAAS,CAAEC,QAAQ,CAAoB,IAAlB,CAAAC,IAAI,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,MAAM,CAC7D,KAAM,CAAAG,UAAU,CAAGZ,YAAY,CAACa,gBAAgB,CAAC,CAAC,CAClD,KAAM,CAAAC,QAAQ,CAAGd,YAAY,CAACe,UAAU,CAAC,CAAC,CAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAChB,YAAY,CAACiB,WAAW,CAAC,CAE1CL,UAAU,CAACP,SAAS,CAACa,cAAc,CAACb,SAAS,CAAEC,SAAS,CAAC,CACzDM,UAAU,CAACJ,IAAI,CAAGA,IAAI,CAEtBM,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,CAAEZ,SAAS,CAAC,CAC1CQ,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,CAAEd,SAAS,CAAG,IAAI,CAAC,CAC5DQ,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,CAAEf,SAAS,CAAGC,QAAQ,CAAC,CAEtEK,UAAU,CAACU,KAAK,CAAChB,SAAS,CAAC,CAC3BM,UAAU,CAACW,IAAI,CAACjB,SAAS,CAAGC,QAAQ,CAAC,CACvC,CAAC,CAED;AACA,KAAM,CAAAiB,UAAU,CAAIlB,SAAS,EAAK,CAChC,KAAM,CAAAmB,KAAK,CAAGzB,YAAY,CAAC0B,kBAAkB,CAAC,CAAC,CAC/C,KAAM,CAAAC,MAAM,CAAG3B,YAAY,CAAC4B,YAAY,CAAC,CAAC,CAAE5B,YAAY,CAAC6B,UAAU,CAAG,GAAG,CAAE7B,YAAY,CAAC6B,UAAU,CAAC,CACnG,KAAM,CAAAC,IAAI,CAAGH,MAAM,CAACI,cAAc,CAAC,CAAC,CAAC,CAErC;AACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,IAAI,CAACpB,MAAM,CAAEsB,CAAC,EAAE,CAAE,CACpCF,IAAI,CAACE,CAAC,CAAC,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACjC,CAEAT,KAAK,CAACE,MAAM,CAAGA,MAAM,CAErB,KAAM,CAAAQ,MAAM,CAAGnC,YAAY,CAACoC,kBAAkB,CAAC,CAAC,CAChDD,MAAM,CAAC3B,IAAI,CAAG,UAAU,CACxB2B,MAAM,CAAC9B,SAAS,CAACa,cAAc,CAAC,IAAI,CAAEZ,SAAS,CAAC,CAEhD,KAAM,CAAAQ,QAAQ,CAAGd,YAAY,CAACe,UAAU,CAAC,CAAC,CAC1CD,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,CAAEZ,SAAS,CAAC,CAC1CQ,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,CAAEd,SAAS,CAAG,IAAI,CAAC,CAC5DQ,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,CAAEf,SAAS,CAAG,GAAG,CAAC,CAEjEmB,KAAK,CAACT,OAAO,CAACmB,MAAM,CAAC,CACrBA,MAAM,CAACnB,OAAO,CAACF,QAAQ,CAAC,CACxBA,QAAQ,CAACE,OAAO,CAAChB,YAAY,CAACiB,WAAW,CAAC,CAE1CQ,KAAK,CAACH,KAAK,CAAChB,SAAS,CAAC,CACtBmB,KAAK,CAACF,IAAI,CAACjB,SAAS,CAAG,GAAG,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA+B,GAAG,CAAGrC,YAAY,CAACsC,WAAW,CAEpC;AACAlC,QAAQ,CAAC,MAAM,CAAEiC,GAAG,CAAE,GAAG,CAAC,CAAE;AAC5BjC,QAAQ,CAAC,MAAM,CAAEiC,GAAG,CAAG,GAAG,CAAE,GAAG,CAAC,CAAE;AAClCjC,QAAQ,CAAC,MAAM,CAAEiC,GAAG,CAAG,GAAG,CAAE,GAAG,CAAC,CAAE;AAClCjC,QAAQ,CAAC,MAAM,CAAEiC,GAAG,CAAG,GAAG,CAAE,GAAG,CAAC,CAAE;AAElC;AACAb,UAAU,CAACa,GAAG,CAAG,GAAG,CAAC,CACrBb,UAAU,CAACa,GAAG,CAAG,GAAG,CAAC,CACrBb,UAAU,CAACa,GAAG,CAAG,GAAG,CAAC,CACrBb,UAAU,CAACa,GAAG,CAAG,GAAG,CAAC,CAEvB,CAAC,IAAM,CACL;AACA,KAAM,CAAArC,YAAY,CAAG,IAAKC,MAAM,CAACC,YAAY,EAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC,CAE7E,KAAM,CAAAC,QAAQ,CAAGA,CAACC,SAAS,CAAEC,SAAS,CAAEC,QAAQ,GAAK,CACnD,KAAM,CAAAK,UAAU,CAAGZ,YAAY,CAACa,gBAAgB,CAAC,CAAC,CAClD,KAAM,CAAAC,QAAQ,CAAGd,YAAY,CAACe,UAAU,CAAC,CAAC,CAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC,CAC5BA,QAAQ,CAACE,OAAO,CAAChB,YAAY,CAACiB,WAAW,CAAC,CAE1CL,UAAU,CAACP,SAAS,CAACa,cAAc,CAACb,SAAS,CAAEC,SAAS,CAAC,CACzDM,UAAU,CAACJ,IAAI,CAAG,MAAM,CAExBM,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,CAAEZ,SAAS,CAAC,CAC1CQ,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAAEd,SAAS,CAAG,IAAI,CAAC,CAC7DQ,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,CAAEf,SAAS,CAAGC,QAAQ,CAAC,CAEtEK,UAAU,CAACU,KAAK,CAAChB,SAAS,CAAC,CAC3BM,UAAU,CAACW,IAAI,CAACjB,SAAS,CAAGC,QAAQ,CAAC,CACvC,CAAC,CAED;AACA,KAAM,CAAA8B,GAAG,CAAGrC,YAAY,CAACsC,WAAW,CACpClC,QAAQ,CAAC,GAAG,CAAEiC,GAAG,CAAE,GAAG,CAAC,CAAE;AACzBjC,QAAQ,CAAC,MAAM,CAAEiC,GAAG,CAAG,GAAG,CAAE,GAAG,CAAC,CAAE;AACpC,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CACpC,CACF,CAAC,CAED;AACA,GAAIpD,QAAQ,CAAE,CACZ;AACA,KAAM,CAAAqD,eAAe,CAAG,EAAE,CAE1B;AACA,IAAK,GAAI,CAAAV,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,GAAG,CAAEA,CAAC,EAAE,CAAE,CAC5B,KAAM,CAAAW,MAAM,CAAG,CACb,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CACrD,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CACrD,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CACtD,CAEDD,eAAe,CAACE,IAAI,CAAC,CACnBrE,EAAE,aAAAsE,MAAA,CAAcb,CAAC,CAAE,CACnBc,IAAI,CAAE,EAAE,CAAGb,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAE;AAC/Ba,KAAK,CAAEd,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CACxB3B,QAAQ,CAAE,CAAC,CAAG0B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAC/Bc,KAAK,CAAEL,MAAM,CAACV,IAAI,CAACgB,KAAK,CAAChB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGS,MAAM,CAACjC,MAAM,CAAC,CAAC,CACxDwC,KAAK,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,UAAU,CAAC,CAACjB,IAAI,CAACgB,KAAK,CAAChB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAC,CACtEiB,IAAI,CAAE,CAAC,CAAGlB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAC3B1B,IAAI,CAAE,UAAU,CAChB4C,OAAO,CAAE,CAACnB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,GAAI;AACvC,CAAC,CAAC,CACJ,CAEA;AACA,IAAK,GAAI,CAAAF,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,EAAE,CAAEA,CAAC,EAAE,CAAE,CAC3BU,eAAe,CAACE,IAAI,CAAC,CACnBrE,EAAE,YAAAsE,MAAA,CAAab,CAAC,CAAE,CAClBc,IAAI,CAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CACzBa,KAAK,CAAEd,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CACxB3B,QAAQ,CAAE,CAAC,CAAG0B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAC/Bc,KAAK,CAAE,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CAACf,IAAI,CAACgB,KAAK,CAAChB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAC,CAClFiB,IAAI,CAAE,CAAC,CAAGlB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAC3B1B,IAAI,CAAE,SACR,CAAC,CAAC,CACJ,CAEA;AACA,IAAK,GAAI,CAAAwB,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,GAAG,CAAEA,CAAC,EAAE,CAAE,CAC5BU,eAAe,CAACE,IAAI,CAAC,CACnBrE,EAAE,UAAAsE,MAAA,CAAWb,CAAC,CAAE,CAChBc,IAAI,CAAE,EAAE,CAAGb,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAE;AAC/Ba,KAAK,CAAEd,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAC1B3B,QAAQ,CAAE,CAAC,CAAG0B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAC/Bc,KAAK,CAAE,CAAC,SAAS,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAC,CAACf,IAAI,CAACgB,KAAK,CAAChB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAC,CAClFgB,KAAK,CAAE,QAAQ,CACfC,IAAI,CAAE,CAAC,CAAGlB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAC3B1B,IAAI,CAAE,OAAO,CACb4C,OAAO,CAAE,CAACnB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,GACnC,CAAC,CAAC,CACJ,CAEA3C,WAAW,CAACmD,eAAe,CAAC,CAE5B;AACA3C,UAAU,CAAC,IAAMR,WAAW,CAAC,EAAE,CAAC,CAAE,KAAK,CAAC,CAC1C,CAAC,IAAM,CACL;AACA,KAAM,CAAA8D,oBAAoB,CAAG,EAAE,CAC/B,KAAM,CAAAC,kBAAkB,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,IAAI,CAAC,CAErF,IAAK,GAAI,CAAAtB,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,EAAE,CAAEA,CAAC,EAAE,CAAE,CAC3BqB,oBAAoB,CAACT,IAAI,CAAC,CACxBrE,EAAE,aAAAsE,MAAA,CAAcb,CAAC,CAAE,CACnBc,IAAI,CAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CACzBa,KAAK,CAAEd,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CACxB3B,QAAQ,CAAE,CAAC,CAAG0B,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAC/BqB,KAAK,CAAED,kBAAkB,CAACrB,IAAI,CAACgB,KAAK,CAAChB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGoB,kBAAkB,CAAC5C,MAAM,CAAC,CAAC,CAChF8C,cAAc,CAAE,IAAI,CACpBL,IAAI,CAAE,CAAC,CAAGlB,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAC5B,CAAC,CAAC,CACJ,CACA3C,WAAW,CAAC8D,oBAAoB,CAAC,CAEjC;AACAtD,UAAU,CAAC,IAAMR,WAAW,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACzC,CAEAO,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACT,QAAQ,CAAC,CAAC,CAId;AACA,KAAM,CAAAoE,gBAAgB,CAAG,KAAAA,CAAOC,aAAa,CAAEC,MAAM,GAAK,CACxD,KAAM,CAAAC,WAAW,aAAAf,MAAA,CAAea,aAAa,CAAE,CAE/C;AACA,GAAIhE,mBAAmB,CAACkE,WAAW,CAAC,EAAIpE,YAAY,CAACoE,WAAW,CAAC,CAAE,CACjE,OACF,CAEA,GAAI,CACFjE,sBAAsB,CAACkE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACD,WAAW,EAAG,IAAI,EAAG,CAAC,CAElE,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAnG,2BAA2B,CAAC,CACjDoG,QAAQ,CAAEL,MAAM,CAACM,YAAY,EAAIN,MAAM,CAACO,YAAY,CACpDC,cAAc,CAAER,MAAM,CAACS,aAAa,CACpCC,UAAU,CAAEV,MAAM,CAACU,UAAU,CAC7BC,QAAQ,CAAEX,MAAM,CAACY,aAAa,EAAIZ,MAAM,CAACa,KAAK,EAAIb,MAAM,CAACW,QAAQ,EAAI,IACvE,CAAC,CAAC,CAEF,GAAIP,QAAQ,CAACU,OAAO,CAAE,CACpBhF,eAAe,CAACoE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACfD,IAAI,MACP,CAACD,WAAW,EAAGG,QAAQ,CAACW,WAAW,EACnC,CAAC,CACL,CAAC,IAAM,CACLlC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEwB,QAAQ,CAACxB,KAAK,CAAC,CAC7D9C,eAAe,CAACoE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACfD,IAAI,MACP,CAACD,WAAW,EAAG,mFAAmF,EAClG,CAAC,CACL,CACF,CAAE,MAAOrB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD9C,eAAe,CAACoE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACfD,IAAI,MACP,CAACD,WAAW,EAAG,mFAAmF,EAClG,CAAC,CACL,CAAC,OAAS,CACRjE,sBAAsB,CAACkE,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACD,WAAW,EAAG,KAAK,EAAG,CAAC,CACrE,CACF,CAAC,CAID,KAAM,CAAAe,mBAAmB,CAAGA,CAAA,GAAM,CAChCnC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/C3F,eAAe,CAAC,IAAM,CACpBuB,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAuG,gBAAgB,CAAGA,CAAA,GAAM,CAC7BpC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAElE,EAAE,CAAC,CAC5C,GAAIA,EAAE,CAAE,CACNzB,eAAe,CAAC,IAAM,CACpBuB,QAAQ,UAAAwE,MAAA,CAAUtE,EAAE,SAAO,CAAC,CAC9B,CAAC,CAAC,CACJ,CAAC,IAAM,CACLiE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC5D3F,eAAe,CAAC,IAAM,CACpBuB,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAC,CAAC,CACJ,CACF,CAAC,CAED,mBACEJ,KAAA,QAAK4G,SAAS,2EAAAhC,MAAA,CACZxD,QAAQ,CACJ,4DAA4D,CAC5D,yDAAyD,MAAAwD,MAAA,CAC3DjD,UAAU,CAAIP,QAAQ,CAAG,aAAa,CAAG,WAAW,CAAI,EAAE,CAAG,CACjEyF,KAAK,CAAE,CACLC,OAAO,CAAE9E,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG/E,MAAM,CAAC+E,UAAU,EAAI,IAAI,CAAG,MAAM,CAAG,MACnF,CAAE,CAAAC,QAAA,EAGC3F,QAAQ,CAAC4F,GAAG,CAAEC,KAAK,EAAK,CACvB,GAAIA,KAAK,CAAC3B,cAAc,CAAE,CACxB,mBACEzF,IAAA,QAEE8G,SAAS,CAAC,qBAAqB,CAC/BC,KAAK,CAAE,CACLhC,IAAI,IAAAD,MAAA,CAAKsC,KAAK,CAACrC,IAAI,KAAG,CACtBsC,GAAG,IAAAvC,MAAA,CAAK,EAAE,CAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,KAAG,CAClCmD,QAAQ,IAAAxC,MAAA,CAAKsC,KAAK,CAAChC,IAAI,EAAI,CAAC,OAAK,CACjCmC,SAAS,eAAAzC,MAAA,CAAgBsC,KAAK,CAAC5E,QAAQ,mBAAAsC,MAAA,CAAiBsC,KAAK,CAACpC,KAAK,cAAY,CAC/EwC,MAAM,CAAE,GACV,CAAE,CAAAN,QAAA,CAEDE,KAAK,CAAC5B,KAAK,EAVP4B,KAAK,CAAC5G,EAWR,CAAC,CAEV,CAEA,GAAI4G,KAAK,CAAC3E,IAAI,GAAK,SAAS,CAAE,CAC5B,mBACEzC,IAAA,QAEE8G,SAAS,CAAC,UAAU,CACpBC,KAAK,CAAE,CACLhC,IAAI,IAAAD,MAAA,CAAKsC,KAAK,CAACrC,IAAI,KAAG,CACtB0C,KAAK,IAAA3C,MAAA,CAAKsC,KAAK,CAAChC,IAAI,MAAI,CACxBsC,MAAM,IAAA5C,MAAA,CAAKsC,KAAK,CAAChC,IAAI,MAAI,CACzBuC,UAAU,4BAAA7C,MAAA,CAA6BsC,KAAK,CAACnC,KAAK,kBAAgB,CAClE2C,YAAY,CAAE,KAAK,CACnBL,SAAS,oBAAAzC,MAAA,CAAqBsC,KAAK,CAAC5E,QAAQ,mBAAAsC,MAAA,CAAiBsC,KAAK,CAACpC,KAAK,cAAY,CACpFqC,GAAG,IAAAvC,MAAA,CAAKZ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC9B0D,SAAS,QAAA/C,MAAA,CAASsC,KAAK,CAAChC,IAAI,CAAG,CAAC,QAAAN,MAAA,CAAMsC,KAAK,CAACnC,KAAK,CAAE,CACnDuC,MAAM,CAAE,GACV,CAAE,EAZGJ,KAAK,CAAC5G,EAaZ,CAAC,CAEN,CAEA,GAAI4G,KAAK,CAAC3E,IAAI,GAAK,OAAO,CAAE,CAC1B,mBACEzC,IAAA,QAEE8G,SAAS,CAAC,qBAAqB,CAC/BC,KAAK,CAAE,CACLhC,IAAI,IAAAD,MAAA,CAAKsC,KAAK,CAACrC,IAAI,KAAG,CACtB0C,KAAK,IAAA3C,MAAA,CAAKsC,KAAK,CAAChC,IAAI,MAAI,CACxBsC,MAAM,IAAA5C,MAAA,CAAKsC,KAAK,CAAChC,IAAI,MAAI,CACzB0C,eAAe,CAAEV,KAAK,CAACnC,KAAK,CAC5B2C,YAAY,CAAER,KAAK,CAACjC,KAAK,GAAK,QAAQ,CAAG,KAAK,CAAGiC,KAAK,CAACjC,KAAK,GAAK,UAAU,CAAG,GAAG,CAAG,IAAI,CACxF4C,QAAQ,CAAEX,KAAK,CAACjC,KAAK,GAAK,UAAU,CAAG,qCAAqC,CAAG,MAAM,CACrFoC,SAAS,mBAAAzC,MAAA,CAAoBsC,KAAK,CAAC5E,QAAQ,gBAAAsC,MAAA,CAAcsC,KAAK,CAACpC,KAAK,cAAY,CAChFqC,GAAG,CAAE,KAAK,CACV,YAAY,IAAAvC,MAAA,CAAKsC,KAAK,CAAC/B,OAAO,MAAI,CAClCwC,SAAS,QAAA/C,MAAA,CAASsC,KAAK,CAAChC,IAAI,QAAAN,MAAA,CAAMsC,KAAK,CAACnC,KAAK,MAAI,CACjDuC,MAAM,CAAE,GACV,CAAE,EAdGJ,KAAK,CAAC5G,EAeZ,CAAC,CAEN,CAEA;AACA,mBACER,IAAA,QAEE8G,SAAS,CAAC,qBAAqB,CAC/BC,KAAK,CAAE,CACLhC,IAAI,IAAAD,MAAA,CAAKsC,KAAK,CAACrC,IAAI,KAAG,CACtB0C,KAAK,IAAA3C,MAAA,CAAKsC,KAAK,CAAChC,IAAI,MAAI,CACxBsC,MAAM,IAAA5C,MAAA,CAAKsC,KAAK,CAAChC,IAAI,MAAI,CACzB0C,eAAe,CAAEV,KAAK,CAACnC,KAAK,CAC5B2C,YAAY,CAAER,KAAK,CAACjC,KAAK,GAAK,QAAQ,CAAG,KAAK,CAAGiC,KAAK,CAACjC,KAAK,GAAK,UAAU,CAAG,GAAG,CAAG,IAAI,CACxF4C,QAAQ,CAAEX,KAAK,CAACjC,KAAK,GAAK,UAAU,CAAG,qCAAqC,CAAG,MAAM,CACrFoC,SAAS,kBAAAzC,MAAA,CAAmBsC,KAAK,CAAC5E,QAAQ,gBAAAsC,MAAA,CAAcsC,KAAK,CAACpC,KAAK,cAAY,CAC/EqC,GAAG,CAAE,OAAO,CACZQ,SAAS,QAAA/C,MAAA,CAASsC,KAAK,CAAChC,IAAI,QAAAN,MAAA,CAAMsC,KAAK,CAACnC,KAAK,MAAI,CACjD+C,MAAM,cAAAlD,MAAA,CAAesC,KAAK,CAACnC,KAAK,CAAE,CAClC0C,UAAU,2BAAA7C,MAAA,CAA4BsC,KAAK,CAACnC,KAAK,OAAAH,MAAA,CAAKsC,KAAK,CAACnC,KAAK,OAAK,CACtEuC,MAAM,CAAE,GACV,CAAE,EAfGJ,KAAK,CAAC5G,EAgBZ,CAAC,CAEN,CAAC,CAAC,cAGFR,IAAA,UAAOD,GAAG,MAAAmH,QAAA,wtUAkUD,CAAC,CAGTrF,UAAU,eACT7B,IAAA,QACE8G,SAAS,CAAC,mCAAmC,CAC7CC,KAAK,CAAE,CACLY,UAAU,CAAErG,QAAQ,CAChB,6GAA6G,CAC7G,6GAA6G,CACjHiG,SAAS,CAAEjG,QAAQ,CAAG,+CAA+C,CAAG,6CAA6C,CACrHkG,MAAM,CAAE,CACV,CAAE,CACH,CACF,cAEDtH,KAAA,QAAK4G,SAAS,6DAAAhC,MAAA,CACZxD,QAAQ,CAAG,mCAAmC,CAAG,+BAA+B,MAAAwD,MAAA,CAC9EjD,UAAU,CAAG,YAAY,CAAG,EAAE,CAAG,CACrCkF,KAAK,CAAE,CACLY,UAAU,CAAE9F,UAAU,CACjBP,QAAQ,CACL,0EAA0E,CAC1E,0EAA0E,CAC9E,OAAO,CACXuG,SAAS,CAAEhG,UAAU,CAChBP,QAAQ,CACL,sEAAsE,CACtE,sEAAsE,CAC1E,8BAA8B,CAClCkG,MAAM,CAAE,EAAE,CACVR,OAAO,CAAE9E,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG/E,MAAM,CAAC+E,UAAU,EAAI,IAAI,CAAG,MAAM,CAAG,MAAM,CACxFgB,QAAQ,CAAE/F,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG/E,MAAM,CAAC+E,UAAU,EAAI,IAAI,CAAG,KAAK,CAAG,OACpF,CAAE,CAAAC,QAAA,eAEAhH,KAAA,QACE4G,SAAS,CAAC,aAAa,CACvBC,KAAK,CAAE,CAAEmB,YAAY,CAAEhG,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAO,CAAE,CAAAC,QAAA,eAEpElH,IAAA,QACE8G,SAAS,uEAAAhC,MAAA,CACPxD,QAAQ,CAAG,iDAAiD,CAAG,4CAA4C,CAC1G,CACHyF,KAAK,CAAE,CACLU,KAAK,CAAEvF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDS,MAAM,CAAExF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CAAAC,QAAA,cAEFlH,IAAA,CAACV,QAAQ,EACPwH,SAAS,IAAAhC,MAAA,CAAKxD,QAAQ,CAAG,iBAAiB,CAAG,eAAe,CAAG,CAC/DyF,KAAK,CAAE,CACLU,KAAK,CAAEvF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDS,MAAM,CAAExF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACH,CAAC,CACC,CAAC,cAENjH,IAAA,OACE8G,SAAS,mBAAAhC,MAAA,CACPxD,QAAQ,CACJ,oDAAoD,CACpD,wCAAwC,CAC3C,CACHyF,KAAK,CAAE,CACLO,QAAQ,CAAEpF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG/E,MAAM,CAAC+E,UAAU,EAAI,IAAI,CAAG,MAAM,CAAG,MACrF,CAAE,CAAAC,QAAA,CAED5F,QAAQ,cACPpB,KAAA,SACE4G,SAAS,CAAC,kCAAkC,CAC5CC,KAAK,CAAE,CAAEoB,GAAG,CAAEjG,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAAM,CAAEmB,QAAQ,CAAE,MAAO,CAAE,CAAAlB,QAAA,eAE5ElH,IAAA,SACE8G,SAAS,CAAC,qBAAqB,CAC/BC,KAAK,CAAE,CAAEO,QAAQ,CAAEpF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAO,CAAE,CAAAC,QAAA,CACjE,cAAE,CAAM,CAAC,cACVlH,IAAA,SAAM8G,SAAS,CAAC,sCAAsC,CAAAI,QAAA,CAAC,kBAAgB,CAAM,CAAC,cAC9ElH,IAAA,SACE8G,SAAS,CAAC,qBAAqB,CAC/BC,KAAK,CAAE,CAAEO,QAAQ,CAAEpF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAO,CAAE,CAAAC,QAAA,CACjE,cAAE,CAAM,CAAC,EACN,CAAC,cAEPhH,KAAA,SACE4G,SAAS,CAAC,kCAAkC,CAC5CC,KAAK,CAAE,CAAEoB,GAAG,CAAEjG,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,KAAK,CAAG,MAAM,CAAEmB,QAAQ,CAAE,MAAO,CAAE,CAAAlB,QAAA,eAE5ElH,IAAA,SACE8G,SAAS,CAAC,uBAAuB,CACjCC,KAAK,CAAE,CAAEO,QAAQ,CAAEpF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAO,CAAE,CAAAC,QAAA,CACjE,cAAE,CAAM,CAAC,cACVlH,IAAA,SAAM8G,SAAS,CAAC,kCAAkC,CAAAI,QAAA,CAAC,aAAW,CAAM,CAAC,cACrElH,IAAA,SACE8G,SAAS,CAAC,uBAAuB,CACjCC,KAAK,CAAE,CAAEO,QAAQ,CAAEpF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAO,CAAE,CAAAC,QAAA,CACjE,cAAE,CAAM,CAAC,EACN,CACP,CACC,CAAC,cAELlH,IAAA,QAAK8G,SAAS,4BAAAhC,MAAA,CACZxD,QAAQ,CACJ,0CAA0C,CAC1C,wCAAwC,CAC3C,CAAA4F,QAAA,CACA5F,QAAQ,cACPtB,IAAA,SAAM8G,SAAS,CAAC,sCAAsC,CAAAI,QAAA,CAAC,2BAAe,CAAM,CAAC,cAE7ElH,IAAA,SAAM8G,SAAS,CAAC,kCAAkC,CAAAI,QAAA,CAAC,0CAAoB,CAAM,CAC9E,CACE,CAAC,cAENhH,KAAA,MAAG4G,SAAS,CAAC,4BAA4B,CAAAI,QAAA,EAAC,eACrC,CAAChG,QAAQ,CAAC,KAAG,CAACC,WAAW,EAC3B,CAAC,EACD,CAAC,cAGNnB,IAAA,QAAK8G,SAAS,CAAC,kBAAkB,CAAAI,QAAA,cAC/BhH,KAAA,QAAK4G,SAAS,uCAAAhC,MAAA,CACZxD,QAAQ,CAAG,uCAAuC,CAAG,mCAAmC,CACvF,CAAA4F,QAAA,eACDhH,KAAA,QAAK4G,SAAS,4BAAAhC,MAAA,CACZxD,QAAQ,CAAG,gBAAgB,CAAG,cAAc,CAC3C,CAAA4F,QAAA,EACAtG,UAAU,CAAC,GACd,EAAK,CAAC,cACNZ,IAAA,QAAK8G,SAAS,CAAC,eAAe,CAAAI,QAAA,CAAC,YAE/B,CAAK,CAAC,EACH,CAAC,CACH,CAAC,cAKNhH,KAAA,QACE4G,SAAS,CAAC,6CAA6C,CACvDC,KAAK,CAAE,CAAEqB,QAAQ,CAAE,MAAO,CAAE,CAAAlB,QAAA,eAG5BhH,KAAA,QACE6G,KAAK,CAAE,CACLe,eAAe,CAAE,SAAS,CAC1Bd,OAAO,CAAE,SAAS,CAClBgB,MAAM,CAAE,mBAAmB,CAC3BJ,YAAY,CAAE,KAAK,CACnBS,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBH,GAAG,CAAE,KAAK,CACVb,QAAQ,CAAE,MACZ,CAAE,CAAAJ,QAAA,eAEFhH,KAAA,QAAK6G,KAAK,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEH,GAAG,CAAE,KAAM,CAAE,CAAAjB,QAAA,eAChElH,IAAA,SAAM+G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,QAAC,CAAM,CAAC,cAC3ClH,IAAA,SAAM+G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEiB,UAAU,CAAE,MAAM,CAAEtD,KAAK,CAAE,SAAU,CAAE,CAAAiC,QAAA,CAAErG,cAAc,CAAO,CAAC,EAC7F,CAAC,cACNX,KAAA,QAAK6G,KAAK,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEH,GAAG,CAAE,KAAM,CAAE,CAAAjB,QAAA,eAChElH,IAAA,SAAM+G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,QAAC,CAAM,CAAC,cAC3ClH,IAAA,SAAM+G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEiB,UAAU,CAAE,MAAM,CAAEtD,KAAK,CAAE,SAAU,CAAE,CAAAiC,QAAA,CAAEpG,cAAc,CAAGD,cAAc,CAAO,CAAC,EAC9G,CAAC,EACH,CAAC,cAGNX,KAAA,QACE6G,KAAK,CAAE,CACLe,eAAe,CAAE,SAAS,CAC1Bd,OAAO,CAAE,SAAS,CAClBgB,MAAM,CAAE,mBAAmB,CAC3BJ,YAAY,CAAE,KAAK,CACnBS,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBH,GAAG,CAAE,KAAK,CACVb,QAAQ,CAAE,MACZ,CAAE,CAAAJ,QAAA,eAEFlH,IAAA,SAAM+G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,cAAE,CAAM,CAAC,cAC5ChH,KAAA,SAAM6G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEiB,UAAU,CAAE,MAAM,CAAEtD,KAAK,CAAE3D,QAAQ,CAAG,SAAS,CAAG,SAAU,CAAE,CAAA4F,QAAA,EAAEtG,UAAU,CAAC,GAAC,EAAM,CAAC,EACjH,CAAC,CAGLG,SAAS,EAAIA,SAAS,CAAG,CAAC,eACzBb,KAAA,QACE6G,KAAK,CAAE,CACLe,eAAe,CAAE,SAAS,CAC1Bd,OAAO,CAAE,SAAS,CAClBgB,MAAM,CAAE,mBAAmB,CAC3BJ,YAAY,CAAE,KAAK,CACnBS,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBH,GAAG,CAAE,KAAK,CACVb,QAAQ,CAAE,MACZ,CAAE,CAAAJ,QAAA,eAEFlH,IAAA,SAAM+G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAO,CAAE,CAAAJ,QAAA,CAAC,cAAE,CAAM,CAAC,cAC5ChH,KAAA,SAAM6G,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEiB,UAAU,CAAE,MAAM,CAAEtD,KAAK,CAAE,SAAU,CAAE,CAAAiC,QAAA,EACrEhD,IAAI,CAACgB,KAAK,CAACnE,SAAS,CAAG,EAAE,CAAC,CAAC,GAAC,CAAC,CAACA,SAAS,CAAG,EAAE,EAAEyH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EACtE,CAAC,EACJ,CACN,EACE,CAAC,CAGLxH,MAAM,eACLjB,IAAA,QAAK8G,SAAS,CAAC,2FAA2F,CAAAI,QAAA,cACxGhH,KAAA,QAAK4G,SAAS,CAAC,mCAAmC,CAAAI,QAAA,eAChDhH,KAAA,QAAK4G,SAAS,CAAC,yBAAyB,CAAAI,QAAA,eACtClH,IAAA,QAAK8G,SAAS,CAAC,uEAAuE,CAAAI,QAAA,cACpFlH,IAAA,CAACN,MAAM,EAACoH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACtC,CAAC,cACN5G,KAAA,QAAAgH,QAAA,eACEhH,KAAA,QAAK4G,SAAS,CAAC,mCAAmC,CAAAI,QAAA,EAAC,GAAC,CAACjG,MAAM,CAACyH,SAAS,EAAI,CAAC,CAAC,KAAG,EAAK,CAAC,cACpF1I,IAAA,QAAK8G,SAAS,CAAC,uBAAuB,CAAAI,QAAA,CAAC,QAAM,CAAK,CAAC,EAChD,CAAC,EACH,CAAC,cAENhH,KAAA,QAAK4G,SAAS,CAAC,YAAY,CAAAI,QAAA,eACzBlH,IAAA,QAAK8G,SAAS,CAAC,oCAAoC,CAAAI,QAAA,CAChD,CAAC,CAAC,CAAAzG,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEkI,OAAO,GAAI,CAAC,GAAK1H,MAAM,CAACyH,SAAS,EAAI,CAAC,CAAC,EAAEE,cAAc,CAAC,CAAC,CAC/D,CAAC,cACN1I,KAAA,QAAK4G,SAAS,CAAC,uBAAuB,CAAAI,QAAA,EAAC,wBAAiB,CAAC,CAAAzG,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoI,YAAY,GAAI,CAAC,EAAM,CAAC,EACpF,CAAC,EACH,CAAC,CACH,CACN,CAGA,CAAC5H,MAAM,EAAIR,IAAI,eACdT,IAAA,QAAK8G,SAAS,CAAC,2FAA2F,CAAAI,QAAA,cACxGhH,KAAA,QAAK4G,SAAS,CAAC,mCAAmC,CAAAI,QAAA,eAChDhH,KAAA,QAAK4G,SAAS,CAAC,yBAAyB,CAAAI,QAAA,eACtClH,IAAA,QAAK8G,SAAS,CAAC,uEAAuE,CAAAI,QAAA,cACpFlH,IAAA,CAACN,MAAM,EAACoH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACtC,CAAC,cACN9G,IAAA,QAAAkH,QAAA,cACElH,IAAA,QAAK8G,SAAS,CAAC,uBAAuB,CAAAI,QAAA,CAAC,eAAa,CAAK,CAAC,CACvD,CAAC,EACH,CAAC,cAENhH,KAAA,QAAK4G,SAAS,CAAC,YAAY,CAAAI,QAAA,eACzBlH,IAAA,QAAK8G,SAAS,CAAC,oCAAoC,CAAAI,QAAA,CAChD,CAACzG,IAAI,CAACkI,OAAO,EAAI,CAAC,EAAEC,cAAc,CAAC,CAAC,CAClC,CAAC,cACN1I,KAAA,QAAK4G,SAAS,CAAC,uBAAuB,CAAAI,QAAA,EAAC,wBAAiB,CAACzG,IAAI,CAACoI,YAAY,EAAI,CAAC,EAAM,CAAC,EACnF,CAAC,EACH,CAAC,CACH,CACN,cAGD7I,IAAA,QAAK8G,SAAS,CAAC,qFAAqF,CAAAI,QAAA,cAClGhH,KAAA,QAAK4G,SAAS,CAAC,8BAA8B,CAAAI,QAAA,eAC3ClH,IAAA,QAAK8G,SAAS,CAAC,qEAAqE,CAAAI,QAAA,cAClFlH,IAAA,CAACL,OAAO,EAACmH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACvC,CAAC,cACN9G,IAAA,OAAI8G,SAAS,CAAC,qCAAqC,CAAAI,QAAA,CAAC,+BAAmB,CAAI,CAAC,EACzE,CAAC,CAGH,CAAC,CAGLlG,aAAa,EAAIA,aAAa,CAAC2B,MAAM,CAAG,CAAC,eACxCzC,KAAA,QAAK4G,SAAS,CAAC,sFAAsF,CAAAI,QAAA,eACnGhH,KAAA,QAAK4G,SAAS,CAAC,8BAA8B,CAAAI,QAAA,eAC3ClH,IAAA,QAAK8G,SAAS,CAAC,qEAAqE,CAAAI,QAAA,cAClFlH,IAAA,CAACJ,UAAU,EAACkH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC1C,CAAC,cACN9G,IAAA,OAAI8G,SAAS,CAAC,qCAAqC,CAAAI,QAAA,CAAC,0CAA8B,CAAI,CAAC,EACpF,CAAC,cAENlH,IAAA,QAAK8G,SAAS,CAAC,oCAAoC,CAAAI,QAAA,CAChDlG,aAAa,CAACmG,GAAG,CAAC,CAACvB,MAAM,CAAEkD,KAAK,GAAK,CACpC;AACArE,OAAO,CAACC,GAAG,aAAAI,MAAA,CAAagE,KAAK,CAAG,CAAC,WAAUlD,MAAM,CAAC,CAClD,mBACA1F,KAAA,QAEE4G,SAAS,oFAAAhC,MAAA,CACPc,MAAM,CAACmD,SAAS,CACZ,4FAA4F,CAC5F,mFAAmF,CACtF,CACHhC,KAAK,CAAE,CACLc,SAAS,CAAEjC,MAAM,CAACmD,SAAS,CACvB,sEAAsE,CACtE,sEACN,CAAE,CAAA7B,QAAA,eAGFlH,IAAA,QAAK8G,SAAS,QAAAhC,MAAA,CACZc,MAAM,CAACmD,SAAS,CACZ,0CAA0C,CAC1C,sCAAsC,CACzC,CAAA7B,QAAA,cACDhH,KAAA,QAAK4G,SAAS,CAAC,yBAAyB,CAAAI,QAAA,eACtClH,IAAA,QAAK8G,SAAS,sEAAAhC,MAAA,CACZc,MAAM,CAACmD,SAAS,CACZ,mCAAmC,CACnC,iCAAiC,CACpC,CAAA7B,QAAA,CACAtB,MAAM,CAACmD,SAAS,cAAG/I,IAAA,CAACT,OAAO,EAACuH,SAAS,CAAC,SAAS,CAAE,CAAC,cAAG9G,IAAA,CAACR,GAAG,EAACsH,SAAS,CAAC,SAAS,CAAE,CAAC,CAC9E,CAAC,cAEN5G,KAAA,QAAK4G,SAAS,CAAC,QAAQ,CAAAI,QAAA,eACrBhH,KAAA,OAAI4G,SAAS,CAAC,iCAAiC,CAAAI,QAAA,EAAC,WACrC,CAAC4B,KAAK,CAAG,CAAC,EACjB,CAAC,cACL9I,IAAA,QAAK8G,SAAS,CAAC,8BAA8B,CAAAI,QAAA,cAC3ClH,IAAA,SAAM8G,SAAS,6CAAAhC,MAAA,CACbc,MAAM,CAACmD,SAAS,CACZ,yBAAyB,CACzB,uBAAuB,CAC1B,CAAA7B,QAAA,CACAtB,MAAM,CAACmD,SAAS,CAAG,WAAW,CAAG,SAAS,CACvC,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN7I,KAAA,QAAK4G,SAAS,CAAC,KAAK,CAAAI,QAAA,eAClBlH,IAAA,QAAK8G,SAAS,CAAC,MAAM,CAAAI,QAAA,cACnBlH,IAAA,MAAG8G,SAAS,CAAC,8CAA8C,CAAAI,QAAA,CACxDtB,MAAM,CAACM,YAAY,EAAIN,MAAM,CAACO,YAAY,CAC1C,CAAC,CACD,CAAC,CAGL,CAACP,MAAM,CAACoD,YAAY,GAAK,OAAO,EAAIpD,MAAM,CAACY,aAAa,EAAIZ,MAAM,CAACa,KAAK,EAAIb,MAAM,CAACW,QAAQ,IAAMX,MAAM,CAACY,aAAa,EAAIZ,MAAM,CAACa,KAAK,EAAIb,MAAM,CAACW,QAAQ,CAAC,eACxJvG,IAAA,QAAK8G,SAAS,CAAC,MAAM,CAAAI,QAAA,cACnBhH,KAAA,QAAK4G,SAAS,4BAAAhC,MAAA,CACZc,MAAM,CAACmD,SAAS,CACZ,8BAA8B,CAC9B,0BAA0B,CAC7B,CAAA7B,QAAA,eACDhH,KAAA,QAAK4G,SAAS,CAAC,8BAA8B,CAAAI,QAAA,eAC3ClH,IAAA,SAAM8G,SAAS,CAAC,qCAAqC,CAAAI,QAAA,CAAC,8BAAkB,CAAM,CAAC,cAC/ElH,IAAA,SAAM8G,SAAS,6CAAAhC,MAAA,CACbc,MAAM,CAACmD,SAAS,CACZ,yBAAyB,CACzB,uBAAuB,CAC1B,CAAA7B,QAAA,CACAtB,MAAM,CAACmD,SAAS,CAAG,WAAW,CAAG,SAAS,CACvC,CAAC,EACJ,CAAC,cACN7I,KAAA,QAAK4G,SAAS,CAAC,gCAAgC,CAAAI,QAAA,eAC7ClH,IAAA,QACEiJ,GAAG,CAAErD,MAAM,CAACY,aAAa,EAAIZ,MAAM,CAACa,KAAK,EAAIb,MAAM,CAACW,QAAS,CAC7D2C,GAAG,CAAC,gBAAgB,CACpBpC,SAAS,CAAC,sDAAsD,CAChEC,KAAK,CAAE,CAAEoC,SAAS,CAAE,OAAQ,CAAE,CAC9BC,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACvC,KAAK,CAACsB,OAAO,CAAG,MAAM,CAC/BgB,CAAC,CAACC,MAAM,CAACC,WAAW,CAACxC,KAAK,CAACsB,OAAO,CAAG,OAAO,CAC9C,CAAE,CACH,CAAC,cACFrI,IAAA,QACE8G,SAAS,CAAC,8DAA8D,CACxEC,KAAK,CAAE,CAAEsB,OAAO,CAAE,MAAO,CAAE,CAAAnB,QAAA,CAC5B,wCAED,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,cAGDhH,KAAA,QAAK4G,SAAS,CAAC,WAAW,CAAAI,QAAA,eACxBhH,KAAA,QAAK4G,SAAS,4BAAAhC,MAAA,CACZc,MAAM,CAACmD,SAAS,CACZ,8BAA8B,CAC9B,0BAA0B,CAC7B,CAAA7B,QAAA,eACDhH,KAAA,QAAK4G,SAAS,CAAC,8BAA8B,CAAAI,QAAA,eAC3ClH,IAAA,QAAK8G,SAAS,0DAAAhC,MAAA,CACZc,MAAM,CAACmD,SAAS,CAAG,cAAc,CAAG,YAAY,CAC/C,CAAA7B,QAAA,CACAtB,MAAM,CAACmD,SAAS,cACf/I,IAAA,CAACT,OAAO,EAACuH,SAAS,CAAC,oBAAoB,CAAE,CAAC,cAE1C9G,IAAA,CAACR,GAAG,EAACsH,SAAS,CAAC,oBAAoB,CAAE,CACtC,CACE,CAAC,cACN9G,IAAA,SAAM8G,SAAS,CAAC,6BAA6B,CAAAI,QAAA,CAAC,cAAY,CAAM,CAAC,EAC9D,CAAC,cACNlH,IAAA,QAAK8G,SAAS,qCAAAhC,MAAA,CACZc,MAAM,CAACmD,SAAS,CACZ,qDAAqD,CACrD,+CAA+C,CAClD,CAAA7B,QAAA,CACAtB,MAAM,CAACU,UAAU,EAAI,oBAAoB,CACvC,CAAC,EACH,CAAC,CAEL,CAACV,MAAM,CAACmD,SAAS,eAChB7I,KAAA,QAAK4G,SAAS,CAAC,sDAAsD,CAAAI,QAAA,eACnEhH,KAAA,QAAK4G,SAAS,CAAC,8BAA8B,CAAAI,QAAA,eAC3ClH,IAAA,QAAK8G,SAAS,CAAC,oEAAoE,CAAAI,QAAA,cACjFlH,IAAA,CAACT,OAAO,EAACuH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACvC,CAAC,cACN9G,IAAA,SAAM8G,SAAS,CAAC,6BAA6B,CAAAI,QAAA,CAAC,iBAAe,CAAM,CAAC,EACjE,CAAC,cACNlH,IAAA,QAAK8G,SAAS,CAAC,wFAAwF,CAAAI,QAAA,CACpGtB,MAAM,CAACS,aAAa,CAClB,CAAC,EACH,CACN,CAGA,CAACT,MAAM,CAACmD,SAAS,eAChB7I,KAAA,QAAK4G,SAAS,CAAC,MAAM,CAAAI,QAAA,eACnBlH,IAAA,WACE8G,SAAS,iHAAAhC,MAAA,CACPnD,mBAAmB,aAAAmD,MAAA,CAAagE,KAAK,EAAG,CACpC,gCAAgC,CAChC,2HAA2H,CAC9H,CACHU,OAAO,CAAEA,CAAA,GAAM9D,gBAAgB,CAACoD,KAAK,CAAElD,MAAM,CAAE,CAC/C6D,QAAQ,CAAE9H,mBAAmB,aAAAmD,MAAA,CAAagE,KAAK,EAAI,CAAA5B,QAAA,CAElDvF,mBAAmB,aAAAmD,MAAA,CAAagE,KAAK,EAAG,cACvC5I,KAAA,CAAAE,SAAA,EAAA8G,QAAA,eACElH,IAAA,QAAK8G,SAAS,CAAC,8EAA8E,CAAM,CAAC,yBAEtG,EAAE,CAAC,cAEH5G,KAAA,CAAAE,SAAA,EAAA8G,QAAA,eACElH,IAAA,CAACL,OAAO,EAACmH,SAAS,CAAC,SAAS,CAAE,CAAC,kBAEjC,EAAE,CACH,CACK,CAAC,CAGRrF,YAAY,aAAAqD,MAAA,CAAagE,KAAK,EAAG,eAChC5I,KAAA,QAAK4G,SAAS,CAAC,yFAAyF,CAAAI,QAAA,eACtGhH,KAAA,QAAK4G,SAAS,CAAC,8BAA8B,CAAAI,QAAA,eAC3ClH,IAAA,CAACL,OAAO,EAACmH,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC7C9G,IAAA,OAAI8G,SAAS,CAAC,yBAAyB,CAAAI,QAAA,CAAC,qCAAyB,CAAI,CAAC,EACnE,CAAC,cACNlH,IAAA,QAAK8G,SAAS,CAAC,+BAA+B,CAAAI,QAAA,cAC5ClH,IAAA,CAACF,eAAe,EAAC4J,IAAI,CAAEjI,YAAY,aAAAqD,MAAA,CAAagE,KAAK,EAAI,CAAE,CAAC,CACzD,CAAC,EACH,CACN,EACE,CACN,EACE,CAAC,EACH,CAAC,GA9KDlD,MAAM,CAAC+D,UAAU,EAAIb,KA+KvB,CAAC,CAER,CAAC,CAAC,CACC,CAAC,EAGH,CACN,cAGD5I,KAAA,QACE4G,SAAS,CAAC,YAAY,CACtBC,KAAK,CAAE,CACL6C,aAAa,CAAE1H,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,QAAQ,CAAG,KACvD,CAAE,CAAAC,QAAA,eAEFhH,KAAA,WACEsJ,OAAO,CAAGH,CAAC,EAAK,CACdA,CAAC,CAACQ,cAAc,CAAC,CAAC,CAClBpF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAC9CkC,mBAAmB,CAAC,CAAC,CACvB,CAAE,CACFE,SAAS,CAAC,qNAAqN,CAC/NC,KAAK,CAAE,CACLC,OAAO,CAAE9E,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,WAAW,CAAG,WAAW,CAC7DK,QAAQ,CAAEpF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CACFxE,IAAI,CAAC,QAAQ,CAAAyE,QAAA,eAEblH,IAAA,CAACP,MAAM,EACLsH,KAAK,CAAE,CACLU,KAAK,CAAEvF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDS,MAAM,CAAExF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACH,CAAC,eAEJ,EAAQ,CAAC,cAET/G,KAAA,WACEsJ,OAAO,CAAGH,CAAC,EAAK,CACdA,CAAC,CAACQ,cAAc,CAAC,CAAC,CAClBpF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7CmC,gBAAgB,CAAC,CAAC,CACpB,CAAE,CACFC,SAAS,CAAC,wNAAwN,CAClOC,KAAK,CAAE,CACLC,OAAO,CAAE9E,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,WAAW,CAAG,WAAW,CAC7DK,QAAQ,CAAEpF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAChD,CAAE,CACFxE,IAAI,CAAC,QAAQ,CAAAyE,QAAA,eAEblH,IAAA,CAACV,QAAQ,EACPyH,KAAK,CAAE,CACLU,KAAK,CAAEvF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACjDS,MAAM,CAAExF,MAAM,CAAC+E,UAAU,EAAI,GAAG,CAAG,MAAM,CAAG,MAC9C,CAAE,CACH,CAAC,cAEJ,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}