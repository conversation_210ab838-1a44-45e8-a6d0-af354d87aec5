{"ast": null, "code": "import{message}from\"antd\";import React,{useEffect,useState}from\"react\";import{useDispatch}from\"react-redux\";import{useNavigate}from\"react-router-dom\";import{motion}from\"framer-motion\";import\"./index.css\";import{getAllUsers,blockUserById,deleteUserById}from\"../../../apicalls/users\";import PageTitle from\"../../../components/PageTitle\";import{HideLoading,ShowLoading}from\"../../../redux/loaderSlice\";import{Card,Button,Input,Loading}from\"../../../components/modern\";import AdminLayout from\"../../../components/AdminLayout\";import AdminCard from\"../../../components/AdminCard\";import{TbUsers,TbSearch,TbFilter,TbUser<PERSON>heck,TbUserX,Tb<PERSON>rash,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON>b<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON>b<PERSON>,Tb<PERSON><PERSON>,TbDownload,TbDashboard}from\"react-icons/tb\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";function Users(){const navigate=useNavigate();const[users,setUsers]=useState([]);const[filteredUsers,setFilteredUsers]=useState([]);const[searchQuery,setSearchQuery]=useState(\"\");const[filterStatus,setFilterStatus]=useState(\"all\");const[filterSubscription,setFilterSubscription]=useState(\"all\");const[loading,setLoading]=useState(false);const dispatch=useDispatch();// Function to determine subscription status for filtering based on subscription dates\nconst getSubscriptionFilterStatus=user=>{const now=new Date();const paymentRequired=user.paymentRequired;const subscriptionEndDate=user.subscriptionEndDate;const subscriptionStartDate=user.subscriptionStartDate;// Debug logging (can be removed in production)\nif(process.env.NODE_ENV==='development'){console.log(\"User \".concat(user.name,\":\"),{paymentRequired,subscriptionStartDate,subscriptionEndDate,isExpired:subscriptionEndDate?new Date(subscriptionEndDate)<now:'no end date'});}// NO-PLAN: Users who never required payment or never had subscription\nif(!paymentRequired){return'no-plan';}// Users with paymentRequired = true (have or had a subscription)\nif(paymentRequired){// Check if subscription has expired by date\nif(subscriptionEndDate){const endDate=new Date(subscriptionEndDate);if(endDate<now){// Subscription end date has passed - EXPIRED PLAN\nreturn'expired-plan';}else{// Subscription is still valid by date - ON PLAN\nreturn'on-plan';}}else{// Has paymentRequired = true but no end date specified\n// This could be a lifetime subscription or missing data\n// Assume they are on plan if they have paymentRequired = true\nreturn'on-plan';}}// Default fallback for edge cases\nreturn'no-plan';};const getUsersData=async()=>{try{dispatch(ShowLoading());const response=await getAllUsers();dispatch(HideLoading());if(response.success){setUsers(response.users);console.log(\"users loaded:\",response.users.length);}else{message.error(response.message);}}catch(error){dispatch(HideLoading());message.error(error.message);}};const blockUser=async studentId=>{try{dispatch(ShowLoading());const response=await blockUserById({studentId});dispatch(HideLoading());if(response.success){message.success(response.message);getUsersData();}else{message.error(response.message);}}catch(error){dispatch(HideLoading());message.error(error.message);}};const deleteUser=async studentId=>{try{dispatch(ShowLoading());const response=await deleteUserById({studentId});dispatch(HideLoading());if(response.success){message.success(\"User deleted successfully\");getUsersData();}else{message.error(response.message);}}catch(error){dispatch(HideLoading());message.error(error.message);}};// Filter users based on search, status, and subscription\nuseEffect(()=>{let filtered=users;// Filter by search query\nif(searchQuery){filtered=filtered.filter(user=>{var _user$name,_user$email,_user$school,_user$class;return((_user$name=user.name)===null||_user$name===void 0?void 0:_user$name.toLowerCase().includes(searchQuery.toLowerCase()))||((_user$email=user.email)===null||_user$email===void 0?void 0:_user$email.toLowerCase().includes(searchQuery.toLowerCase()))||((_user$school=user.school)===null||_user$school===void 0?void 0:_user$school.toLowerCase().includes(searchQuery.toLowerCase()))||((_user$class=user.class)===null||_user$class===void 0?void 0:_user$class.toLowerCase().includes(searchQuery.toLowerCase()));});}// Filter by status\nif(filterStatus!==\"all\"){filtered=filtered.filter(user=>{if(filterStatus===\"blocked\")return user.isBlocked;if(filterStatus===\"active\")return!user.isBlocked;return true;});}// Filter by subscription plan\nif(filterSubscription!==\"all\"){filtered=filtered.filter(user=>{const subscriptionStatus=getSubscriptionFilterStatus(user);return subscriptionStatus===filterSubscription;});}setFilteredUsers(filtered);},[users,searchQuery,filterStatus,filterSubscription]);useEffect(()=>{getUsersData();},[]);const UserCard=_ref=>{let{user}=_ref;const subscriptionStatus=getSubscriptionFilterStatus(user);const getSubscriptionBadge=()=>{switch(subscriptionStatus){case'on-plan':return/*#__PURE__*/_jsxs(\"span\",{className:\"badge-modern bg-green-100 text-green-800 flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(TbCrown,{className:\"w-3 h-3\"}),/*#__PURE__*/_jsx(\"span\",{children:\"On Plan\"})]});case'expired-plan':return/*#__PURE__*/_jsxs(\"span\",{className:\"badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(TbClock,{className:\"w-3 h-3\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Expired\"})]});case'no-plan':return/*#__PURE__*/_jsxs(\"span\",{className:\"badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(TbX,{className:\"w-3 h-3\"}),/*#__PURE__*/_jsx(\"span\",{children:\"No Plan\"})]});default:return null;}};const formatDate=dateString=>{if(!dateString)return'N/A';return new Date(dateString).toLocaleDateString();};return/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},transition:{duration:0.2},children:/*#__PURE__*/_jsx(Card,{className:\"p-6 hover:shadow-large\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-full flex items-center justify-center \".concat(user.isBlocked?'bg-error-100':'bg-primary-100'),children:/*#__PURE__*/_jsx(TbUser,{className:\"w-6 h-6 \".concat(user.isBlocked?'text-error-600':'text-primary-600')})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mb-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:user.name}),/*#__PURE__*/_jsx(\"span\",{className:\"badge-modern \".concat(user.isBlocked?'bg-error-100 text-error-800':'bg-success-100 text-success-800'),children:user.isBlocked?'Blocked':'Active'}),getSubscriptionBadge()]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-1 text-sm text-gray-600\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbMail,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:user.email})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbSchool,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:user.school||'No school specified'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbUsers,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Class: \",user.class||'Not assigned']})]}),user.subscriptionPlan&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbCrown,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Plan: \",user.subscriptionPlan]})]}),user.subscriptionStartDate&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbClock,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Started: \",formatDate(user.subscriptionStartDate)]})]}),user.subscriptionEndDate&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbClock,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{className:new Date(user.subscriptionEndDate)<new Date()?'text-red-600 font-medium':'text-green-600',children:[new Date(user.subscriptionEndDate)<new Date()?'Expired: ':'Expires: ',formatDate(user.subscriptionEndDate)]})]}),user.paymentRequired!==undefined&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbCrown,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:user.paymentRequired?'text-blue-600':'text-gray-600',children:user.paymentRequired?'Paid Subscription':'Free Account'})]}),user.totalQuizzesTaken>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbUser,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Quizzes: \",user.totalQuizzesTaken]})]}),user.lastActivity&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TbClock,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Last Active: \",formatDate(user.lastActivity)]})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:user.isBlocked?\"success\":\"warning\",size:\"sm\",onClick:()=>blockUser(user.studentId),icon:user.isBlocked?/*#__PURE__*/_jsx(TbUserCheck,{}):/*#__PURE__*/_jsx(TbUserX,{}),children:user.isBlocked?\"Unblock\":\"Block\"}),/*#__PURE__*/_jsx(Button,{variant:\"error\",size:\"sm\",onClick:()=>{if(window.confirm(\"Are you sure you want to delete this user?\")){deleteUser(user.studentId);}},icon:/*#__PURE__*/_jsx(TbTrash,{}),children:\"Delete\"})]})]})})});};const actionButtons=[/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>navigate('/admin/reports'),className:\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(TbEye,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:\"View Reports\"})]},\"reports\"),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>{/* Add export functionality */},className:\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(TbDownload,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline\",children:\"Export\"})]},\"export\")];return/*#__PURE__*/_jsxs(AdminLayout,{showHeader:false,children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6 sm:mb-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-6 sm:p-8 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4\",children:[/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.05},whileTap:{scale:0.95},onClick:()=>navigate('/admin/dashboard'),className:\"flex items-center gap-2 px-3 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-white border border-white/30\",children:[/*#__PURE__*/_jsx(TbDashboard,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden sm:inline text-sm font-medium\",children:\"Dashboard\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl sm:text-3xl font-bold mb-2\",children:\"User Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-green-100 text-sm sm:text-base\",children:\"Manage student accounts, permissions, and access controls\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-2\",children:actionButtons})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\",children:[/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm font-medium mb-1\",children:\"Total Users\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:users.length}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-200 text-xs mt-1\",children:\"All registered\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbUsers,{className:\"w-6 h-6\"})})]})}),/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-green-100 text-sm font-medium mb-1\",children:\"Active Users\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:users.filter(u=>!u.isBlocked).length}),/*#__PURE__*/_jsx(\"p\",{className:\"text-green-200 text-xs mt-1\",children:\"Not blocked\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbUserCheck,{className:\"w-6 h-6\"})})]})}),/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-orange-100 text-sm font-medium mb-1\",children:\"Expired Plans\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:users.filter(u=>getSubscriptionFilterStatus(u)==='expired-plan').length}),/*#__PURE__*/_jsx(\"p\",{className:\"text-orange-200 text-xs mt-1\",children:\"Need renewal\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbClock,{className:\"w-6 h-6\"})})]})}),/*#__PURE__*/_jsx(AdminCard,{className:\"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-purple-100 text-sm font-medium mb-1\",children:\"No Plan\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-2xl sm:text-3xl font-bold\",children:users.filter(u=>getSubscriptionFilterStatus(u)==='no-plan').length}),/*#__PURE__*/_jsx(\"p\",{className:\"text-purple-200 text-xs mt-1\",children:\"Free users\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",children:/*#__PURE__*/_jsx(TbX,{className:\"w-6 h-6\"})})]})})]}),/*#__PURE__*/_jsxs(AdminCard,{title:\"Search & Filter\",subtitle:\"Find and filter users by various criteria\",className:\"mb-6 sm:mb-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-slate-700 mb-2\",children:\"Search Users\"}),/*#__PURE__*/_jsx(Input,{placeholder:\"Search by name, email, school, or class...\",value:searchQuery,onChange:e=>setSearchQuery(e.target.value),icon:/*#__PURE__*/_jsx(TbSearch,{})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-slate-700 mb-2\",children:\"Filter by Status\"}),/*#__PURE__*/_jsxs(\"select\",{value:filterStatus,onChange:e=>setFilterStatus(e.target.value),className:\"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Users\"}),/*#__PURE__*/_jsx(\"option\",{value:\"active\",children:\"Active Only\"}),/*#__PURE__*/_jsx(\"option\",{value:\"blocked\",children:\"Blocked Only\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-slate-700 mb-2\",children:\"Filter by Plan\"}),/*#__PURE__*/_jsxs(\"select\",{value:filterSubscription,onChange:e=>setFilterSubscription(e.target.value),className:\"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"All Plans\"}),/*#__PURE__*/_jsx(\"option\",{value:\"on-plan\",children:\"On Plan\"}),/*#__PURE__*/_jsx(\"option\",{value:\"expired-plan\",children:\"Expired Plan\"}),/*#__PURE__*/_jsx(\"option\",{value:\"no-plan\",children:\"No Plan\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-4\",children:[/*#__PURE__*/_jsx(\"div\",{children:(searchQuery||filterStatus!==\"all\"||filterSubscription!==\"all\")&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-slate-600\",children:[\"Showing \",filteredUsers.length,\" of \",users.length,\" users\",filterSubscription!==\"all\"&&/*#__PURE__*/_jsxs(\"span\",{className:\"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs\",children:[filterSubscription==='on-plan'&&'On Plan',filterSubscription==='expired-plan'&&'Expired Plan',filterSubscription==='no-plan'&&'No Plan']})]})}),/*#__PURE__*/_jsxs(motion.button,{whileHover:{scale:1.02},whileTap:{scale:0.98},onClick:()=>{setSearchQuery(\"\");setFilterStatus(\"all\");setFilterSubscription(\"all\");},className:\"px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors duration-200 flex items-center gap-2\",children:[/*#__PURE__*/_jsx(TbFilter,{className:\"w-4 h-4\"}),\"Clear Filters\"]})]})]}),/*#__PURE__*/_jsx(AdminCard,{title:\"Users (\".concat(filteredUsers.length,\")\"),subtitle:\"Manage individual user accounts and permissions\",loading:loading,children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center py-12\",children:/*#__PURE__*/_jsx(Loading,{text:\"Loading users...\"})}):filteredUsers.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:filteredUsers.map((user,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.05},children:/*#__PURE__*/_jsx(UserCard,{user:user})},user.studentId))}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(TbUsers,{className:\"w-16 h-16 text-slate-400 mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold text-slate-900 mb-2\",children:\"No Users Found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-slate-600\",children:searchQuery||filterStatus!==\"all\"||filterSubscription!==\"all\"?\"Try adjusting your search or filter criteria\":\"No users have been registered yet\"})]})})]});}export default Users;", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useDispatch", "useNavigate", "motion", "getAllUsers", "blockUserById", "deleteUserById", "Page<PERSON><PERSON>le", "HideLoading", "ShowLoading", "Card", "<PERSON><PERSON>", "Input", "Loading", "AdminLayout", "AdminCard", "TbUsers", "TbSearch", "Tb<PERSON><PERSON>er", "TbUserCheck", "TbUserX", "TbTrash", "TbEye", "TbSchool", "TbMail", "TbUser", "TbCrown", "TbClock", "TbX", "TbPlus", "TbDownload", "TbDashboard", "jsx", "_jsx", "jsxs", "_jsxs", "Users", "navigate", "users", "setUsers", "filteredUsers", "setFilteredUsers", "searchQuery", "setSearch<PERSON>uery", "filterStatus", "setFilterStatus", "filterSubscription", "setFilterSubscription", "loading", "setLoading", "dispatch", "getSubscriptionFilterStatus", "user", "now", "Date", "paymentRequired", "subscriptionEndDate", "subscriptionStartDate", "process", "env", "NODE_ENV", "console", "log", "concat", "name", "isExpired", "endDate", "getUsersData", "response", "success", "length", "error", "blockUser", "studentId", "deleteUser", "filtered", "filter", "_user$name", "_user$email", "_user$school", "_user$class", "toLowerCase", "includes", "email", "school", "class", "isBlocked", "subscriptionStatus", "UserCard", "_ref", "getSubscriptionBadge", "className", "children", "formatDate", "dateString", "toLocaleDateString", "div", "initial", "opacity", "y", "animate", "whileHover", "transition", "duration", "subscriptionPlan", "undefined", "totalQuizzesTaken", "lastActivity", "variant", "size", "onClick", "icon", "window", "confirm", "actionButtons", "button", "scale", "whileTap", "showHeader", "u", "title", "subtitle", "placeholder", "value", "onChange", "e", "target", "text", "map", "index", "delay"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Users/<USER>"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport \"./index.css\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport AdminLayout from \"../../../components/AdminLayout\";\r\nimport AdminCard from \"../../../components/AdminCard\";\r\nimport {\r\n  TbUsers,\r\n  TbSearch,\r\n  TbFilter,\r\n  TbUserCheck,\r\n  TbUserX,\r\n  Tb<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON>b<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  TbX,\r\n  TbPlus,\r\n  TbDownload,\r\n  TbDashboard\r\n} from \"react-icons/tb\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [filteredUsers, setFilteredUsers] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [filterSubscription, setFilterSubscription] = useState(\"all\");\r\n  const [loading, setLoading] = useState(false);\r\n  const dispatch = useDispatch();\r\n\r\n  // Function to determine subscription status for filtering based on subscription dates\r\n  const getSubscriptionFilterStatus = (user) => {\r\n    const now = new Date();\r\n    const paymentRequired = user.paymentRequired;\r\n    const subscriptionEndDate = user.subscriptionEndDate;\r\n    const subscriptionStartDate = user.subscriptionStartDate;\r\n\r\n    // Debug logging (can be removed in production)\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`User ${user.name}:`, {\r\n        paymentRequired,\r\n        subscriptionStartDate,\r\n        subscriptionEndDate,\r\n        isExpired: subscriptionEndDate ? new Date(subscriptionEndDate) < now : 'no end date'\r\n      });\r\n    }\r\n\r\n    // NO-PLAN: Users who never required payment or never had subscription\r\n    if (!paymentRequired) {\r\n      return 'no-plan';\r\n    }\r\n\r\n    // Users with paymentRequired = true (have or had a subscription)\r\n    if (paymentRequired) {\r\n      // Check if subscription has expired by date\r\n      if (subscriptionEndDate) {\r\n        const endDate = new Date(subscriptionEndDate);\r\n\r\n        if (endDate < now) {\r\n          // Subscription end date has passed - EXPIRED PLAN\r\n          return 'expired-plan';\r\n        } else {\r\n          // Subscription is still valid by date - ON PLAN\r\n          return 'on-plan';\r\n        }\r\n      } else {\r\n        // Has paymentRequired = true but no end date specified\r\n        // This could be a lifetime subscription or missing data\r\n        // Assume they are on plan if they have paymentRequired = true\r\n        return 'on-plan';\r\n      }\r\n    }\r\n\r\n    // Default fallback for edge cases\r\n    return 'no-plan';\r\n  };\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users loaded:\", response.users.length);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n\r\n  // Filter users based on search, status, and subscription\r\n  useEffect(() => {\r\n    let filtered = users;\r\n\r\n    // Filter by search query\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(user =>\r\n        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.school?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.class?.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Filter by status\r\n    if (filterStatus !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        if (filterStatus === \"blocked\") return user.isBlocked;\r\n        if (filterStatus === \"active\") return !user.isBlocked;\r\n        return true;\r\n      });\r\n    }\r\n\r\n    // Filter by subscription plan\r\n    if (filterSubscription !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n        return subscriptionStatus === filterSubscription;\r\n      });\r\n    }\r\n\r\n    setFilteredUsers(filtered);\r\n  }, [users, searchQuery, filterStatus, filterSubscription]);\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const UserCard = ({ user }) => {\r\n    const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n\r\n    const getSubscriptionBadge = () => {\r\n      switch (subscriptionStatus) {\r\n        case 'on-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-green-100 text-green-800 flex items-center space-x-1\">\r\n              <TbCrown className=\"w-3 h-3\" />\r\n              <span>On Plan</span>\r\n            </span>\r\n          );\r\n        case 'expired-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1\">\r\n              <TbClock className=\"w-3 h-3\" />\r\n              <span>Expired</span>\r\n            </span>\r\n          );\r\n        case 'no-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1\">\r\n              <TbX className=\"w-3 h-3\" />\r\n              <span>No Plan</span>\r\n            </span>\r\n          );\r\n        default:\r\n          return null;\r\n      }\r\n    };\r\n\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return 'N/A';\r\n      return new Date(dateString).toLocaleDateString();\r\n    };\r\n\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        whileHover={{ y: -2 }}\r\n        transition={{ duration: 0.2 }}\r\n      >\r\n        <Card className=\"p-6 hover:shadow-large\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex items-start space-x-4\">\r\n              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\r\n                user.isBlocked ? 'bg-error-100' : 'bg-primary-100'\r\n              }`}>\r\n                <TbUser className={`w-6 h-6 ${user.isBlocked ? 'text-error-600' : 'text-primary-600'}`} />\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex items-center space-x-2 mb-2\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">{user.name}</h3>\r\n                  <span className={`badge-modern ${\r\n                    user.isBlocked ? 'bg-error-100 text-error-800' : 'bg-success-100 text-success-800'\r\n                  }`}>\r\n                    {user.isBlocked ? 'Blocked' : 'Active'}\r\n                  </span>\r\n                  {getSubscriptionBadge()}\r\n                </div>\r\n\r\n                <div className=\"space-y-1 text-sm text-gray-600\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbMail className=\"w-4 h-4\" />\r\n                    <span>{user.email}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbSchool className=\"w-4 h-4\" />\r\n                    <span>{user.school || 'No school specified'}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbUsers className=\"w-4 h-4\" />\r\n                    <span>Class: {user.class || 'Not assigned'}</span>\r\n                  </div>\r\n\r\n                  {/* Subscription Details */}\r\n                  {user.subscriptionPlan && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbCrown className=\"w-4 h-4\" />\r\n                      <span>Plan: {user.subscriptionPlan}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Subscription Period */}\r\n                  {user.subscriptionStartDate && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Started: {formatDate(user.subscriptionStartDate)}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.subscriptionEndDate && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span className={new Date(user.subscriptionEndDate) < new Date() ? 'text-red-600 font-medium' : 'text-green-600'}>\r\n                        {new Date(user.subscriptionEndDate) < new Date() ? 'Expired: ' : 'Expires: '}\r\n                        {formatDate(user.subscriptionEndDate)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Payment Status */}\r\n                  {user.paymentRequired !== undefined && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbCrown className=\"w-4 h-4\" />\r\n                      <span className={user.paymentRequired ? 'text-blue-600' : 'text-gray-600'}>\r\n                        {user.paymentRequired ? 'Paid Subscription' : 'Free Account'}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Activity Information */}\r\n                  {user.totalQuizzesTaken > 0 && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbUser className=\"w-4 h-4\" />\r\n                      <span>Quizzes: {user.totalQuizzesTaken}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.lastActivity && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Last Active: {formatDate(user.lastActivity)}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button\r\n                variant={user.isBlocked ? \"success\" : \"warning\"}\r\n                size=\"sm\"\r\n                onClick={() => blockUser(user.studentId)}\r\n                icon={user.isBlocked ? <TbUserCheck /> : <TbUserX />}\r\n              >\r\n                {user.isBlocked ? \"Unblock\" : \"Block\"}\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"error\"\r\n                size=\"sm\"\r\n                onClick={() => {\r\n                  if (window.confirm(\"Are you sure you want to delete this user?\")) {\r\n                    deleteUser(user.studentId);\r\n                  }\r\n                }}\r\n                icon={<TbTrash />}\r\n              >\r\n                Delete\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  const actionButtons = [\r\n    <motion.button\r\n      key=\"reports\"\r\n      whileHover={{ scale: 1.02 }}\r\n      whileTap={{ scale: 0.98 }}\r\n      onClick={() => navigate('/admin/reports')}\r\n      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\"\r\n    >\r\n      <TbEye className=\"w-4 h-4\" />\r\n      <span className=\"hidden sm:inline\">View Reports</span>\r\n    </motion.button>,\r\n    <motion.button\r\n      key=\"export\"\r\n      whileHover={{ scale: 1.02 }}\r\n      whileTap={{ scale: 0.98 }}\r\n      onClick={() => {/* Add export functionality */}}\r\n      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\"\r\n    >\r\n      <TbDownload className=\"w-4 h-4\" />\r\n      <span className=\"hidden sm:inline\">Export</span>\r\n    </motion.button>\r\n  ];\r\n\r\n  return (\r\n    <AdminLayout showHeader={false}>\r\n      {/* Page Header */}\r\n      <div className=\"mb-6 sm:mb-8\">\r\n        <div className=\"bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-6 sm:p-8 text-white\">\r\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n            <div className=\"flex items-center gap-4\">\r\n              {/* Dashboard Shortcut */}\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={() => navigate('/admin/dashboard')}\r\n                className=\"flex items-center gap-2 px-3 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-white border border-white/30\"\r\n              >\r\n                <TbDashboard className=\"w-4 h-4\" />\r\n                <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\r\n              </motion.button>\r\n\r\n              <div>\r\n                <h1 className=\"text-2xl sm:text-3xl font-bold mb-2\">\r\n                  User Management\r\n                </h1>\r\n                <p className=\"text-green-100 text-sm sm:text-base\">\r\n                  Manage student accounts, permissions, and access controls\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {actionButtons}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\">\r\n        <AdminCard className=\"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-blue-100 text-sm font-medium mb-1\">Total Users</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.length}</p>\r\n              <p className=\"text-blue-200 text-xs mt-1\">All registered</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbUsers className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n\r\n        <AdminCard className=\"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-green-100 text-sm font-medium mb-1\">Active Users</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.filter(u => !u.isBlocked).length}</p>\r\n              <p className=\"text-green-200 text-xs mt-1\">Not blocked</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbUserCheck className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n\r\n        <AdminCard className=\"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-orange-100 text-sm font-medium mb-1\">Expired Plans</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.filter(u => getSubscriptionFilterStatus(u) === 'expired-plan').length}</p>\r\n              <p className=\"text-orange-200 text-xs mt-1\">Need renewal</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbClock className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n\r\n        <AdminCard className=\"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-purple-100 text-sm font-medium mb-1\">No Plan</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.filter(u => getSubscriptionFilterStatus(u) === 'no-plan').length}</p>\r\n              <p className=\"text-purple-200 text-xs mt-1\">Free users</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbX className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <AdminCard\r\n        title=\"Search & Filter\"\r\n        subtitle=\"Find and filter users by various criteria\"\r\n        className=\"mb-6 sm:mb-8\"\r\n      >\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n          <div className=\"lg:col-span-2\">\r\n            <label className=\"block text-sm font-medium text-slate-700 mb-2\">\r\n              Search Users\r\n            </label>\r\n            <Input\r\n              placeholder=\"Search by name, email, school, or class...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              icon={<TbSearch />}\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-slate-700 mb-2\">\r\n              Filter by Status\r\n            </label>\r\n            <select\r\n              value={filterStatus}\r\n              onChange={(e) => setFilterStatus(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n            >\r\n              <option value=\"all\">All Users</option>\r\n              <option value=\"active\">Active Only</option>\r\n              <option value=\"blocked\">Blocked Only</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-slate-700 mb-2\">\r\n              Filter by Plan\r\n            </label>\r\n            <select\r\n              value={filterSubscription}\r\n              onChange={(e) => setFilterSubscription(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n            >\r\n              <option value=\"all\">All Plans</option>\r\n              <option value=\"on-plan\">On Plan</option>\r\n              <option value=\"expired-plan\">Expired Plan</option>\r\n              <option value=\"no-plan\">No Plan</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-4\">\r\n          <div>\r\n            {(searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\") && (\r\n              <span className=\"text-sm text-slate-600\">\r\n                Showing {filteredUsers.length} of {users.length} users\r\n                {filterSubscription !== \"all\" && (\r\n                  <span className=\"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs\">\r\n                    {filterSubscription === 'on-plan' && 'On Plan'}\r\n                    {filterSubscription === 'expired-plan' && 'Expired Plan'}\r\n                    {filterSubscription === 'no-plan' && 'No Plan'}\r\n                  </span>\r\n                )}\r\n              </span>\r\n            )}\r\n          </div>\r\n\r\n          <motion.button\r\n            whileHover={{ scale: 1.02 }}\r\n            whileTap={{ scale: 0.98 }}\r\n            onClick={() => {\r\n              setSearchQuery(\"\");\r\n              setFilterStatus(\"all\");\r\n              setFilterSubscription(\"all\");\r\n            }}\r\n            className=\"px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors duration-200 flex items-center gap-2\"\r\n          >\r\n            <TbFilter className=\"w-4 h-4\" />\r\n            Clear Filters\r\n          </motion.button>\r\n        </div>\r\n      </AdminCard>\r\n\r\n      {/* Users Grid */}\r\n      <AdminCard\r\n        title={`Users (${filteredUsers.length})`}\r\n        subtitle=\"Manage individual user accounts and permissions\"\r\n        loading={loading}\r\n      >\r\n        {loading ? (\r\n          <div className=\"flex justify-center py-12\">\r\n            <Loading text=\"Loading users...\" />\r\n          </div>\r\n        ) : filteredUsers.length > 0 ? (\r\n          <div className=\"space-y-4\">\r\n            {filteredUsers.map((user, index) => (\r\n              <motion.div\r\n                key={user.studentId}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: index * 0.05 }}\r\n              >\r\n                <UserCard user={user} />\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-12\">\r\n            <TbUsers className=\"w-16 h-16 text-slate-400 mx-auto mb-4\" />\r\n            <h3 className=\"text-xl font-semibold text-slate-900 mb-2\">No Users Found</h3>\r\n            <p className=\"text-slate-600\">\r\n              {searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\"\r\n                ? \"Try adjusting your search or filter criteria\"\r\n                : \"No users have been registered yet\"}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </AdminCard>\r\n    </AdminLayout>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n"], "mappings": "AAAA,OAASA,OAAO,KAAQ,MAAM,CAC9B,MAAO,CAAAC,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,KAAQ,eAAe,CACtC,MAAO,aAAa,CACpB,OACEC,WAAW,CACXC,aAAa,CACbC,cAAc,KACT,yBAAyB,CAChC,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,OAASC,WAAW,CAAEC,WAAW,KAAQ,4BAA4B,CACrE,OAASC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,OAAO,KAAQ,4BAA4B,CACzE,MAAO,CAAAC,WAAW,KAAM,iCAAiC,CACzD,MAAO,CAAAC,SAAS,KAAM,+BAA+B,CACrD,OACEC,OAAO,CACPC,QAAQ,CACRC,QAAQ,CACRC,WAAW,CACXC,OAAO,CACPC,OAAO,CACPC,KAAK,CACLC,QAAQ,CACRC,MAAM,CACNC,MAAM,CACNC,OAAO,CACPC,OAAO,CACPC,GAAG,CACHC,MAAM,CACNC,UAAU,CACVC,WAAW,KACN,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAExB,QAAS,CAAAC,KAAKA,CAAA,CAAG,CACf,KAAM,CAAAC,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACoC,KAAK,CAAEC,QAAQ,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0C,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC4C,YAAY,CAAEC,eAAe,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC8C,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACgD,OAAO,CAAEC,UAAU,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAAkD,QAAQ,CAAGjD,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAkD,2BAA2B,CAAIC,IAAI,EAAK,CAC5C,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,eAAe,CAAGH,IAAI,CAACG,eAAe,CAC5C,KAAM,CAAAC,mBAAmB,CAAGJ,IAAI,CAACI,mBAAmB,CACpD,KAAM,CAAAC,qBAAqB,CAAGL,IAAI,CAACK,qBAAqB,CAExD;AACA,GAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC1CC,OAAO,CAACC,GAAG,SAAAC,MAAA,CAASX,IAAI,CAACY,IAAI,MAAK,CAChCT,eAAe,CACfE,qBAAqB,CACrBD,mBAAmB,CACnBS,SAAS,CAAET,mBAAmB,CAAG,GAAI,CAAAF,IAAI,CAACE,mBAAmB,CAAC,CAAGH,GAAG,CAAG,aACzE,CAAC,CAAC,CACJ,CAEA;AACA,GAAI,CAACE,eAAe,CAAE,CACpB,MAAO,SAAS,CAClB,CAEA;AACA,GAAIA,eAAe,CAAE,CACnB;AACA,GAAIC,mBAAmB,CAAE,CACvB,KAAM,CAAAU,OAAO,CAAG,GAAI,CAAAZ,IAAI,CAACE,mBAAmB,CAAC,CAE7C,GAAIU,OAAO,CAAGb,GAAG,CAAE,CACjB;AACA,MAAO,cAAc,CACvB,CAAC,IAAM,CACL;AACA,MAAO,SAAS,CAClB,CACF,CAAC,IAAM,CACL;AACA;AACA;AACA,MAAO,SAAS,CAClB,CACF,CAEA;AACA,MAAO,SAAS,CAClB,CAAC,CAED,KAAM,CAAAc,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACFjB,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC,CACvB,KAAM,CAAA2D,QAAQ,CAAG,KAAM,CAAAhE,WAAW,CAAC,CAAC,CACpC8C,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC,CACvB,GAAI4D,QAAQ,CAACC,OAAO,CAAE,CACpB9B,QAAQ,CAAC6B,QAAQ,CAAC9B,KAAK,CAAC,CACxBuB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEM,QAAQ,CAAC9B,KAAK,CAACgC,MAAM,CAAC,CACrD,CAAC,IAAM,CACLzE,OAAO,CAAC0E,KAAK,CAACH,QAAQ,CAACvE,OAAO,CAAC,CACjC,CACF,CAAE,MAAO0E,KAAK,CAAE,CACdrB,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC,CACvBX,OAAO,CAAC0E,KAAK,CAACA,KAAK,CAAC1E,OAAO,CAAC,CAC9B,CACF,CAAC,CACD,KAAM,CAAA2E,SAAS,CAAG,KAAO,CAAAC,SAAS,EAAK,CACrC,GAAI,CACFvB,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC,CACvB,KAAM,CAAA2D,QAAQ,CAAG,KAAM,CAAA/D,aAAa,CAAC,CACnCoE,SACF,CAAC,CAAC,CACFvB,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC,CACvB,GAAI4D,QAAQ,CAACC,OAAO,CAAE,CACpBxE,OAAO,CAACwE,OAAO,CAACD,QAAQ,CAACvE,OAAO,CAAC,CACjCsE,YAAY,CAAC,CAAC,CAChB,CAAC,IAAM,CACLtE,OAAO,CAAC0E,KAAK,CAACH,QAAQ,CAACvE,OAAO,CAAC,CACjC,CACF,CAAE,MAAO0E,KAAK,CAAE,CACdrB,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC,CACvBX,OAAO,CAAC0E,KAAK,CAACA,KAAK,CAAC1E,OAAO,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAA6E,UAAU,CAAG,KAAO,CAAAD,SAAS,EAAK,CACtC,GAAI,CACFvB,QAAQ,CAACzC,WAAW,CAAC,CAAC,CAAC,CACvB,KAAM,CAAA2D,QAAQ,CAAG,KAAM,CAAA9D,cAAc,CAAC,CAAEmE,SAAU,CAAC,CAAC,CACpDvB,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC,CACvB,GAAI4D,QAAQ,CAACC,OAAO,CAAE,CACpBxE,OAAO,CAACwE,OAAO,CAAC,2BAA2B,CAAC,CAC5CF,YAAY,CAAC,CAAC,CAChB,CAAC,IAAM,CACLtE,OAAO,CAAC0E,KAAK,CAACH,QAAQ,CAACvE,OAAO,CAAC,CACjC,CACF,CAAE,MAAO0E,KAAK,CAAE,CACdrB,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC,CACvBX,OAAO,CAAC0E,KAAK,CAACA,KAAK,CAAC1E,OAAO,CAAC,CAC9B,CACF,CAAC,CAGD;AACAE,SAAS,CAAC,IAAM,CACd,GAAI,CAAA4E,QAAQ,CAAGrC,KAAK,CAEpB;AACA,GAAII,WAAW,CAAE,CACfiC,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACxB,IAAI,OAAAyB,UAAA,CAAAC,WAAA,CAAAC,YAAA,CAAAC,WAAA,OAC7B,EAAAH,UAAA,CAAAzB,IAAI,CAACY,IAAI,UAAAa,UAAA,iBAATA,UAAA,CAAWI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,WAAW,CAACuC,WAAW,CAAC,CAAC,CAAC,KAAAH,WAAA,CAC5D1B,IAAI,CAAC+B,KAAK,UAAAL,WAAA,iBAAVA,WAAA,CAAYG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,WAAW,CAACuC,WAAW,CAAC,CAAC,CAAC,KAAAF,YAAA,CAC7D3B,IAAI,CAACgC,MAAM,UAAAL,YAAA,iBAAXA,YAAA,CAAaE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,WAAW,CAACuC,WAAW,CAAC,CAAC,CAAC,KAAAD,WAAA,CAC9D5B,IAAI,CAACiC,KAAK,UAAAL,WAAA,iBAAVA,WAAA,CAAYC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,WAAW,CAACuC,WAAW,CAAC,CAAC,CAAC,GAC/D,CAAC,CACH,CAEA;AACA,GAAIrC,YAAY,GAAK,KAAK,CAAE,CAC1B+B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACxB,IAAI,EAAI,CACjC,GAAIR,YAAY,GAAK,SAAS,CAAE,MAAO,CAAAQ,IAAI,CAACkC,SAAS,CACrD,GAAI1C,YAAY,GAAK,QAAQ,CAAE,MAAO,CAACQ,IAAI,CAACkC,SAAS,CACrD,MAAO,KAAI,CACb,CAAC,CAAC,CACJ,CAEA;AACA,GAAIxC,kBAAkB,GAAK,KAAK,CAAE,CAChC6B,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACxB,IAAI,EAAI,CACjC,KAAM,CAAAmC,kBAAkB,CAAGpC,2BAA2B,CAACC,IAAI,CAAC,CAC5D,MAAO,CAAAmC,kBAAkB,GAAKzC,kBAAkB,CAClD,CAAC,CAAC,CACJ,CAEAL,gBAAgB,CAACkC,QAAQ,CAAC,CAC5B,CAAC,CAAE,CAACrC,KAAK,CAAEI,WAAW,CAAEE,YAAY,CAAEE,kBAAkB,CAAC,CAAC,CAE1D/C,SAAS,CAAC,IAAM,CACdoE,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAqB,QAAQ,CAAGC,IAAA,EAAc,IAAb,CAAErC,IAAK,CAAC,CAAAqC,IAAA,CACxB,KAAM,CAAAF,kBAAkB,CAAGpC,2BAA2B,CAACC,IAAI,CAAC,CAE5D,KAAM,CAAAsC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,OAAQH,kBAAkB,EACxB,IAAK,SAAS,CACZ,mBACEpD,KAAA,SAAMwD,SAAS,CAAC,sEAAsE,CAAAC,QAAA,eACpF3D,IAAA,CAACP,OAAO,EAACiE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/B1D,IAAA,SAAA2D,QAAA,CAAM,SAAO,CAAM,CAAC,EAChB,CAAC,CAEX,IAAK,cAAc,CACjB,mBACEzD,KAAA,SAAMwD,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eACtF3D,IAAA,CAACN,OAAO,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/B1D,IAAA,SAAA2D,QAAA,CAAM,SAAO,CAAM,CAAC,EAChB,CAAC,CAEX,IAAK,SAAS,CACZ,mBACEzD,KAAA,SAAMwD,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eAClF3D,IAAA,CAACL,GAAG,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,cAC3B1D,IAAA,SAAA2D,QAAA,CAAM,SAAO,CAAM,CAAC,EAChB,CAAC,CAEX,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAI,CAACA,UAAU,CAAE,MAAO,KAAK,CAC7B,MAAO,IAAI,CAAAxC,IAAI,CAACwC,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAClD,CAAC,CAED,mBACE9D,IAAA,CAAC9B,MAAM,CAAC6F,GAAG,EACTC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEF,CAAC,CAAE,CAAC,CAAE,CAAE,CACtBG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAX,QAAA,cAE9B3D,IAAA,CAACvB,IAAI,EAACiF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACtCzD,KAAA,QAAKwD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CzD,KAAA,QAAKwD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC3D,IAAA,QAAK0D,SAAS,4DAAA5B,MAAA,CACZX,IAAI,CAACkC,SAAS,CAAG,cAAc,CAAG,gBAAgB,CACjD,CAAAM,QAAA,cACD3D,IAAA,CAACR,MAAM,EAACkE,SAAS,YAAA5B,MAAA,CAAaX,IAAI,CAACkC,SAAS,CAAG,gBAAgB,CAAG,kBAAkB,CAAG,CAAE,CAAC,CACvF,CAAC,cACNnD,KAAA,QAAKwD,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBzD,KAAA,QAAKwD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C3D,IAAA,OAAI0D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAExC,IAAI,CAACY,IAAI,CAAK,CAAC,cACpE/B,IAAA,SAAM0D,SAAS,iBAAA5B,MAAA,CACbX,IAAI,CAACkC,SAAS,CAAG,6BAA6B,CAAG,iCAAiC,CACjF,CAAAM,QAAA,CACAxC,IAAI,CAACkC,SAAS,CAAG,SAAS,CAAG,QAAQ,CAClC,CAAC,CACNI,oBAAoB,CAAC,CAAC,EACpB,CAAC,cAENvD,KAAA,QAAKwD,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CzD,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACT,MAAM,EAACmE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC9B1D,IAAA,SAAA2D,QAAA,CAAOxC,IAAI,CAAC+B,KAAK,CAAO,CAAC,EACtB,CAAC,cACNhD,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACV,QAAQ,EAACoE,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC1D,IAAA,SAAA2D,QAAA,CAAOxC,IAAI,CAACgC,MAAM,EAAI,qBAAqB,CAAO,CAAC,EAChD,CAAC,cACNjD,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACjB,OAAO,EAAC2E,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/BxD,KAAA,SAAAyD,QAAA,EAAM,SAAO,CAACxC,IAAI,CAACiC,KAAK,EAAI,cAAc,EAAO,CAAC,EAC/C,CAAC,CAGLjC,IAAI,CAACoD,gBAAgB,eACpBrE,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACP,OAAO,EAACiE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/BxD,KAAA,SAAAyD,QAAA,EAAM,QAAM,CAACxC,IAAI,CAACoD,gBAAgB,EAAO,CAAC,EACvC,CACN,CAGApD,IAAI,CAACK,qBAAqB,eACzBtB,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACN,OAAO,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/BxD,KAAA,SAAAyD,QAAA,EAAM,WAAS,CAACC,UAAU,CAACzC,IAAI,CAACK,qBAAqB,CAAC,EAAO,CAAC,EAC3D,CACN,CACAL,IAAI,CAACI,mBAAmB,eACvBrB,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACN,OAAO,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/BxD,KAAA,SAAMwD,SAAS,CAAE,GAAI,CAAArC,IAAI,CAACF,IAAI,CAACI,mBAAmB,CAAC,CAAG,GAAI,CAAAF,IAAI,CAAC,CAAC,CAAG,0BAA0B,CAAG,gBAAiB,CAAAsC,QAAA,EAC9G,GAAI,CAAAtC,IAAI,CAACF,IAAI,CAACI,mBAAmB,CAAC,CAAG,GAAI,CAAAF,IAAI,CAAC,CAAC,CAAG,WAAW,CAAG,WAAW,CAC3EuC,UAAU,CAACzC,IAAI,CAACI,mBAAmB,CAAC,EACjC,CAAC,EACJ,CACN,CAGAJ,IAAI,CAACG,eAAe,GAAKkD,SAAS,eACjCtE,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACP,OAAO,EAACiE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/B1D,IAAA,SAAM0D,SAAS,CAAEvC,IAAI,CAACG,eAAe,CAAG,eAAe,CAAG,eAAgB,CAAAqC,QAAA,CACvExC,IAAI,CAACG,eAAe,CAAG,mBAAmB,CAAG,cAAc,CACxD,CAAC,EACJ,CACN,CAGAH,IAAI,CAACsD,iBAAiB,CAAG,CAAC,eACzBvE,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACR,MAAM,EAACkE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC9BxD,KAAA,SAAAyD,QAAA,EAAM,WAAS,CAACxC,IAAI,CAACsD,iBAAiB,EAAO,CAAC,EAC3C,CACN,CACAtD,IAAI,CAACuD,YAAY,eAChBxE,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACN,OAAO,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/BxD,KAAA,SAAAyD,QAAA,EAAM,eAAa,CAACC,UAAU,CAACzC,IAAI,CAACuD,YAAY,CAAC,EAAO,CAAC,EACtD,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,cAENxE,KAAA,QAAKwD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3D,IAAA,CAACtB,MAAM,EACLiG,OAAO,CAAExD,IAAI,CAACkC,SAAS,CAAG,SAAS,CAAG,SAAU,CAChDuB,IAAI,CAAC,IAAI,CACTC,OAAO,CAAEA,CAAA,GAAMtC,SAAS,CAACpB,IAAI,CAACqB,SAAS,CAAE,CACzCsC,IAAI,CAAE3D,IAAI,CAACkC,SAAS,cAAGrD,IAAA,CAACd,WAAW,GAAE,CAAC,cAAGc,IAAA,CAACb,OAAO,GAAE,CAAE,CAAAwE,QAAA,CAEpDxC,IAAI,CAACkC,SAAS,CAAG,SAAS,CAAG,OAAO,CAC/B,CAAC,cAETrD,IAAA,CAACtB,MAAM,EACLiG,OAAO,CAAC,OAAO,CACfC,IAAI,CAAC,IAAI,CACTC,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIE,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,CAAE,CAChEvC,UAAU,CAACtB,IAAI,CAACqB,SAAS,CAAC,CAC5B,CACF,CAAE,CACFsC,IAAI,cAAE9E,IAAA,CAACZ,OAAO,GAAE,CAAE,CAAAuE,QAAA,CACnB,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACF,CAAC,CACG,CAAC,CAEjB,CAAC,CAED,KAAM,CAAAsB,aAAa,CAAG,cACpB/E,KAAA,CAAChC,MAAM,CAACgH,MAAM,EAEZd,UAAU,CAAE,CAAEe,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BN,OAAO,CAAEA,CAAA,GAAMzE,QAAQ,CAAC,gBAAgB,CAAE,CAC1CsD,SAAS,CAAC,sHAAsH,CAAAC,QAAA,eAEhI3D,IAAA,CAACX,KAAK,EAACqE,SAAS,CAAC,SAAS,CAAE,CAAC,cAC7B1D,IAAA,SAAM0D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,GAPlD,SAQS,CAAC,cAChBzD,KAAA,CAAChC,MAAM,CAACgH,MAAM,EAEZd,UAAU,CAAE,CAAEe,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BN,OAAO,CAAEA,CAAA,GAAM,CAAC,+BAAgC,CAChDnB,SAAS,CAAC,wHAAwH,CAAAC,QAAA,eAElI3D,IAAA,CAACH,UAAU,EAAC6D,SAAS,CAAC,SAAS,CAAE,CAAC,cAClC1D,IAAA,SAAM0D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,GAP5C,QAQS,CAAC,CACjB,CAED,mBACEzD,KAAA,CAACrB,WAAW,EAACwG,UAAU,CAAE,KAAM,CAAA1B,QAAA,eAE7B3D,IAAA,QAAK0D,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B3D,IAAA,QAAK0D,SAAS,CAAC,+EAA+E,CAAAC,QAAA,cAC5FzD,KAAA,QAAKwD,SAAS,CAAC,oEAAoE,CAAAC,QAAA,eACjFzD,KAAA,QAAKwD,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAEtCzD,KAAA,CAAChC,MAAM,CAACgH,MAAM,EACZd,UAAU,CAAE,CAAEe,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BN,OAAO,CAAEA,CAAA,GAAMzE,QAAQ,CAAC,kBAAkB,CAAE,CAC5CsD,SAAS,CAAC,6IAA6I,CAAAC,QAAA,eAEvJ3D,IAAA,CAACF,WAAW,EAAC4D,SAAS,CAAC,SAAS,CAAE,CAAC,cACnC1D,IAAA,SAAM0D,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EAC1D,CAAC,cAEhBzD,KAAA,QAAAyD,QAAA,eACE3D,IAAA,OAAI0D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,iBAEpD,CAAI,CAAC,cACL3D,IAAA,MAAG0D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,2DAEnD,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cACN3D,IAAA,QAAK0D,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClCsB,aAAa,CACX,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAGN/E,KAAA,QAAKwD,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eACzF3D,IAAA,CAAClB,SAAS,EAAC4E,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cACpFzD,KAAA,QAAKwD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzD,KAAA,QAAAyD,QAAA,eACE3D,IAAA,MAAG0D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,aAAW,CAAG,CAAC,cACrE3D,IAAA,MAAG0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEtD,KAAK,CAACgC,MAAM,CAAI,CAAC,cAChErC,IAAA,MAAG0D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,EACzD,CAAC,cACN3D,IAAA,QAAK0D,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF3D,IAAA,CAACjB,OAAO,EAAC2E,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,EACH,CAAC,CACG,CAAC,cAEZ1D,IAAA,CAAClB,SAAS,EAAC4E,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cACtFzD,KAAA,QAAKwD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzD,KAAA,QAAAyD,QAAA,eACE3D,IAAA,MAAG0D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,cACvE3D,IAAA,MAAG0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEtD,KAAK,CAACsC,MAAM,CAAC2C,CAAC,EAAI,CAACA,CAAC,CAACjC,SAAS,CAAC,CAAChB,MAAM,CAAI,CAAC,cAC1FrC,IAAA,MAAG0D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,aAAW,CAAG,CAAC,EACvD,CAAC,cACN3D,IAAA,QAAK0D,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF3D,IAAA,CAACd,WAAW,EAACwE,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,EACH,CAAC,CACG,CAAC,cAEZ1D,IAAA,CAAClB,SAAS,EAAC4E,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cACxFzD,KAAA,QAAKwD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzD,KAAA,QAAAyD,QAAA,eACE3D,IAAA,MAAG0D,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,cACzE3D,IAAA,MAAG0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEtD,KAAK,CAACsC,MAAM,CAAC2C,CAAC,EAAIpE,2BAA2B,CAACoE,CAAC,CAAC,GAAK,cAAc,CAAC,CAACjD,MAAM,CAAI,CAAC,cAC/HrC,IAAA,MAAG0D,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,EACzD,CAAC,cACN3D,IAAA,QAAK0D,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF3D,IAAA,CAACN,OAAO,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,EACH,CAAC,CACG,CAAC,cAEZ1D,IAAA,CAAClB,SAAS,EAAC4E,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cACxFzD,KAAA,QAAKwD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzD,KAAA,QAAAyD,QAAA,eACE3D,IAAA,MAAG0D,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,SAAO,CAAG,CAAC,cACnE3D,IAAA,MAAG0D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEtD,KAAK,CAACsC,MAAM,CAAC2C,CAAC,EAAIpE,2BAA2B,CAACoE,CAAC,CAAC,GAAK,SAAS,CAAC,CAACjD,MAAM,CAAI,CAAC,cAC1HrC,IAAA,MAAG0D,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,YAAU,CAAG,CAAC,EACvD,CAAC,cACN3D,IAAA,QAAK0D,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF3D,IAAA,CAACL,GAAG,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,CACxB,CAAC,EACH,CAAC,CACG,CAAC,EACT,CAAC,cAGNxD,KAAA,CAACpB,SAAS,EACRyG,KAAK,CAAC,iBAAiB,CACvBC,QAAQ,CAAC,2CAA2C,CACpD9B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAExBzD,KAAA,QAAKwD,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEzD,KAAA,QAAKwD,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B3D,IAAA,UAAO0D,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,cAEjE,CAAO,CAAC,cACR3D,IAAA,CAACrB,KAAK,EACJ8G,WAAW,CAAC,4CAA4C,CACxDC,KAAK,CAAEjF,WAAY,CACnBkF,QAAQ,CAAGC,CAAC,EAAKlF,cAAc,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDZ,IAAI,cAAE9E,IAAA,CAAChB,QAAQ,GAAE,CAAE,CACpB,CAAC,EACC,CAAC,cAENkB,KAAA,QAAAyD,QAAA,eACE3D,IAAA,UAAO0D,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,kBAEjE,CAAO,CAAC,cACRzD,KAAA,WACEwF,KAAK,CAAE/E,YAAa,CACpBgF,QAAQ,CAAGC,CAAC,EAAKhF,eAAe,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDhC,SAAS,CAAC,8HAA8H,CAAAC,QAAA,eAExI3D,IAAA,WAAQ0F,KAAK,CAAC,KAAK,CAAA/B,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtC3D,IAAA,WAAQ0F,KAAK,CAAC,QAAQ,CAAA/B,QAAA,CAAC,aAAW,CAAQ,CAAC,cAC3C3D,IAAA,WAAQ0F,KAAK,CAAC,SAAS,CAAA/B,QAAA,CAAC,cAAY,CAAQ,CAAC,EACvC,CAAC,EACN,CAAC,cAENzD,KAAA,QAAAyD,QAAA,eACE3D,IAAA,UAAO0D,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,gBAEjE,CAAO,CAAC,cACRzD,KAAA,WACEwF,KAAK,CAAE7E,kBAAmB,CAC1B8E,QAAQ,CAAGC,CAAC,EAAK9E,qBAAqB,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACvDhC,SAAS,CAAC,8HAA8H,CAAAC,QAAA,eAExI3D,IAAA,WAAQ0F,KAAK,CAAC,KAAK,CAAA/B,QAAA,CAAC,WAAS,CAAQ,CAAC,cACtC3D,IAAA,WAAQ0F,KAAK,CAAC,SAAS,CAAA/B,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC3D,IAAA,WAAQ0F,KAAK,CAAC,cAAc,CAAA/B,QAAA,CAAC,cAAY,CAAQ,CAAC,cAClD3D,IAAA,WAAQ0F,KAAK,CAAC,SAAS,CAAA/B,QAAA,CAAC,SAAO,CAAQ,CAAC,EAClC,CAAC,EACN,CAAC,EACH,CAAC,cAENzD,KAAA,QAAKwD,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eACtF3D,IAAA,QAAA2D,QAAA,CACG,CAAClD,WAAW,EAAIE,YAAY,GAAK,KAAK,EAAIE,kBAAkB,GAAK,KAAK,gBACrEX,KAAA,SAAMwD,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAC,UAC/B,CAACpD,aAAa,CAAC8B,MAAM,CAAC,MAAI,CAAChC,KAAK,CAACgC,MAAM,CAAC,QAChD,CAACxB,kBAAkB,GAAK,KAAK,eAC3BX,KAAA,SAAMwD,SAAS,CAAC,+DAA+D,CAAAC,QAAA,EAC5E9C,kBAAkB,GAAK,SAAS,EAAI,SAAS,CAC7CA,kBAAkB,GAAK,cAAc,EAAI,cAAc,CACvDA,kBAAkB,GAAK,SAAS,EAAI,SAAS,EAC1C,CACP,EACG,CACP,CACE,CAAC,cAENX,KAAA,CAAChC,MAAM,CAACgH,MAAM,EACZd,UAAU,CAAE,CAAEe,KAAK,CAAE,IAAK,CAAE,CAC5BC,QAAQ,CAAE,CAAED,KAAK,CAAE,IAAK,CAAE,CAC1BN,OAAO,CAAEA,CAAA,GAAM,CACbnE,cAAc,CAAC,EAAE,CAAC,CAClBE,eAAe,CAAC,KAAK,CAAC,CACtBE,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAE,CACF4C,SAAS,CAAC,wHAAwH,CAAAC,QAAA,eAElI3D,IAAA,CAACf,QAAQ,EAACyE,SAAS,CAAC,SAAS,CAAE,CAAC,gBAElC,EAAe,CAAC,EACb,CAAC,EACG,CAAC,cAGZ1D,IAAA,CAAClB,SAAS,EACRyG,KAAK,WAAAzD,MAAA,CAAYvB,aAAa,CAAC8B,MAAM,KAAI,CACzCmD,QAAQ,CAAC,iDAAiD,CAC1DzE,OAAO,CAAEA,OAAQ,CAAA4C,QAAA,CAEhB5C,OAAO,cACNf,IAAA,QAAK0D,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC3D,IAAA,CAACpB,OAAO,EAACkH,IAAI,CAAC,kBAAkB,CAAE,CAAC,CAChC,CAAC,CACJvF,aAAa,CAAC8B,MAAM,CAAG,CAAC,cAC1BrC,IAAA,QAAK0D,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBpD,aAAa,CAACwF,GAAG,CAAC,CAAC5E,IAAI,CAAE6E,KAAK,gBAC7BhG,IAAA,CAAC9B,MAAM,CAAC6F,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BG,UAAU,CAAE,CAAE4B,KAAK,CAAED,KAAK,CAAG,IAAK,CAAE,CAAArC,QAAA,cAEpC3D,IAAA,CAACuD,QAAQ,EAACpC,IAAI,CAAEA,IAAK,CAAE,CAAC,EALnBA,IAAI,CAACqB,SAMA,CACb,CAAC,CACC,CAAC,cAENtC,KAAA,QAAKwD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3D,IAAA,CAACjB,OAAO,EAAC2E,SAAS,CAAC,uCAAuC,CAAE,CAAC,cAC7D1D,IAAA,OAAI0D,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7E3D,IAAA,MAAG0D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC1BlD,WAAW,EAAIE,YAAY,GAAK,KAAK,EAAIE,kBAAkB,GAAK,KAAK,CAClE,8CAA8C,CAC9C,mCAAmC,CACtC,CAAC,EACD,CACN,CACQ,CAAC,EACD,CAAC,CAElB,CAEA,cAAe,CAAAV,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}