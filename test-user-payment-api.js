const axios = require('axios');

const testUserPayment = async () => {
  try {
    console.log('🔍 Testing user payment status via API...\n');

    // First, let's check if the server is running
    try {
      const healthResponse = await axios.get('http://localhost:5000/api/health');
      console.log('✅ Server is running:', healthResponse.data);
    } catch (error) {
      console.log('❌ Server is not responding. Please start the server first.');
      return;
    }

    // Test data for lucymosha - we'll need to find the user first
    console.log('\n🔍 Searching for user "lucymosha"...');
    
    // Try to get all users (if there's an admin endpoint)
    try {
      const usersResponse = await axios.get('http://localhost:5000/api/users/all');
      const users = usersResponse.data;
      
      const lucyUser = users.find(user => 
        user.username?.toLowerCase().includes('lucy') ||
        user.name?.toLowerCase().includes('lucy') ||
        user.email?.toLowerCase().includes('lucy')
      );
      
      if (lucyUser) {
        console.log('👤 Found user:', {
          id: lucyUser._id,
          name: lucyUser.name,
          username: lucyUser.username,
          email: lucyUser.email,
          phone: lucyUser.phoneNumber,
          paymentRequired: lucyUser.paymentRequired,
          subscriptionStatus: lucyUser.subscriptionStatus
        });

        // Now check payment status for this user
        console.log('\n💳 Checking payment status...');
        
        try {
          const paymentResponse = await axios.get(`http://localhost:5000/api/payment/check-payment-status`, {
            data: { userId: lucyUser._id }
          });
          console.log('✅ Payment Status:', paymentResponse.data);
        } catch (paymentError) {
          console.log('❌ Payment check error:', paymentError.response?.data || paymentError.message);
        }

        // Check subscriptions
        console.log('\n📋 Checking subscriptions...');
        try {
          const subscriptionResponse = await axios.get(`http://localhost:5000/api/subscriptions/user/${lucyUser._id}`);
          console.log('✅ Subscriptions:', subscriptionResponse.data);
        } catch (subError) {
          console.log('❌ Subscription check error:', subError.response?.data || subError.message);
        }

      } else {
        console.log('❌ User "lucymosha" not found in users list');
        
        // Show all users for debugging
        console.log('\n📋 Available users:');
        users.slice(0, 10).forEach(user => {
          console.log(`- ${user.name} (${user.username}) - ${user.email}`);
        });
      }
      
    } catch (error) {
      console.log('❌ Could not fetch users:', error.response?.data || error.message);
      
      // Try alternative approach - direct database query through API
      console.log('\n🔄 Trying alternative approach...');
      
      try {
        const dbTestResponse = await axios.get('http://localhost:5000/api/test/db');
        console.log('✅ Database test:', dbTestResponse.data);
      } catch (dbError) {
        console.log('❌ Database test error:', dbError.response?.data || dbError.message);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
};

testUserPayment();
