{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef, startTransition } from \"react\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport AdminNavigation from \"./AdminNavigation\";\nimport ModernSidebar from \"./ModernSidebar\";\nimport SubscriptionTrigger from \"./SubscriptionTrigger/SubscriptionTrigger\";\nimport { TbHome, TbBrandTanzania, TbMenu2, TbX, TbChevronDown, TbLogout, TbUser, TbSettings, TbBell, TbStar } from \"react-icons/tb\";\nimport OnlineStatusIndicator from './common/OnlineStatusIndicator';\nimport NotificationBell from './common/NotificationBell';\nimport ProfilePicture from './common/ProfilePicture';\nimport FloatingBrainwaveAI from './FloatingBrainwaveAI';\nimport { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const heartbeatRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Check if current page should show floating AI (exclude quiz, results, plans, and profile pages)\n  const shouldShowFloatingAI = () => {\n    const currentPath = location.pathname;\n    const excludedPaths = ['/user/quiz', '/user/quiz/', '/quiz', '/quiz/', '/results', '/results/', '/user/results', '/user/results/', '/user/plans', '/user/plans/', '/plans', '/plans/', '/profile', '/profile/', '/user/profile', '/user/profile/'];\n\n    // Check if current path starts with any excluded path or contains quiz/result keywords\n    return !excludedPaths.some(path => currentPath.includes(path)) && !currentPath.includes('quiz') && !currentPath.includes('result') && !currentPath.includes('plans') && !currentPath.includes('profile');\n  };\n  const activeRoute = location.pathname;\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n\n        // Store user data in localStorage for consistency\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\n\n        // Debug log to help identify admin login issues\n        console.log(\"User data loaded:\", {\n          name: response.data.name,\n          isAdmin: response.data.isAdmin,\n          email: response.data.email\n        });\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      // Check if user data already exists in Redux (from login)\n      if (!user) {\n        // Try to load user from localStorage first\n        const storedUser = localStorage.getItem(\"user\");\n        if (storedUser) {\n          try {\n            const userData = JSON.parse(storedUser);\n            console.log(\"ProtectedRoute: Loading user from localStorage\", {\n              name: userData.name,\n              isAdmin: userData.isAdmin\n            });\n            dispatch(SetUser(userData));\n          } catch (error) {\n            console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");\n            getUserData();\n          }\n        } else {\n          console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");\n          getUserData();\n        }\n      } else {\n        console.log(\"ProtectedRoute: User already in Redux\", {\n          name: user.name,\n          isAdmin: user.isAdmin\n        });\n      }\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    // Allow access to profile page even without subscription\n    const allowedRoutes = ['/user/profile', '/profile'];\n    const isAllowedRoute = allowedRoutes.some(route => activeRoute.includes(route));\n    if (isPaymentPending && !isAllowedRoute) {\n      navigate('/user/profile'); // Redirect to profile where subscription management is now located\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n\n  // Online status management\n  useEffect(() => {\n    if (user && !user.isAdmin) {\n      // Set user as online when component mounts\n      setUserOnline().catch(console.error);\n\n      // Send heartbeat every 2 minutes\n      heartbeatRef.current = setInterval(() => {\n        sendHeartbeat().catch(console.error);\n      }, 120000); // 2 minutes\n\n      // Set user as offline when component unmounts or page unloads\n      const handleBeforeUnload = () => {\n        setUserOffline().catch(console.error);\n      };\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      return () => {\n        if (heartbeatRef.current) {\n          clearInterval(heartbeatRef.current);\n        }\n        window.removeEventListener('beforeunload', handleBeforeUnload);\n        setUserOffline().catch(console.error);\n      };\n    }\n  }, [user]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: [!(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(ModernSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 26\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: `nav-modern safe-header-animation ${location.pathname.includes('/quiz') || location.pathname.includes('/write-exam') ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98' : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'} backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative group flex items-center space-x-3 safe-center-animation\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                  style: {\n                    width: '32px',\n                    height: '24px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"https://flagcdn.com/w40/tz.png\",\n                    alt: \"Tanzania Flag\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      // Fallback to another flag source if first fails\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                      e.target.onerror = () => {\n                        // Final fallback - hide image and show text\n                        e.target.style.display = 'none';\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                      };\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative brainwave-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                    style: {\n                      fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                      letterSpacing: '-0.02em'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"relative inline-block brain-text\",\n                      style: {\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)',\n                        animation: 'brainGlow 3s ease-in-out infinite'\n                      },\n                      children: [\"Brain\", /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full electric-spark\",\n                        style: {\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6',\n                          animation: 'sparkPulse 2s ease-in-out infinite'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 296,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"relative inline-block wave-text\",\n                      style: {\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)',\n                        animation: 'waveFlow 3s ease-in-out infinite'\n                      },\n                      children: [\"wave\", /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full wave-particle\",\n                        style: {\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981',\n                          animation: 'waveParticle 3s ease-in-out infinite'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute -bottom-1 left-0 h-1 rounded-full glowing-underline\",\n                    style: {\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)',\n                      width: '100%',\n                      animation: 'underlineGlow 3s ease-in-out infinite'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                  style: {\n                    background: '#f0f0f0',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                    width: '32px',\n                    height: '32px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/favicon.png\",\n                    alt: \"Brainwave Logo\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                    style: {\n                      display: 'none',\n                      fontSize: '12px'\n                    },\n                    children: \"\\uD83E\\uDDE0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n              children: [!(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"safe-notification-animation\",\n                children: /*#__PURE__*/_jsxDEV(NotificationBell, {\n                  unreadCount: 2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 group safe-profile-animation cursor-pointer\",\n                onClick: () => navigate('/profile'),\n                title: \"Go to Profile\",\n                children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                  user: {\n                    ...user,\n                    isOnline: true,\n                    lastActivity: new Date().toISOString()\n                  },\n                  size: \"sm\",\n                  showOnlineStatus: true,\n                  style: {\n                    width: '32px',\n                    height: '32px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden sm:block text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\",\n                    children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                    children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden md:block\",\n                  children: /*#__PURE__*/_jsxDEV(TbUser, {\n                    className: \"text-gray-400 group-hover:text-blue-500 transition-colors duration-300 text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `flex-1 overflow-auto ${user !== null && user !== void 0 && user.isAdmin ? 'bg-gray-100' : 'bg-gradient-to-br from-gray-50 to-blue-50'} ${user !== null && user !== void 0 && user.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full safe-content-animation\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this), shouldShowFloatingAI() && /*#__PURE__*/_jsxDEV(FloatingBrainwaveAI, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 36\n      }, this), !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(SubscriptionTrigger, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 28\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"NYNgiZh2QMxHh24W/N9oO4sdwys=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "startTransition", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "AdminNavigation", "ModernSidebar", "SubscriptionTrigger", "TbHome", "TbBrandTanzania", "TbMenu2", "TbX", "TbChevronDown", "TbLogout", "TbUser", "TbSettings", "TbBell", "TbStar", "OnlineStatusIndicator", "NotificationBell", "ProfilePicture", "FloatingBrainwaveAI", "setUserOnline", "setUserOffline", "sendHeartbeat", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "heartbeatRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "shouldShowFloatingAI", "currentPath", "pathname", "excludedPaths", "some", "path", "includes", "activeRoute", "getUserData", "response", "success", "data", "localStorage", "setItem", "JSON", "stringify", "console", "log", "name", "isAdmin", "email", "error", "token", "getItem", "storedUser", "userData", "parse", "allowedRoutes", "isAllowedRoute", "route", "verifyPaymentStatus", "paymentStatus", "current", "clearInterval", "paymentRequired", "setInterval", "catch", "handleBeforeUnload", "window", "addEventListener", "removeEventListener", "getButtonClass", "title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "height", "src", "alt", "objectFit", "onError", "e", "target", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "color", "fontWeight", "textShadow", "animation", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "unreadCount", "onClick", "isOnline", "lastActivity", "Date", "toISOString", "size", "showOnlineStatus", "class", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef, startTransition } from \"react\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport AdminNavigation from \"./AdminNavigation\";\r\nimport ModernSidebar from \"./ModernSidebar\";\r\nimport SubscriptionTrigger from \"./SubscriptionTrigger/SubscriptionTrigger\";\r\nimport { TbHome, TbBrandTanzania, TbMenu2, Tb<PERSON>, <PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TbBell, TbStar } from \"react-icons/tb\";\r\nimport OnlineStatusIndicator from './common/OnlineStatusIndicator';\r\nimport NotificationBell from './common/NotificationBell';\r\nimport ProfilePicture from './common/ProfilePicture';\r\nimport FloatingBrainwaveAI from './FloatingBrainwaveAI';\r\nimport { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const heartbeatRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n\r\n  // Check if current page should show floating AI (exclude quiz, results, plans, and profile pages)\r\n  const shouldShowFloatingAI = () => {\r\n    const currentPath = location.pathname;\r\n    const excludedPaths = [\r\n      '/user/quiz',\r\n      '/user/quiz/',\r\n      '/quiz',\r\n      '/quiz/',\r\n      '/results',\r\n      '/results/',\r\n      '/user/results',\r\n      '/user/results/',\r\n      '/user/plans',\r\n      '/user/plans/',\r\n      '/plans',\r\n      '/plans/',\r\n      '/profile',\r\n      '/profile/',\r\n      '/user/profile',\r\n      '/user/profile/'\r\n    ];\r\n\r\n    // Check if current path starts with any excluded path or contains quiz/result keywords\r\n    return !excludedPaths.some(path => currentPath.includes(path)) &&\r\n           !currentPath.includes('quiz') &&\r\n           !currentPath.includes('result') &&\r\n           !currentPath.includes('plans') &&\r\n           !currentPath.includes('profile');\r\n  };\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n\r\n        // Store user data in localStorage for consistency\r\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\r\n\r\n        // Debug log to help identify admin login issues\r\n        console.log(\"User data loaded:\", {\r\n          name: response.data.name,\r\n          isAdmin: response.data.isAdmin,\r\n          email: response.data.email\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      // Check if user data already exists in Redux (from login)\r\n      if (!user) {\r\n        // Try to load user from localStorage first\r\n        const storedUser = localStorage.getItem(\"user\");\r\n        if (storedUser) {\r\n          try {\r\n            const userData = JSON.parse(storedUser);\r\n            console.log(\"ProtectedRoute: Loading user from localStorage\", { name: userData.name, isAdmin: userData.isAdmin });\r\n            dispatch(SetUser(userData));\r\n          } catch (error) {\r\n            console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");\r\n            getUserData();\r\n          }\r\n        } else {\r\n          console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");\r\n          getUserData();\r\n        }\r\n      } else {\r\n        console.log(\"ProtectedRoute: User already in Redux\", { name: user.name, isAdmin: user.isAdmin });\r\n      }\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    // Allow access to profile page even without subscription\r\n    const allowedRoutes = ['/user/profile', '/profile'];\r\n    const isAllowedRoute = allowedRoutes.some(route => activeRoute.includes(route));\r\n\r\n    if (isPaymentPending && !isAllowedRoute) {\r\n      navigate('/user/profile'); // Redirect to profile where subscription management is now located\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n  // Online status management\r\n  useEffect(() => {\r\n    if (user && !user.isAdmin) {\r\n      // Set user as online when component mounts\r\n      setUserOnline().catch(console.error);\r\n\r\n      // Send heartbeat every 2 minutes\r\n      heartbeatRef.current = setInterval(() => {\r\n        sendHeartbeat().catch(console.error);\r\n      }, 120000); // 2 minutes\r\n\r\n      // Set user as offline when component unmounts or page unloads\r\n      const handleBeforeUnload = () => {\r\n        setUserOffline().catch(console.error);\r\n      };\r\n\r\n      window.addEventListener('beforeunload', handleBeforeUnload);\r\n\r\n      return () => {\r\n        if (heartbeatRef.current) {\r\n          clearInterval(heartbeatRef.current);\r\n        }\r\n        window.removeEventListener('beforeunload', handleBeforeUnload);\r\n        setUserOffline().catch(console.error);\r\n      };\r\n    }\r\n  }, [user]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* Modern Sidebar for regular users */}\r\n      {!user?.isAdmin && <ModernSidebar />}\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Responsive Header - Show for all users */}\r\n        {(\r\n          <header\r\n            className={`nav-modern safe-header-animation ${\r\n              location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')\r\n                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'\r\n                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'\r\n            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}\r\n          >\r\n          <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n            <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n              {/* Left section - Modern Sidebar */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                {/* Modern Sidebar Toggle Button */}\r\n              </div>\r\n\r\n              {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n              <div className=\"flex-1 flex justify-center\">\r\n                <div\r\n                  className=\"relative group flex items-center space-x-3 safe-center-animation\"\r\n                >\r\n                  {/* Tanzania Flag - Using actual flag image */}\r\n                  <div\r\n                    className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '24px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"https://flagcdn.com/w40/tz.png\"\r\n                      alt=\"Tanzania Flag\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        // Fallback to another flag source if first fails\r\n                        e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                        e.target.onerror = () => {\r\n                          // Final fallback - hide image and show text\r\n                          e.target.style.display = 'none';\r\n                          e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                        };\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Amazing Animated Brainwave Text */}\r\n                  <div className=\"relative brainwave-container\">\r\n                    <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                        style={{\r\n                          fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                          letterSpacing: '-0.02em'\r\n                        }}>\r\n                      {/* Brain - simplified safe animation */}\r\n                      <span\r\n                        className=\"relative inline-block brain-text\"\r\n                        style={{\r\n                          color: '#1f2937',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(59, 130, 246, 0.5)',\r\n                          animation: 'brainGlow 3s ease-in-out infinite'\r\n                        }}\r\n                      >\r\n                        Brain\r\n\r\n                        {/* Electric spark - CSS animation */}\r\n                        <div\r\n                          className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full electric-spark\"\r\n                          style={{\r\n                            backgroundColor: '#3b82f6',\r\n                            boxShadow: '0 0 10px #3b82f6',\r\n                            animation: 'sparkPulse 2s ease-in-out infinite'\r\n                          }}\r\n                        />\r\n                      </span>\r\n\r\n                      {/* Wave - simplified safe animation */}\r\n                      <span\r\n                        className=\"relative inline-block wave-text\"\r\n                        style={{\r\n                          color: '#059669',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                          animation: 'waveFlow 3s ease-in-out infinite'\r\n                        }}\r\n                      >\r\n                        wave\r\n\r\n                        {/* Wave particle - CSS animation */}\r\n                        <div\r\n                          className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full wave-particle\"\r\n                          style={{\r\n                            backgroundColor: '#10b981',\r\n                            boxShadow: '0 0 8px #10b981',\r\n                            animation: 'waveParticle 3s ease-in-out infinite'\r\n                          }}\r\n                        />\r\n                      </span>\r\n                    </h1>\r\n\r\n                    {/* Glowing underline effect - CSS animation */}\r\n                    <div\r\n                      className=\"absolute -bottom-1 left-0 h-1 rounded-full glowing-underline\"\r\n                      style={{\r\n                        background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)',\r\n                        width: '100%',\r\n                        animation: 'underlineGlow 3s ease-in-out infinite'\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Official Logo - Small like profile */}\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"/favicon.png\"\r\n                      alt=\"Brainwave Logo\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        e.target.style.display = 'none';\r\n                        e.target.nextSibling.style.display = 'flex';\r\n                      }}\r\n                    />\r\n                    <div\r\n                      className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                      style={{\r\n                        display: 'none',\r\n                        fontSize: '12px'\r\n                      }}\r\n                    >\r\n                      🧠\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Modern Glow Effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Right Section - Notifications + User Profile */}\r\n              <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n                {/* Notification Bell */}\r\n                {!user?.isAdmin && (\r\n                  <div className=\"safe-notification-animation\">\r\n                    <NotificationBell unreadCount={2} />\r\n                  </div>\r\n                )}\r\n\r\n                <div\r\n                  className=\"flex items-center space-x-2 group safe-profile-animation cursor-pointer\"\r\n                  onClick={() => navigate('/profile')}\r\n                  title=\"Go to Profile\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={{\r\n                      ...user,\r\n                      isOnline: true,\r\n                      lastActivity: new Date().toISOString()\r\n                    }}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Profile Access Indicator */}\r\n                  <div className=\"hidden md:block\">\r\n                    <TbUser className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-300 text-sm\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n        )}\r\n\r\n        {/* Page Content */}\r\n        <main className={`flex-1 overflow-auto ${\r\n          user?.isAdmin\r\n            ? 'bg-gray-100'\r\n            : 'bg-gradient-to-br from-gray-50 to-blue-50'\r\n        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>\r\n          <div\r\n            className=\"h-full safe-content-animation\"\r\n          >\r\n            {children}\r\n          </div>\r\n        </main>\r\n\r\n        {/* Floating Brainwave AI - Show on all pages except quiz and results */}\r\n        {shouldShowFloatingAI() && <FloatingBrainwaveAI />}\r\n\r\n        {/* Subscription Trigger for Non-Admin Users */}\r\n        {!user?.isAdmin && <SubscriptionTrigger />}\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,eAAe,QAAQ,OAAO;AAC3E,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,SAASC,MAAM,EAAEC,eAAe,EAAEC,OAAO,EAAEC,GAAG,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AACnI,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,aAAa,EAAEC,cAAc,EAAEC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzF,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGlC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM2C,WAAW,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM2C,YAAY,GAAG3C,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAE4C;EAAiB,CAAC,GAAGxC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACM,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAG1C,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACQ,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,WAAW,GAAGF,QAAQ,CAACG,QAAQ;IACrC,MAAMC,aAAa,GAAG,CACpB,YAAY,EACZ,aAAa,EACb,OAAO,EACP,QAAQ,EACR,UAAU,EACV,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,cAAc,EACd,QAAQ,EACR,SAAS,EACT,UAAU,EACV,WAAW,EACX,eAAe,EACf,gBAAgB,CACjB;;IAED;IACA,OAAO,CAACA,aAAa,CAACC,IAAI,CAACC,IAAI,IAAIJ,WAAW,CAACK,QAAQ,CAACD,IAAI,CAAC,CAAC,IACvD,CAACJ,WAAW,CAACK,QAAQ,CAAC,MAAM,CAAC,IAC7B,CAACL,WAAW,CAACK,QAAQ,CAAC,QAAQ,CAAC,IAC/B,CAACL,WAAW,CAACK,QAAQ,CAAC,OAAO,CAAC,IAC9B,CAACL,WAAW,CAACK,QAAQ,CAAC,SAAS,CAAC;EACzC,CAAC;EACD,MAAMC,WAAW,GAAGR,QAAQ,CAACG,QAAQ;EAMrC,MAAMM,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM1D,WAAW,CAAC,CAAC;MACpC,IAAI0D,QAAQ,CAACC,OAAO,EAAE;QACpBb,QAAQ,CAAC3C,OAAO,CAACuD,QAAQ,CAACE,IAAI,CAAC,CAAC;;QAEhC;QACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAACE,IAAI,CAAC,CAAC;;QAE3D;QACAK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/BC,IAAI,EAAET,QAAQ,CAACE,IAAI,CAACO,IAAI;UACxBC,OAAO,EAAEV,QAAQ,CAACE,IAAI,CAACQ,OAAO;UAC9BC,KAAK,EAAEX,QAAQ,CAACE,IAAI,CAACS;QACvB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL3E,OAAO,CAAC4E,KAAK,CAACZ,QAAQ,CAAChE,OAAO,CAAC;QAC/BqD,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdvB,QAAQ,CAAC,QAAQ,CAAC;MAClBrD,OAAO,CAAC4E,KAAK,CAACA,KAAK,CAAC5E,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAM2E,KAAK,GAAGV,YAAY,CAACW,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAID,KAAK,EAAE;MACT;MACA,IAAI,CAACnC,IAAI,EAAE;QACT;QACA,MAAMqC,UAAU,GAAGZ,YAAY,CAACW,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIC,UAAU,EAAE;UACd,IAAI;YACF,MAAMC,QAAQ,GAAGX,IAAI,CAACY,KAAK,CAACF,UAAU,CAAC;YACvCR,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;cAAEC,IAAI,EAAEO,QAAQ,CAACP,IAAI;cAAEC,OAAO,EAAEM,QAAQ,CAACN;YAAQ,CAAC,CAAC;YACjHtB,QAAQ,CAAC3C,OAAO,CAACuE,QAAQ,CAAC,CAAC;UAC7B,CAAC,CAAC,OAAOJ,KAAK,EAAE;YACdL,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;YACnFT,WAAW,CAAC,CAAC;UACf;QACF,CAAC,MAAM;UACLQ,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;UACrFT,WAAW,CAAC,CAAC;QACf;MACF,CAAC,MAAM;QACLQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;UAAEC,IAAI,EAAE/B,IAAI,CAAC+B,IAAI;UAAEC,OAAO,EAAEhC,IAAI,CAACgC;QAAQ,CAAC,CAAC;MAClG;IACF,CAAC,MAAM;MACLrB,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINnD,SAAS,CAAC,MAAM;IACd;IACA,MAAMgF,aAAa,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC;IACnD,MAAMC,cAAc,GAAGD,aAAa,CAACvB,IAAI,CAACyB,KAAK,IAAItB,WAAW,CAACD,QAAQ,CAACuB,KAAK,CAAC,CAAC;IAE/E,IAAIxC,gBAAgB,IAAI,CAACuC,cAAc,EAAE;MACvC9B,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACT,gBAAgB,EAAEkB,WAAW,EAAET,QAAQ,CAAC,CAAC;EAE7C,MAAMgC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMnB,IAAI,GAAG,MAAMpD,kBAAkB,CAAC,CAAC;MACvCyD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEN,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEU,KAAK,IAAI,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,aAAa,MAAK,MAAM,EAAE;QACjD,IAAItC,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACrC,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACA8B,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BO,QAAQ,CAACrC,eAAe,CAACmD,IAAI,CAAC,CAAC;QAC/B,IAAIpB,WAAW,CAACyC,OAAO,EAAE;UACvBC,aAAa,CAAC1C,WAAW,CAACyC,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,KAAK,CAAC;MACpDxB,QAAQ,CAACrC,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/B8B,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED3C,SAAS,CAAC,MAAM;IACd,IAAIwC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,eAAe,IAAI,EAAC/C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,OAAO,GAAE;MAC3CH,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAItB,yBAAyB,EAAE;QAC7BqB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C1B,WAAW,CAACyC,OAAO,GAAGG,WAAW,CAAC,MAAM;UACtCnB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCa,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTjC,QAAQ,CAACpC,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACkC,yBAAyB,CAAC,CAAC;EAE/BhD,SAAS,CAAC,MAAM;IACd,IAAIwC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,eAAe,IAAI,EAAC/C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,OAAO,GAAE;MAC3CH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/Ba,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAC3C,IAAI,EAAEoB,WAAW,CAAC,CAAC;;EAEvB;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIwC,IAAI,IAAI,CAACA,IAAI,CAACgC,OAAO,EAAE;MACzB;MACAxC,aAAa,CAAC,CAAC,CAACyD,KAAK,CAACpB,OAAO,CAACK,KAAK,CAAC;;MAEpC;MACA7B,YAAY,CAACwC,OAAO,GAAGG,WAAW,CAAC,MAAM;QACvCtD,aAAa,CAAC,CAAC,CAACuD,KAAK,CAACpB,OAAO,CAACK,KAAK,CAAC;MACtC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;MAEZ;MACA,MAAMgB,kBAAkB,GAAGA,CAAA,KAAM;QAC/BzD,cAAc,CAAC,CAAC,CAACwD,KAAK,CAACpB,OAAO,CAACK,KAAK,CAAC;MACvC,CAAC;MAEDiB,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEF,kBAAkB,CAAC;MAE3D,OAAO,MAAM;QACX,IAAI7C,YAAY,CAACwC,OAAO,EAAE;UACxBC,aAAa,CAACzC,YAAY,CAACwC,OAAO,CAAC;QACrC;QACAM,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEH,kBAAkB,CAAC;QAC9DzD,cAAc,CAAC,CAAC,CAACwD,KAAK,CAACpB,OAAO,CAACK,KAAK,CAAC;MACvC,CAAC;IACH;EACF,CAAC,EAAE,CAAClC,IAAI,CAAC,CAAC;EAGV,MAAMsD,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAI,CAACvD,IAAI,CAAC+C,eAAe,IAAIQ,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAAjD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsC,aAAa,MAAK,MAAM,IAAI5C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAKD,oBACEnD,OAAA;IAAK4D,SAAS,EAAC,0CAA0C;IAAA1D,QAAA,GAEtD,EAACE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,OAAO,kBAAIpC,OAAA,CAACpB,aAAa;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpChE,OAAA;MAAK4D,SAAS,EAAC,mCAAmC;MAAA1D,QAAA,gBAG9CF,OAAA;QACE4D,SAAS,EAAG,oCACV5C,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,OAAO,CAAC,IAAIP,QAAQ,CAACG,QAAQ,CAACI,QAAQ,CAAC,aAAa,CAAC,GAC5E,8EAA8E,GAC9E,2DACL,8FAA8F;QAAArB,QAAA,eAEjGF,OAAA;UAAK4D,SAAS,EAAC,+CAA+C;UAAA1D,QAAA,eAC5DF,OAAA;YAAK4D,SAAS,EAAC,wEAAwE;YAAA1D,QAAA,gBAErFF,OAAA;cAAK4D,SAAS,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEvC,CAAC,eAGNhE,OAAA;cAAK4D,SAAS,EAAC,4BAA4B;cAAA1D,QAAA,eACzCF,OAAA;gBACE4D,SAAS,EAAC,kEAAkE;gBAAA1D,QAAA,gBAG5EF,OAAA;kBACE4D,SAAS,EAAC,wEAAwE;kBAClFK,KAAK,EAAE;oBACLC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBAAAjE,QAAA,eAEFF,OAAA;oBACEoE,GAAG,EAAC,gCAAgC;oBACpCC,GAAG,EAAC,eAAe;oBACnBT,SAAS,EAAC,4BAA4B;oBACtCK,KAAK,EAAE;sBAAEK,SAAS,EAAE;oBAAQ,CAAE;oBAC9BC,OAAO,EAAGC,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,8GAA8G;sBAC7HI,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,MAAM;wBACvB;wBACAF,CAAC,CAACC,MAAM,CAACR,KAAK,CAACU,OAAO,GAAG,MAAM;wBAC/BH,CAAC,CAACC,MAAM,CAACG,aAAa,CAACC,SAAS,GAAG,+GAA+G;sBACpJ,CAAC;oBACH;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNhE,OAAA;kBAAK4D,SAAS,EAAC,8BAA8B;kBAAA1D,QAAA,gBAC3CF,OAAA;oBAAI4D,SAAS,EAAC,qFAAqF;oBAC/FK,KAAK,EAAE;sBACLa,UAAU,EAAE,yDAAyD;sBACrEC,aAAa,EAAE;oBACjB,CAAE;oBAAA7E,QAAA,gBAEJF,OAAA;sBACE4D,SAAS,EAAC,kCAAkC;sBAC5CK,KAAK,EAAE;wBACLe,KAAK,EAAE,SAAS;wBAChBC,UAAU,EAAE,KAAK;wBACjBC,UAAU,EAAE,kCAAkC;wBAC9CC,SAAS,EAAE;sBACb,CAAE;sBAAAjF,QAAA,GACH,OAGC,eACAF,OAAA;wBACE4D,SAAS,EAAC,8DAA8D;wBACxEK,KAAK,EAAE;0BACLmB,eAAe,EAAE,SAAS;0BAC1BC,SAAS,EAAE,kBAAkB;0BAC7BF,SAAS,EAAE;wBACb;sBAAE;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGPhE,OAAA;sBACE4D,SAAS,EAAC,iCAAiC;sBAC3CK,KAAK,EAAE;wBACLe,KAAK,EAAE,SAAS;wBAChBC,UAAU,EAAE,KAAK;wBACjBC,UAAU,EAAE,kCAAkC;wBAC9CC,SAAS,EAAE;sBACb,CAAE;sBAAAjF,QAAA,GACH,MAGC,eACAF,OAAA;wBACE4D,SAAS,EAAC,8DAA8D;wBACxEK,KAAK,EAAE;0BACLmB,eAAe,EAAE,SAAS;0BAC1BC,SAAS,EAAE,iBAAiB;0BAC5BF,SAAS,EAAE;wBACb;sBAAE;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAGLhE,OAAA;oBACE4D,SAAS,EAAC,8DAA8D;oBACxEK,KAAK,EAAE;sBACLqB,UAAU,EAAE,mDAAmD;sBAC/DD,SAAS,EAAE,kCAAkC;sBAC7CnB,KAAK,EAAE,MAAM;sBACbiB,SAAS,EAAE;oBACb;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNhE,OAAA;kBACE4D,SAAS,EAAC,gEAAgE;kBAC1EK,KAAK,EAAE;oBACLqB,UAAU,EAAE,SAAS;oBACrBD,SAAS,EAAE,4BAA4B;oBACvCnB,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBAAAjE,QAAA,gBAEFF,OAAA;oBACEoE,GAAG,EAAC,cAAc;oBAClBC,GAAG,EAAC,gBAAgB;oBACpBT,SAAS,EAAC,4BAA4B;oBACtCK,KAAK,EAAE;sBAAEK,SAAS,EAAE;oBAAQ,CAAE;oBAC9BC,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACR,KAAK,CAACU,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACc,WAAW,CAACtB,KAAK,CAACU,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFhE,OAAA;oBACE4D,SAAS,EAAC,gHAAgH;oBAC1HK,KAAK,EAAE;sBACLU,OAAO,EAAE,MAAM;sBACfa,QAAQ,EAAE;oBACZ,CAAE;oBAAAtF,QAAA,EACH;kBAED;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNhE,OAAA;kBAAK4D,SAAS,EAAC;gBAAyK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5L;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhE,OAAA;cAAK4D,SAAS,EAAC,sDAAsD;cAAA1D,QAAA,GAElE,EAACE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,OAAO,kBACbpC,OAAA;gBAAK4D,SAAS,EAAC,6BAA6B;gBAAA1D,QAAA,eAC1CF,OAAA,CAACP,gBAAgB;kBAACgG,WAAW,EAAE;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CACN,eAEDhE,OAAA;gBACE4D,SAAS,EAAC,yEAAyE;gBACnF8B,OAAO,EAAEA,CAAA,KAAM3E,QAAQ,CAAC,UAAU,CAAE;gBACpC4C,KAAK,EAAC,eAAe;gBAAAzD,QAAA,gBAGrBF,OAAA,CAACN,cAAc;kBACbU,IAAI,EAAE;oBACJ,GAAGA,IAAI;oBACPuF,QAAQ,EAAE,IAAI;oBACdC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;kBACvC,CAAE;kBACFC,IAAI,EAAC,IAAI;kBACTC,gBAAgB,EAAE,IAAK;kBACvB/B,KAAK,EAAE;oBACLC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGFhE,OAAA;kBAAK4D,SAAS,EAAC,4BAA4B;kBAAA1D,QAAA,gBACzCF,OAAA;oBAAK4D,SAAS,EAAC,uGAAuG;oBAAA1D,QAAA,EACnH,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,KAAI;kBAAM;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACNhE,OAAA;oBAAK4D,SAAS,EAAC,gFAAgF;oBAAA1D,QAAA,GAAC,QACxF,EAACE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,KAAK;kBAAA;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNhE,OAAA;kBAAK4D,SAAS,EAAC,iBAAiB;kBAAA1D,QAAA,eAC9BF,OAAA,CAACZ,MAAM;oBAACwE,SAAS,EAAC;kBAAgF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAIThE,OAAA;QAAM4D,SAAS,EAAG,wBAChBxD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,OAAO,GACT,aAAa,GACb,2CACL,IAAGhC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,OAAO,GAAG,KAAK,GAAG,eAAgB,EAAE;QAAAlC,QAAA,eAC5CF,OAAA;UACE4D,SAAS,EAAC,+BAA+B;UAAA1D,QAAA,EAExCA;QAAQ;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGN/C,oBAAoB,CAAC,CAAC,iBAAIjB,OAAA,CAACL,mBAAmB;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGjD,EAAC5D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,OAAO,kBAAIpC,OAAA,CAACnB,mBAAmB;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7D,EAAA,CA3aQF,cAAc;EAAA,QACJ/B,WAAW,EAICA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW;AAAA;AAAA6H,EAAA,GATrBjG,cAAc;AA6avB,eAAeA,cAAc;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}