{"ast": null, "code": "import React from'react';import{jsx as _jsx}from\"react/jsx-runtime\";function Loader(){return/*#__PURE__*/_jsx(\"div\",{className:\"loader-parent\",children:/*#__PURE__*/_jsx(\"div\",{className:\"loader\"})});}export default Loader;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "Loader", "className", "children"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/Loader.js"], "sourcesContent": ["import React from 'react'\r\n\r\nfunction Loader() {\r\n  return (\r\n    <div className='loader-parent'>\r\n      <div className='loader'></div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Loader"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,QAAAC,GAAA,IAAAC,IAAA,yBAEzB,QAAS,CAAAC,MAAMA,CAAA,CAAG,CAChB,mBACED,IAAA,QAAKE,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BH,IAAA,QAAKE,SAAS,CAAC,QAAQ,CAAM,CAAC,CAC3B,CAAC,CAEV,CAEA,cAAe,CAAAD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}