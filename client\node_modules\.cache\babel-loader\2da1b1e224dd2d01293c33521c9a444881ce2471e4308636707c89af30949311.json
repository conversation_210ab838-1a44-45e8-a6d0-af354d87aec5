{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Plans\\\\Plans.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { message } from \"antd\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport { addPayment } from \"../../../apicalls/payment\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport \"./Plans.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Plans = () => {\n  _s();\n  var _subscriptionData$pla2, _subscriptionData$pla3, _subscriptionData$pla4;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [selectedPlanId, setSelectedPlanId] = useState(null);\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [paymentSuccess, setPaymentSuccess] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n\n  // Fetch plans on component mount\n  useEffect(() => {\n    fetchPlans();\n  }, []);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error(\"Error fetching plans:\", error);\n      setError(\"Failed to load plans. Please try again.\");\n      setPlans([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = async plan => {\n    if (!plan || paymentLoading) return;\n    if (!(user !== null && user !== void 0 && user.phoneNumber)) {\n      message.error(\"Please update your phone number in profile to proceed with payment.\");\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      setSelectedPlanId(plan._id);\n      setShowPaymentModal(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        planId: plan._id,\n        amount: plan.discountedPrice,\n        planTitle: plan.title,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success(\"Payment initiated successfully! Please check your phone for SMS confirmation.\");\n        // In real implementation, you would handle the payment flow here\n        setTimeout(() => {\n          setPaymentSuccess(true);\n          setShowPaymentModal(false);\n        }, 3000);\n      } else {\n        throw new Error(response.message || \"Payment failed\");\n      }\n    } catch (error) {\n      console.error(\"Payment error:\", error);\n      message.error(error.message || \"Payment failed. Please try again.\");\n      setShowPaymentModal(false);\n    } finally {\n      setPaymentLoading(false);\n      setSelectedPlanId(null);\n      dispatch(HideLoading());\n    }\n  };\n  const formatDate = dateString => {\n    if (!dateString) return \"Not available\";\n    try {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    } catch {\n      return \"Invalid date\";\n    }\n  };\n  const getDaysRemaining = endDate => {\n    if (!endDate) return 0;\n    try {\n      const end = new Date(endDate);\n      const now = new Date();\n      const diffTime = end - now;\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      return Math.max(0, diffDays);\n    } catch {\n      return 0;\n    }\n  };\n\n  // Check if subscription is active - handle missing status field and validate dates\n  const isSubscriptionActive = () => {\n    if (!subscriptionData || subscriptionData.paymentStatus !== \"paid\") {\n      return false;\n    }\n\n    // Check if status is explicitly active\n    if (subscriptionData.status === \"active\") {\n      return true;\n    }\n\n    // If status is missing but we have payment and dates, check if not expired\n    if (!subscriptionData.status && subscriptionData.startDate && subscriptionData.endDate) {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      return endDate > now; // Not expired\n    }\n\n    return false;\n  };\n  const subscriptionActive = isSubscriptionActive();\n\n  // Debug: Log subscription data\n  console.log(\"🔍 Plans Page Debug:\");\n  console.log(\"User:\", user);\n  console.log(\"Subscription Data:\", subscriptionData);\n  console.log(\"Is Subscription Active:\", subscriptionActive);\n  if (subscriptionData) {\n    var _subscriptionData$pla;\n    console.log(\"🔍 Detailed Analysis:\");\n    console.log(\"- Payment Status:\", subscriptionData.paymentStatus);\n    console.log(\"- Status Field:\", subscriptionData.status || \"MISSING\");\n    console.log(\"- Start Date:\", subscriptionData.startDate);\n    console.log(\"- End Date:\", subscriptionData.endDate);\n    console.log(\"- Plan Title:\", (_subscriptionData$pla = subscriptionData.plan) === null || _subscriptionData$pla === void 0 ? void 0 : _subscriptionData$pla.title);\n    if (subscriptionData.endDate) {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      const isExpired = endDate <= now;\n      console.log(\"- Is Expired:\", isExpired);\n      console.log(\"- Days Remaining:\", Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)));\n    }\n  }\n\n  // Loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Loading your plans...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please wait while we fetch the latest subscription options\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Oops! Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"retry-btn\",\n          onClick: fetchPlans,\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"plans-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"plans-title\",\n        children: \"Choose Your Learning Plan\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"plans-subtitle\",\n        children: \"Unlock your potential with our premium educational content\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 13\n    }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f0f0f0',\n        padding: '1rem',\n        margin: '1rem 0',\n        borderRadius: '8px',\n        fontSize: '0.9rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDD0D Debug Info:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"User:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 24\n        }, this), \" \", (user === null || user === void 0 ? void 0 : user.name) || 'Not logged in']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Subscription Data:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 24\n        }, this), \" \", subscriptionData ? 'Available' : 'Not available']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 21\n      }, this), subscriptionData && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Payment Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 32\n          }, this), \" \", subscriptionData.paymentStatus]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Status:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 32\n          }, this), \" \", subscriptionData.status]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Start Date:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 32\n          }, this), \" \", subscriptionData.startDate]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"End Date:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 32\n          }, this), \" \", subscriptionData.endDate]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Plan:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 32\n          }, this), \" \", ((_subscriptionData$pla2 = subscriptionData.plan) === null || _subscriptionData$pla2 === void 0 ? void 0 : _subscriptionData$pla2.title) || 'No plan']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Is Active:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 24\n        }, this), \" \", subscriptionActive ? 'YES' : 'NO']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 17\n    }, this), isSubscriptionActive && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"current-subscription\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"status-badge active\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 29\n          }, this), \"Active Subscription\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"subscription-title\",\n          children: (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$pla3 = subscriptionData.plan) === null || _subscriptionData$pla3 === void 0 ? void 0 : _subscriptionData$pla3.title) || \"Premium Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-icon\",\n            children: \"\\uD83D\\uDCC5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.startDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-icon\",\n            children: \"\\u23F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Expires\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-icon\",\n            children: \"\\uD83C\\uDFAF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Days Remaining\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value highlight\",\n              children: [getDaysRemaining(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate), \" days\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-icon\",\n            children: \"\\uD83D\\uDC8E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-label\",\n              children: \"Plan Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"detail-value\",\n              children: (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$pla4 = subscriptionData.plan) === null || _subscriptionData$pla4 === void 0 ? void 0 : _subscriptionData$pla4.title) || \"Premium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-benefits\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Your Premium Benefits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"benefits-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefit-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-icon\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-text\",\n              children: \"Unlimited Quiz Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefit-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-text\",\n              children: \"Progress Tracking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefit-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-icon\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-text\",\n              children: \"Achievement Badges\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefit-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-icon\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"benefit-text\",\n              children: \"AI Study Assistant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"action-btn primary\",\n          onClick: () => navigate('/user/hub'),\n          children: \"Continue Learning \\uD83C\\uDF93\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"action-btn secondary\",\n          onClick: () => navigate('/user/profile'),\n          children: \"Manage Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 17\n    }, this), !isSubscriptionActive && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"available-plans\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Available Plans\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 21\n      }, this), plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-icon\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Plans Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please check back later for subscription options.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"plans-grid\",\n        children: plans.map(plan => {\n          var _plan$title, _plan$discountedPrice, _plan$features, _plan$title2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"plan-name\",\n                children: plan.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 41\n              }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-badge\",\n                children: \"\\uD83D\\uDE80 Quick Start\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-pricing\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-main\",\n                children: [((_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()) || '0', \" TZS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 41\n              }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-original\",\n                children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-period\",\n                children: plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-features\",\n              children: ((_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-check\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-text\",\n                  children: feature\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 49\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 45\n              }, this))) || /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-check\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-text\",\n                  children: \"Premium access included\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `plan-btn ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`,\n              onClick: () => handlePlanSelect(plan),\n              disabled: paymentLoading,\n              children: paymentLoading && selectedPlanId === plan._id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 49\n                }, this), \"Processing...\"]\n              }, void 0, true) : (_plan$title2 = plan.title) !== null && _plan$title2 !== void 0 && _plan$title2.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 37\n            }, this)]\n          }, plan._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 17\n    }, this), showPaymentModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"payment-spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Processing Payment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: \"1. Check your SMS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: \"2. Follow the instructions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: \"3. Complete payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 17\n    }, this), paymentSuccess && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-icon\",\n            children: \"\\uD83C\\uDF89\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Successful!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Your subscription has been activated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"success-btn\",\n            onClick: () => {\n              setPaymentSuccess(false);\n              window.location.reload();\n            },\n            children: \"Continue Learning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 9\n  }, this);\n};\n_s(Plans, \"SMny4RnB0bAWlEMZR4ApMDTCo/k=\", false, function () {\n  return [useSelector, useSelector, useDispatch, useNavigate];\n});\n_c = Plans;\nexport default Plans;\nvar _c;\n$RefreshReg$(_c, \"Plans\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "useDispatch", "useNavigate", "message", "getPlans", "addPayment", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Plans", "_s", "_subscriptionData$pla2", "_subscriptionData$pla3", "_subscriptionData$pla4", "plans", "setPlans", "loading", "setLoading", "error", "setError", "paymentLoading", "setPaymentLoading", "selectedPlanId", "setSelectedPlanId", "showPaymentModal", "setShowPaymentModal", "paymentSuccess", "setPaymentSuccess", "user", "state", "subscriptionData", "subscription", "dispatch", "navigate", "fetchPlans", "response", "Array", "isArray", "console", "handlePlanSelect", "plan", "phoneNumber", "_user$name", "_id", "paymentData", "planId", "amount", "discountedPrice", "planTitle", "title", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "success", "setTimeout", "Error", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "endDate", "end", "now", "diffTime", "diffDays", "Math", "ceil", "max", "isSubscriptionActive", "paymentStatus", "status", "startDate", "subscriptionActive", "log", "_subscriptionData$pla", "isExpired", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "process", "env", "NODE_ENV", "style", "background", "padding", "margin", "borderRadius", "fontSize", "length", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$title2", "includes", "toLocaleString", "actualPrice", "duration", "features", "feature", "index", "disabled", "window", "location", "reload", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Plans/Plans.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { message } from \"antd\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport { addPayment } from \"../../../apicalls/payment\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport \"./Plans.css\";\n\nconst Plans = () => {\n    const [plans, setPlans] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [paymentLoading, setPaymentLoading] = useState(false);\n    const [selectedPlanId, setSelectedPlanId] = useState(null);\n    const [showPaymentModal, setShowPaymentModal] = useState(false);\n    const [paymentSuccess, setPaymentSuccess] = useState(false);\n    \n    const { user } = useSelector((state) => state.user);\n    const { subscriptionData } = useSelector((state) => state.subscription);\n    const dispatch = useDispatch();\n    const navigate = useNavigate();\n\n    // Fetch plans on component mount\n    useEffect(() => {\n        fetchPlans();\n    }, []);\n\n    const fetchPlans = async () => {\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await getPlans();\n            setPlans(Array.isArray(response) ? response : []);\n        } catch (error) {\n            console.error(\"Error fetching plans:\", error);\n            setError(\"Failed to load plans. Please try again.\");\n            setPlans([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handlePlanSelect = async (plan) => {\n        if (!plan || paymentLoading) return;\n        \n        if (!user?.phoneNumber) {\n            message.error(\"Please update your phone number in profile to proceed with payment.\");\n            return;\n        }\n\n        try {\n            setPaymentLoading(true);\n            setSelectedPlanId(plan._id);\n            setShowPaymentModal(true);\n            \n            dispatch(ShowLoading());\n            \n            const paymentData = {\n                planId: plan._id,\n                amount: plan.discountedPrice,\n                planTitle: plan.title,\n                userId: user._id,\n                userPhone: user.phoneNumber,\n                userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n            };\n\n            const response = await addPayment(paymentData);\n            \n            if (response.success) {\n                message.success(\"Payment initiated successfully! Please check your phone for SMS confirmation.\");\n                // In real implementation, you would handle the payment flow here\n                setTimeout(() => {\n                    setPaymentSuccess(true);\n                    setShowPaymentModal(false);\n                }, 3000);\n            } else {\n                throw new Error(response.message || \"Payment failed\");\n            }\n            \n        } catch (error) {\n            console.error(\"Payment error:\", error);\n            message.error(error.message || \"Payment failed. Please try again.\");\n            setShowPaymentModal(false);\n        } finally {\n            setPaymentLoading(false);\n            setSelectedPlanId(null);\n            dispatch(HideLoading());\n        }\n    };\n\n    const formatDate = (dateString) => {\n        if (!dateString) return \"Not available\";\n        try {\n            return new Date(dateString).toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            });\n        } catch {\n            return \"Invalid date\";\n        }\n    };\n\n    const getDaysRemaining = (endDate) => {\n        if (!endDate) return 0;\n        try {\n            const end = new Date(endDate);\n            const now = new Date();\n            const diffTime = end - now;\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            return Math.max(0, diffDays);\n        } catch {\n            return 0;\n        }\n    };\n\n    // Check if subscription is active - handle missing status field and validate dates\n    const isSubscriptionActive = () => {\n        if (!subscriptionData || subscriptionData.paymentStatus !== \"paid\") {\n            return false;\n        }\n\n        // Check if status is explicitly active\n        if (subscriptionData.status === \"active\") {\n            return true;\n        }\n\n        // If status is missing but we have payment and dates, check if not expired\n        if (!subscriptionData.status && subscriptionData.startDate && subscriptionData.endDate) {\n            const endDate = new Date(subscriptionData.endDate);\n            const now = new Date();\n            return endDate > now; // Not expired\n        }\n\n        return false;\n    };\n\n    const subscriptionActive = isSubscriptionActive();\n\n    // Debug: Log subscription data\n    console.log(\"🔍 Plans Page Debug:\");\n    console.log(\"User:\", user);\n    console.log(\"Subscription Data:\", subscriptionData);\n    console.log(\"Is Subscription Active:\", subscriptionActive);\n\n    if (subscriptionData) {\n        console.log(\"🔍 Detailed Analysis:\");\n        console.log(\"- Payment Status:\", subscriptionData.paymentStatus);\n        console.log(\"- Status Field:\", subscriptionData.status || \"MISSING\");\n        console.log(\"- Start Date:\", subscriptionData.startDate);\n        console.log(\"- End Date:\", subscriptionData.endDate);\n        console.log(\"- Plan Title:\", subscriptionData.plan?.title);\n\n        if (subscriptionData.endDate) {\n            const endDate = new Date(subscriptionData.endDate);\n            const now = new Date();\n            const isExpired = endDate <= now;\n            console.log(\"- Is Expired:\", isExpired);\n            console.log(\"- Days Remaining:\", Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)));\n        }\n    }\n\n    // Loading state\n    if (loading) {\n        return (\n            <div className=\"plans-container\">\n                <div className=\"loading-section\">\n                    <div className=\"loading-spinner\"></div>\n                    <h3>Loading your plans...</h3>\n                    <p>Please wait while we fetch the latest subscription options</p>\n                </div>\n            </div>\n        );\n    }\n\n    // Error state\n    if (error) {\n        return (\n            <div className=\"plans-container\">\n                <div className=\"error-section\">\n                    <div className=\"error-icon\">⚠️</div>\n                    <h3>Oops! Something went wrong</h3>\n                    <p>{error}</p>\n                    <button className=\"retry-btn\" onClick={fetchPlans}>\n                        Try Again\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"plans-container\">\n            {/* Header Section */}\n            <div className=\"plans-header\">\n                <h1 className=\"plans-title\">Choose Your Learning Plan</h1>\n                <p className=\"plans-subtitle\">\n                    Unlock your potential with our premium educational content\n                </p>\n            </div>\n\n            {/* Debug Section - Remove in production */}\n            {process.env.NODE_ENV === 'development' && (\n                <div style={{\n                    background: '#f0f0f0',\n                    padding: '1rem',\n                    margin: '1rem 0',\n                    borderRadius: '8px',\n                    fontSize: '0.9rem'\n                }}>\n                    <h3>🔍 Debug Info:</h3>\n                    <p><strong>User:</strong> {user?.name || 'Not logged in'}</p>\n                    <p><strong>Subscription Data:</strong> {subscriptionData ? 'Available' : 'Not available'}</p>\n                    {subscriptionData && (\n                        <>\n                            <p><strong>Payment Status:</strong> {subscriptionData.paymentStatus}</p>\n                            <p><strong>Status:</strong> {subscriptionData.status}</p>\n                            <p><strong>Start Date:</strong> {subscriptionData.startDate}</p>\n                            <p><strong>End Date:</strong> {subscriptionData.endDate}</p>\n                            <p><strong>Plan:</strong> {subscriptionData.plan?.title || 'No plan'}</p>\n                        </>\n                    )}\n                    <p><strong>Is Active:</strong> {subscriptionActive ? 'YES' : 'NO'}</p>\n                </div>\n            )}\n\n            {/* Current Subscription Section */}\n            {isSubscriptionActive && (\n                <div className=\"current-subscription\">\n                    <div className=\"subscription-header\">\n                        <div className=\"status-badge active\">\n                            <span className=\"status-dot\"></span>\n                            Active Subscription\n                        </div>\n                        <h2 className=\"subscription-title\">\n                            {subscriptionData?.plan?.title || \"Premium Plan\"}\n                        </h2>\n                    </div>\n\n                    <div className=\"subscription-details\">\n                        <div className=\"detail-card\">\n                            <div className=\"detail-icon\">📅</div>\n                            <div className=\"detail-content\">\n                                <span className=\"detail-label\">Started</span>\n                                <span className=\"detail-value\">\n                                    {formatDate(subscriptionData?.startDate)}\n                                </span>\n                            </div>\n                        </div>\n\n                        <div className=\"detail-card\">\n                            <div className=\"detail-icon\">⏰</div>\n                            <div className=\"detail-content\">\n                                <span className=\"detail-label\">Expires</span>\n                                <span className=\"detail-value\">\n                                    {formatDate(subscriptionData?.endDate)}\n                                </span>\n                            </div>\n                        </div>\n\n                        <div className=\"detail-card\">\n                            <div className=\"detail-icon\">🎯</div>\n                            <div className=\"detail-content\">\n                                <span className=\"detail-label\">Days Remaining</span>\n                                <span className=\"detail-value highlight\">\n                                    {getDaysRemaining(subscriptionData?.endDate)} days\n                                </span>\n                            </div>\n                        </div>\n\n                        <div className=\"detail-card\">\n                            <div className=\"detail-icon\">💎</div>\n                            <div className=\"detail-content\">\n                                <span className=\"detail-label\">Plan Type</span>\n                                <span className=\"detail-value\">\n                                    {subscriptionData?.plan?.title || \"Premium\"}\n                                </span>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"subscription-benefits\">\n                        <h3>Your Premium Benefits</h3>\n                        <div className=\"benefits-grid\">\n                            <div className=\"benefit-item\">\n                                <span className=\"benefit-icon\">📚</span>\n                                <span className=\"benefit-text\">Unlimited Quiz Access</span>\n                            </div>\n                            <div className=\"benefit-item\">\n                                <span className=\"benefit-icon\">🎯</span>\n                                <span className=\"benefit-text\">Progress Tracking</span>\n                            </div>\n                            <div className=\"benefit-item\">\n                                <span className=\"benefit-icon\">🏆</span>\n                                <span className=\"benefit-text\">Achievement Badges</span>\n                            </div>\n                            <div className=\"benefit-item\">\n                                <span className=\"benefit-icon\">🚀</span>\n                                <span className=\"benefit-text\">AI Study Assistant</span>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"subscription-actions\">\n                        <button \n                            className=\"action-btn primary\"\n                            onClick={() => navigate('/user/hub')}\n                        >\n                            Continue Learning 🎓\n                        </button>\n                        <button \n                            className=\"action-btn secondary\"\n                            onClick={() => navigate('/user/profile')}\n                        >\n                            Manage Account\n                        </button>\n                    </div>\n                </div>\n            )}\n\n            {/* Available Plans Section */}\n            {!isSubscriptionActive && (\n                <div className=\"available-plans\">\n                    <h2 className=\"section-title\">Available Plans</h2>\n                    \n                    {plans.length === 0 ? (\n                        <div className=\"no-plans\">\n                            <div className=\"no-plans-icon\">📋</div>\n                            <h3>No Plans Available</h3>\n                            <p>Please check back later for subscription options.</p>\n                        </div>\n                    ) : (\n                        <div className=\"plans-grid\">\n                            {plans.map((plan) => (\n                                <div key={plan._id} className=\"plan-card\">\n                                    <div className=\"plan-header\">\n                                        <h3 className=\"plan-name\">{plan.title}</h3>\n                                        {plan.title?.toLowerCase().includes('glimp') && (\n                                            <div className=\"plan-badge\">🚀 Quick Start</div>\n                                        )}\n                                    </div>\n\n                                    <div className=\"plan-pricing\">\n                                        <div className=\"price-main\">\n                                            {plan.discountedPrice?.toLocaleString() || '0'} TZS\n                                        </div>\n                                        {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                                            <div className=\"price-original\">\n                                                {plan.actualPrice.toLocaleString()} TZS\n                                            </div>\n                                        )}\n                                        <div className=\"price-period\">\n                                            {plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'}\n                                        </div>\n                                    </div>\n\n                                    <div className=\"plan-features\">\n                                        {plan.features?.map((feature, index) => (\n                                            <div key={index} className=\"feature-item\">\n                                                <span className=\"feature-check\">✓</span>\n                                                <span className=\"feature-text\">{feature}</span>\n                                            </div>\n                                        )) || (\n                                            <div className=\"feature-item\">\n                                                <span className=\"feature-check\">✓</span>\n                                                <span className=\"feature-text\">Premium access included</span>\n                                            </div>\n                                        )}\n                                    </div>\n\n                                    <button\n                                        className={`plan-btn ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`}\n                                        onClick={() => handlePlanSelect(plan)}\n                                        disabled={paymentLoading}\n                                    >\n                                        {paymentLoading && selectedPlanId === plan._id ? (\n                                            <>\n                                                <span className=\"btn-spinner\"></span>\n                                                Processing...\n                                            </>\n                                        ) : (\n                                            plan.title?.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'\n                                        )}\n                                    </button>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n            )}\n\n            {/* Payment Modal */}\n            {showPaymentModal && (\n                <div className=\"payment-modal-overlay\">\n                    <div className=\"payment-modal\">\n                        <div className=\"payment-content\">\n                            <div className=\"payment-icon\">\n                                <div className=\"payment-spinner\"></div>\n                            </div>\n                            <h3>Processing Payment</h3>\n                            <p>Please check your phone for SMS confirmation</p>\n                            <div className=\"payment-steps\">\n                                <div className=\"step\">1. Check your SMS</div>\n                                <div className=\"step\">2. Follow the instructions</div>\n                                <div className=\"step\">3. Complete payment</div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Success Modal */}\n            {paymentSuccess && (\n                <div className=\"success-modal-overlay\">\n                    <div className=\"success-modal\">\n                        <div className=\"success-content\">\n                            <div className=\"success-icon\">🎉</div>\n                            <h3>Payment Successful!</h3>\n                            <p>Your subscription has been activated</p>\n                            <button \n                                className=\"success-btn\"\n                                onClick={() => {\n                                    setPaymentSuccess(false);\n                                    window.location.reload();\n                                }}\n                            >\n                                Continue Learning\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Plans;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAChB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM;IAAEgC;EAAK,CAAC,GAAG/B,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGjC,WAAW,CAAEgC,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;;EAE9B;EACAJ,SAAS,CAAC,MAAM;IACZuC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACAjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMgB,QAAQ,GAAG,MAAMlC,QAAQ,CAAC,CAAC;MACjCc,QAAQ,CAACqB,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACZoB,OAAO,CAACpB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,yCAAyC,CAAC;MACnDJ,QAAQ,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsB,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI,CAACA,IAAI,IAAIpB,cAAc,EAAE;IAE7B,IAAI,EAACQ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEa,WAAW,GAAE;MACpBzC,OAAO,CAACkB,KAAK,CAAC,qEAAqE,CAAC;MACpF;IACJ;IAEA,IAAI;MAAA,IAAAwB,UAAA;MACArB,iBAAiB,CAAC,IAAI,CAAC;MACvBE,iBAAiB,CAACiB,IAAI,CAACG,GAAG,CAAC;MAC3BlB,mBAAmB,CAAC,IAAI,CAAC;MAEzBO,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMwC,WAAW,GAAG;QAChBC,MAAM,EAAEL,IAAI,CAACG,GAAG;QAChBG,MAAM,EAAEN,IAAI,CAACO,eAAe;QAC5BC,SAAS,EAAER,IAAI,CAACS,KAAK;QACrBC,MAAM,EAAEtB,IAAI,CAACe,GAAG;QAChBQ,SAAS,EAAEvB,IAAI,CAACa,WAAW;QAC3BW,SAAS,EAAExB,IAAI,CAACyB,KAAK,IAAK,IAAAX,UAAA,GAAEd,IAAI,CAAC0B,IAAI,cAAAZ,UAAA,uBAATA,UAAA,CAAWa,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC7E,CAAC;MAED,MAAMrB,QAAQ,GAAG,MAAMjC,UAAU,CAAC0C,WAAW,CAAC;MAE9C,IAAIT,QAAQ,CAACsB,OAAO,EAAE;QAClBzD,OAAO,CAACyD,OAAO,CAAC,+EAA+E,CAAC;QAChG;QACAC,UAAU,CAAC,MAAM;UACb/B,iBAAiB,CAAC,IAAI,CAAC;UACvBF,mBAAmB,CAAC,KAAK,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,MAAM;QACH,MAAM,IAAIkC,KAAK,CAACxB,QAAQ,CAACnC,OAAO,IAAI,gBAAgB,CAAC;MACzD;IAEJ,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACZoB,OAAO,CAACpB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtClB,OAAO,CAACkB,KAAK,CAACA,KAAK,CAAClB,OAAO,IAAI,mCAAmC,CAAC;MACnEyB,mBAAmB,CAAC,KAAK,CAAC;IAC9B,CAAC,SAAS;MACNJ,iBAAiB,CAAC,KAAK,CAAC;MACxBE,iBAAiB,CAAC,IAAI,CAAC;MACvBS,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMyD,UAAU,GAAIC,UAAU,IAAK;IAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,eAAe;IACvC,IAAI;MACA,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACpDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACT,CAAC,CAAC;IACN,CAAC,CAAC,MAAM;MACJ,OAAO,cAAc;IACzB;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IACtB,IAAI;MACA,MAAMC,GAAG,GAAG,IAAIP,IAAI,CAACM,OAAO,CAAC;MAC7B,MAAME,GAAG,GAAG,IAAIR,IAAI,CAAC,CAAC;MACtB,MAAMS,QAAQ,GAAGF,GAAG,GAAGC,GAAG;MAC1B,MAAME,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;IAChC,CAAC,CAAC,MAAM;MACJ,OAAO,CAAC;IACZ;EACJ,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAC9C,gBAAgB,IAAIA,gBAAgB,CAAC+C,aAAa,KAAK,MAAM,EAAE;MAChE,OAAO,KAAK;IAChB;;IAEA;IACA,IAAI/C,gBAAgB,CAACgD,MAAM,KAAK,QAAQ,EAAE;MACtC,OAAO,IAAI;IACf;;IAEA;IACA,IAAI,CAAChD,gBAAgB,CAACgD,MAAM,IAAIhD,gBAAgB,CAACiD,SAAS,IAAIjD,gBAAgB,CAACsC,OAAO,EAAE;MACpF,MAAMA,OAAO,GAAG,IAAIN,IAAI,CAAChC,gBAAgB,CAACsC,OAAO,CAAC;MAClD,MAAME,GAAG,GAAG,IAAIR,IAAI,CAAC,CAAC;MACtB,OAAOM,OAAO,GAAGE,GAAG,CAAC,CAAC;IAC1B;;IAEA,OAAO,KAAK;EAChB,CAAC;EAED,MAAMU,kBAAkB,GAAGJ,oBAAoB,CAAC,CAAC;;EAEjD;EACAtC,OAAO,CAAC2C,GAAG,CAAC,sBAAsB,CAAC;EACnC3C,OAAO,CAAC2C,GAAG,CAAC,OAAO,EAAErD,IAAI,CAAC;EAC1BU,OAAO,CAAC2C,GAAG,CAAC,oBAAoB,EAAEnD,gBAAgB,CAAC;EACnDQ,OAAO,CAAC2C,GAAG,CAAC,yBAAyB,EAAED,kBAAkB,CAAC;EAE1D,IAAIlD,gBAAgB,EAAE;IAAA,IAAAoD,qBAAA;IAClB5C,OAAO,CAAC2C,GAAG,CAAC,uBAAuB,CAAC;IACpC3C,OAAO,CAAC2C,GAAG,CAAC,mBAAmB,EAAEnD,gBAAgB,CAAC+C,aAAa,CAAC;IAChEvC,OAAO,CAAC2C,GAAG,CAAC,iBAAiB,EAAEnD,gBAAgB,CAACgD,MAAM,IAAI,SAAS,CAAC;IACpExC,OAAO,CAAC2C,GAAG,CAAC,eAAe,EAAEnD,gBAAgB,CAACiD,SAAS,CAAC;IACxDzC,OAAO,CAAC2C,GAAG,CAAC,aAAa,EAAEnD,gBAAgB,CAACsC,OAAO,CAAC;IACpD9B,OAAO,CAAC2C,GAAG,CAAC,eAAe,GAAAC,qBAAA,GAAEpD,gBAAgB,CAACU,IAAI,cAAA0C,qBAAA,uBAArBA,qBAAA,CAAuBjC,KAAK,CAAC;IAE1D,IAAInB,gBAAgB,CAACsC,OAAO,EAAE;MAC1B,MAAMA,OAAO,GAAG,IAAIN,IAAI,CAAChC,gBAAgB,CAACsC,OAAO,CAAC;MAClD,MAAME,GAAG,GAAG,IAAIR,IAAI,CAAC,CAAC;MACtB,MAAMqB,SAAS,GAAGf,OAAO,IAAIE,GAAG;MAChChC,OAAO,CAAC2C,GAAG,CAAC,eAAe,EAAEE,SAAS,CAAC;MACvC7C,OAAO,CAAC2C,GAAG,CAAC,mBAAmB,EAAER,IAAI,CAACC,IAAI,CAAC,CAACN,OAAO,GAAGE,GAAG,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACxF;EACJ;;EAEA;EACA,IAAItD,OAAO,EAAE;IACT,oBACIV,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC5B/E,OAAA;QAAK8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5B/E,OAAA;UAAK8E,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCnF,OAAA;UAAA+E,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BnF,OAAA;UAAA+E,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;;EAEA;EACA,IAAIvE,KAAK,EAAE;IACP,oBACIZ,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC5B/E,OAAA;QAAK8E,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B/E,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpCnF,OAAA;UAAA+E,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCnF,OAAA;UAAA+E,QAAA,EAAInE;QAAK;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdnF,OAAA;UAAQ8E,SAAS,EAAC,WAAW;UAACM,OAAO,EAAExD,UAAW;UAAAmD,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACInF,OAAA;IAAK8E,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE5B/E,OAAA;MAAK8E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzB/E,OAAA;QAAI8E,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1DnF,OAAA;QAAG8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAE9B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACnCvF,OAAA;MAAKwF,KAAK,EAAE;QACRC,UAAU,EAAE,SAAS;QACrBC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,QAAQ;QAChBC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE;MACd,CAAE;MAAAd,QAAA,gBACE/E,OAAA;QAAA+E,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBnF,OAAA;QAAA+E,QAAA,gBAAG/E,OAAA;UAAA+E,QAAA,EAAQ;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC,CAAA7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,KAAI,eAAe;MAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DnF,OAAA;QAAA+E,QAAA,gBAAG/E,OAAA;UAAA+E,QAAA,EAAQ;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC3D,gBAAgB,GAAG,WAAW,GAAG,eAAe;MAAA;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC5F3D,gBAAgB,iBACbxB,OAAA,CAAAE,SAAA;QAAA6E,QAAA,gBACI/E,OAAA;UAAA+E,QAAA,gBAAG/E,OAAA;YAAA+E,QAAA,EAAQ;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC3D,gBAAgB,CAAC+C,aAAa;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEnF,OAAA;UAAA+E,QAAA,gBAAG/E,OAAA;YAAA+E,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC3D,gBAAgB,CAACgD,MAAM;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDnF,OAAA;UAAA+E,QAAA,gBAAG/E,OAAA;YAAA+E,QAAA,EAAQ;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC3D,gBAAgB,CAACiD,SAAS;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEnF,OAAA;UAAA+E,QAAA,gBAAG/E,OAAA;YAAA+E,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC3D,gBAAgB,CAACsC,OAAO;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5DnF,OAAA;UAAA+E,QAAA,gBAAG/E,OAAA;YAAA+E,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC,EAAA9E,sBAAA,GAAAmB,gBAAgB,CAACU,IAAI,cAAA7B,sBAAA,uBAArBA,sBAAA,CAAuBsC,KAAK,KAAI,SAAS;QAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,eAC3E,CACL,eACDnF,OAAA;QAAA+E,QAAA,gBAAG/E,OAAA;UAAA+E,QAAA,EAAQ;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACT,kBAAkB,GAAG,KAAK,GAAG,IAAI;MAAA;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CACR,EAGAb,oBAAoB,iBACjBtE,OAAA;MAAK8E,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACjC/E,OAAA;QAAK8E,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChC/E,OAAA;UAAK8E,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAChC/E,OAAA;YAAM8E,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,uBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnF,OAAA;UAAI8E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC7B,CAAAvD,gBAAgB,aAAhBA,gBAAgB,wBAAAlB,sBAAA,GAAhBkB,gBAAgB,CAAEU,IAAI,cAAA5B,sBAAA,uBAAtBA,sBAAA,CAAwBqC,KAAK,KAAI;QAAc;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjC/E,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/E,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCnF,OAAA;YAAK8E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CnF,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EACzBzB,UAAU,CAAC9B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiD,SAAS;YAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/E,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpCnF,OAAA;YAAK8E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CnF,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EACzBzB,UAAU,CAAC9B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsC,OAAO;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/E,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCnF,OAAA;YAAK8E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpDnF,OAAA;cAAM8E,SAAS,EAAC,wBAAwB;cAAAC,QAAA,GACnClB,gBAAgB,CAACrC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsC,OAAO,CAAC,EAAC,OACjD;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENnF,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB/E,OAAA;YAAK8E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCnF,OAAA;YAAK8E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/CnF,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EACzB,CAAAvD,gBAAgB,aAAhBA,gBAAgB,wBAAAjB,sBAAA,GAAhBiB,gBAAgB,CAAEU,IAAI,cAAA3B,sBAAA,uBAAtBA,sBAAA,CAAwBoC,KAAK,KAAI;YAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAClC/E,OAAA;UAAA+E,QAAA,EAAI;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BnF,OAAA;UAAK8E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B/E,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCnF,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eACNnF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCnF,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNnF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCnF,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNnF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB/E,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCnF,OAAA;cAAM8E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENnF,OAAA;QAAK8E,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjC/E,OAAA;UACI8E,SAAS,EAAC,oBAAoB;UAC9BM,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,WAAW,CAAE;UAAAoD,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnF,OAAA;UACI8E,SAAS,EAAC,sBAAsB;UAChCM,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,eAAe,CAAE;UAAAoD,QAAA,EAC5C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGA,CAACb,oBAAoB,iBAClBtE,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5B/E,OAAA;QAAI8E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEjD3E,KAAK,CAACsF,MAAM,KAAK,CAAC,gBACf9F,OAAA;QAAK8E,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrB/E,OAAA;UAAK8E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvCnF,OAAA;UAAA+E,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnF,OAAA;UAAA+E,QAAA,EAAG;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,gBAENnF,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,EACtBvE,KAAK,CAACuF,GAAG,CAAE7D,IAAI;UAAA,IAAA8D,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,YAAA;UAAA,oBACZnG,OAAA;YAAoB8E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACrC/E,OAAA;cAAK8E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB/E,OAAA;gBAAI8E,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAE7C,IAAI,CAACS;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAC1C,EAAAa,WAAA,GAAA9D,IAAI,CAACS,KAAK,cAAAqD,WAAA,uBAAVA,WAAA,CAAY9C,WAAW,CAAC,CAAC,CAACkD,QAAQ,CAAC,OAAO,CAAC,kBACxCpG,OAAA;gBAAK8E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAClD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAENnF,OAAA;cAAK8E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB/E,OAAA;gBAAK8E,SAAS,EAAC,YAAY;gBAAAC,QAAA,GACtB,EAAAkB,qBAAA,GAAA/D,IAAI,CAACO,eAAe,cAAAwD,qBAAA,uBAApBA,qBAAA,CAAsBI,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,MACnD;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACLjD,IAAI,CAACoE,WAAW,IAAIpE,IAAI,CAACoE,WAAW,KAAKpE,IAAI,CAACO,eAAe,iBAC1DzC,OAAA;gBAAK8E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC1B7C,IAAI,CAACoE,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MACvC;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACR,eACDnF,OAAA;gBAAK8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EACxB7C,IAAI,CAACqE,QAAQ,GAAI,GAAErE,IAAI,CAACqE,QAAS,SAAQrE,IAAI,CAACqE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG,EAAC,GAAG;cAAU;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENnF,OAAA;cAAK8E,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzB,EAAAmB,cAAA,GAAAhE,IAAI,CAACsE,QAAQ,cAAAN,cAAA,uBAAbA,cAAA,CAAeH,GAAG,CAAC,CAACU,OAAO,EAAEC,KAAK,kBAC/B1G,OAAA;gBAAiB8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACrC/E,OAAA;kBAAM8E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCnF,OAAA;kBAAM8E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAE0B;gBAAO;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFzCuB,KAAK;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACR,CAAC,kBACEnF,OAAA;gBAAK8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzB/E,OAAA;kBAAM8E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxCnF,OAAA;kBAAM8E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAENnF,OAAA;cACI8E,SAAS,EAAG,YAAWhE,cAAc,IAAIE,cAAc,KAAKkB,IAAI,CAACG,GAAG,GAAG,SAAS,GAAG,EAAG,EAAE;cACxF+C,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAACC,IAAI,CAAE;cACtCyE,QAAQ,EAAE7F,cAAe;cAAAiE,QAAA,EAExBjE,cAAc,IAAIE,cAAc,KAAKkB,IAAI,CAACG,GAAG,gBAC1CrC,OAAA,CAAAE,SAAA;gBAAA6E,QAAA,gBACI/E,OAAA;kBAAM8E,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEzC;cAAA,eAAE,CAAC,GAEH,CAAAgB,YAAA,GAAAjE,IAAI,CAACS,KAAK,cAAAwD,YAAA,eAAVA,YAAA,CAAYjD,WAAW,CAAC,CAAC,CAACkD,QAAQ,CAAC,OAAO,CAAC,GAAG,gBAAgB,GAAG;YACpE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAjDHjD,IAAI,CAACG,GAAG;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkDb,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,EAGAjE,gBAAgB,iBACblB,OAAA;MAAK8E,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAClC/E,OAAA;QAAK8E,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1B/E,OAAA;UAAK8E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5B/E,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,eACzB/E,OAAA;cAAK8E,SAAS,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACNnF,OAAA;YAAA+E,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BnF,OAAA;YAAA+E,QAAA,EAAG;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnDnF,OAAA;YAAK8E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B/E,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7CnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtDnF,OAAA;cAAK8E,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGA/D,cAAc,iBACXpB,OAAA;MAAK8E,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eAClC/E,OAAA;QAAK8E,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1B/E,OAAA;UAAK8E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5B/E,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCnF,OAAA;YAAA+E,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BnF,OAAA;YAAA+E,QAAA,EAAG;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3CnF,OAAA;YACI8E,SAAS,EAAC,aAAa;YACvBM,OAAO,EAAEA,CAAA,KAAM;cACX/D,iBAAiB,CAAC,KAAK,CAAC;cACxBuF,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;YAC5B,CAAE;YAAA/B,QAAA,EACL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC/E,EAAA,CA1aID,KAAK;EAAA,QASUZ,WAAW,EACCA,WAAW,EACvBC,WAAW,EACXC,WAAW;AAAA;AAAAsH,EAAA,GAZ1B5G,KAAK;AA4aX,eAAeA,KAAK;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}