.subscription-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 1rem;
}

.subscription-container {
  max-width: 1200px;
  margin: 0 auto;
}

.subscription-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.title-icon {
  color: #f59e0b;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  text-align: center;
}

.section-subtitle {
  font-size: 1rem;
  color: #6b7280;
  text-align: center;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Current Subscription */
.current-subscription {
  margin-bottom: 3rem;
}

.subscription-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.subscription-card.active {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
}

.subscription-card.expired {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2, #fef7f7);
}

.subscription-card.none {
  border-color: #6b7280;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
}

.subscription-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.status-icon {
  font-size: 1.5rem;
}

.status-icon.active {
  color: #10b981;
}

.status-icon.expired {
  color: #ef4444;
}

.status-icon.none {
  color: #6b7280;
}

.status-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.subscription-details {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  color: #374151;
}

.detail-icon {
  color: #6b7280;
  font-size: 1rem;
}

.renewal-message,
.upgrade-message {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 0.5rem;
  color: #1e40af;
  font-weight: 500;
}

/* Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.plan-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.plan-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.plan-header {
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.plan-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.plan-badge {
  position: absolute;
  top: -1rem;
  right: -1rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  transform: rotate(15deg);
}

.plan-pricing {
  text-align: center;
  margin-bottom: 2rem;
}

.price-display {
  margin-bottom: 0.5rem;
}

.current-price {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.original-price {
  font-size: 1.2rem;
  color: #9ca3af;
  text-decoration: line-through;
  margin-left: 0.5rem;
}

.plan-duration {
  color: #6b7280;
  font-weight: 500;
}

.plan-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  color: #374151;
}

.feature-icon {
  color: #10b981;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.select-plan-btn {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.select-plan-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
}

.select-plan-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-icon {
  font-size: 0.9rem;
}

/* Phone Warning */
.phone-warning {
  margin-top: 2rem;
}

.warning-content {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 2px solid #f59e0b;
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.warning-icon {
  color: #d97706;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.warning-content h4 {
  margin: 0 0 0.5rem 0;
  color: #92400e;
  font-weight: 600;
}

.warning-content p {
  margin: 0 0 1rem 0;
  color: #a16207;
}

.update-phone-btn {
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.update-phone-btn:hover {
  background: #d97706;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

/* No Plans State */
.no-plans-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.no-plans-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-plans-state h3 {
  color: #374151;
  margin-bottom: 1rem;
}

.no-plans-state p {
  margin-bottom: 2rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.refresh-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.refresh-btn:hover {
  background: #2563eb;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Payment Modal Styles */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.payment-modal {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.payment-modal.success-modal {
  max-width: 600px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.payment-modal-content {
  text-align: center;
}

/* Payment Processing Animation */
.payment-processing-animation {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 0 auto 2rem;
}

.payment-spinner {
  width: 80px;
  height: 80px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 10px;
  left: 10px;
}

.payment-pulse {
  width: 100px;
  height: 100px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
  position: absolute;
  top: 0;
  left: 0;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

.payment-modal h3 {
  color: #1f2937;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.payment-status {
  color: #6b7280;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.payment-plan-info {
  background: #f8fafc;
  border-radius: 10px;
  padding: 1rem;
  margin: 1rem 0;
}

.payment-plan-info h4 {
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.payment-plan-info p {
  color: #6b7280;
  margin: 0.25rem 0;
}

.payment-instructions {
  background: #e0f2fe;
  border-radius: 10px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.payment-instructions p {
  margin: 0.5rem 0;
  color: #0c4a6e;
  font-size: 0.9rem;
}

/* Success Modal Styles */
.success-animation {
  position: relative;
  margin-bottom: 2rem;
}

.success-checkmark {
  font-size: 4rem;
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-confetti {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  border-radius: 50%;
  animation: confetti 1s ease-out;
  opacity: 0;
}

@keyframes confetti {
  0% {
    transform: translateX(-50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scale(1.5);
    opacity: 0.8;
  }
  100% {
    transform: translateX(-50%) scale(0);
    opacity: 0;
  }
}

.success-message {
  color: #059669;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  font-weight: 500;
}

.success-plan-info {
  background: #ecfdf5;
  border: 1px solid #10b981;
  border-radius: 10px;
  padding: 1rem;
  margin: 1rem 0;
}

.success-plan-info h4 {
  color: #065f46;
  margin-bottom: 0.5rem;
}

.success-plan-info p {
  color: #047857;
  margin: 0.25rem 0;
}

.success-features {
  text-align: left;
  background: #f8fafc;
  border-radius: 10px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.success-features h4 {
  color: #1f2937;
  margin-bottom: 1rem;
  text-align: center;
}

.success-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.success-features li {
  color: #374151;
  margin: 0.5rem 0;
  font-size: 0.95rem;
}

.success-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.success-btn {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 120px;
}

.success-btn.primary {
  background: #3b82f6;
  color: white;
}

.success-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.success-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.success-btn.secondary:hover {
  background: #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-modal {
    padding: 1.5rem;
    margin: 1rem;
  }

  .success-actions {
    flex-direction: column;
  }

  .success-btn {
    width: 100%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .subscription-page {
    padding: 1rem 0.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .plan-card {
    padding: 1.5rem;
  }

  .warning-content {
    flex-direction: column;
    text-align: center;
  }
}
