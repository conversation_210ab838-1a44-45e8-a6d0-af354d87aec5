{"ast": null, "code": "import React,{useState,useEffect}from\"react\";import{useLocation,useNavigate}from\"react-router-dom\";import{motion}from\"framer-motion\";import TrialQuizSelection from\"../../components/trial/TrialQuizSelection\";import TrialQuizPlay from\"../../components/trial/TrialQuizPlay\";import TrialQuizResult from\"../../components/trial/TrialQuizResult\";import TrialRegistrationPrompt from\"../../components/trial/TrialRegistrationPrompt\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const TrialPage=()=>{const location=useLocation();const navigate=useNavigate();const[currentStep,setCurrentStep]=useState(\"selection\");// selection, playing, result\nconst[trialUserInfo,setTrialUserInfo]=useState(null);const[selectedQuiz,setSelectedQuiz]=useState(null);const[quizResult,setQuizResult]=useState(null);const[showRegistrationPrompt,setShowRegistrationPrompt]=useState(false);// Get trial user info from navigation state\nuseEffect(()=>{var _location$state;if((_location$state=location.state)!==null&&_location$state!==void 0&&_location$state.trialUserInfo){setTrialUserInfo(location.state.trialUserInfo);}else{// If no trial user info, redirect to home\nnavigate('/');}},[location.state,navigate]);// Handle quiz selection\nconst handleQuizSelected=quizData=>{setSelectedQuiz(quizData);setCurrentStep(\"playing\");};// Handle quiz completion\nconst handleQuizComplete=result=>{setQuizResult(result);setCurrentStep(\"result\");// Show registration prompt after a short delay\nsetTimeout(()=>{setShowRegistrationPrompt(true);},3000);};// Handle back navigation\nconst handleBack=()=>{if(currentStep===\"playing\"){setCurrentStep(\"selection\");setSelectedQuiz(null);}else if(currentStep===\"selection\"){navigate('/');}};// Handle try another quiz\nconst handleTryAnother=()=>{setCurrentStep(\"selection\");setSelectedQuiz(null);setQuizResult(null);setShowRegistrationPrompt(false);};// Handle registration\nconst handleRegister=()=>{navigate('/register',{state:{trialCompleted:true,trialResult:quizResult,trialUserInfo:trialUserInfo}});};if(!trialUserInfo){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Loading trial experience...\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"trial-page\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-2 sm:top-4 left-1/2 transform -translate-x-1/2 z-40 px-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white/90 backdrop-blur-sm rounded-full px-3 sm:px-6 py-2 shadow-lg border max-w-sm sm:max-w-none overflow-hidden\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 sm:space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 sm:space-x-2 \".concat(currentStep===\"selection\"?\"text-blue-600\":\"text-gray-400\"),children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 sm:w-3 sm:h-3 rounded-full \".concat(currentStep===\"selection\"?\"bg-blue-600\":\"bg-gray-300\")}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs sm:text-sm font-medium hidden sm:inline\",children:\"Select Quiz\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs sm:text-sm font-medium sm:hidden\",children:\"Select\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-4 sm:w-8 h-px bg-gray-300\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 sm:space-x-2 \".concat(currentStep===\"playing\"?\"text-blue-600\":\"text-gray-400\"),children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 sm:w-3 sm:h-3 rounded-full \".concat(currentStep===\"playing\"?\"bg-blue-600\":\"bg-gray-300\")}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs sm:text-sm font-medium hidden sm:inline\",children:\"Take Quiz\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs sm:text-sm font-medium sm:hidden\",children:\"Quiz\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-4 sm:w-8 h-px bg-gray-300\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 sm:space-x-2 \".concat(currentStep===\"result\"?\"text-blue-600\":\"text-gray-400\"),children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 sm:w-3 sm:h-3 rounded-full \".concat(currentStep===\"result\"?\"bg-blue-600\":\"bg-gray-300\")}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs sm:text-sm font-medium\",children:\"Results\"})]})]})})}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:0.3},children:[currentStep===\"selection\"&&/*#__PURE__*/_jsx(TrialQuizSelection,{trialUserInfo:trialUserInfo,onQuizSelected:handleQuizSelected,onBack:handleBack}),currentStep===\"playing\"&&selectedQuiz&&/*#__PURE__*/_jsx(TrialQuizPlay,{quizData:selectedQuiz,onComplete:handleQuizComplete,onBack:handleBack}),currentStep===\"result\"&&quizResult&&/*#__PURE__*/_jsx(TrialQuizResult,{result:quizResult,onTryAnother:handleTryAnother,onRegister:handleRegister})]},currentStep),/*#__PURE__*/_jsx(TrialRegistrationPrompt,{isOpen:showRegistrationPrompt,onClose:()=>setShowRegistrationPrompt(false),trialResult:quizResult})]});};export default TrialPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "useNavigate", "motion", "TrialQuizSelection", "TrialQuizPlay", "TrialQuizResult", "TrialRegistrationPrompt", "jsx", "_jsx", "jsxs", "_jsxs", "TrialPage", "location", "navigate", "currentStep", "setCurrentStep", "trialUserInfo", "setTrialUserInfo", "selectedQuiz", "setSelectedQuiz", "quizResult", "setQuizResult", "showRegistrationPrompt", "setShowRegistrationPrompt", "_location$state", "state", "handleQuizSelected", "quizData", "handleQuizComplete", "result", "setTimeout", "handleBack", "handleTryAnother", "handleRegister", "trialCompleted", "trialResult", "className", "children", "concat", "div", "initial", "opacity", "x", "animate", "exit", "transition", "duration", "onQuizSelected", "onBack", "onComplete", "onTryAnother", "onRegister", "isOpen", "onClose"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/trial/TrialPage.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport TrialQuizSelection from \"../../components/trial/TrialQuizSelection\";\nimport TrialQuizPlay from \"../../components/trial/TrialQuizPlay\";\nimport TrialQuizResult from \"../../components/trial/TrialQuizResult\";\nimport TrialRegistrationPrompt from \"../../components/trial/TrialRegistrationPrompt\";\n\nconst TrialPage = () => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  \n  const [currentStep, setCurrentStep] = useState(\"selection\"); // selection, playing, result\n  const [trialUserInfo, setTrialUserInfo] = useState(null);\n  const [selectedQuiz, setSelectedQuiz] = useState(null);\n  const [quizResult, setQuizResult] = useState(null);\n  const [showRegistrationPrompt, setShowRegistrationPrompt] = useState(false);\n\n  // Get trial user info from navigation state\n  useEffect(() => {\n    if (location.state?.trialUserInfo) {\n      setTrialUserInfo(location.state.trialUserInfo);\n    } else {\n      // If no trial user info, redirect to home\n      navigate('/');\n    }\n  }, [location.state, navigate]);\n\n  // Handle quiz selection\n  const handleQuizSelected = (quizData) => {\n    setSelectedQuiz(quizData);\n    setCurrentStep(\"playing\");\n  };\n\n  // Handle quiz completion\n  const handleQuizComplete = (result) => {\n    setQuizResult(result);\n    setCurrentStep(\"result\");\n    \n    // Show registration prompt after a short delay\n    setTimeout(() => {\n      setShowRegistrationPrompt(true);\n    }, 3000);\n  };\n\n  // Handle back navigation\n  const handleBack = () => {\n    if (currentStep === \"playing\") {\n      setCurrentStep(\"selection\");\n      setSelectedQuiz(null);\n    } else if (currentStep === \"selection\") {\n      navigate('/');\n    }\n  };\n\n  // Handle try another quiz\n  const handleTryAnother = () => {\n    setCurrentStep(\"selection\");\n    setSelectedQuiz(null);\n    setQuizResult(null);\n    setShowRegistrationPrompt(false);\n  };\n\n  // Handle registration\n  const handleRegister = () => {\n    navigate('/register', { \n      state: { \n        trialCompleted: true,\n        trialResult: quizResult,\n        trialUserInfo: trialUserInfo\n      }\n    });\n  };\n\n  if (!trialUserInfo) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\n            <div className=\"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\"></div>\n          </div>\n          <p className=\"text-gray-600\">Loading trial experience...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"trial-page\">\n      {/* Step Indicator */}\n      <div className=\"fixed top-2 sm:top-4 left-1/2 transform -translate-x-1/2 z-40 px-4\">\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-full px-3 sm:px-6 py-2 shadow-lg border max-w-sm sm:max-w-none overflow-hidden\">\n          <div className=\"flex items-center space-x-2 sm:space-x-4\">\n            <div className={`flex items-center space-x-1 sm:space-x-2 ${\n              currentStep === \"selection\" ? \"text-blue-600\" : \"text-gray-400\"\n            }`}>\n              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${\n                currentStep === \"selection\" ? \"bg-blue-600\" : \"bg-gray-300\"\n              }`}></div>\n              <span className=\"text-xs sm:text-sm font-medium hidden sm:inline\">Select Quiz</span>\n              <span className=\"text-xs sm:text-sm font-medium sm:hidden\">Select</span>\n            </div>\n\n            <div className=\"w-4 sm:w-8 h-px bg-gray-300\"></div>\n\n            <div className={`flex items-center space-x-1 sm:space-x-2 ${\n              currentStep === \"playing\" ? \"text-blue-600\" : \"text-gray-400\"\n            }`}>\n              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${\n                currentStep === \"playing\" ? \"bg-blue-600\" : \"bg-gray-300\"\n              }`}></div>\n              <span className=\"text-xs sm:text-sm font-medium hidden sm:inline\">Take Quiz</span>\n              <span className=\"text-xs sm:text-sm font-medium sm:hidden\">Quiz</span>\n            </div>\n\n            <div className=\"w-4 sm:w-8 h-px bg-gray-300\"></div>\n\n            <div className={`flex items-center space-x-1 sm:space-x-2 ${\n              currentStep === \"result\" ? \"text-blue-600\" : \"text-gray-400\"\n            }`}>\n              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${\n                currentStep === \"result\" ? \"bg-blue-600\" : \"bg-gray-300\"\n              }`}></div>\n              <span className=\"text-xs sm:text-sm font-medium\">Results</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <motion.div\n        key={currentStep}\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        exit={{ opacity: 0, x: -20 }}\n        transition={{ duration: 0.3 }}\n      >\n        {currentStep === \"selection\" && (\n          <TrialQuizSelection\n            trialUserInfo={trialUserInfo}\n            onQuizSelected={handleQuizSelected}\n            onBack={handleBack}\n          />\n        )}\n\n        {currentStep === \"playing\" && selectedQuiz && (\n          <TrialQuizPlay\n            quizData={selectedQuiz}\n            onComplete={handleQuizComplete}\n            onBack={handleBack}\n          />\n        )}\n\n        {currentStep === \"result\" && quizResult && (\n          <TrialQuizResult\n            result={quizResult}\n            onTryAnother={handleTryAnother}\n            onRegister={handleRegister}\n          />\n        )}\n      </motion.div>\n\n      {/* Registration Prompt Modal */}\n      <TrialRegistrationPrompt\n        isOpen={showRegistrationPrompt}\n        onClose={() => setShowRegistrationPrompt(false)}\n        trialResult={quizResult}\n      />\n    </div>\n  );\n};\n\nexport default TrialPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OAASC,MAAM,KAAQ,eAAe,CACtC,MAAO,CAAAC,kBAAkB,KAAM,2CAA2C,CAC1E,MAAO,CAAAC,aAAa,KAAM,sCAAsC,CAChE,MAAO,CAAAC,eAAe,KAAM,wCAAwC,CACpE,MAAO,CAAAC,uBAAuB,KAAM,gDAAgD,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAErF,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAa,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACa,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,WAAW,CAAC,CAAE;AAC7D,KAAM,CAACkB,aAAa,CAAEC,gBAAgB,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACoB,YAAY,CAAEC,eAAe,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACwB,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAE3E;AACAC,SAAS,CAAC,IAAM,KAAAyB,eAAA,CACd,IAAAA,eAAA,CAAIZ,QAAQ,CAACa,KAAK,UAAAD,eAAA,WAAdA,eAAA,CAAgBR,aAAa,CAAE,CACjCC,gBAAgB,CAACL,QAAQ,CAACa,KAAK,CAACT,aAAa,CAAC,CAChD,CAAC,IAAM,CACL;AACAH,QAAQ,CAAC,GAAG,CAAC,CACf,CACF,CAAC,CAAE,CAACD,QAAQ,CAACa,KAAK,CAAEZ,QAAQ,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAa,kBAAkB,CAAIC,QAAQ,EAAK,CACvCR,eAAe,CAACQ,QAAQ,CAAC,CACzBZ,cAAc,CAAC,SAAS,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAa,kBAAkB,CAAIC,MAAM,EAAK,CACrCR,aAAa,CAACQ,MAAM,CAAC,CACrBd,cAAc,CAAC,QAAQ,CAAC,CAExB;AACAe,UAAU,CAAC,IAAM,CACfP,yBAAyB,CAAC,IAAI,CAAC,CACjC,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA,KAAM,CAAAQ,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAIjB,WAAW,GAAK,SAAS,CAAE,CAC7BC,cAAc,CAAC,WAAW,CAAC,CAC3BI,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,IAAM,IAAIL,WAAW,GAAK,WAAW,CAAE,CACtCD,QAAQ,CAAC,GAAG,CAAC,CACf,CACF,CAAC,CAED;AACA,KAAM,CAAAmB,gBAAgB,CAAGA,CAAA,GAAM,CAC7BjB,cAAc,CAAC,WAAW,CAAC,CAC3BI,eAAe,CAAC,IAAI,CAAC,CACrBE,aAAa,CAAC,IAAI,CAAC,CACnBE,yBAAyB,CAAC,KAAK,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAU,cAAc,CAAGA,CAAA,GAAM,CAC3BpB,QAAQ,CAAC,WAAW,CAAE,CACpBY,KAAK,CAAE,CACLS,cAAc,CAAE,IAAI,CACpBC,WAAW,CAAEf,UAAU,CACvBJ,aAAa,CAAEA,aACjB,CACF,CAAC,CAAC,CACJ,CAAC,CAED,GAAI,CAACA,aAAa,CAAE,CAClB,mBACER,IAAA,QAAK4B,SAAS,CAAC,mGAAmG,CAAAC,QAAA,cAChH3B,KAAA,QAAK0B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7B,IAAA,QAAK4B,SAAS,CAAC,kFAAkF,CAAAC,QAAA,cAC/F7B,IAAA,QAAK4B,SAAS,CAAC,iFAAiF,CAAM,CAAC,CACpG,CAAC,cACN5B,IAAA,MAAG4B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,6BAA2B,CAAG,CAAC,EACzD,CAAC,CACH,CAAC,CAEV,CAEA,mBACE3B,KAAA,QAAK0B,SAAS,CAAC,YAAY,CAAAC,QAAA,eAEzB7B,IAAA,QAAK4B,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjF7B,IAAA,QAAK4B,SAAS,CAAC,qHAAqH,CAAAC,QAAA,cAClI3B,KAAA,QAAK0B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvD3B,KAAA,QAAK0B,SAAS,6CAAAE,MAAA,CACZxB,WAAW,GAAK,WAAW,CAAG,eAAe,CAAG,eAAe,CAC9D,CAAAuB,QAAA,eACD7B,IAAA,QAAK4B,SAAS,uCAAAE,MAAA,CACZxB,WAAW,GAAK,WAAW,CAAG,aAAa,CAAG,aAAa,CAC1D,CAAM,CAAC,cACVN,IAAA,SAAM4B,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,cACpF7B,IAAA,SAAM4B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,EACrE,CAAC,cAEN7B,IAAA,QAAK4B,SAAS,CAAC,6BAA6B,CAAM,CAAC,cAEnD1B,KAAA,QAAK0B,SAAS,6CAAAE,MAAA,CACZxB,WAAW,GAAK,SAAS,CAAG,eAAe,CAAG,eAAe,CAC5D,CAAAuB,QAAA,eACD7B,IAAA,QAAK4B,SAAS,uCAAAE,MAAA,CACZxB,WAAW,GAAK,SAAS,CAAG,aAAa,CAAG,aAAa,CACxD,CAAM,CAAC,cACVN,IAAA,SAAM4B,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,cAClF7B,IAAA,SAAM4B,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,EACnE,CAAC,cAEN7B,IAAA,QAAK4B,SAAS,CAAC,6BAA6B,CAAM,CAAC,cAEnD1B,KAAA,QAAK0B,SAAS,6CAAAE,MAAA,CACZxB,WAAW,GAAK,QAAQ,CAAG,eAAe,CAAG,eAAe,CAC3D,CAAAuB,QAAA,eACD7B,IAAA,QAAK4B,SAAS,uCAAAE,MAAA,CACZxB,WAAW,GAAK,QAAQ,CAAG,aAAa,CAAG,aAAa,CACvD,CAAM,CAAC,cACVN,IAAA,SAAM4B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,EAC5D,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAGN3B,KAAA,CAACR,MAAM,CAACqC,GAAG,EAETC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,IAAI,CAAE,CAAEH,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BG,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAT,QAAA,EAE7BvB,WAAW,GAAK,WAAW,eAC1BN,IAAA,CAACL,kBAAkB,EACjBa,aAAa,CAAEA,aAAc,CAC7B+B,cAAc,CAAErB,kBAAmB,CACnCsB,MAAM,CAAEjB,UAAW,CACpB,CACF,CAEAjB,WAAW,GAAK,SAAS,EAAII,YAAY,eACxCV,IAAA,CAACJ,aAAa,EACZuB,QAAQ,CAAET,YAAa,CACvB+B,UAAU,CAAErB,kBAAmB,CAC/BoB,MAAM,CAAEjB,UAAW,CACpB,CACF,CAEAjB,WAAW,GAAK,QAAQ,EAAIM,UAAU,eACrCZ,IAAA,CAACH,eAAe,EACdwB,MAAM,CAAET,UAAW,CACnB8B,YAAY,CAAElB,gBAAiB,CAC/BmB,UAAU,CAAElB,cAAe,CAC5B,CACF,GA5BInB,WA6BK,CAAC,cAGbN,IAAA,CAACF,uBAAuB,EACtB8C,MAAM,CAAE9B,sBAAuB,CAC/B+B,OAAO,CAAEA,CAAA,GAAM9B,yBAAyB,CAAC,KAAK,CAAE,CAChDY,WAAW,CAAEf,UAAW,CACzB,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}