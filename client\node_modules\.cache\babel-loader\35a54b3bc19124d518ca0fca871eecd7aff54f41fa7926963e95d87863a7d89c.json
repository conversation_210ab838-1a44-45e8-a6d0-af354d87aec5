{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Plans\\\\Plans.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport \"./Plans.css\";\nimport ConfirmModal from \"./components/ConfirmModal\";\nimport WaitingModal from \"./components/WaitingModal\";\nimport { addPayment } from \"../../../apicalls/payment\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { message } from \"antd\";\nimport { useNavigate } from \"react-router-dom\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Plans = () => {\n  _s();\n  const [plans, setPlans] = useState([]);\n  const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\n  const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\n  const [paymentInProgress, setPaymentInProgress] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  useEffect(() => {\n    const fetchPlans = async () => {\n      try {\n        const response = await getPlans();\n        setPlans(response);\n      } catch (error) {\n        console.error(\"Error fetching plans:\", error);\n      }\n    };\n    fetchPlans();\n  }, []);\n  const transactionDetails = {\n    amount: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.discountedPrice) || 'N/A',\n    currency: \"TZS\",\n    destination: \"brainwave.zone\"\n  };\n  const handlePaymentStart = async plan => {\n    setSelectedPlan(plan);\n    try {\n      dispatch(ShowLoading());\n      console.log('💳 Initiating payment for plan:', plan.title);\n      const response = await addPayment({\n        plan\n      });\n      console.log('📥 Payment response:', response);\n      if (response.success) {\n        localStorage.setItem(\"order_id\", response.order_id);\n        setWaitingModalOpen(true);\n        setPaymentInProgress(true);\n        dispatch(setPaymentVerificationNeeded(true));\n\n        // Show success message - confidential payment processing\n        message.success({\n          content: `🎉 Payment request initiated successfully! Please check your phone for SMS confirmation to complete the payment.`,\n          duration: 6,\n          style: {\n            marginTop: '20px',\n            fontSize: '16px'\n          }\n        });\n      } else {\n        message.error(response.message || \"Payment initiation failed. Please try again.\");\n      }\n    } catch (error) {\n      console.error(\"❌ Error processing payment:\", error);\n      message.error(\"Unable to process payment. Please try again.\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    console.log(\"subscription Data in Plans\", subscriptionData);\n    if ((user === null || user === void 0 ? void 0 : user.paymentRequired) === true && (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) === \"paid\" && paymentInProgress) {\n      setWaitingModalOpen(false);\n      setConfirmModalOpen(true);\n      setPaymentInProgress(false);\n    }\n  }, [user, subscriptionData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [!user ? /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false) : !user.paymentRequired ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-plan-required\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-plan-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"No Plan Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 21\n    }, this) : (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-container\",\n      children: plans.sort((a, b) => {\n        // Sort order: Glimp Plan first, then Basic Membership, then others\n        if (a.title === \"Glimp Plan\") return -1;\n        if (b.title === \"Glimp Plan\") return 1;\n        if (a.title === \"Basic Membership\") return -1;\n        if (b.title === \"Basic Membership\") return 1;\n        return 0;\n      }).map(plan => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `plan-card ${plan.title === \"Basic Membership\" ? \"basic\" : plan.title === \"Glimp Plan\" ? \"glimp\" : \"\"}`,\n        children: [plan.title === \"Basic Membership\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"most-popular-label\",\n          children: \"MOST POPULAR\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 41\n        }, this), plan.title === \"Glimp Plan\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"glimp-label\",\n          children: \"QUICK START\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 41\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"plan-title\",\n            children: plan.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 41\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-duration-highlight\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"duration-number\",\n              children: plan.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 45\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"duration-text\",\n              children: [\"Month\", plan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-pricing\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"plan-actual-price\",\n            children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 41\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"plan-discounted-price\",\n            children: [plan.discountedPrice.toLocaleString(), \" TZS\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 41\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"plan-discount-tag\",\n            children: [plan.discountPercentage, \"% OFF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-value\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"value-text\",\n            children: [Math.round(plan.discountedPrice / plan.duration).toLocaleString(), \" TZS/month\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"plan-button\",\n          onClick: () => handlePaymentStart(plan),\n          children: plan.title === \"Glimp Plan\" ? \"🚀 Start Quick\" : \"Choose Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 37\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"plan-features\",\n          children: plan.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"plan-feature\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"plan-feature-icon\",\n              children: \"\\u2714\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 49\n            }, this), feature]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 45\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 37\n        }, this)]\n      }, plan._id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 33\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 25\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"current-subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-plan-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-status-badge\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"status-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\",\n                fill: \"#10B981\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"m9 12 2 2 4-4\",\n                stroke: \"white\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-text\",\n            children: \"Active Subscription\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"current-plan-title\",\n          children: subscriptionData.plan.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"current-plan-subtitle\",\n          children: \"You're currently enjoying premium access\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-plan-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plan-info-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83D\\uDCC5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Start Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: new Date(subscriptionData.startDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\u23F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"End Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: new Date(subscriptionData.endDate).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83D\\uDC8E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Plan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: subscriptionData.plan.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-info-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value status-active\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"current-plan-features\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"features-title\",\n            children: \"\\u2728 Your Premium Benefits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-benefit\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83D\\uDCDA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-text\",\n                children: \"Unlimited Quiz Access\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-benefit\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-text\",\n                children: \"Progress Tracking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-benefit\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83C\\uDFC6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-text\",\n                children: \"Achievement Badges\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-benefit\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83D\\uDE80\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"benefit-text\",\n                children: \"AI Study Assistant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"current-plan-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn primary\",\n            onClick: () => window.location.href = '/user/hub',\n            children: \"Continue Learning \\uD83C\\uDF93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn secondary\",\n            onClick: () => window.location.href = '/user/profile',\n            children: \"Manage Subscription\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 25\n    }, this), /*#__PURE__*/_jsxDEV(WaitingModal, {\n      isOpen: isWaitingModalOpen,\n      onClose: () => setWaitingModalOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmModal, {\n      isOpen: isConfirmModalOpen,\n      onClose: () => setConfirmModalOpen(false),\n      transaction: transactionDetails\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 9\n  }, this);\n};\n_s(Plans, \"2p4MB4rxyXadxPAb2VoMYdg0Pik=\", false, function () {\n  return [useSelector, useSelector, useDispatch, useNavigate];\n});\n_c = Plans;\nexport default Plans;\nvar _c;\n$RefreshReg$(_c, \"Plans\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "getPlans", "ConfirmModal", "WaitingModal", "addPayment", "useDispatch", "useSelector", "setPaymentVerificationNeeded", "HideLoading", "ShowLoading", "message", "useNavigate", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "Plans", "_s", "plans", "setPlans", "isConfirmModalOpen", "setConfirmModalOpen", "isWaitingModalOpen", "setWaitingModalOpen", "paymentInProgress", "setPaymentInProgress", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "user", "state", "subscriptionData", "subscription", "dispatch", "navigate", "fetchPlans", "response", "error", "console", "transactionDetails", "amount", "discountedPrice", "currency", "destination", "handlePaymentStart", "plan", "log", "title", "success", "localStorage", "setItem", "order_id", "content", "duration", "style", "marginTop", "fontSize", "paymentRequired", "paymentStatus", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sort", "a", "b", "map", "actualPrice", "toLocaleString", "discountPercentage", "Math", "round", "onClick", "features", "feature", "index", "_id", "width", "height", "viewBox", "fill", "xmlns", "cx", "cy", "r", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "Date", "startDate", "toLocaleDateString", "endDate", "window", "location", "href", "isOpen", "onClose", "transaction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Plans/Plans.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { getPlans } from \"../../../apicalls/plans\";\r\nimport \"./Plans.css\";\r\nimport ConfirmModal from \"./components/ConfirmModal\";\r\nimport WaitingModal from \"./components/WaitingModal\";\r\nimport { addPayment } from \"../../../apicalls/payment\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { message } from \"antd\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nconst Plans = () => {\r\n    const [plans, setPlans] = useState([]);\r\n    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\r\n    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\r\n    const [paymentInProgress, setPaymentInProgress] = useState(false);\r\n    const [selectedPlan, setSelectedPlan] = useState(null);\r\n    const { user } = useSelector((state) => state.user);\r\n    const { subscriptionData } = useSelector((state) => state.subscription);\r\n    const dispatch = useDispatch();\r\n    const navigate = useNavigate();\r\n\r\n\r\n\r\n    useEffect(() => {\r\n        const fetchPlans = async () => {\r\n            try {\r\n                const response = await getPlans();\r\n                setPlans(response);\r\n            } catch (error) {\r\n                console.error(\"Error fetching plans:\", error);\r\n            }\r\n        };\r\n\r\n        fetchPlans();\r\n    }, []);\r\n\r\n    const transactionDetails = {\r\n        amount: selectedPlan?.discountedPrice || 'N/A',\r\n        currency: \"TZS\",\r\n        destination: \"brainwave.zone\",\r\n    };\r\n\r\n\r\n    const handlePaymentStart = async (plan) => {\r\n        setSelectedPlan(plan);\r\n        try {\r\n            dispatch(ShowLoading());\r\n            console.log('💳 Initiating payment for plan:', plan.title);\r\n\r\n            const response = await addPayment({ plan });\r\n            console.log('📥 Payment response:', response);\r\n\r\n            if (response.success) {\r\n                localStorage.setItem(\"order_id\", response.order_id);\r\n                setWaitingModalOpen(true);\r\n                setPaymentInProgress(true);\r\n                dispatch(setPaymentVerificationNeeded(true));\r\n\r\n                // Show success message - confidential payment processing\r\n                message.success({\r\n                    content: `🎉 Payment request initiated successfully! Please check your phone for SMS confirmation to complete the payment.`,\r\n                    duration: 6,\r\n                    style: {\r\n                        marginTop: '20px',\r\n                        fontSize: '16px'\r\n                    }\r\n                });\r\n            } else {\r\n                message.error(response.message || \"Payment initiation failed. Please try again.\");\r\n            }\r\n        } catch (error) {\r\n            console.error(\"❌ Error processing payment:\", error);\r\n            message.error(\"Unable to process payment. Please try again.\");\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        console.log(\"subscription Data in Plans\", subscriptionData)\r\n        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === \"paid\" && paymentInProgress) {\r\n            setWaitingModalOpen(false);\r\n            setConfirmModalOpen(true);\r\n            setPaymentInProgress(false);\r\n        }\r\n    }, [user, subscriptionData]);\r\n\r\n    return (\r\n        <div>\r\n            {!user ?\r\n                <>\r\n                </>\r\n                :\r\n                !user.paymentRequired ?\r\n                    <div className=\"no-plan-required\">\r\n                        <div className=\"no-plan-content\">\r\n                            <h2>No Plan Required</h2>\r\n                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>\r\n                        </div>\r\n                    </div>\r\n                    :\r\n                    subscriptionData?.paymentStatus !== \"paid\" ?\r\n                        <div className=\"plans-container\">\r\n                            {plans\r\n                                .sort((a, b) => {\r\n                                    // Sort order: Glimp Plan first, then Basic Membership, then others\r\n                                    if (a.title === \"Glimp Plan\") return -1;\r\n                                    if (b.title === \"Glimp Plan\") return 1;\r\n                                    if (a.title === \"Basic Membership\") return -1;\r\n                                    if (b.title === \"Basic Membership\") return 1;\r\n                                    return 0;\r\n                                })\r\n                                .map((plan) => (\r\n                                <div\r\n                                    key={plan._id}\r\n                                    className={`plan-card ${\r\n                                        plan.title === \"Basic Membership\" ? \"basic\" :\r\n                                        plan.title === \"Glimp Plan\" ? \"glimp\" : \"\"\r\n                                    }`}\r\n                                >\r\n                                    {plan.title === \"Basic Membership\" && (\r\n                                        <div className=\"most-popular-label\">MOST POPULAR</div>\r\n                                    )}\r\n                                    {plan.title === \"Glimp Plan\" && (\r\n                                        <div className=\"glimp-label\">QUICK START</div>\r\n                                    )}\r\n\r\n                                    <div className=\"plan-header\">\r\n                                        <h2 className=\"plan-title\">{plan.title}</h2>\r\n                                        <div className=\"plan-duration-highlight\">\r\n                                            <span className=\"duration-number\">{plan.duration}</span>\r\n                                            <span className=\"duration-text\">Month{plan.duration > 1 ? 's' : ''}</span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"plan-pricing\">\r\n                                        <p className=\"plan-actual-price\">\r\n                                            {plan.actualPrice.toLocaleString()} TZS\r\n                                        </p>\r\n                                        <p className=\"plan-discounted-price\">\r\n                                            {plan.discountedPrice.toLocaleString()} TZS\r\n                                        </p>\r\n                                        <span className=\"plan-discount-tag\">\r\n                                            {plan.discountPercentage}% OFF\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    <div className=\"plan-value\">\r\n                                        <span className=\"value-text\">\r\n                                            {Math.round(plan.discountedPrice / plan.duration).toLocaleString()} TZS/month\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    <button className=\"plan-button\"\r\n                                        onClick={() => handlePaymentStart(plan)}\r\n                                    >\r\n                                        {plan.title === \"Glimp Plan\" ? \"🚀 Start Quick\" : \"Choose Plan\"}\r\n                                    </button>\r\n\r\n                                    <ul className=\"plan-features\">\r\n                                        {plan.features.map((feature, index) => (\r\n                                            <li key={index} className=\"plan-feature\">\r\n                                                <span className=\"plan-feature-icon\">✔</span>\r\n                                                {feature}\r\n                                            </li>\r\n                                        ))}\r\n                                    </ul>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        :\r\n                        <div className=\"current-subscription-container\">\r\n                            {/* Header Section */}\r\n                            <div className=\"current-plan-header\">\r\n                                <div className=\"plan-status-badge\">\r\n                                    <div className=\"status-icon\">\r\n                                        <svg\r\n                                            width=\"24\"\r\n                                            height=\"24\"\r\n                                            viewBox=\"0 0 24 24\"\r\n                                            fill=\"none\"\r\n                                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                                        >\r\n                                            <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#10B981\"/>\r\n                                            <path d=\"m9 12 2 2 4-4\" stroke=\"white\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                                        </svg>\r\n                                    </div>\r\n                                    <span className=\"status-text\">Active Subscription</span>\r\n                                </div>\r\n                                <h2 className=\"current-plan-title\">{subscriptionData.plan.title}</h2>\r\n                                <p className=\"current-plan-subtitle\">You're currently enjoying premium access</p>\r\n                            </div>\r\n\r\n                            {/* Plan Details Card */}\r\n                            <div className=\"current-plan-details\">\r\n                                <div className=\"plan-info-grid\">\r\n                                    <div className=\"plan-info-item\">\r\n                                        <div className=\"info-icon\">📅</div>\r\n                                        <div className=\"info-content\">\r\n                                            <span className=\"info-label\">Start Date</span>\r\n                                            <span className=\"info-value\">{new Date(subscriptionData.startDate).toLocaleDateString()}</span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"plan-info-item\">\r\n                                        <div className=\"info-icon\">⏰</div>\r\n                                        <div className=\"info-content\">\r\n                                            <span className=\"info-label\">End Date</span>\r\n                                            <span className=\"info-value\">{new Date(subscriptionData.endDate).toLocaleDateString()}</span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"plan-info-item\">\r\n                                        <div className=\"info-icon\">💎</div>\r\n                                        <div className=\"info-content\">\r\n                                            <span className=\"info-label\">Plan Type</span>\r\n                                            <span className=\"info-value\">{subscriptionData.plan.title}</span>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <div className=\"plan-info-item\">\r\n                                        <div className=\"info-icon\">🎯</div>\r\n                                        <div className=\"info-content\">\r\n                                            <span className=\"info-label\">Status</span>\r\n                                            <span className=\"info-value status-active\">Active</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Plan Features */}\r\n                                <div className=\"current-plan-features\">\r\n                                    <h3 className=\"features-title\">✨ Your Premium Benefits</h3>\r\n                                    <div className=\"features-grid\">\r\n                                        <div className=\"feature-benefit\">\r\n                                            <span className=\"benefit-icon\">📚</span>\r\n                                            <span className=\"benefit-text\">Unlimited Quiz Access</span>\r\n                                        </div>\r\n                                        <div className=\"feature-benefit\">\r\n                                            <span className=\"benefit-icon\">🎯</span>\r\n                                            <span className=\"benefit-text\">Progress Tracking</span>\r\n                                        </div>\r\n                                        <div className=\"feature-benefit\">\r\n                                            <span className=\"benefit-icon\">🏆</span>\r\n                                            <span className=\"benefit-text\">Achievement Badges</span>\r\n                                        </div>\r\n                                        <div className=\"feature-benefit\">\r\n                                            <span className=\"benefit-icon\">🚀</span>\r\n                                            <span className=\"benefit-text\">AI Study Assistant</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n\r\n                                {/* Action Buttons */}\r\n                                <div className=\"current-plan-actions\">\r\n                                    <button\r\n                                        className=\"action-btn primary\"\r\n                                        onClick={() => window.location.href = '/user/hub'}\r\n                                    >\r\n                                        Continue Learning 🎓\r\n                                    </button>\r\n                                    <button\r\n                                        className=\"action-btn secondary\"\r\n                                        onClick={() => window.location.href = '/user/profile'}\r\n                                    >\r\n                                        Manage Subscription\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n            }\r\n\r\n            <WaitingModal\r\n                isOpen={isWaitingModalOpen}\r\n                onClose={() => setWaitingModalOpen(false)}\r\n            />\r\n\r\n            <ConfirmModal\r\n                isOpen={isConfirmModalOpen}\r\n                onClose={() => setConfirmModalOpen(false)}\r\n                transaction={transactionDetails}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Plans;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,OAAO,aAAa;AACpB,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,4BAA4B,QAAQ,6BAA6B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,QAAA,IAAAC,SAAA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsB,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM;IAAE4B;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGxB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAI9BZ,SAAS,CAAC,MAAM;IACZ,MAAMmC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACA,MAAMC,QAAQ,GAAG,MAAMlC,QAAQ,CAAC,CAAC;QACjCkB,QAAQ,CAACgB,QAAQ,CAAC;MACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD;IACJ,CAAC;IAEDF,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,kBAAkB,GAAG;IACvBC,MAAM,EAAE,CAAAb,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEc,eAAe,KAAI,KAAK;IAC9CC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACjB,CAAC;EAGD,MAAMC,kBAAkB,GAAG,MAAOC,IAAI,IAAK;IACvCjB,eAAe,CAACiB,IAAI,CAAC;IACrB,IAAI;MACAZ,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvB4B,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAED,IAAI,CAACE,KAAK,CAAC;MAE1D,MAAMX,QAAQ,GAAG,MAAM/B,UAAU,CAAC;QAAEwC;MAAK,CAAC,CAAC;MAC3CP,OAAO,CAACQ,GAAG,CAAC,sBAAsB,EAAEV,QAAQ,CAAC;MAE7C,IAAIA,QAAQ,CAACY,OAAO,EAAE;QAClBC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEd,QAAQ,CAACe,QAAQ,CAAC;QACnD3B,mBAAmB,CAAC,IAAI,CAAC;QACzBE,oBAAoB,CAAC,IAAI,CAAC;QAC1BO,QAAQ,CAACzB,4BAA4B,CAAC,IAAI,CAAC,CAAC;;QAE5C;QACAG,OAAO,CAACqC,OAAO,CAAC;UACZI,OAAO,EAAG,kHAAiH;UAC3HC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YACHC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE;UACd;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACH7C,OAAO,CAAC0B,KAAK,CAACD,QAAQ,CAACzB,OAAO,IAAI,8CAA8C,CAAC;MACrF;IACJ,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD1B,OAAO,CAAC0B,KAAK,CAAC,8CAA8C,CAAC;IACjE,CAAC,SAAS;MACNJ,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAGDT,SAAS,CAAC,MAAM;IACZsC,OAAO,CAACQ,GAAG,CAAC,4BAA4B,EAAEf,gBAAgB,CAAC;IAC3D,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,eAAe,MAAK,IAAI,IAAI,CAAA1B,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B,aAAa,MAAK,MAAM,IAAIjC,iBAAiB,EAAE;MACnGD,mBAAmB,CAAC,KAAK,CAAC;MAC1BF,mBAAmB,CAAC,IAAI,CAAC;MACzBI,oBAAoB,CAAC,KAAK,CAAC;IAC/B;EACJ,CAAC,EAAE,CAACG,IAAI,EAAEE,gBAAgB,CAAC,CAAC;EAE5B,oBACIf,OAAA;IAAA2C,QAAA,GACK,CAAC9B,IAAI,gBACFb,OAAA,CAAAF,SAAA,mBACE,CAAC,GAEH,CAACe,IAAI,CAAC4B,eAAe,gBACjBzC,OAAA;MAAK4C,SAAS,EAAC,kBAAkB;MAAAD,QAAA,eAC7B3C,OAAA;QAAK4C,SAAS,EAAC,iBAAiB;QAAAD,QAAA,gBAC5B3C,OAAA;UAAA2C,QAAA,EAAI;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBhD,OAAA;UAAA2C,QAAA,EAAG;QAAoG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1G;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,GAEN,CAAAjC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B,aAAa,MAAK,MAAM,gBACtC1C,OAAA;MAAK4C,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC3BxC,KAAK,CACD8C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACZ;QACA,IAAID,CAAC,CAACnB,KAAK,KAAK,YAAY,EAAE,OAAO,CAAC,CAAC;QACvC,IAAIoB,CAAC,CAACpB,KAAK,KAAK,YAAY,EAAE,OAAO,CAAC;QACtC,IAAImB,CAAC,CAACnB,KAAK,KAAK,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAIoB,CAAC,CAACpB,KAAK,KAAK,kBAAkB,EAAE,OAAO,CAAC;QAC5C,OAAO,CAAC;MACZ,CAAC,CAAC,CACDqB,GAAG,CAAEvB,IAAI,iBACV7B,OAAA;QAEI4C,SAAS,EAAG,aACRf,IAAI,CAACE,KAAK,KAAK,kBAAkB,GAAG,OAAO,GAC3CF,IAAI,CAACE,KAAK,KAAK,YAAY,GAAG,OAAO,GAAG,EAC3C,EAAE;QAAAY,QAAA,GAEFd,IAAI,CAACE,KAAK,KAAK,kBAAkB,iBAC9B/B,OAAA;UAAK4C,SAAS,EAAC,oBAAoB;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACxD,EACAnB,IAAI,CAACE,KAAK,KAAK,YAAY,iBACxB/B,OAAA;UAAK4C,SAAS,EAAC,aAAa;UAAAD,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAChD,eAEDhD,OAAA;UAAK4C,SAAS,EAAC,aAAa;UAAAD,QAAA,gBACxB3C,OAAA;YAAI4C,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAEd,IAAI,CAACE;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5ChD,OAAA;YAAK4C,SAAS,EAAC,yBAAyB;YAAAD,QAAA,gBACpC3C,OAAA;cAAM4C,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAEd,IAAI,CAACQ;YAAQ;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxDhD,OAAA;cAAM4C,SAAS,EAAC,eAAe;cAAAD,QAAA,GAAC,OAAK,EAACd,IAAI,CAACQ,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAD,QAAA,gBACzB3C,OAAA;YAAG4C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,GAC3Bd,IAAI,CAACwB,WAAW,CAACC,cAAc,CAAC,CAAC,EAAC,MACvC;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJhD,OAAA;YAAG4C,SAAS,EAAC,uBAAuB;YAAAD,QAAA,GAC/Bd,IAAI,CAACJ,eAAe,CAAC6B,cAAc,CAAC,CAAC,EAAC,MAC3C;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJhD,OAAA;YAAM4C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,GAC9Bd,IAAI,CAAC0B,kBAAkB,EAAC,OAC7B;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhD,OAAA;UAAK4C,SAAS,EAAC,YAAY;UAAAD,QAAA,eACvB3C,OAAA;YAAM4C,SAAS,EAAC,YAAY;YAAAD,QAAA,GACvBa,IAAI,CAACC,KAAK,CAAC5B,IAAI,CAACJ,eAAe,GAAGI,IAAI,CAACQ,QAAQ,CAAC,CAACiB,cAAc,CAAC,CAAC,EAAC,YACvE;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhD,OAAA;UAAQ4C,SAAS,EAAC,aAAa;UAC3Bc,OAAO,EAAEA,CAAA,KAAM9B,kBAAkB,CAACC,IAAI,CAAE;UAAAc,QAAA,EAEvCd,IAAI,CAACE,KAAK,KAAK,YAAY,GAAG,gBAAgB,GAAG;QAAa;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAEThD,OAAA;UAAI4C,SAAS,EAAC,eAAe;UAAAD,QAAA,EACxBd,IAAI,CAAC8B,QAAQ,CAACP,GAAG,CAAC,CAACQ,OAAO,EAAEC,KAAK,kBAC9B7D,OAAA;YAAgB4C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACpC3C,OAAA;cAAM4C,SAAS,EAAC,mBAAmB;cAAAD,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC3CY,OAAO;UAAA,GAFHC,KAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,GApDAnB,IAAI,CAACiC,GAAG;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqDZ,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAENhD,OAAA;MAAK4C,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAE3C3C,OAAA;QAAK4C,SAAS,EAAC,qBAAqB;QAAAD,QAAA,gBAChC3C,OAAA;UAAK4C,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B3C,OAAA;YAAK4C,SAAS,EAAC,aAAa;YAAAD,QAAA,eACxB3C,OAAA;cACI+D,KAAK,EAAC,IAAI;cACVC,MAAM,EAAC,IAAI;cACXC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,4BAA4B;cAAAxB,QAAA,gBAElC3C,OAAA;gBAAQoE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,IAAI;gBAACJ,IAAI,EAAC;cAAS;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAC/ChD,OAAA;gBAAMuE,CAAC,EAAC,eAAe;gBAACC,MAAM,EAAC,OAAO;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC;cAAO;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNhD,OAAA;YAAM4C,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNhD,OAAA;UAAI4C,SAAS,EAAC,oBAAoB;UAAAD,QAAA,EAAE5B,gBAAgB,CAACc,IAAI,CAACE;QAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrEhD,OAAA;UAAG4C,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAC;QAAwC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eAGNhD,OAAA;QAAK4C,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBACjC3C,OAAA;UAAK4C,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC3B3C,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B3C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB3C,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9ChD,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAE,IAAIiC,IAAI,CAAC7D,gBAAgB,CAAC8D,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhD,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B3C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClChD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB3C,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5ChD,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAE,IAAIiC,IAAI,CAAC7D,gBAAgB,CAACgE,OAAO,CAAC,CAACD,kBAAkB,CAAC;cAAC;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhD,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B3C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB3C,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ChD,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAE5B,gBAAgB,CAACc,IAAI,CAACE;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhD,OAAA;YAAK4C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC3B3C,OAAA;cAAK4C,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChD,OAAA;cAAK4C,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB3C,OAAA;gBAAM4C,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1ChD,OAAA;gBAAM4C,SAAS,EAAC,0BAA0B;gBAAAD,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNhD,OAAA;UAAK4C,SAAS,EAAC,uBAAuB;UAAAD,QAAA,gBAClC3C,OAAA;YAAI4C,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DhD,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAD,QAAA,gBAC1B3C,OAAA;cAAK4C,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5B3C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxChD,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNhD,OAAA;cAAK4C,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5B3C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxChD,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNhD,OAAA;cAAK4C,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5B3C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxChD,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNhD,OAAA;cAAK4C,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5B3C,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxChD,OAAA;gBAAM4C,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNhD,OAAA;UAAK4C,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjC3C,OAAA;YACI4C,SAAS,EAAC,oBAAoB;YAC9Bc,OAAO,EAAEA,CAAA,KAAMsB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAY;YAAAvC,QAAA,EACrD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA;YACI4C,SAAS,EAAC,sBAAsB;YAChCc,OAAO,EAAEA,CAAA,KAAMsB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,eAAgB;YAAAvC,QAAA,EACzD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGlBhD,OAAA,CAACZ,YAAY;MACT+F,MAAM,EAAE5E,kBAAmB;MAC3B6E,OAAO,EAAEA,CAAA,KAAM5E,mBAAmB,CAAC,KAAK;IAAE;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC,eAEFhD,OAAA,CAACb,YAAY;MACTgG,MAAM,EAAE9E,kBAAmB;MAC3B+E,OAAO,EAAEA,CAAA,KAAM9E,mBAAmB,CAAC,KAAK,CAAE;MAC1C+E,WAAW,EAAE9D;IAAmB;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC9C,EAAA,CAlRID,KAAK;EAAA,QAMUV,WAAW,EACCA,WAAW,EACvBD,WAAW,EACXM,WAAW;AAAA;AAAA0F,EAAA,GAT1BrF,KAAK;AAoRX,eAAeA,KAAK;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}