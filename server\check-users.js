const mongoose = require('mongoose');
require('dotenv').config();

// User model (simplified)
const userSchema = new mongoose.Schema({
  name: String,
  email: String,
  password: String,
  level: String,
  class: String,
  isAdmin: { type: Boolean, default: false },
  isBlocked: { type: Boolean, default: false }
});

const User = mongoose.model('users', userSchema);

const checkUsers = async () => {
  try {
    console.log('🔍 Connecting to MongoDB...');
    
    await mongoose.connect(process.env.MONGO_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Count total users
    const totalUsers = await User.countDocuments();
    console.log(`📊 Total users in database: ${totalUsers}`);
    
    // Find lucymosha user
    console.log('\n🔍 Searching for lucymosha...');
    const lucyUsers = await User.find({
      $or: [
        { name: { $regex: 'lucy', $options: 'i' } },
        { email: { $regex: 'lucy', $options: 'i' } }
      ]
    });

    if (lucyUsers.length > 0) {
      console.log(`👤 Found ${lucyUsers.length} user(s) matching "lucy":`);
      lucyUsers.forEach((user, index) => {
        console.log(`  ${index + 1}. Name: ${user.name}`);
        console.log(`     Email: ${user.email}`);
        console.log(`     Level: ${user.level}`);
        console.log(`     Class: ${user.class}`);
        console.log(`     Is Admin: ${user.isAdmin}`);
        console.log(`     Is Blocked: ${user.isBlocked}`);
        console.log(`     Password Hash: ${user.password ? 'Present' : 'Missing'}`);
        console.log('');
      });
    } else {
      console.log('❌ No users found matching "lucy"');
    }

    // Find test user
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (testUser) {
      console.log('👤 Test user found:');
      console.log('  - Name:', testUser.name);
      console.log('  - Email:', testUser.email);
      console.log('  - Level:', testUser.level);
      console.log('  - Class:', testUser.class);
      console.log('  - Is Admin:', testUser.isAdmin);
      console.log('  - Is Blocked:', testUser.isBlocked);
      console.log('  - Password Hash:', testUser.password ? 'Present' : 'Missing');
    } else {
      console.log('❌ Test user not found');
    }
    
    // Find first few users
    const users = await User.find({}).limit(5).select('name email level class isAdmin');
    console.log('\n📋 First 5 users:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name} (${user.email}) - ${user.level}/${user.class}`);
    });
    
    // Find admin users
    const adminUsers = await User.find({ isAdmin: true }).select('name email');
    console.log('\n👑 Admin users:');
    if (adminUsers.length > 0) {
      adminUsers.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.name} (${user.email})`);
      });
    } else {
      console.log('  No admin users found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
};

checkUsers();
