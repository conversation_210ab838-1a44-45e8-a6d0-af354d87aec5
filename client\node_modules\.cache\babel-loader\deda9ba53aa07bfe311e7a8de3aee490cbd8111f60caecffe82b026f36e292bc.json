{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionModal\\\\SubscriptionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionModal = ({\n  isOpen,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  var _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n\n  // Check if user has valid phone number\n  const hasValidPhone = () => {\n    const phone = user === null || user === void 0 ? void 0 : user.phoneNumber;\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n    }\n  }, [isOpen]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = plan => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n    if (!hasValidPhone()) {\n      message.error('Please update your phone number in your profile first. Go to Profile → Edit → Phone Number');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        // Use phone number from user profile\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n          return true;\n        }\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: [step === 'plans' && '🚀 Choose Your Learning Plan', step === 'payment' && '💳 Complete Your Payment', step === 'success' && '⏳ Processing Payment...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [step === 'plans' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading plans...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 17\n          }, this) : plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$features, _plan$features2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-card\",\n              onClick: () => handlePlanSelect(plan),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-amount\",\n                  children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 23\n                }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-original\",\n                  children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-period\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: [(_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 25\n                }, this)), ((_plan$features2 = plan.features) === null || _plan$features2 === void 0 ? void 0 : _plan$features2.length) > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: [plan.features.length - 4, \" more features\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                children: [\"Choose \", plan.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), step === 'payment' && selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-plan-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Selected Plan: \", selectedPlan.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"plan-price-summary\",\n              children: [(_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS for \", selectedPlan.duration, \" month\", selectedPlan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-label\",\n                  children: \"Phone Number for Payment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), !isEditingPhone ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `info-value ${phoneUpdated ? 'updated' : ''}`,\n                    children: [paymentPhone || 'Not provided', phoneUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"updated-indicator\",\n                      children: \"\\u2705 Updated\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 44\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 25\n                  }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '8px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                      style: {\n                        color: '#666',\n                        fontSize: '10px',\n                        display: 'block'\n                      },\n                      children: [\"Debug: Payment=\", paymentPhone, \" | User=\", user === null || user === void 0 ? void 0 : user.phoneNumber, \" | Updated=\", phoneUpdated ? 'Yes' : 'No']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        console.log('🔄 Testing page refresh...');\n                        window.location.reload();\n                      },\n                      style: {\n                        fontSize: '10px',\n                        padding: '2px 6px',\n                        marginTop: '4px',\n                        background: '#f0f0f0',\n                        border: '1px solid #ccc',\n                        borderRadius: '4px',\n                        cursor: 'pointer'\n                      },\n                      children: \"\\uD83D\\uDD04 Test Refresh\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"edit-phone-btn\",\n                    onClick: () => setIsEditingPhone(true),\n                    type: \"button\",\n                    children: \"\\u270F\\uFE0F Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-edit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    value: paymentPhone,\n                    onChange: e => setPaymentPhone(e.target.value),\n                    placeholder: \"Enter phone number (e.g., 0744963858)\",\n                    className: `phone-input ${paymentPhone ? isValidPhone(paymentPhone) ? 'valid' : 'invalid' : ''}`,\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 25\n                  }, this), paymentPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`,\n                    children: isValidPhone(paymentPhone) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message valid\",\n                      children: \"\\u2705 Valid phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message invalid\",\n                      children: \"\\u274C Must start with 06 or 07 and be 10 digits\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"phone-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"save-phone-btn\",\n                      onClick: async e => {\n                        e.preventDefault();\n                        console.log('🔥 SAVE BUTTON CLICKED!');\n                        console.log('📱 Payment phone:', paymentPhone);\n                        console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n                        if (!isValidPhone(paymentPhone)) {\n                          console.log('❌ Invalid phone number');\n                          message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                          return;\n                        }\n                        try {\n                          console.log('🔥 STARTING PERMANENT PHONE NUMBER SAVE...');\n                          console.log('📱 Phone to save:', paymentPhone);\n\n                          // Show loading state\n                          const btn = e.target;\n                          const originalText = btn.textContent;\n                          btn.textContent = '⏳ Saving...';\n                          btn.disabled = true;\n\n                          // ACTUALLY UPDATE THE USER PROFILE - NOT BACKGROUND!\n                          console.log('🔄 PERMANENTLY UPDATING USER PROFILE...');\n                          const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n                          if (updateSuccess) {\n                            console.log('🎉 PHONE NUMBER PERMANENTLY SAVED!');\n\n                            // Close editing mode\n                            setIsEditingPhone(false);\n                            setPhoneUpdated(true);\n\n                            // Show success message\n                            message.success({\n                              content: '🎉 Phone number updated permanently!',\n                              duration: 4,\n                              style: {\n                                marginTop: '20vh',\n                                fontSize: '15px',\n                                fontWeight: '600'\n                              }\n                            });\n\n                            // Additional confirmation\n                            setTimeout(() => {\n                              message.info({\n                                content: '✅ Your phone number will persist after page refresh!',\n                                duration: 4,\n                                style: {\n                                  marginTop: '20vh',\n                                  fontSize: '14px'\n                                }\n                              });\n                            }, 1500);\n                          } else {\n                            console.log('❌ PHONE NUMBER UPDATE FAILED');\n                            message.error({\n                              content: '❌ Failed to save phone number permanently. Please try again.',\n                              duration: 4\n                            });\n                          }\n\n                          // Restore button\n                          btn.textContent = originalText;\n                          btn.disabled = !isValidPhone(paymentPhone);\n\n                          // Reset the updated indicator after 8 seconds\n                          setTimeout(() => {\n                            setPhoneUpdated(false);\n                          }, 8000);\n                        } catch (error) {\n                          console.error('❌ Error saving phone number:', error);\n                          message.error('Failed to save phone number. Please try again.');\n                        }\n                      },\n                      disabled: !isValidPhone(paymentPhone),\n                      type: \"button\",\n                      children: \"\\u2705 Save\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"cancel-phone-btn\",\n                      onClick: () => {\n                        setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n                        setIsEditingPhone(false);\n                      },\n                      type: \"button\",\n                      children: \"\\u274C Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-note\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"\\uD83D\\uDCA1 This number will receive the payment SMS. You can use a different number than your profile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Payment Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: \"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"back-btn\",\n              onClick: () => setStep('plans'),\n              children: \"\\u2190 Back to Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pay-btn\",\n              onClick: e => {\n                e.preventDefault();\n                console.log('💳 PAY BUTTON CLICKED!');\n                console.log('📱 Payment phone:', paymentPhone);\n                console.log('✏️ Is editing phone:', isEditingPhone);\n                console.log('⏳ Payment loading:', paymentLoading);\n                if (isEditingPhone) {\n                  message.warning('Please save your phone number first');\n                  return;\n                }\n                if (!paymentPhone) {\n                  message.error('Please enter a phone number');\n                  return;\n                }\n                if (!isValidPhone(paymentPhone)) {\n                  message.error('Please enter a valid phone number');\n                  return;\n                }\n                handlePayment();\n              },\n              disabled: paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone),\n              children: paymentLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 23\n                }, this), \"Processing...\"]\n              }, void 0, true) : isEditingPhone ? 'Save phone number first' : !paymentPhone ? 'Enter phone number' : !isValidPhone(paymentPhone) ? 'Invalid phone number' : `Pay ${(_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString()} TZS`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), step === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-animation\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Request Sent!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation and complete the payment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Check your phone for SMS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Follow the payment instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Your subscription will activate automatically\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"check-status-btn\",\n              onClick: async () => {\n                console.log('🔍 Manual payment check triggered');\n                try {\n                  const response = await checkPaymentStatus();\n                  console.log('📥 Manual check response:', response);\n                  if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                    console.log('✅ Payment confirmed manually!');\n                    dispatch(SetSubscription(response));\n                    message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                    onSuccess && onSuccess();\n                    setTimeout(() => onClose(), 1000);\n                  } else {\n                    message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                  }\n                } catch (error) {\n                  console.error('❌ Manual check error:', error);\n                  message.error('Error checking payment status');\n                }\n              },\n              children: \"\\u2705 Check Payment Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"done-btn\",\n              onClick: handleClose,\n              children: \"I'll complete the payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(SubscriptionModal, \"OiBbBX2RGhp2Ezw7XWW7V79Lirs=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = SubscriptionModal;\nexport default SubscriptionModal;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "message", "getPlans", "addPayment", "checkPaymentStatus", "updateUserInfo", "axiosInstance", "SetSubscription", "SetUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionModal", "isOpen", "onClose", "onSuccess", "_s", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "hasValidPhone", "phone", "user", "phoneNumber", "test", "state", "dispatch", "fetchPlans", "response", "Array", "isArray", "error", "console", "handlePlanSelect", "plan", "handlePayment", "_user$name", "paymentData", "userId", "_id", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "success", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "checkStatus", "log", "paymentStatus", "status", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "warning", "handleClose", "setIsEditingPhone", "setPhoneUpdated", "setPaymentPhone", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "title", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "length", "isEditingPhone", "phoneUpdated", "paymentPhone", "process", "env", "NODE_ENV", "color", "display", "window", "location", "reload", "padding", "background", "border", "borderRadius", "cursor", "type", "value", "onChange", "e", "target", "placeholder", "isValidPhone", "max<PERSON><PERSON><PERSON>", "preventDefault", "btn", "originalText", "textContent", "disabled", "updateSuccess", "updateUserPhoneNumber", "info", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionModal/SubscriptionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport axiosInstance from '../../apicalls/index';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n\n  // Check if user has valid phone number\n  const hasValidPhone = () => {\n    const phone = user?.phoneNumber;\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n\n  \n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n    }\n  }, [isOpen]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!hasValidPhone()) {\n      message.error('Please update your phone number in your profile first. Go to Profile → Edit → Phone Number');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: user.phoneNumber, // Use phone number from user profile\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n        \n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPhoneUpdated(false);\n    setPaymentPhone(user?.phoneNumber || '');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n            {step === 'success' && '⏳ Processing Payment...'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('glimp') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"phone-section\">\n                  <div className=\"info-item\">\n                    <span className=\"info-label\">Phone Number for Payment:</span>\n                    {!isEditingPhone ? (\n                      <div className=\"phone-display\">\n                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>\n                          {paymentPhone || 'Not provided'}\n                          {phoneUpdated && <span className=\"updated-indicator\">✅ Updated</span>}\n                        </span>\n                        {process.env.NODE_ENV === 'development' && (\n                          <div style={{marginTop: '8px'}}>\n                            <small style={{color: '#666', fontSize: '10px', display: 'block'}}>\n                              Debug: Payment={paymentPhone} | User={user?.phoneNumber} | Updated={phoneUpdated ? 'Yes' : 'No'}\n                            </small>\n                            <button\n                              onClick={() => {\n                                console.log('🔄 Testing page refresh...');\n                                window.location.reload();\n                              }}\n                              style={{\n                                fontSize: '10px',\n                                padding: '2px 6px',\n                                marginTop: '4px',\n                                background: '#f0f0f0',\n                                border: '1px solid #ccc',\n                                borderRadius: '4px',\n                                cursor: 'pointer'\n                              }}\n                            >\n                              🔄 Test Refresh\n                            </button>\n                          </div>\n                        )}\n                        <button\n                          className=\"edit-phone-btn\"\n                          onClick={() => setIsEditingPhone(true)}\n                          type=\"button\"\n                        >\n                          ✏️ Change\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"phone-edit\">\n                        <input\n                          type=\"tel\"\n                          value={paymentPhone}\n                          onChange={(e) => setPaymentPhone(e.target.value)}\n                          placeholder=\"Enter phone number (e.g., 0744963858)\"\n                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}\n                          maxLength=\"10\"\n                        />\n                        {paymentPhone && (\n                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>\n                            {isValidPhone(paymentPhone) ? (\n                              <span className=\"validation-message valid\">✅ Valid phone number</span>\n                            ) : (\n                              <span className=\"validation-message invalid\">❌ Must start with 06 or 07 and be 10 digits</span>\n                            )}\n                          </div>\n                        )}\n                        <div className=\"phone-actions\">\n                          <button\n                            className=\"save-phone-btn\"\n                            onClick={async (e) => {\n                              e.preventDefault();\n                              console.log('🔥 SAVE BUTTON CLICKED!');\n                              console.log('📱 Payment phone:', paymentPhone);\n                              console.log('✅ Is valid phone:', isValidPhone(paymentPhone));\n\n                              if (!isValidPhone(paymentPhone)) {\n                                console.log('❌ Invalid phone number');\n                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                                return;\n                              }\n\n                              try {\n                                console.log('🔥 STARTING PERMANENT PHONE NUMBER SAVE...');\n                                console.log('📱 Phone to save:', paymentPhone);\n\n                                // Show loading state\n                                const btn = e.target;\n                                const originalText = btn.textContent;\n                                btn.textContent = '⏳ Saving...';\n                                btn.disabled = true;\n\n                                // ACTUALLY UPDATE THE USER PROFILE - NOT BACKGROUND!\n                                console.log('🔄 PERMANENTLY UPDATING USER PROFILE...');\n                                const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n\n                                if (updateSuccess) {\n                                  console.log('🎉 PHONE NUMBER PERMANENTLY SAVED!');\n\n                                  // Close editing mode\n                                  setIsEditingPhone(false);\n                                  setPhoneUpdated(true);\n\n                                  // Show success message\n                                  message.success({\n                                    content: '🎉 Phone number updated permanently!',\n                                    duration: 4,\n                                    style: {\n                                      marginTop: '20vh',\n                                      fontSize: '15px',\n                                      fontWeight: '600'\n                                    }\n                                  });\n\n                                  // Additional confirmation\n                                  setTimeout(() => {\n                                    message.info({\n                                      content: '✅ Your phone number will persist after page refresh!',\n                                      duration: 4,\n                                      style: {\n                                        marginTop: '20vh',\n                                        fontSize: '14px'\n                                      }\n                                    });\n                                  }, 1500);\n\n                                } else {\n                                  console.log('❌ PHONE NUMBER UPDATE FAILED');\n                                  message.error({\n                                    content: '❌ Failed to save phone number permanently. Please try again.',\n                                    duration: 4\n                                  });\n                                }\n\n                                // Restore button\n                                btn.textContent = originalText;\n                                btn.disabled = !isValidPhone(paymentPhone);\n\n                                // Reset the updated indicator after 8 seconds\n                                setTimeout(() => {\n                                  setPhoneUpdated(false);\n                                }, 8000);\n\n                              } catch (error) {\n                                console.error('❌ Error saving phone number:', error);\n                                message.error('Failed to save phone number. Please try again.');\n                              }\n                            }}\n                            disabled={!isValidPhone(paymentPhone)}\n                            type=\"button\"\n                          >\n                            ✅ Save\n                          </button>\n                          <button\n                            className=\"cancel-phone-btn\"\n                            onClick={() => {\n                              setPaymentPhone(user?.phoneNumber || '');\n                              setIsEditingPhone(false);\n                            }}\n                            type=\"button\"\n                          >\n                            ❌ Cancel\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"phone-note\">\n                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    console.log('💳 PAY BUTTON CLICKED!');\n                    console.log('📱 Payment phone:', paymentPhone);\n                    console.log('✏️ Is editing phone:', isEditingPhone);\n                    console.log('⏳ Payment loading:', paymentLoading);\n\n                    if (isEditingPhone) {\n                      message.warning('Please save your phone number first');\n                      return;\n                    }\n\n                    if (!paymentPhone) {\n                      message.error('Please enter a phone number');\n                      return;\n                    }\n\n                    if (!isValidPhone(paymentPhone)) {\n                      message.error('Please enter a valid phone number');\n                      return;\n                    }\n\n                    handlePayment();\n                  }}\n                  disabled={paymentLoading || !paymentPhone || isEditingPhone || !isValidPhone(paymentPhone)}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : isEditingPhone ? (\n                    'Save phone number first'\n                  ) : !paymentPhone ? (\n                    'Enter phone number'\n                  ) : !isValidPhone(paymentPhone) ? (\n                    'Invalid phone number'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {step === 'success' && (\n            <div className=\"success-step\">\n              <div className=\"success-animation\">\n                <div className=\"pulse-circle\">\n                  <div className=\"phone-icon\">📱</div>\n                </div>\n              </div>\n              \n              <h3>Payment Request Sent!</h3>\n              <p>Please check your phone for SMS confirmation and complete the payment.</p>\n              \n              <div className=\"payment-steps\">\n                <div className=\"step\">\n                  <span className=\"step-number\">1</span>\n                  <span className=\"step-text\">Check your phone for SMS</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">2</span>\n                  <span className=\"step-text\">Follow the payment instructions</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">3</span>\n                  <span className=\"step-text\">Your subscription will activate automatically</span>\n                </div>\n              </div>\n\n              <div className=\"success-actions\">\n                <button\n                  className=\"check-status-btn\"\n                  onClick={async () => {\n                    console.log('🔍 Manual payment check triggered');\n                    try {\n                      const response = await checkPaymentStatus();\n                      console.log('📥 Manual check response:', response);\n\n                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                        console.log('✅ Payment confirmed manually!');\n                        dispatch(SetSubscription(response));\n                        message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                        onSuccess && onSuccess();\n                        setTimeout(() => onClose(), 1000);\n                      } else {\n                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                      }\n                    } catch (error) {\n                      console.error('❌ Manual check error:', error);\n                      message.error('Error checking payment status');\n                    }\n                  }}\n                >\n                  ✅ Check Payment Status\n                </button>\n\n                <button className=\"done-btn\" onClick={handleClose}>\n                  I'll complete the payment\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,wBAAwB;AACvE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAClE,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAE3C;EACA,MAAMmC,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,KAAK,GAAGC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW;IAC/B,OAAOF,KAAK,IAAI,gBAAgB,CAACG,IAAI,CAACH,KAAK,CAAC;EAC9C,CAAC;EAID,MAAM;IAAEC;EAAK,CAAC,GAAGnC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACH,IAAI,CAAC;EACnD,MAAMI,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIkB,MAAM,EAAE;MACVuB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACvB,MAAM,CAAC,CAAC;EAEZ,MAAMuB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,QAAQ,GAAG,MAAMtC,QAAQ,CAAC,CAAC;MACjCqB,QAAQ,CAACkB,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C1C,OAAO,CAAC0C,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,gBAAgB,GAAIC,IAAI,IAAK;IACjCrB,eAAe,CAACqB,IAAI,CAAC;IACrBf,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACvB,YAAY,EAAE;MACjBvB,OAAO,CAAC0C,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF;IAEA,IAAI,CAACX,aAAa,CAAC,CAAC,EAAE;MACpB/B,OAAO,CAAC0C,KAAK,CAAC,4FAA4F,CAAC;MAC3G;IACF;IAEA,IAAI;MAAA,IAAAK,UAAA;MACFnB,iBAAiB,CAAC,IAAI,CAAC;MACvBS,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMuC,WAAW,GAAG;QAClBH,IAAI,EAAEtB,YAAY;QAClB0B,MAAM,EAAEhB,IAAI,CAACiB,GAAG;QAChBC,SAAS,EAAElB,IAAI,CAACC,WAAW;QAAE;QAC7BkB,SAAS,EAAEnB,IAAI,CAACoB,KAAK,IAAK,IAAAN,UAAA,GAAEd,IAAI,CAACqB,IAAI,cAAAP,UAAA,uBAATA,UAAA,CAAWQ,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAMjB,QAAQ,GAAG,MAAMrC,UAAU,CAAC8C,WAAW,CAAC;MAE9C,IAAIT,QAAQ,CAACkB,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAC,kEAAkE,CAAC;QACnF3B,OAAO,CAAC,SAAS,CAAC;;QAElB;QACA4B,wBAAwB,CAACnB,QAAQ,CAACoB,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACrB,QAAQ,CAACvC,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC1C,OAAO,CAAC0C,KAAK,CAACA,KAAK,CAAC1C,OAAO,IAAI,mCAAmC,CAAC;IACrE,CAAC,SAAS;MACR4B,iBAAiB,CAAC,KAAK,CAAC;MACxBS,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMkD,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFF,QAAQ,EAAE;QACVnB,OAAO,CAACsB,GAAG,CAAE,yCAAwCH,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAMxB,QAAQ,GAAG,MAAMpC,kBAAkB,CAAC,CAAC;QAC3CwC,OAAO,CAACsB,GAAG,CAAC,6BAA6B,EAAE1B,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAAC2B,aAAa,KAAK,MAAM,IAAI3B,QAAQ,CAAC4B,MAAM,KAAK,QAAQ,EAAE;UACpGxB,OAAO,CAACsB,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACA5B,QAAQ,CAAC/B,eAAe,CAACiC,QAAQ,CAAC,CAAC;;UAEnC;UACAvC,OAAO,CAACyD,OAAO,CAAC;YACdW,OAAO,EAAE,2CAA2C;YACpDC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;;UAEF;UACAxD,SAAS,IAAIA,SAAS,CAAC,CAAC;;UAExB;UACAyD,UAAU,CAAC,MAAM;YACf1D,OAAO,CAAC,CAAC;UACX,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,IAAI;QACb;QAEA,IAAI8C,QAAQ,IAAIC,WAAW,EAAE;UAC3BpB,OAAO,CAACsB,GAAG,CAAC,iCAAiC,CAAC;UAC9CjE,OAAO,CAAC2E,OAAO,CAAC;YACdP,OAAO,EAAE,sGAAsG;YAC/GC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,OAAO,KAAK;QACd;;QAEA;QACAK,UAAU,CAACV,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAIoB,QAAQ,IAAIC,WAAW,EAAE;UAC3B/D,OAAO,CAAC0C,KAAK,CAAC,4EAA4E,CAAC;QAC7F,CAAC,MAAM;UACLgC,UAAU,CAACV,WAAW,EAAE,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACAA,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACxB9C,OAAO,CAAC,OAAO,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,KAAK,CAAC;IACxBiD,iBAAiB,CAAC,KAAK,CAAC;IACxBC,eAAe,CAAC,KAAK,CAAC;IACtBC,eAAe,CAAC,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,KAAI,EAAE,CAAC;IACxClB,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKqE,SAAS,EAAC,4BAA4B;IAAAC,QAAA,eACzCtE,OAAA;MAAKqE,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCtE,OAAA;QAAKqE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtE,OAAA;UAAIqE,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBpD,IAAI,KAAK,OAAO,IAAI,8BAA8B,EAClDA,IAAI,KAAK,SAAS,IAAI,0BAA0B,EAChDA,IAAI,KAAK,SAAS,IAAI,yBAAyB;QAAA;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACL1E,OAAA;UAAQqE,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEV,WAAY;UAAAK,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEN1E,OAAA;QAAKqE,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC3BpD,IAAI,KAAK,OAAO,iBACflB,OAAA;UAAKqE,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBxD,OAAO,gBACNd,OAAA;YAAKqE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtE,OAAA;cAAKqE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/B1E,OAAA;cAAAsE,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAENhE,KAAK,CAACkE,GAAG,CAAE1C,IAAI;YAAA,IAAA2C,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA;YAAA,oBACbhF,OAAA;cAAoBqE,SAAS,EAAC,WAAW;cAACM,OAAO,EAAEA,CAAA,KAAM1C,gBAAgB,CAACC,IAAI,CAAE;cAAAoC,QAAA,gBAC9EtE,OAAA;gBAAKqE,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtE,OAAA;kBAAIqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpC,IAAI,CAAC+C;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAG,WAAA,GAAA3C,IAAI,CAAC+C,KAAK,cAAAJ,WAAA,uBAAVA,WAAA,CAAYhC,WAAW,CAAC,CAAC,CAACqC,QAAQ,CAAC,OAAO,CAAC,kBAC1ClF,OAAA;kBAAMqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtE,OAAA;kBAAMqE,SAAS,EAAC,cAAc;kBAAAC,QAAA,IAAAQ,qBAAA,GAAE5C,IAAI,CAACiD,eAAe,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACjFxC,IAAI,CAACmD,WAAW,IAAInD,IAAI,CAACmD,WAAW,KAAKnD,IAAI,CAACiD,eAAe,iBAC5DnF,OAAA;kBAAMqE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAEpC,IAAI,CAACmD,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/E,eACD1E,OAAA;kBAAMqE,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAEpC,IAAI,CAACwB,QAAQ,EAAC,QAAM,EAACxB,IAAI,CAACwB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eAEN1E,OAAA;gBAAKqE,SAAS,EAAC,eAAe;gBAAAC,QAAA,IAAAS,cAAA,GAC3B7C,IAAI,CAACoD,QAAQ,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,KAAK,kBAC7CzF,OAAA;kBAAiBqE,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAClCtE,OAAA;oBAAMqE,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC1E,OAAA;oBAAMqE,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEkB;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFvCe,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC,EACD,EAAAM,eAAA,GAAA9C,IAAI,CAACoD,QAAQ,cAAAN,eAAA,uBAAbA,eAAA,CAAeU,MAAM,IAAG,CAAC,iBACxB1F,OAAA;kBAAKqE,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBtE,OAAA;oBAAMqE,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC1E,OAAA;oBAAMqE,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAEpC,IAAI,CAACoD,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAC,gBAAc;kBAAA;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1E,OAAA;gBAAQqE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,SAC3B,EAACpC,IAAI,CAAC+C,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjCDxC,IAAI,CAACK,GAAG;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCb,CAAC;UAAA,CACP;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEAxD,IAAI,KAAK,SAAS,IAAIN,YAAY,iBACjCZ,OAAA;UAAKqE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtE,OAAA;YAAKqE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCtE,OAAA;cAAAsE,QAAA,GAAI,iBAAe,EAAC1D,YAAY,CAACqE,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C1E,OAAA;cAAGqE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,IAAA9D,qBAAA,GAC9BI,YAAY,CAACuE,eAAe,cAAA3E,qBAAA,uBAA5BA,qBAAA,CAA8B4E,cAAc,CAAC,CAAC,EAAC,WAAS,EAACxE,YAAY,CAAC8C,QAAQ,EAAC,QAAM,EAAC9C,YAAY,CAAC8C,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN1E,OAAA;YAAKqE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BtE,OAAA;cAAKqE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BtE,OAAA;gBAAKqE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBtE,OAAA;kBAAMqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC5D,CAACiB,cAAc,gBACd3F,OAAA;kBAAKqE,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BtE,OAAA;oBAAMqE,SAAS,EAAG,cAAauB,YAAY,GAAG,SAAS,GAAG,EAAG,EAAE;oBAAAtB,QAAA,GAC5DuB,YAAY,IAAI,cAAc,EAC9BD,YAAY,iBAAI5F,OAAA;sBAAMqE,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,EACNoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrChG,OAAA;oBAAK2D,KAAK,EAAE;sBAACC,SAAS,EAAE;oBAAK,CAAE;oBAAAU,QAAA,gBAC7BtE,OAAA;sBAAO2D,KAAK,EAAE;wBAACsC,KAAK,EAAE,MAAM;wBAAEpC,QAAQ,EAAE,MAAM;wBAAEqC,OAAO,EAAE;sBAAO,CAAE;sBAAA5B,QAAA,GAAC,iBAClD,EAACuB,YAAY,EAAC,UAAQ,EAACvE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,EAAC,aAAW,EAACqE,YAAY,GAAG,KAAK,GAAG,IAAI;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC,eACR1E,OAAA;sBACE2E,OAAO,EAAEA,CAAA,KAAM;wBACb3C,OAAO,CAACsB,GAAG,CAAC,4BAA4B,CAAC;wBACzC6C,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;sBAC1B,CAAE;sBACF1C,KAAK,EAAE;wBACLE,QAAQ,EAAE,MAAM;wBAChByC,OAAO,EAAE,SAAS;wBAClB1C,SAAS,EAAE,KAAK;wBAChB2C,UAAU,EAAE,SAAS;wBACrBC,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBC,MAAM,EAAE;sBACV,CAAE;sBAAApC,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,eACD1E,OAAA;oBACEqE,SAAS,EAAC,gBAAgB;oBAC1BM,OAAO,EAAEA,CAAA,KAAMT,iBAAiB,CAAC,IAAI,CAAE;oBACvCyC,IAAI,EAAC,QAAQ;oBAAArC,QAAA,EACd;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAEN1E,OAAA;kBAAKqE,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtE,OAAA;oBACE2G,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAEf,YAAa;oBACpBgB,QAAQ,EAAGC,CAAC,IAAK1C,eAAe,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,WAAW,EAAC,uCAAuC;oBACnD3C,SAAS,EAAG,eAAcwB,YAAY,GAAIoB,YAAY,CAACpB,YAAY,CAAC,GAAG,OAAO,GAAG,SAAS,GAAI,EAAG,EAAE;oBACnGqB,SAAS,EAAC;kBAAI;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACDmB,YAAY,iBACX7F,OAAA;oBAAKqE,SAAS,EAAG,oBAAmB4C,YAAY,CAACpB,YAAY,CAAC,GAAG,OAAO,GAAG,SAAU,EAAE;oBAAAvB,QAAA,EACpF2C,YAAY,CAACpB,YAAY,CAAC,gBACzB7F,OAAA;sBAAMqE,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEtE1E,OAAA;sBAAMqE,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC/F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eACD1E,OAAA;oBAAKqE,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BtE,OAAA;sBACEqE,SAAS,EAAC,gBAAgB;sBAC1BM,OAAO,EAAE,MAAOmC,CAAC,IAAK;wBACpBA,CAAC,CAACK,cAAc,CAAC,CAAC;wBAClBnF,OAAO,CAACsB,GAAG,CAAC,yBAAyB,CAAC;wBACtCtB,OAAO,CAACsB,GAAG,CAAC,mBAAmB,EAAEuC,YAAY,CAAC;wBAC9C7D,OAAO,CAACsB,GAAG,CAAC,mBAAmB,EAAE2D,YAAY,CAACpB,YAAY,CAAC,CAAC;wBAE5D,IAAI,CAACoB,YAAY,CAACpB,YAAY,CAAC,EAAE;0BAC/B7D,OAAO,CAACsB,GAAG,CAAC,wBAAwB,CAAC;0BACrCjE,OAAO,CAAC0C,KAAK,CAAC,wEAAwE,CAAC;0BACvF;wBACF;wBAEA,IAAI;0BACFC,OAAO,CAACsB,GAAG,CAAC,4CAA4C,CAAC;0BACzDtB,OAAO,CAACsB,GAAG,CAAC,mBAAmB,EAAEuC,YAAY,CAAC;;0BAE9C;0BACA,MAAMuB,GAAG,GAAGN,CAAC,CAACC,MAAM;0BACpB,MAAMM,YAAY,GAAGD,GAAG,CAACE,WAAW;0BACpCF,GAAG,CAACE,WAAW,GAAG,aAAa;0BAC/BF,GAAG,CAACG,QAAQ,GAAG,IAAI;;0BAEnB;0BACAvF,OAAO,CAACsB,GAAG,CAAC,yCAAyC,CAAC;0BACtD,MAAMkE,aAAa,GAAG,MAAMC,qBAAqB,CAAC5B,YAAY,CAAC;0BAE/D,IAAI2B,aAAa,EAAE;4BACjBxF,OAAO,CAACsB,GAAG,CAAC,oCAAoC,CAAC;;4BAEjD;4BACAY,iBAAiB,CAAC,KAAK,CAAC;4BACxBC,eAAe,CAAC,IAAI,CAAC;;4BAErB;4BACA9E,OAAO,CAACyD,OAAO,CAAC;8BACdW,OAAO,EAAE,sCAAsC;8BAC/CC,QAAQ,EAAE,CAAC;8BACXC,KAAK,EAAE;gCACLC,SAAS,EAAE,MAAM;gCACjBC,QAAQ,EAAE,MAAM;gCAChBC,UAAU,EAAE;8BACd;4BACF,CAAC,CAAC;;4BAEF;4BACAC,UAAU,CAAC,MAAM;8BACf1E,OAAO,CAACqI,IAAI,CAAC;gCACXjE,OAAO,EAAE,sDAAsD;gCAC/DC,QAAQ,EAAE,CAAC;gCACXC,KAAK,EAAE;kCACLC,SAAS,EAAE,MAAM;kCACjBC,QAAQ,EAAE;gCACZ;8BACF,CAAC,CAAC;4BACJ,CAAC,EAAE,IAAI,CAAC;0BAEV,CAAC,MAAM;4BACL7B,OAAO,CAACsB,GAAG,CAAC,8BAA8B,CAAC;4BAC3CjE,OAAO,CAAC0C,KAAK,CAAC;8BACZ0B,OAAO,EAAE,8DAA8D;8BACvEC,QAAQ,EAAE;4BACZ,CAAC,CAAC;0BACJ;;0BAEA;0BACA0D,GAAG,CAACE,WAAW,GAAGD,YAAY;0BAC9BD,GAAG,CAACG,QAAQ,GAAG,CAACN,YAAY,CAACpB,YAAY,CAAC;;0BAE1C;0BACA9B,UAAU,CAAC,MAAM;4BACfI,eAAe,CAAC,KAAK,CAAC;0BACxB,CAAC,EAAE,IAAI,CAAC;wBAEV,CAAC,CAAC,OAAOpC,KAAK,EAAE;0BACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;0BACpD1C,OAAO,CAAC0C,KAAK,CAAC,gDAAgD,CAAC;wBACjE;sBACF,CAAE;sBACFwF,QAAQ,EAAE,CAACN,YAAY,CAACpB,YAAY,CAAE;sBACtCc,IAAI,EAAC,QAAQ;sBAAArC,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT1E,OAAA;sBACEqE,SAAS,EAAC,kBAAkB;sBAC5BM,OAAO,EAAEA,CAAA,KAAM;wBACbP,eAAe,CAAC,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,KAAI,EAAE,CAAC;wBACxC2C,iBAAiB,CAAC,KAAK,CAAC;sBAC1B,CAAE;sBACFyC,IAAI,EAAC,QAAQ;sBAAArC,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN1E,OAAA;gBAAKqE,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBtE,OAAA;kBAAAsE,QAAA,EAAO;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBtE,OAAA;gBAAMqE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnD1E,OAAA;gBAAMqE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1E,OAAA;YAAKqE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BtE,OAAA;cAAQqE,SAAS,EAAC,UAAU;cAACM,OAAO,EAAEA,CAAA,KAAMxD,OAAO,CAAC,OAAO,CAAE;cAAAmD,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1E,OAAA;cACEqE,SAAS,EAAC,SAAS;cACnBM,OAAO,EAAGmC,CAAC,IAAK;gBACdA,CAAC,CAACK,cAAc,CAAC,CAAC;gBAClBnF,OAAO,CAACsB,GAAG,CAAC,wBAAwB,CAAC;gBACrCtB,OAAO,CAACsB,GAAG,CAAC,mBAAmB,EAAEuC,YAAY,CAAC;gBAC9C7D,OAAO,CAACsB,GAAG,CAAC,sBAAsB,EAAEqC,cAAc,CAAC;gBACnD3D,OAAO,CAACsB,GAAG,CAAC,oBAAoB,EAAEtC,cAAc,CAAC;gBAEjD,IAAI2E,cAAc,EAAE;kBAClBtG,OAAO,CAAC2E,OAAO,CAAC,qCAAqC,CAAC;kBACtD;gBACF;gBAEA,IAAI,CAAC6B,YAAY,EAAE;kBACjBxG,OAAO,CAAC0C,KAAK,CAAC,6BAA6B,CAAC;kBAC5C;gBACF;gBAEA,IAAI,CAACkF,YAAY,CAACpB,YAAY,CAAC,EAAE;kBAC/BxG,OAAO,CAAC0C,KAAK,CAAC,mCAAmC,CAAC;kBAClD;gBACF;gBAEAI,aAAa,CAAC,CAAC;cACjB,CAAE;cACFoF,QAAQ,EAAEvG,cAAc,IAAI,CAAC6E,YAAY,IAAIF,cAAc,IAAI,CAACsB,YAAY,CAACpB,YAAY,CAAE;cAAAvB,QAAA,EAE1FtD,cAAc,gBACbhB,OAAA,CAAAE,SAAA;gBAAAoE,QAAA,gBACEtE,OAAA;kBAAMqE,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEvC;cAAA,eAAE,CAAC,GACDiB,cAAc,GAChB,yBAAyB,GACvB,CAACE,YAAY,GACf,oBAAoB,GAClB,CAACoB,YAAY,CAACpB,YAAY,CAAC,GAC7B,sBAAsB,GAErB,OAAI,CAAApF,sBAAA,GAAEG,YAAY,CAACuE,eAAe,cAAA1E,sBAAA,uBAA5BA,sBAAA,CAA8B2E,cAAc,CAAC,CAAE;YACvD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxD,IAAI,KAAK,SAAS,iBACjBlB,OAAA;UAAKqE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtE,OAAA;YAAKqE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCtE,OAAA;cAAKqE,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BtE,OAAA;gBAAKqE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1E,OAAA;YAAAsE,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B1E,OAAA;YAAAsE,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE7E1E,OAAA;YAAKqE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtE,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAMqE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC1E,OAAA;gBAAMqE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN1E,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAMqE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC1E,OAAA;gBAAMqE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN1E,OAAA;cAAKqE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtE,OAAA;gBAAMqE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC1E,OAAA;gBAAMqE,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1E,OAAA;YAAKqE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BtE,OAAA;cACEqE,SAAS,EAAC,kBAAkB;cAC5BM,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnB3C,OAAO,CAACsB,GAAG,CAAC,mCAAmC,CAAC;gBAChD,IAAI;kBACF,MAAM1B,QAAQ,GAAG,MAAMpC,kBAAkB,CAAC,CAAC;kBAC3CwC,OAAO,CAACsB,GAAG,CAAC,2BAA2B,EAAE1B,QAAQ,CAAC;kBAElD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAAC2B,aAAa,KAAK,MAAM,IAAI3B,QAAQ,CAAC4B,MAAM,KAAK,QAAQ,EAAE;oBACpGxB,OAAO,CAACsB,GAAG,CAAC,+BAA+B,CAAC;oBAC5C5B,QAAQ,CAAC/B,eAAe,CAACiC,QAAQ,CAAC,CAAC;oBACnCvC,OAAO,CAACyD,OAAO,CAAC,2CAA2C,CAAC;oBAC5DxC,SAAS,IAAIA,SAAS,CAAC,CAAC;oBACxByD,UAAU,CAAC,MAAM1D,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;kBACnC,CAAC,MAAM;oBACLhB,OAAO,CAACqI,IAAI,CAAC,0EAA0E,CAAC;kBAC1F;gBACF,CAAC,CAAC,OAAO3F,KAAK,EAAE;kBACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;kBAC7C1C,OAAO,CAAC0C,KAAK,CAAC,+BAA+B,CAAC;gBAChD;cACF,CAAE;cAAAuC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1E,OAAA;cAAQqE,SAAS,EAAC,UAAU;cAACM,OAAO,EAAEV,WAAY;cAAAK,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAtgBIJ,iBAAiB;EAAA,QAeJhB,WAAW,EACXC,WAAW;AAAA;AAAAuI,EAAA,GAhBxBxH,iBAAiB;AAwgBvB,eAAeA,iBAAiB;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}