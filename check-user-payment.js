const mongoose = require('mongoose');
const User = require('./server/models/userModel');
const Subscription = require('./server/models/subscriptionModel');
const Plan = require('./server/models/planModel');
require('dotenv').config({ path: './server/.env' });

const checkUserPayment = async () => {
  try {
    // Connect to MongoDB with timeout settings
    await mongoose.connect(process.env.MONGO_URL, {
      bufferCommands: false,
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
    });
    console.log('✅ Connected to MongoDB');

    // Find user lucymosha
    const user = await User.findOne({ 
      $or: [
        { username: 'lucymosha' },
        { name: { $regex: 'lucy', $options: 'i' } },
        { email: { $regex: 'lucy', $options: 'i' } }
      ]
    });

    if (!user) {
      console.log('❌ User "lucymosha" not found');
      return;
    }

    console.log('\n👤 User Details:');
    console.log('- ID:', user._id);
    console.log('- Name:', user.name);
    console.log('- Username:', user.username);
    console.log('- Email:', user.email);
    console.log('- Phone:', user.phoneNumber);
    console.log('- Payment Required:', user.paymentRequired);
    console.log('- Subscription Status:', user.subscriptionStatus);
    console.log('- Subscription Plan:', user.subscriptionPlan);
    console.log('- Subscription Start:', user.subscriptionStartDate);
    console.log('- Subscription End:', user.subscriptionEndDate);

    // Find all subscriptions for this user
    const subscriptions = await Subscription.find({ user: user._id })
      .populate('activePlan')
      .sort({ createdAt: -1 });

    console.log('\n💳 Subscription Records:');
    if (subscriptions.length === 0) {
      console.log('❌ No subscription records found');
    } else {
      subscriptions.forEach((sub, index) => {
        console.log(`\n--- Subscription ${index + 1} ---`);
        console.log('- ID:', sub._id);
        console.log('- Plan:', sub.activePlan?.title || 'Unknown');
        console.log('- Payment Status:', sub.paymentStatus);
        console.log('- Status:', sub.status);
        console.log('- Start Date:', sub.startDate);
        console.log('- End Date:', sub.endDate);
        console.log('- Created:', sub.createdAt);
        console.log('- Updated:', sub.updatedAt);
        
        console.log('\n📋 Payment History:');
        if (sub.paymentHistory.length === 0) {
          console.log('  No payment history');
        } else {
          sub.paymentHistory.forEach((payment, pIndex) => {
            console.log(`  Payment ${pIndex + 1}:`);
            console.log(`  - Order ID: ${payment.orderId}`);
            console.log(`  - Reference ID: ${payment.referenceId || 'None'}`);
            console.log(`  - Amount: ${payment.amount}`);
            console.log(`  - Status: ${payment.paymentStatus}`);
            console.log(`  - Date: ${payment.paymentDate}`);
            console.log(`  - Method: ${payment.paymentMethod}`);
          });
        }
      });
    }

    // Check for pending payments
    const pendingSubscriptions = await Subscription.find({
      user: user._id,
      paymentStatus: 'pending'
    }).populate('activePlan');

    console.log('\n⏳ Pending Payments:');
    if (pendingSubscriptions.length === 0) {
      console.log('✅ No pending payments found');
    } else {
      pendingSubscriptions.forEach((sub, index) => {
        console.log(`\nPending Payment ${index + 1}:`);
        console.log('- Plan:', sub.activePlan?.title);
        console.log('- Amount:', sub.activePlan?.discountedPrice);
        console.log('- Created:', sub.createdAt);
        
        const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
        if (latestPayment) {
          console.log('- Order ID:', latestPayment.orderId);
          console.log('- Payment Date:', latestPayment.paymentDate);
        }
      });
    }

    // Check current date vs subscription dates
    const currentDate = new Date().toISOString().split('T')[0];
    console.log('\n📅 Date Analysis:');
    console.log('- Current Date:', currentDate);
    
    if (user.subscriptionEndDate) {
      const isExpired = currentDate > user.subscriptionEndDate;
      console.log('- Subscription Expired:', isExpired);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
};

checkUserPayment();
