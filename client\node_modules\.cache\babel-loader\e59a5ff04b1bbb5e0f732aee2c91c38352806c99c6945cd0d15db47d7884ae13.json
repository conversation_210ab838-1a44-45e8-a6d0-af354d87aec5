{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Suspense, lazy } from \"react\";\nimport \"./stylesheets/theme.css\";\nimport \"./stylesheets/alignments.css\";\nimport \"./stylesheets/textelements.css\";\nimport \"./stylesheets/form-elements.css\";\nimport \"./stylesheets/custom-components.css\";\nimport \"./stylesheets/layout.css\";\nimport \"./styles/modern.css\";\nimport \"./styles/animations.css\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Loader from \"./components/Loader\";\nimport { useSelector } from \"react-redux\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { ErrorBoundary } from \"./components/modern\";\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\n\n// Immediate load components (critical for initial render)\nimport Login from \"./pages/common/Login\";\nimport Register from \"./pages/common/Register\";\nimport Home from \"./pages/common/Home\";\n\n// Lazy load components for better performance\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = /*#__PURE__*/lazy(_c = () => import(\"./pages/user/Quiz\"));\n_c2 = Quiz;\nconst QuizPlay = /*#__PURE__*/lazy(_c3 = () => import(\"./pages/user/Quiz/QuizPlay\"));\n_c4 = QuizPlay;\nconst QuizResult = /*#__PURE__*/lazy(_c5 = () => import(\"./pages/user/Quiz/QuizResult\"));\n_c6 = QuizResult;\nconst Exams = /*#__PURE__*/lazy(_c7 = () => import(\"./pages/admin/Exams\"));\n_c8 = Exams;\nconst AddEditExam = /*#__PURE__*/lazy(_c9 = () => import(\"./pages/admin/Exams/AddEditExam\"));\n_c10 = AddEditExam;\nconst Users = /*#__PURE__*/lazy(_c11 = () => import(\"./pages/admin/Users\"));\n_c12 = Users;\nconst AdminDashboard = /*#__PURE__*/lazy(_c13 = () => import(\"./pages/admin/Dashboard\"));\n_c14 = AdminDashboard;\nconst TrialPage = /*#__PURE__*/lazy(_c15 = () => import(\"./pages/trial/TrialPage\"));\n_c16 = TrialPage;\nconst WriteExam = /*#__PURE__*/lazy(_c17 = () => import(\"./pages/user/WriteExam\"));\n_c18 = WriteExam;\nconst UserReports = /*#__PURE__*/lazy(_c19 = () => import(\"./pages/user/UserReports\"));\n_c20 = UserReports;\nconst AdminReports = /*#__PURE__*/lazy(_c21 = () => import(\"./pages/admin/AdminReports\"));\n_c22 = AdminReports;\nconst StudyMaterial = /*#__PURE__*/lazy(_c23 = () => import(\"./pages/user/StudyMaterial\"));\n_c24 = StudyMaterial;\nconst Ranking = /*#__PURE__*/lazy(_c25 = () => import(\"./pages/user/Ranking\"));\n_c26 = Ranking;\nconst RankingErrorBoundary = /*#__PURE__*/lazy(_c27 = () => import(\"./components/RankingErrorBoundary\"));\n_c28 = RankingErrorBoundary;\nconst Profile = /*#__PURE__*/lazy(_c29 = () => import(\"./pages/common/Profile\"));\n_c30 = Profile;\nconst AboutUs = /*#__PURE__*/lazy(_c31 = () => import(\"./pages/user/AboutUs\"));\n_c32 = AboutUs;\nconst Forum = /*#__PURE__*/lazy(_c33 = () => import(\"./pages/common/Forum\"));\n_c34 = Forum;\nconst Test = /*#__PURE__*/lazy(_c35 = () => import(\"./pages/user/Test\"));\n_c36 = Test;\nconst Hub = /*#__PURE__*/lazy(_c37 = () => import(\"./pages/user/Hub\"));\n_c38 = Hub;\nconst AdminStudyMaterials = /*#__PURE__*/lazy(_c39 = () => import(\"./pages/admin/StudyMaterials\"));\n_c40 = AdminStudyMaterials;\nconst AdminNotifications = /*#__PURE__*/lazy(_c41 = () => import(\"./pages/admin/Notifications/AdminNotifications\"));\n_c42 = AdminNotifications;\nconst AdminForum = /*#__PURE__*/lazy(_c43 = () => import(\"./pages/admin/Forum\"));\n_c44 = AdminForum;\nconst DebugAuth = /*#__PURE__*/lazy(_c45 = () => import(\"./components/DebugAuth\"));\n_c46 = DebugAuth;\nconst RankingDemo = /*#__PURE__*/lazy(_c47 = () => import(\"./components/modern/RankingDemo\"));\n\n// Global error handler for CSS style errors\n_c48 = RankingDemo;\nwindow.addEventListener('error', event => {\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Error caught and handled:', event.message);\n    event.preventDefault();\n    return false;\n  }\n});\n\n// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection', event => {\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\n    event.preventDefault();\n  }\n});\n// Fast loading component for lazy routes\nconst FastLoader = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 font-medium\",\n      children: \"Loading page...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-400 text-sm mt-2\",\n      children: \"Please wait a moment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 68,\n  columnNumber: 3\n}, this);\n_c49 = FastLoader;\nfunction App() {\n  _s();\n  const {\n    loading\n  } = useSelector(state => state.loader);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ranking-demo\",\n            element: /*#__PURE__*/_jsxDEV(RankingDemo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/trial\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(TrialPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/test\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/forum\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Forum, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/plans\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Plans, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/hub\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Hub, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/quiz\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Quiz, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/write-exam/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(WriteExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/result\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(QuizResult, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/play\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(UserReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/study-material\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(StudyMaterial, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/ranking\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(RankingErrorBoundary, {\n                  children: /*#__PURE__*/_jsxDEV(Ranking, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/about-us\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/users\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/add\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/edit/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/study-materials\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminStudyMaterials, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/notifications\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminNotifications, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/forum\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminForum, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/debug\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(DebugAuth, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c50 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50;\n$RefreshReg$(_c, \"Quiz$lazy\");\n$RefreshReg$(_c2, \"Quiz\");\n$RefreshReg$(_c3, \"QuizPlay$lazy\");\n$RefreshReg$(_c4, \"QuizPlay\");\n$RefreshReg$(_c5, \"QuizResult$lazy\");\n$RefreshReg$(_c6, \"QuizResult\");\n$RefreshReg$(_c7, \"Exams$lazy\");\n$RefreshReg$(_c8, \"Exams\");\n$RefreshReg$(_c9, \"AddEditExam$lazy\");\n$RefreshReg$(_c10, \"AddEditExam\");\n$RefreshReg$(_c11, \"Users$lazy\");\n$RefreshReg$(_c12, \"Users\");\n$RefreshReg$(_c13, \"AdminDashboard$lazy\");\n$RefreshReg$(_c14, \"AdminDashboard\");\n$RefreshReg$(_c15, \"TrialPage$lazy\");\n$RefreshReg$(_c16, \"TrialPage\");\n$RefreshReg$(_c17, \"WriteExam$lazy\");\n$RefreshReg$(_c18, \"WriteExam\");\n$RefreshReg$(_c19, \"UserReports$lazy\");\n$RefreshReg$(_c20, \"UserReports\");\n$RefreshReg$(_c21, \"AdminReports$lazy\");\n$RefreshReg$(_c22, \"AdminReports\");\n$RefreshReg$(_c23, \"StudyMaterial$lazy\");\n$RefreshReg$(_c24, \"StudyMaterial\");\n$RefreshReg$(_c25, \"Ranking$lazy\");\n$RefreshReg$(_c26, \"Ranking\");\n$RefreshReg$(_c27, \"RankingErrorBoundary$lazy\");\n$RefreshReg$(_c28, \"RankingErrorBoundary\");\n$RefreshReg$(_c29, \"Profile$lazy\");\n$RefreshReg$(_c30, \"Profile\");\n$RefreshReg$(_c31, \"AboutUs$lazy\");\n$RefreshReg$(_c32, \"AboutUs\");\n$RefreshReg$(_c33, \"Forum$lazy\");\n$RefreshReg$(_c34, \"Forum\");\n$RefreshReg$(_c35, \"Test$lazy\");\n$RefreshReg$(_c36, \"Test\");\n$RefreshReg$(_c37, \"Hub$lazy\");\n$RefreshReg$(_c38, \"Hub\");\n$RefreshReg$(_c39, \"AdminStudyMaterials$lazy\");\n$RefreshReg$(_c40, \"AdminStudyMaterials\");\n$RefreshReg$(_c41, \"AdminNotifications$lazy\");\n$RefreshReg$(_c42, \"AdminNotifications\");\n$RefreshReg$(_c43, \"AdminForum$lazy\");\n$RefreshReg$(_c44, \"AdminForum\");\n$RefreshReg$(_c45, \"DebugAuth$lazy\");\n$RefreshReg$(_c46, \"DebugAuth\");\n$RefreshReg$(_c47, \"RankingDemo$lazy\");\n$RefreshReg$(_c48, \"RankingDemo\");\n$RefreshReg$(_c49, \"FastLoader\");\n$RefreshReg$(_c50, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "ProtectedRoute", "Loader", "useSelector", "ThemeProvider", "Error<PERSON>ou<PERSON><PERSON>", "AdminProtectedRoute", "<PERSON><PERSON>", "Register", "Home", "jsxDEV", "_jsxDEV", "Quiz", "_c", "_c2", "QuizPlay", "_c3", "_c4", "QuizResult", "_c5", "_c6", "<PERSON><PERSON>", "_c7", "_c8", "AddEditExam", "_c9", "_c10", "Users", "_c11", "_c12", "AdminDashboard", "_c13", "_c14", "TrialPage", "_c15", "_c16", "WriteExam", "_c17", "_c18", "UserReports", "_c19", "_c20", "AdminReports", "_c21", "_c22", "StudyMaterial", "_c23", "_c24", "Ranking", "_c25", "_c26", "RankingError<PERSON><PERSON><PERSON>ry", "_c27", "_c28", "Profile", "_c29", "_c30", "AboutUs", "_c31", "_c32", "Forum", "_c33", "_c34", "Test", "_c35", "_c36", "<PERSON><PERSON>", "_c37", "_c38", "AdminStudyMaterials", "_c39", "_c40", "AdminNotifications", "_c41", "_c42", "AdminForum", "_c43", "_c44", "DebugAuth", "_c45", "_c46", "RankingDemo", "_c47", "_c48", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "FastLoader", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c49", "App", "_s", "loading", "state", "loader", "path", "element", "fallback", "Plans", "_c50", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense, lazy } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\r\n\r\n// Immediate load components (critical for initial render)\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport Home from \"./pages/common/Home\";\r\n\r\n// Lazy load components for better performance\r\nconst Quiz = lazy(() => import(\"./pages/user/Quiz\"));\r\nconst QuizPlay = lazy(() => import(\"./pages/user/Quiz/QuizPlay\"));\r\nconst QuizResult = lazy(() => import(\"./pages/user/Quiz/QuizResult\"));\r\nconst Exams = lazy(() => import(\"./pages/admin/Exams\"));\r\nconst AddEditExam = lazy(() => import(\"./pages/admin/Exams/AddEditExam\"));\r\nconst Users = lazy(() => import(\"./pages/admin/Users\"));\r\nconst AdminDashboard = lazy(() => import(\"./pages/admin/Dashboard\"));\r\nconst TrialPage = lazy(() => import(\"./pages/trial/TrialPage\"));\r\nconst WriteExam = lazy(() => import(\"./pages/user/WriteExam\"));\r\nconst UserReports = lazy(() => import(\"./pages/user/UserReports\"));\r\nconst AdminReports = lazy(() => import(\"./pages/admin/AdminReports\"));\r\nconst StudyMaterial = lazy(() => import(\"./pages/user/StudyMaterial\"));\r\nconst Ranking = lazy(() => import(\"./pages/user/Ranking\"));\r\nconst RankingErrorBoundary = lazy(() => import(\"./components/RankingErrorBoundary\"));\r\nconst Profile = lazy(() => import(\"./pages/common/Profile\"));\r\nconst AboutUs = lazy(() => import(\"./pages/user/AboutUs\"));\r\nconst Forum = lazy(() => import(\"./pages/common/Forum\"));\r\nconst Test = lazy(() => import(\"./pages/user/Test\"));\r\n\r\nconst Hub = lazy(() => import(\"./pages/user/Hub\"));\r\nconst AdminStudyMaterials = lazy(() => import(\"./pages/admin/StudyMaterials\"));\r\nconst AdminNotifications = lazy(() => import(\"./pages/admin/Notifications/AdminNotifications\"));\r\nconst AdminForum = lazy(() => import(\"./pages/admin/Forum\"));\r\nconst DebugAuth = lazy(() => import(\"./components/DebugAuth\"));\r\nconst RankingDemo = lazy(() => import(\"./components/modern/RankingDemo\"));\r\n\r\n// Global error handler for CSS style errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// Fast loading component for lazy routes\r\nconst FastLoader = () => (\r\n  <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n    <div className=\"text-center\">\r\n      <div className=\"relative\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n        <div className=\"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"></div>\r\n      </div>\r\n      <p className=\"text-gray-600 font-medium\">Loading page...</p>\r\n      <p className=\"text-gray-400 text-sm mt-2\">Please wait a moment</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        {loading && <Loader />}\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n\r\n          {/* Trial Route (No authentication required) */}\r\n          <Route path=\"/trial\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <TrialPage />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Forum />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n\r\n          <Route\r\n            path=\"/user/plans\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Plans />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Hub />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Quiz />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <WriteExam />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <QuizResult />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <UserReports />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <StudyMaterial />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <RankingErrorBoundary>\r\n                    <Ranking />\r\n                  </RankingErrorBoundary>\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/about-us\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AboutUs />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminDashboard />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Users />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Exams />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminStudyMaterials />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminReports />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/notifications\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminNotifications />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminForum />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/debug\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <DebugAuth />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AAC7C,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAO,gCAAgC;AACvC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAC5C,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,IAAI,MAAM,qBAAqB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,gBAAGf,IAAI,CAAAgB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA/CF,IAAI;AACV,MAAMG,QAAQ,gBAAGlB,IAAI,CAAAmB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAA5DF,QAAQ;AACd,MAAMG,UAAU,gBAAGrB,IAAI,CAAAsB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,GAAA,GAAhEF,UAAU;AAChB,MAAMG,KAAK,gBAAGxB,IAAI,CAAAyB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,GAAA,GAAlDF,KAAK;AACX,MAAMG,WAAW,gBAAG3B,IAAI,CAAA4B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,KAAK,gBAAG9B,IAAI,CAAA+B,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AACX,MAAMG,cAAc,gBAAGjC,IAAI,CAAAkC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA/DF,cAAc;AACpB,MAAMG,SAAS,gBAAGpC,IAAI,CAAAqC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA1DF,SAAS;AACf,MAAMG,SAAS,gBAAGvC,IAAI,CAAAwC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAG1C,IAAI,CAAA2C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAA7DF,WAAW;AACjB,MAAMG,YAAY,gBAAG7C,IAAI,CAAA8C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAhEF,YAAY;AAClB,MAAMG,aAAa,gBAAGhD,IAAI,CAAAiD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAjEF,aAAa;AACnB,MAAMG,OAAO,gBAAGnD,IAAI,CAAAoD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,oBAAoB,gBAAGtD,IAAI,CAAAuD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAA/EF,oBAAoB;AAC1B,MAAMG,OAAO,gBAAGzD,IAAI,CAAA0D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAvDF,OAAO;AACb,MAAMG,OAAO,gBAAG5D,IAAI,CAAA6D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,KAAK,gBAAG/D,IAAI,CAAAgE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAnDF,KAAK;AACX,MAAMG,IAAI,gBAAGlE,IAAI,CAAAmE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AAEV,MAAMG,GAAG,gBAAGrE,IAAI,CAAAsE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAA7CF,GAAG;AACT,MAAMG,mBAAmB,gBAAGxE,IAAI,CAAAyE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,IAAA,GAAzEF,mBAAmB;AACzB,MAAMG,kBAAkB,gBAAG3E,IAAI,CAAA4E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAAC;AAACC,IAAA,GAA1FF,kBAAkB;AACxB,MAAMG,UAAU,gBAAG9E,IAAI,CAAA+E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAvDF,UAAU;AAChB,MAAMG,SAAS,gBAAGjF,IAAI,CAAAkF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAGpF,IAAI,CAAAqF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;;AAEzE;AAAAC,IAAA,GAFMF,WAAW;AAGjBG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACvFC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEJ,KAAK,CAACC,OAAO,CAAC;IAClED,KAAK,CAACK,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvD,IAAIA,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,IAAID,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACrHC,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC;IACrFD,KAAK,CAACK,cAAc,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF;AACA,MAAME,UAAU,GAAGA,CAAA,kBACjBlF,OAAA;EAAKmF,SAAS,EAAC,4FAA4F;EAAAC,QAAA,eACzGpF,OAAA;IAAKmF,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BpF,OAAA;MAAKmF,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBpF,OAAA;QAAKmF,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnGxF,OAAA;QAAKmF,SAAS,EAAC;MAA0F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7G,CAAC,eACNxF,OAAA;MAAGmF,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC5DxF,OAAA;MAAGmF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,IAAA,GAXIP,UAAU;AAahB,SAASQ,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAQ,CAAC,GAAGpG,WAAW,CAAEqG,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACxD,oBACE9F,OAAA,CAACN,aAAa;IAAA0F,QAAA,eACZpF,OAAA,CAACP,aAAa;MAAA2F,QAAA,GACXQ,OAAO,iBAAI5F,OAAA,CAACT,MAAM;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtBxF,OAAA,CAACb,aAAa;QAAAiG,QAAA,eACdpF,OAAA,CAACZ,MAAM;UAAAgG,QAAA,gBAELpF,OAAA,CAACX,KAAK;YAAC0G,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEhG,OAAA,CAACJ,KAAK;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CxF,OAAA,CAACX,KAAK;YAAC0G,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEhG,OAAA,CAACH,QAAQ;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDxF,OAAA,CAACX,KAAK;YAAC0G,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEhG,OAAA,CAACF,IAAI;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCxF,OAAA,CAACX,KAAK;YAAC0G,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEhG,OAAA,CAACsE,WAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGxDxF,OAAA,CAACX,KAAK;YAAC0G,IAAI,EAAC,QAAQ;YAACC,OAAO,eAC1BhG,OAAA,CAACf,QAAQ;cAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjCpF,OAAA,CAACsB,SAAS;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJxF,OAAA,CAACX,KAAK;YAAC0G,IAAI,EAAC,OAAO;YAACC,OAAO,eACzBhG,OAAA,CAACf,QAAQ;cAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjCpF,OAAA,CAACoD,IAAI;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAACiD,KAAK;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,UAAU;YACfC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAAC2C,OAAO;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAAC2C,OAAO;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACkG,KAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAACuD,GAAG;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAACC,IAAI;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAACyB,SAAS;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAACO,UAAU;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAIFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACI,QAAQ;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAAC4B,WAAW;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAACkC,aAAa;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACf,QAAQ;gBAACgH,QAAQ,eAAEjG,OAAA,CAACkF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjCpF,OAAA,CAACwC,oBAAoB;kBAAA4C,QAAA,eACnBpF,OAAA,CAACqC,OAAO;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAAC8C,OAAO;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAACmB,cAAc;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAACgB,KAAK;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAACU,KAAK;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAACa,WAAW;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,uBAAuB;YAC5BC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAACa,WAAW;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAAC0D,mBAAmB;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAAC+B,YAAY;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAAC6D,kBAAkB;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAACgE,UAAU;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFxF,OAAA,CAACX,KAAK;YACJ0G,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLhG,OAAA,CAACV,cAAc;cAAA8F,QAAA,eACbpF,OAAA,CAACL,mBAAmB;gBAAAyF,QAAA,eAClBpF,OAAA,CAACmE,SAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACG,EAAA,CAlRQD,GAAG;EAAA,QACUlG,WAAW;AAAA;AAAA2G,IAAA,GADxBT,GAAG;AAoRZ,eAAeA,GAAG;AAAC,IAAAxF,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAiB,IAAA,EAAAU,IAAA;AAAAC,YAAA,CAAAlG,EAAA;AAAAkG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAA5C,IAAA;AAAA4C,YAAA,CAAA3C,IAAA;AAAA2C,YAAA,CAAAzC,IAAA;AAAAyC,YAAA,CAAAxC,IAAA;AAAAwC,YAAA,CAAAtC,IAAA;AAAAsC,YAAA,CAAArC,IAAA;AAAAqC,YAAA,CAAAnC,IAAA;AAAAmC,YAAA,CAAAlC,IAAA;AAAAkC,YAAA,CAAAhC,IAAA;AAAAgC,YAAA,CAAA/B,IAAA;AAAA+B,YAAA,CAAA7B,IAAA;AAAA6B,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAAX,IAAA;AAAAW,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}