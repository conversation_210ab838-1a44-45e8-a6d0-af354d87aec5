// Simple server test without MongoDB
const express = require('express');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:3001"],
  credentials: true
}));
app.use(express.json());

// Simple routes
app.get('/', (req, res) => {
  res.send('Server is Up!');
});

app.get('/api/health', (req, res) => {
  res.json({
    status: "success",
    message: "Server is running",
    timestamp: new Date().toISOString(),
    port: port,
    database: "Not connected (test mode)"
  });
});

// Simple login test route
app.post('/api/users/login', (req, res) => {
  console.log('Login request received:', req.body);
  res.json({
    message: "Test login successful",
    success: true,
    data: "test-token",
    response: {
      _id: "test-id",
      email: req.body.email,
      isAdmin: false
    }
  });
});

app.listen(port, () => {
  console.log(`✅ Simple test server listening on port ${port}`);
  console.log(`🔗 Health check: http://localhost:${port}/api/health`);
  console.log(`🔗 Test login: POST http://localhost:${port}/api/users/login`);
});
