{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Login\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input } from \"antd\";\nimport React, { useEffect } from \"react\";\nimport './index.css';\nimport Logo from '../../../assets/logo.png';\nimport { useDispatch } from \"react-redux\";\nimport { Link, useNavigate, useLocation } from \"react-router-dom\";\nimport { loginUser } from \"../../../apicalls/users\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { SetUser } from \"../../../redux/usersSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const location = useLocation();\n  const [form] = Form.useForm();\n\n  // Handle pre-filled data from registration\n  useEffect(() => {\n    var _location$state;\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.autoFill) {\n      const {\n        username,\n        password,\n        message: welcomeMessage\n      } = location.state;\n\n      // Pre-fill the form\n      form.setFieldsValue({\n        email: username,\n        // Using email field for username\n        password: password\n      });\n\n      // Show welcome message\n      if (welcomeMessage) {\n        message.success({\n          content: welcomeMessage,\n          duration: 4,\n          style: {\n            marginTop: '20px'\n          }\n        });\n      }\n\n      // Clear the state to prevent re-filling on refresh\n      window.history.replaceState({}, document.title);\n    }\n  }, [location.state, form]);\n  const onFinish = async values => {\n    try {\n      dispatch(ShowLoading());\n      const response = await loginUser(values);\n      dispatch(HideLoading());\n      console.log('Login response:', response);\n      if (response.success) {\n        var _response$response;\n        message.success(response.message);\n        localStorage.setItem(\"token\", response.data);\n\n        // Store user data in localStorage for consistency\n        if (response.response) {\n          localStorage.setItem(\"user\", JSON.stringify(response.response));\n\n          // IMPORTANT: Set user data in Redux immediately to prevent redirect issues\n          dispatch(SetUser(response.response));\n        }\n\n        // Check admin status from response.response (user object)\n        if ((_response$response = response.response) !== null && _response$response !== void 0 && _response$response.isAdmin) {\n          console.log(\"Admin user detected, redirecting to admin dashboard\");\n          navigate(\"/admin/dashboard\");\n        } else {\n          console.log(\"Regular user detected, redirecting to user hub\");\n          navigate(\"/user/hub\");\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Login error:', error);\n      message.error(\"Login failed. Please try again.\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: Logo,\n          alt: \"BrainWave Logo\",\n          className: \"login-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"login-title\",\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"login-subtitle\",\n          children: \"Sign in to your account to continue learning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"vertical\",\n        onFinish: onFinish,\n        className: \"login-form\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"Email\",\n          rules: [{\n            required: true,\n            message: \"Please input your email!\"\n          }, {\n            type: \"email\",\n            message: \"Please enter a valid email!\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"email\",\n            className: \"form-input\",\n            placeholder: \"Enter your email\",\n            autoComplete: \"email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"Password\",\n          rules: [{\n            required: true,\n            message: \"Please input your password!\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            className: \"form-input\",\n            placeholder: \"Enter your password\",\n            autoComplete: \"current-password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"login-btn\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"login-link\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"ii41EUcgcs3j7GLs2QQs2Q2WsCw=\", false, function () {\n  return [useNavigate, useDispatch, useLocation, Form.useForm];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["Form", "message", "Input", "React", "useEffect", "Logo", "useDispatch", "Link", "useNavigate", "useLocation", "loginUser", "HideLoading", "ShowLoading", "SetUser", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "dispatch", "location", "form", "useForm", "_location$state", "state", "autoFill", "username", "password", "welcomeMessage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email", "success", "content", "duration", "style", "marginTop", "window", "history", "replaceState", "document", "title", "onFinish", "values", "response", "console", "log", "_response$response", "localStorage", "setItem", "data", "JSON", "stringify", "isAdmin", "error", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "type", "placeholder", "autoComplete", "Password", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Login/index.js"], "sourcesContent": ["import { Form, message, Input } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport './index.css';\r\nimport Logo from '../../../assets/logo.png';\r\nimport { useDispatch } from \"react-redux\";\r\nimport { Link, useNavigate, useLocation } from \"react-router-dom\";\r\nimport { loginUser } from \"../../../apicalls/users\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { SetUser } from \"../../../redux/usersSlice\";\r\n\r\nfunction Login() {\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const location = useLocation();\r\n  const [form] = Form.useForm();\r\n\r\n  // Handle pre-filled data from registration\r\n  useEffect(() => {\r\n    if (location.state?.autoFill) {\r\n      const { username, password, message: welcomeMessage } = location.state;\r\n\r\n      // Pre-fill the form\r\n      form.setFieldsValue({\r\n        email: username, // Using email field for username\r\n        password: password\r\n      });\r\n\r\n      // Show welcome message\r\n      if (welcomeMessage) {\r\n        message.success({\r\n          content: welcomeMessage,\r\n          duration: 4,\r\n          style: { marginTop: '20px' }\r\n        });\r\n      }\r\n\r\n      // Clear the state to prevent re-filling on refresh\r\n      window.history.replaceState({}, document.title);\r\n    }\r\n  }, [location.state, form]);\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await loginUser(values);\r\n      dispatch(HideLoading());\r\n\r\n      console.log('Login response:', response);\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        localStorage.setItem(\"token\", response.data);\r\n\r\n        // Store user data in localStorage for consistency\r\n        if (response.response) {\r\n          localStorage.setItem(\"user\", JSON.stringify(response.response));\r\n\r\n          // IMPORTANT: Set user data in Redux immediately to prevent redirect issues\r\n          dispatch(SetUser(response.response));\r\n        }\r\n\r\n        // Check admin status from response.response (user object)\r\n        if (response.response?.isAdmin) {\r\n          console.log(\"Admin user detected, redirecting to admin dashboard\");\r\n          navigate(\"/admin/dashboard\");\r\n        } else {\r\n          console.log(\"Regular user detected, redirecting to user hub\");\r\n          navigate(\"/user/hub\");\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Login error:', error);\r\n      message.error(\"Login failed. Please try again.\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login-container\">\r\n      <div className=\"login-card\">\r\n        <div className=\"login-header\">\r\n          <img src={Logo} alt=\"BrainWave Logo\" className=\"login-logo\" />\r\n          <h1 className=\"login-title\">Welcome Back</h1>\r\n          <p className=\"login-subtitle\">Sign in to your account to continue learning</p>\r\n        </div>\r\n\r\n        <Form layout=\"vertical\" onFinish={onFinish} className=\"login-form\">\r\n          <Form.Item\r\n            name=\"email\"\r\n            label=\"Email\"\r\n            rules={[\r\n              { required: true, message: \"Please input your email!\" },\r\n              { type: \"email\", message: \"Please enter a valid email!\" }\r\n            ]}\r\n          >\r\n            <Input\r\n              type=\"email\"\r\n              className=\"form-input\"\r\n              placeholder=\"Enter your email\"\r\n              autoComplete=\"email\"\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item\r\n            name=\"password\"\r\n            label=\"Password\"\r\n            rules={[{ required: true, message: \"Please input your password!\" }]}\r\n          >\r\n            <Input.Password\r\n              className=\"form-input\"\r\n              placeholder=\"Enter your password\"\r\n              autoComplete=\"current-password\"\r\n            />\r\n          </Form.Item>\r\n\r\n          <Form.Item>\r\n            <button type=\"submit\" className=\"login-btn\">\r\n              Sign In\r\n            </button>\r\n          </Form.Item>\r\n        </Form>\r\n\r\n        <div className=\"login-footer\">\r\n          <p>\r\n            Don't have an account?{' '}\r\n            <Link to=\"/register\" className=\"login-link\">\r\n              Create Account\r\n            </Link>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AAC3C,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAO,aAAa;AACpB,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,IAAI,CAAC,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;;EAE7B;EACAlB,SAAS,CAAC,MAAM;IAAA,IAAAmB,eAAA;IACd,KAAAA,eAAA,GAAIH,QAAQ,CAACI,KAAK,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,QAAQ,EAAE;MAC5B,MAAM;QAAEC,QAAQ;QAAEC,QAAQ;QAAE1B,OAAO,EAAE2B;MAAe,CAAC,GAAGR,QAAQ,CAACI,KAAK;;MAEtE;MACAH,IAAI,CAACQ,cAAc,CAAC;QAClBC,KAAK,EAAEJ,QAAQ;QAAE;QACjBC,QAAQ,EAAEA;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIC,cAAc,EAAE;QAClB3B,OAAO,CAAC8B,OAAO,CAAC;UACdC,OAAO,EAAEJ,cAAc;UACvBK,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;MACJ;;MAEA;MACAC,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,CAAC;IACjD;EACF,CAAC,EAAE,CAACpB,QAAQ,CAACI,KAAK,EAAEH,IAAI,CAAC,CAAC;EAE1B,MAAMoB,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACFvB,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+B,QAAQ,GAAG,MAAMjC,SAAS,CAACgC,MAAM,CAAC;MACxCvB,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC;MAEvBiC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACZ,OAAO,EAAE;QAAA,IAAAe,kBAAA;QACpB7C,OAAO,CAAC8B,OAAO,CAACY,QAAQ,CAAC1C,OAAO,CAAC;QACjC8C,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACM,IAAI,CAAC;;QAE5C;QACA,IAAIN,QAAQ,CAACA,QAAQ,EAAE;UACrBI,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACR,QAAQ,CAACA,QAAQ,CAAC,CAAC;;UAE/D;UACAxB,QAAQ,CAACN,OAAO,CAAC8B,QAAQ,CAACA,QAAQ,CAAC,CAAC;QACtC;;QAEA;QACA,KAAAG,kBAAA,GAAIH,QAAQ,CAACA,QAAQ,cAAAG,kBAAA,eAAjBA,kBAAA,CAAmBM,OAAO,EAAE;UAC9BR,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE3B,QAAQ,CAAC,kBAAkB,CAAC;QAC9B,CAAC,MAAM;UACL0B,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;UAC7D3B,QAAQ,CAAC,WAAW,CAAC;QACvB;MACF,CAAC,MAAM;QACLjB,OAAO,CAACoD,KAAK,CAACV,QAAQ,CAAC1C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOoD,KAAK,EAAE;MACdlC,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC;MACvBiC,OAAO,CAACS,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCpD,OAAO,CAACoD,KAAK,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC;EAED,oBACEtC,OAAA;IAAKuC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BxC,OAAA;MAAKuC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBxC,OAAA;QAAKuC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxC,OAAA;UAAKyC,GAAG,EAAEnD,IAAK;UAACoD,GAAG,EAAC,gBAAgB;UAACH,SAAS,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9D9C,OAAA;UAAIuC,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C9C,OAAA;UAAGuC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAA4C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAEN9C,OAAA,CAACf,IAAI;QAAC8D,MAAM,EAAC,UAAU;QAACrB,QAAQ,EAAEA,QAAS;QAACa,SAAS,EAAC,YAAY;QAAAC,QAAA,gBAChExC,OAAA,CAACf,IAAI,CAAC+D,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,OAAO;UACbC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAElE,OAAO,EAAE;UAA2B,CAAC,EACvD;YAAEmE,IAAI,EAAE,OAAO;YAAEnE,OAAO,EAAE;UAA8B,CAAC,CACzD;UAAAsD,QAAA,eAEFxC,OAAA,CAACb,KAAK;YACJkE,IAAI,EAAC,OAAO;YACZd,SAAS,EAAC,YAAY;YACtBe,WAAW,EAAC,kBAAkB;YAC9BC,YAAY,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ9C,OAAA,CAACf,IAAI,CAAC+D,IAAI;UACRC,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,UAAU;UAChBC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElE,OAAO,EAAE;UAA8B,CAAC,CAAE;UAAAsD,QAAA,eAEpExC,OAAA,CAACb,KAAK,CAACqE,QAAQ;YACbjB,SAAS,EAAC,YAAY;YACtBe,WAAW,EAAC,qBAAqB;YACjCC,YAAY,EAAC;UAAkB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ9C,OAAA,CAACf,IAAI,CAAC+D,IAAI;UAAAR,QAAA,eACRxC,OAAA;YAAQqD,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP9C,OAAA;QAAKuC,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxC,OAAA;UAAAwC,QAAA,GAAG,wBACqB,EAAC,GAAG,eAC1BxC,OAAA,CAACR,IAAI;YAACiE,EAAE,EAAC,WAAW;YAAClB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5C,EAAA,CA7HQD,KAAK;EAAA,QACKR,WAAW,EACXF,WAAW,EACXG,WAAW,EACbT,IAAI,CAACsB,OAAO;AAAA;AAAAmD,EAAA,GAJpBzD,KAAK;AA+Hd,eAAeA,KAAK;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}