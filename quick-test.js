const axios = require('axios');

const quickTest = async () => {
  console.log('🚀 BrainWave Quick Test\n');

  // Test Server
  try {
    const response = await axios.get('http://localhost:5000/api/health');
    console.log('✅ Server Status:', response.data.status);
    console.log('✅ Port:', response.data.port);
    console.log('✅ Environment:', response.data.environment);
  } catch (error) {
    console.log('❌ Server not responding');
    return;
  }

  // Test Database
  try {
    const dbResponse = await axios.get('http://localhost:5000/api/test/db');
    console.log('✅ Database Status:', dbResponse.data.status);
  } catch (error) {
    console.log('❌ Database connection issue');
  }

  // Test Plans API
  try {
    const plansResponse = await axios.get('http://localhost:5000/api/plans/get-plans');
    console.log('✅ Plans API:', plansResponse.data.length, 'plans available');
  } catch (error) {
    console.log('❌ Plans API issue');
  }

  console.log('\n🎉 All systems operational!');
  console.log('🌐 Frontend: http://localhost:3000');
  console.log('🔧 Backend: http://localhost:5000');
  console.log('📋 Plans Page: http://localhost:3000/user/plans');
};

quickTest();
