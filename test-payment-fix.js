const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

async function testPaymentConfiguration() {
  console.log('🔧 Testing Payment Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log('✅ ZENOPAY_API_KEY:', process.env.ZENOPAY_API_KEY ? 'SET' : '❌ MISSING');
  console.log('✅ ZENOPAY_ACCOUNT_ID:', process.env.ZENOPAY_ACCOUNT_ID ? 'SET' : '❌ MISSING');
  console.log('✅ ZENOPAY_WEBHOOK_URL:', process.env.ZENOPAY_WEBHOOK_URL || '❌ MISSING');
  console.log('');

  // Test server connection
  console.log('🚀 Testing Server Connection...');
  try {
    const serverResponse = await axios.get('http://localhost:5000');
    console.log('✅ Server is running on port 5000');
  } catch (error) {
    console.log('❌ Server is not running on port 5000');
    console.log('   Please make sure the server is started');
    return;
  }

  // Test ZenoPay API directly
  console.log('\n🧪 Testing ZenoPay API...');
  
  const testData = {
    order_id: `TEST_${Date.now()}`,
    buyer_email: '<EMAIL>',
    buyer_name: 'Test User',
    buyer_phone: '**********',
    amount: 1000
  };

  try {
    console.log('📤 Sending test payment to ZenoPay...');
    console.log('Data:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post('https://zenoapi.com/api/payments/mobile_money_tanzania', testData, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 30000
    });

    console.log('✅ ZenoPay API Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ ZenoPay API Error:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
      
      // Analyze specific errors
      const errorData = error.response.data;
      if (errorData.message && errorData.message.includes('Invalid API key')) {
        console.log('\n🔑 API Key Issue:');
        console.log('- Check if ZENOPAY_API_KEY is correct in .env file');
        console.log('- Verify the API key with ZenoPay support');
      } else if (errorData.message && errorData.message.includes('Imunify360')) {
        console.log('\n🛡️ IP Whitelist Issue:');
        console.log('- Server IP needs to be whitelisted by ZenoPay');
        console.log('- Contact ZenoPay support to whitelist your server IP');
      } else if (errorData.message && errorData.message.includes('Invalid input data')) {
        console.log('\n📝 Data Validation Issue:');
        console.log('- Check phone number format (should be 07xxxxxxxx)');
        console.log('- Verify email format');
        console.log('- Check amount is a positive number');
      }
    } else {
      console.log('Network error:', error.message);
      if (error.code === 'ECONNREFUSED') {
        console.log('\n🌐 Connection Issue:');
        console.log('- Check internet connection');
        console.log('- Verify ZenoPay API endpoint is accessible');
      }
    }
  }

  // Test local payment endpoint
  console.log('\n🔧 Testing Local Payment Endpoint...');
  try {
    // First, we need to get a user token (simulate login)
    console.log('📝 Note: To test the full payment flow, you need to:');
    console.log('1. Login to get an authentication token');
    console.log('2. Use that token to call the payment endpoint');
    console.log('3. Check that user profile has required fields (phone, email, name)');
    
    // Test if payment endpoint is accessible
    const paymentEndpointTest = await axios.get('http://localhost:5000/api/payment/check-payment-status', {
      headers: {
        'Authorization': 'Bearer test-token'
      }
    }).catch(error => {
      if (error.response && error.response.status === 401) {
        console.log('✅ Payment endpoint is accessible (returns 401 for invalid token)');
        return { status: 401 };
      }
      throw error;
    });

  } catch (error) {
    console.log('❌ Payment endpoint test failed:', error.message);
  }

  console.log('\n📋 Summary:');
  console.log('1. Check that server is running on port 5000');
  console.log('2. Verify ZenoPay API key is correct');
  console.log('3. Ensure user profile has phone number, email, and name');
  console.log('4. Check that phone number format is 07xxxxxxxx or 06xxxxxxxx');
  console.log('5. Verify server IP is whitelisted with ZenoPay if needed');
}

// Run the test
testPaymentConfiguration().catch(console.error);
