{"ast": null, "code": "import React,{useState,useEffect}from'react';import{<PERSON>,Button,message,Space,Typography}from'antd';import{getAllSyllabuses,getAvailableSubjects}from'../apicalls/syllabus';import{getSubjectsForLevel}from'../apicalls/aiQuestions';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";import{Fragment as _Fragment}from\"react/jsx-runtime\";const{Text,Title}=Typography;const DebugAuth=()=>{const[authInfo,setAuthInfo]=useState({});const[testResults,setTestResults]=useState({});const[loading,setLoading]=useState(false);useEffect(()=>{checkAuthInfo();},[]);const checkAuthInfo=()=>{const token=localStorage.getItem('token');const user=localStorage.getItem('user');let tokenInfo={};if(token){try{const payload=JSON.parse(atob(token.split('.')[1]));tokenInfo={userId:payload.userId,exp:payload.exp,iat:payload.iat,isExpired:payload.exp<Date.now()/1000};}catch(e){tokenInfo={error:'Invalid token format'};}}setAuthInfo({hasToken:!!token,hasUser:!!user,token:token?\"\".concat(token.substring(0,20),\"...\"):null,user:user?JSON.parse(user):null,tokenInfo});};const testSyllabusAPI=async()=>{setLoading(true);const results={};try{var _syllabusResponse$dat;console.log('🧪 Testing getAllSyllabuses...');const syllabusResponse=await getAllSyllabuses();results.getAllSyllabuses={success:syllabusResponse.success,dataLength:((_syllabusResponse$dat=syllabusResponse.data)===null||_syllabusResponse$dat===void 0?void 0:_syllabusResponse$dat.length)||0,message:syllabusResponse.message,error:syllabusResponse.success?null:syllabusResponse.message};console.log('📚 Syllabus response:',syllabusResponse);}catch(error){var _error$response;results.getAllSyllabuses={success:false,error:error.message,status:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status};console.error('❌ Syllabus error:',error);}try{var _subjectsResponse$dat;console.log('🧪 Testing getAvailableSubjects...');const subjectsResponse=await getAvailableSubjects('primary');results.getAvailableSubjects={success:subjectsResponse.success,dataLength:((_subjectsResponse$dat=subjectsResponse.data)===null||_subjectsResponse$dat===void 0?void 0:_subjectsResponse$dat.length)||0,data:subjectsResponse.data,message:subjectsResponse.message,error:subjectsResponse.success?null:subjectsResponse.message};console.log('📖 Subjects response:',subjectsResponse);}catch(error){var _error$response2;results.getAvailableSubjects={success:false,error:error.message,status:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status};console.error('❌ Subjects error:',error);}try{var _aiSubjectsResponse$d;console.log('🧪 Testing getSubjectsForLevel (AI)...');const aiSubjectsResponse=await getSubjectsForLevel('primary');results.getSubjectsForLevel={success:aiSubjectsResponse.success,dataLength:((_aiSubjectsResponse$d=aiSubjectsResponse.data)===null||_aiSubjectsResponse$d===void 0?void 0:_aiSubjectsResponse$d.length)||0,data:aiSubjectsResponse.data,message:aiSubjectsResponse.message,error:aiSubjectsResponse.success?null:aiSubjectsResponse.message};console.log('🤖 AI Subjects response:',aiSubjectsResponse);}catch(error){var _error$response3;results.getSubjectsForLevel={success:false,error:error.message,status:(_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.status};console.error('❌ AI Subjects error:',error);}setTestResults(results);setLoading(false);};const clearAuth=()=>{localStorage.removeItem('token');localStorage.removeItem('user');message.info('Authentication cleared');checkAuthInfo();};const refreshAuth=()=>{checkAuthInfo();message.info('Authentication info refreshed');};return/*#__PURE__*/_jsxs(\"div\",{style:{padding:'20px',maxWidth:'800px',margin:'0 auto'},children:[/*#__PURE__*/_jsx(Title,{level:2,children:\"\\uD83D\\uDD0D Authentication & API Debug\"}),/*#__PURE__*/_jsxs(Card,{title:\"\\uD83D\\uDD10 Authentication Status\",style:{marginBottom:'20px'},children:[/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Has Token:\"}),\" \",authInfo.hasToken?'✅ Yes':'❌ No']}),/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Has User:\"}),\" \",authInfo.hasUser?'✅ Yes':'❌ No']}),authInfo.token&&/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Token Preview:\"}),\" \",authInfo.token]}),authInfo.user&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"User:\"}),\" \",authInfo.user.name,\" (\",authInfo.user.email,\")\"]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Is Admin:\"}),\" \",authInfo.user.isAdmin?'✅ Yes':'❌ No']})]}),authInfo.tokenInfo&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{children:/*#__PURE__*/_jsx(\"strong\",{children:\"Token Info:\"})}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{children:[\"User ID: \",authInfo.tokenInfo.userId]}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{children:[\"Expires: \",authInfo.tokenInfo.exp?new Date(authInfo.tokenInfo.exp*1000).toLocaleString():'N/A']}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{children:[\"Is Expired: \",authInfo.tokenInfo.isExpired?'❌ Yes':'✅ No']}),authInfo.tokenInfo.error&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Text,{type:\"danger\",children:[\"Error: \",authInfo.tokenInfo.error]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'15px'},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:refreshAuth,children:\"Refresh\"}),/*#__PURE__*/_jsx(Button,{onClick:clearAuth,danger:true,children:\"Clear Auth\"})]})})]}),/*#__PURE__*/_jsx(Card,{title:\"\\uD83E\\uDDEA API Test Results\",style:{marginBottom:'20px'},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:testSyllabusAPI,loading:loading,disabled:!authInfo.hasToken,children:\"Test API Endpoints\"}),Object.keys(testResults).length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Title,{level:4,children:\"Test Results:\"}),Object.entries(testResults).map(_ref=>{let[testName,result]=_ref;return/*#__PURE__*/_jsxs(Card,{size:\"small\",title:testName,style:{marginBottom:'10px'},children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Success:\"}),\" \",result.success?'✅ Yes':'❌ No']}),/*#__PURE__*/_jsx(\"br\",{}),result.dataLength!==undefined&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Data Length:\"}),\" \",result.dataLength]}),/*#__PURE__*/_jsx(\"br\",{})]}),result.data&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Data:\"}),\" \",JSON.stringify(result.data)]}),/*#__PURE__*/_jsx(\"br\",{})]}),result.message&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Message:\"}),\" \",result.message]}),/*#__PURE__*/_jsx(\"br\",{})]}),result.error&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Text,{type:\"danger\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Error:\"}),\" \",result.error]}),/*#__PURE__*/_jsx(\"br\",{})]}),result.status&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Text,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Status:\"}),\" \",result.status]}),/*#__PURE__*/_jsx(\"br\",{})]})]},testName);})]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'15px'},children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{onClick:async()=>{try{const response=await getSubjectsForLevel('primary');console.log('Quick test - Primary subjects:',response);message.info(\"Primary subjects: \".concat(JSON.stringify(response.data)));}catch(error){console.error('Quick test error:',error);message.error(\"Error: \".concat(error.message));}},children:\"Quick Test: Primary Subjects\"}),/*#__PURE__*/_jsx(Button,{onClick:async()=>{try{const response=await getAvailableSubjects('primary');console.log('Quick test - Available subjects:',response);message.info(\"Available subjects: \".concat(JSON.stringify(response.data)));}catch(error){console.error('Quick test error:',error);message.error(\"Error: \".concat(error.message));}},children:\"Quick Test: Syllabus Subjects\"})]})})]})}),/*#__PURE__*/_jsx(Card,{title:\"\\uD83D\\uDCCB Debug Instructions\",children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",children:[/*#__PURE__*/_jsx(Text,{children:\"1. Check if you have a valid authentication token\"}),/*#__PURE__*/_jsx(Text,{children:\"2. Verify the token is not expired\"}),/*#__PURE__*/_jsx(Text,{children:\"3. Test API endpoints to see specific error messages\"}),/*#__PURE__*/_jsx(Text,{children:\"4. Check browser console for detailed error logs\"}),/*#__PURE__*/_jsx(Text,{children:\"5. If token is invalid, try logging out and logging back in\"})]})})]});};export default DebugAuth;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON>", "message", "Space", "Typography", "getAllSyllabuses", "getAvailableSubjects", "getSubjectsForLevel", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Text", "Title", "DebugAuth", "authInfo", "setAuthInfo", "testResults", "setTestResults", "loading", "setLoading", "checkAuthInfo", "token", "localStorage", "getItem", "user", "tokenInfo", "payload", "JSON", "parse", "atob", "split", "userId", "exp", "iat", "isExpired", "Date", "now", "e", "error", "hasToken", "<PERSON><PERSON>ser", "concat", "substring", "testSyllabusAPI", "results", "_syllabusResponse$dat", "console", "log", "syllabusResponse", "success", "dataLength", "data", "length", "_error$response", "status", "response", "_subjectsResponse$dat", "subjectsResponse", "_error$response2", "_aiSubjectsResponse$d", "aiSubjectsResponse", "_error$response3", "clearAuth", "removeItem", "info", "refreshAuth", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "level", "title", "marginBottom", "direction", "width", "name", "email", "isAdmin", "toLocaleString", "type", "marginTop", "onClick", "danger", "disabled", "Object", "keys", "entries", "map", "_ref", "testName", "result", "size", "undefined", "stringify"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/DebugAuth.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Button, message, Space, Typography } from 'antd';\nimport { getAllSyllabuses, getAvailableSubjects } from '../apicalls/syllabus';\nimport { getSubjectsForLevel } from '../apicalls/aiQuestions';\n\nconst { Text, Title } = Typography;\n\nconst DebugAuth = () => {\n  const [authInfo, setAuthInfo] = useState({});\n  const [testResults, setTestResults] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    checkAuthInfo();\n  }, []);\n\n  const checkAuthInfo = () => {\n    const token = localStorage.getItem('token');\n    const user = localStorage.getItem('user');\n    \n    let tokenInfo = {};\n    if (token) {\n      try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        tokenInfo = {\n          userId: payload.userId,\n          exp: payload.exp,\n          iat: payload.iat,\n          isExpired: payload.exp < Date.now() / 1000\n        };\n      } catch (e) {\n        tokenInfo = { error: 'Invalid token format' };\n      }\n    }\n\n    setAuthInfo({\n      hasToken: !!token,\n      hasUser: !!user,\n      token: token ? `${token.substring(0, 20)}...` : null,\n      user: user ? JSON.parse(user) : null,\n      tokenInfo\n    });\n  };\n\n  const testSyllabusAPI = async () => {\n    setLoading(true);\n    const results = {};\n\n    try {\n      console.log('🧪 Testing getAllSyllabuses...');\n      const syllabusResponse = await getAllSyllabuses();\n      results.getAllSyllabuses = {\n        success: syllabusResponse.success,\n        dataLength: syllabusResponse.data?.length || 0,\n        message: syllabusResponse.message,\n        error: syllabusResponse.success ? null : syllabusResponse.message\n      };\n      console.log('📚 Syllabus response:', syllabusResponse);\n    } catch (error) {\n      results.getAllSyllabuses = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ Syllabus error:', error);\n    }\n\n    try {\n      console.log('🧪 Testing getAvailableSubjects...');\n      const subjectsResponse = await getAvailableSubjects('primary');\n      results.getAvailableSubjects = {\n        success: subjectsResponse.success,\n        dataLength: subjectsResponse.data?.length || 0,\n        data: subjectsResponse.data,\n        message: subjectsResponse.message,\n        error: subjectsResponse.success ? null : subjectsResponse.message\n      };\n      console.log('📖 Subjects response:', subjectsResponse);\n    } catch (error) {\n      results.getAvailableSubjects = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ Subjects error:', error);\n    }\n\n    try {\n      console.log('🧪 Testing getSubjectsForLevel (AI)...');\n      const aiSubjectsResponse = await getSubjectsForLevel('primary');\n      results.getSubjectsForLevel = {\n        success: aiSubjectsResponse.success,\n        dataLength: aiSubjectsResponse.data?.length || 0,\n        data: aiSubjectsResponse.data,\n        message: aiSubjectsResponse.message,\n        error: aiSubjectsResponse.success ? null : aiSubjectsResponse.message\n      };\n      console.log('🤖 AI Subjects response:', aiSubjectsResponse);\n    } catch (error) {\n      results.getSubjectsForLevel = {\n        success: false,\n        error: error.message,\n        status: error.response?.status\n      };\n      console.error('❌ AI Subjects error:', error);\n    }\n\n    setTestResults(results);\n    setLoading(false);\n  };\n\n  const clearAuth = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    message.info('Authentication cleared');\n    checkAuthInfo();\n  };\n\n  const refreshAuth = () => {\n    checkAuthInfo();\n    message.info('Authentication info refreshed');\n  };\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>\n      <Title level={2}>🔍 Authentication & API Debug</Title>\n      \n      {/* Authentication Info */}\n      <Card title=\"🔐 Authentication Status\" style={{ marginBottom: '20px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Text><strong>Has Token:</strong> {authInfo.hasToken ? '✅ Yes' : '❌ No'}</Text>\n          <Text><strong>Has User:</strong> {authInfo.hasUser ? '✅ Yes' : '❌ No'}</Text>\n          \n          {authInfo.token && (\n            <Text><strong>Token Preview:</strong> {authInfo.token}</Text>\n          )}\n          \n          {authInfo.user && (\n            <div>\n              <Text><strong>User:</strong> {authInfo.user.name} ({authInfo.user.email})</Text>\n              <br />\n              <Text><strong>Is Admin:</strong> {authInfo.user.isAdmin ? '✅ Yes' : '❌ No'}</Text>\n            </div>\n          )}\n          \n          {authInfo.tokenInfo && (\n            <div>\n              <Text><strong>Token Info:</strong></Text>\n              <br />\n              <Text>User ID: {authInfo.tokenInfo.userId}</Text>\n              <br />\n              <Text>Expires: {authInfo.tokenInfo.exp ? new Date(authInfo.tokenInfo.exp * 1000).toLocaleString() : 'N/A'}</Text>\n              <br />\n              <Text>Is Expired: {authInfo.tokenInfo.isExpired ? '❌ Yes' : '✅ No'}</Text>\n              {authInfo.tokenInfo.error && (\n                <>\n                  <br />\n                  <Text type=\"danger\">Error: {authInfo.tokenInfo.error}</Text>\n                </>\n              )}\n            </div>\n          )}\n        </Space>\n        \n        <div style={{ marginTop: '15px' }}>\n          <Space>\n            <Button onClick={refreshAuth}>Refresh</Button>\n            <Button onClick={clearAuth} danger>Clear Auth</Button>\n          </Space>\n        </div>\n      </Card>\n\n      {/* API Test Results */}\n      <Card title=\"🧪 API Test Results\" style={{ marginBottom: '20px' }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Button \n            type=\"primary\" \n            onClick={testSyllabusAPI} \n            loading={loading}\n            disabled={!authInfo.hasToken}\n          >\n            Test API Endpoints\n          </Button>\n          \n          {Object.keys(testResults).length > 0 && (\n            <div>\n              <Title level={4}>Test Results:</Title>\n\n              {Object.entries(testResults).map(([testName, result]) => (\n                <Card\n                  key={testName}\n                  size=\"small\"\n                  title={testName}\n                  style={{ marginBottom: '10px' }}\n                >\n                  <Text><strong>Success:</strong> {result.success ? '✅ Yes' : '❌ No'}</Text>\n                  <br />\n                  {result.dataLength !== undefined && (\n                    <>\n                      <Text><strong>Data Length:</strong> {result.dataLength}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.data && (\n                    <>\n                      <Text><strong>Data:</strong> {JSON.stringify(result.data)}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.message && (\n                    <>\n                      <Text><strong>Message:</strong> {result.message}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.error && (\n                    <>\n                      <Text type=\"danger\"><strong>Error:</strong> {result.error}</Text>\n                      <br />\n                    </>\n                  )}\n                  {result.status && (\n                    <>\n                      <Text><strong>Status:</strong> {result.status}</Text>\n                      <br />\n                    </>\n                  )}\n                </Card>\n              ))}\n            </div>\n          )}\n\n          {/* Quick Test Buttons */}\n          <div style={{ marginTop: '15px' }}>\n            <Space>\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await getSubjectsForLevel('primary');\n                    console.log('Quick test - Primary subjects:', response);\n                    message.info(`Primary subjects: ${JSON.stringify(response.data)}`);\n                  } catch (error) {\n                    console.error('Quick test error:', error);\n                    message.error(`Error: ${error.message}`);\n                  }\n                }}\n              >\n                Quick Test: Primary Subjects\n              </Button>\n\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await getAvailableSubjects('primary');\n                    console.log('Quick test - Available subjects:', response);\n                    message.info(`Available subjects: ${JSON.stringify(response.data)}`);\n                  } catch (error) {\n                    console.error('Quick test error:', error);\n                    message.error(`Error: ${error.message}`);\n                  }\n                }}\n              >\n                Quick Test: Syllabus Subjects\n              </Button>\n            </Space>\n          </div>\n        </Space>\n      </Card>\n\n      {/* Instructions */}\n      <Card title=\"📋 Debug Instructions\">\n        <Space direction=\"vertical\">\n          <Text>1. Check if you have a valid authentication token</Text>\n          <Text>2. Verify the token is not expired</Text>\n          <Text>3. Test API endpoints to see specific error messages</Text>\n          <Text>4. Check browser console for detailed error logs</Text>\n          <Text>5. If token is invalid, try logging out and logging back in</Text>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default DebugAuth;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,MAAM,CAAEC,OAAO,CAAEC,KAAK,CAAEC,UAAU,KAAQ,MAAM,CAC/D,OAASC,gBAAgB,CAAEC,oBAAoB,KAAQ,sBAAsB,CAC7E,OAASC,mBAAmB,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,gCAAAC,QAAA,IAAAC,SAAA,yBAE9D,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGX,UAAU,CAElC,KAAM,CAAAY,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5C,KAAM,CAACqB,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAClD,KAAM,CAACuB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACdwB,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAC,IAAI,CAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAEzC,GAAI,CAAAE,SAAS,CAAG,CAAC,CAAC,CAClB,GAAIJ,KAAK,CAAE,CACT,GAAI,CACF,KAAM,CAAAK,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACR,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACrDL,SAAS,CAAG,CACVM,MAAM,CAAEL,OAAO,CAACK,MAAM,CACtBC,GAAG,CAAEN,OAAO,CAACM,GAAG,CAChBC,GAAG,CAAEP,OAAO,CAACO,GAAG,CAChBC,SAAS,CAAER,OAAO,CAACM,GAAG,CAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IACxC,CAAC,CACH,CAAE,MAAOC,CAAC,CAAE,CACVZ,SAAS,CAAG,CAAEa,KAAK,CAAE,sBAAuB,CAAC,CAC/C,CACF,CAEAvB,WAAW,CAAC,CACVwB,QAAQ,CAAE,CAAC,CAAClB,KAAK,CACjBmB,OAAO,CAAE,CAAC,CAAChB,IAAI,CACfH,KAAK,CAAEA,KAAK,IAAAoB,MAAA,CAAMpB,KAAK,CAACqB,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,QAAQ,IAAI,CACpDlB,IAAI,CAAEA,IAAI,CAAGG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,CAAG,IAAI,CACpCC,SACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAkB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClCxB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAyB,OAAO,CAAG,CAAC,CAAC,CAElB,GAAI,KAAAC,qBAAA,CACFC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7C,KAAM,CAAAC,gBAAgB,CAAG,KAAM,CAAA9C,gBAAgB,CAAC,CAAC,CACjD0C,OAAO,CAAC1C,gBAAgB,CAAG,CACzB+C,OAAO,CAAED,gBAAgB,CAACC,OAAO,CACjCC,UAAU,CAAE,EAAAL,qBAAA,CAAAG,gBAAgB,CAACG,IAAI,UAAAN,qBAAA,iBAArBA,qBAAA,CAAuBO,MAAM,GAAI,CAAC,CAC9CrD,OAAO,CAAEiD,gBAAgB,CAACjD,OAAO,CACjCuC,KAAK,CAAEU,gBAAgB,CAACC,OAAO,CAAG,IAAI,CAAGD,gBAAgB,CAACjD,OAC5D,CAAC,CACD+C,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEC,gBAAgB,CAAC,CACxD,CAAE,MAAOV,KAAK,CAAE,KAAAe,eAAA,CACdT,OAAO,CAAC1C,gBAAgB,CAAG,CACzB+C,OAAO,CAAE,KAAK,CACdX,KAAK,CAAEA,KAAK,CAACvC,OAAO,CACpBuD,MAAM,EAAAD,eAAA,CAAEf,KAAK,CAACiB,QAAQ,UAAAF,eAAA,iBAAdA,eAAA,CAAgBC,MAC1B,CAAC,CACDR,OAAO,CAACR,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CAC3C,CAEA,GAAI,KAAAkB,qBAAA,CACFV,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CACjD,KAAM,CAAAU,gBAAgB,CAAG,KAAM,CAAAtD,oBAAoB,CAAC,SAAS,CAAC,CAC9DyC,OAAO,CAACzC,oBAAoB,CAAG,CAC7B8C,OAAO,CAAEQ,gBAAgB,CAACR,OAAO,CACjCC,UAAU,CAAE,EAAAM,qBAAA,CAAAC,gBAAgB,CAACN,IAAI,UAAAK,qBAAA,iBAArBA,qBAAA,CAAuBJ,MAAM,GAAI,CAAC,CAC9CD,IAAI,CAAEM,gBAAgB,CAACN,IAAI,CAC3BpD,OAAO,CAAE0D,gBAAgB,CAAC1D,OAAO,CACjCuC,KAAK,CAAEmB,gBAAgB,CAACR,OAAO,CAAG,IAAI,CAAGQ,gBAAgB,CAAC1D,OAC5D,CAAC,CACD+C,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEU,gBAAgB,CAAC,CACxD,CAAE,MAAOnB,KAAK,CAAE,KAAAoB,gBAAA,CACdd,OAAO,CAACzC,oBAAoB,CAAG,CAC7B8C,OAAO,CAAE,KAAK,CACdX,KAAK,CAAEA,KAAK,CAACvC,OAAO,CACpBuD,MAAM,EAAAI,gBAAA,CAAEpB,KAAK,CAACiB,QAAQ,UAAAG,gBAAA,iBAAdA,gBAAA,CAAgBJ,MAC1B,CAAC,CACDR,OAAO,CAACR,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CAC3C,CAEA,GAAI,KAAAqB,qBAAA,CACFb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACrD,KAAM,CAAAa,kBAAkB,CAAG,KAAM,CAAAxD,mBAAmB,CAAC,SAAS,CAAC,CAC/DwC,OAAO,CAACxC,mBAAmB,CAAG,CAC5B6C,OAAO,CAAEW,kBAAkB,CAACX,OAAO,CACnCC,UAAU,CAAE,EAAAS,qBAAA,CAAAC,kBAAkB,CAACT,IAAI,UAAAQ,qBAAA,iBAAvBA,qBAAA,CAAyBP,MAAM,GAAI,CAAC,CAChDD,IAAI,CAAES,kBAAkB,CAACT,IAAI,CAC7BpD,OAAO,CAAE6D,kBAAkB,CAAC7D,OAAO,CACnCuC,KAAK,CAAEsB,kBAAkB,CAACX,OAAO,CAAG,IAAI,CAAGW,kBAAkB,CAAC7D,OAChE,CAAC,CACD+C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEa,kBAAkB,CAAC,CAC7D,CAAE,MAAOtB,KAAK,CAAE,KAAAuB,gBAAA,CACdjB,OAAO,CAACxC,mBAAmB,CAAG,CAC5B6C,OAAO,CAAE,KAAK,CACdX,KAAK,CAAEA,KAAK,CAACvC,OAAO,CACpBuD,MAAM,EAAAO,gBAAA,CAAEvB,KAAK,CAACiB,QAAQ,UAAAM,gBAAA,iBAAdA,gBAAA,CAAgBP,MAC1B,CAAC,CACDR,OAAO,CAACR,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CAEArB,cAAc,CAAC2B,OAAO,CAAC,CACvBzB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAA2C,SAAS,CAAGA,CAAA,GAAM,CACtBxC,YAAY,CAACyC,UAAU,CAAC,OAAO,CAAC,CAChCzC,YAAY,CAACyC,UAAU,CAAC,MAAM,CAAC,CAC/BhE,OAAO,CAACiE,IAAI,CAAC,wBAAwB,CAAC,CACtC5C,aAAa,CAAC,CAAC,CACjB,CAAC,CAED,KAAM,CAAA6C,WAAW,CAAGA,CAAA,GAAM,CACxB7C,aAAa,CAAC,CAAC,CACfrB,OAAO,CAACiE,IAAI,CAAC,+BAA+B,CAAC,CAC/C,CAAC,CAED,mBACExD,KAAA,QAAK0D,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAO,CAAEC,MAAM,CAAE,QAAS,CAAE,CAAAC,QAAA,eACnEhE,IAAA,CAACM,KAAK,EAAC2D,KAAK,CAAE,CAAE,CAAAD,QAAA,CAAC,yCAA6B,CAAO,CAAC,cAGtD9D,KAAA,CAACX,IAAI,EAAC2E,KAAK,CAAC,oCAA0B,CAACN,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAH,QAAA,eACrE9D,KAAA,CAACR,KAAK,EAAC0E,SAAS,CAAC,UAAU,CAACR,KAAK,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnD9D,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAACxD,QAAQ,CAACyB,QAAQ,CAAG,OAAO,CAAG,MAAM,EAAO,CAAC,cAC/E/B,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAACxD,QAAQ,CAAC0B,OAAO,CAAG,OAAO,CAAG,MAAM,EAAO,CAAC,CAE5E1B,QAAQ,CAACO,KAAK,eACbb,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAACxD,QAAQ,CAACO,KAAK,EAAO,CAC7D,CAEAP,QAAQ,CAACU,IAAI,eACZhB,KAAA,QAAA8D,QAAA,eACE9D,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACxD,QAAQ,CAACU,IAAI,CAACoD,IAAI,CAAC,IAAE,CAAC9D,QAAQ,CAACU,IAAI,CAACqD,KAAK,CAAC,GAAC,EAAM,CAAC,cAChFvE,IAAA,QAAK,CAAC,cACNE,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAACxD,QAAQ,CAACU,IAAI,CAACsD,OAAO,CAAG,OAAO,CAAG,MAAM,EAAO,CAAC,EAC/E,CACN,CAEAhE,QAAQ,CAACW,SAAS,eACjBjB,KAAA,QAAA8D,QAAA,eACEhE,IAAA,CAACK,IAAI,EAAA2D,QAAA,cAAChE,IAAA,WAAAgE,QAAA,CAAQ,aAAW,CAAQ,CAAC,CAAM,CAAC,cACzChE,IAAA,QAAK,CAAC,cACNE,KAAA,CAACG,IAAI,EAAA2D,QAAA,EAAC,WAAS,CAACxD,QAAQ,CAACW,SAAS,CAACM,MAAM,EAAO,CAAC,cACjDzB,IAAA,QAAK,CAAC,cACNE,KAAA,CAACG,IAAI,EAAA2D,QAAA,EAAC,WAAS,CAACxD,QAAQ,CAACW,SAAS,CAACO,GAAG,CAAG,GAAI,CAAAG,IAAI,CAACrB,QAAQ,CAACW,SAAS,CAACO,GAAG,CAAG,IAAI,CAAC,CAAC+C,cAAc,CAAC,CAAC,CAAG,KAAK,EAAO,CAAC,cACjHzE,IAAA,QAAK,CAAC,cACNE,KAAA,CAACG,IAAI,EAAA2D,QAAA,EAAC,cAAY,CAACxD,QAAQ,CAACW,SAAS,CAACS,SAAS,CAAG,OAAO,CAAG,MAAM,EAAO,CAAC,CACzEpB,QAAQ,CAACW,SAAS,CAACa,KAAK,eACvB9B,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACEhE,IAAA,QAAK,CAAC,cACNE,KAAA,CAACG,IAAI,EAACqE,IAAI,CAAC,QAAQ,CAAAV,QAAA,EAAC,SAAO,CAACxD,QAAQ,CAACW,SAAS,CAACa,KAAK,EAAO,CAAC,EAC5D,CACH,EACE,CACN,EACI,CAAC,cAERhC,IAAA,QAAK4D,KAAK,CAAE,CAAEe,SAAS,CAAE,MAAO,CAAE,CAAAX,QAAA,cAChC9D,KAAA,CAACR,KAAK,EAAAsE,QAAA,eACJhE,IAAA,CAACR,MAAM,EAACoF,OAAO,CAAEjB,WAAY,CAAAK,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC9ChE,IAAA,CAACR,MAAM,EAACoF,OAAO,CAAEpB,SAAU,CAACqB,MAAM,MAAAb,QAAA,CAAC,YAAU,CAAQ,CAAC,EACjD,CAAC,CACL,CAAC,EACF,CAAC,cAGPhE,IAAA,CAACT,IAAI,EAAC2E,KAAK,CAAC,+BAAqB,CAACN,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAH,QAAA,cAChE9D,KAAA,CAACR,KAAK,EAAC0E,SAAS,CAAC,UAAU,CAACR,KAAK,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,eACnDhE,IAAA,CAACR,MAAM,EACLkF,IAAI,CAAC,SAAS,CACdE,OAAO,CAAEvC,eAAgB,CACzBzB,OAAO,CAAEA,OAAQ,CACjBkE,QAAQ,CAAE,CAACtE,QAAQ,CAACyB,QAAS,CAAA+B,QAAA,CAC9B,oBAED,CAAQ,CAAC,CAERe,MAAM,CAACC,IAAI,CAACtE,WAAW,CAAC,CAACoC,MAAM,CAAG,CAAC,eAClC5C,KAAA,QAAA8D,QAAA,eACEhE,IAAA,CAACM,KAAK,EAAC2D,KAAK,CAAE,CAAE,CAAAD,QAAA,CAAC,eAAa,CAAO,CAAC,CAErCe,MAAM,CAACE,OAAO,CAACvE,WAAW,CAAC,CAACwE,GAAG,CAACC,IAAA,MAAC,CAACC,QAAQ,CAAEC,MAAM,CAAC,CAAAF,IAAA,oBAClDjF,KAAA,CAACX,IAAI,EAEH+F,IAAI,CAAC,OAAO,CACZpB,KAAK,CAAEkB,QAAS,CAChBxB,KAAK,CAAE,CAAEO,YAAY,CAAE,MAAO,CAAE,CAAAH,QAAA,eAEhC9D,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAACqB,MAAM,CAAC1C,OAAO,CAAG,OAAO,CAAG,MAAM,EAAO,CAAC,cAC1E3C,IAAA,QAAK,CAAC,CACLqF,MAAM,CAACzC,UAAU,GAAK2C,SAAS,eAC9BrF,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACE9D,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAACqB,MAAM,CAACzC,UAAU,EAAO,CAAC,cAC9D5C,IAAA,QAAK,CAAC,EACN,CACH,CACAqF,MAAM,CAACxC,IAAI,eACV3C,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACE9D,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAAC3C,IAAI,CAACmE,SAAS,CAACH,MAAM,CAACxC,IAAI,CAAC,EAAO,CAAC,cACjE7C,IAAA,QAAK,CAAC,EACN,CACH,CACAqF,MAAM,CAAC5F,OAAO,eACbS,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACE9D,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAACqB,MAAM,CAAC5F,OAAO,EAAO,CAAC,cACvDO,IAAA,QAAK,CAAC,EACN,CACH,CACAqF,MAAM,CAACrD,KAAK,eACX9B,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACE9D,KAAA,CAACG,IAAI,EAACqE,IAAI,CAAC,QAAQ,CAAAV,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACqB,MAAM,CAACrD,KAAK,EAAO,CAAC,cACjEhC,IAAA,QAAK,CAAC,EACN,CACH,CACAqF,MAAM,CAACrC,MAAM,eACZ9C,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACE9D,KAAA,CAACG,IAAI,EAAA2D,QAAA,eAAChE,IAAA,WAAAgE,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACqB,MAAM,CAACrC,MAAM,EAAO,CAAC,cACrDhD,IAAA,QAAK,CAAC,EACN,CACH,GApCIoF,QAqCD,CAAC,EACR,CAAC,EACC,CACN,cAGDpF,IAAA,QAAK4D,KAAK,CAAE,CAAEe,SAAS,CAAE,MAAO,CAAE,CAAAX,QAAA,cAChC9D,KAAA,CAACR,KAAK,EAAAsE,QAAA,eACJhE,IAAA,CAACR,MAAM,EACLoF,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CACF,KAAM,CAAA3B,QAAQ,CAAG,KAAM,CAAAnD,mBAAmB,CAAC,SAAS,CAAC,CACrD0C,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEQ,QAAQ,CAAC,CACvDxD,OAAO,CAACiE,IAAI,sBAAAvB,MAAA,CAAsBd,IAAI,CAACmE,SAAS,CAACvC,QAAQ,CAACJ,IAAI,CAAC,CAAE,CAAC,CACpE,CAAE,MAAOb,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzCvC,OAAO,CAACuC,KAAK,WAAAG,MAAA,CAAWH,KAAK,CAACvC,OAAO,CAAE,CAAC,CAC1C,CACF,CAAE,CAAAuE,QAAA,CACH,8BAED,CAAQ,CAAC,cAEThE,IAAA,CAACR,MAAM,EACLoF,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CACF,KAAM,CAAA3B,QAAQ,CAAG,KAAM,CAAApD,oBAAoB,CAAC,SAAS,CAAC,CACtD2C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEQ,QAAQ,CAAC,CACzDxD,OAAO,CAACiE,IAAI,wBAAAvB,MAAA,CAAwBd,IAAI,CAACmE,SAAS,CAACvC,QAAQ,CAACJ,IAAI,CAAC,CAAE,CAAC,CACtE,CAAE,MAAOb,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzCvC,OAAO,CAACuC,KAAK,WAAAG,MAAA,CAAWH,KAAK,CAACvC,OAAO,CAAE,CAAC,CAC1C,CACF,CAAE,CAAAuE,QAAA,CACH,+BAED,CAAQ,CAAC,EACJ,CAAC,CACL,CAAC,EACD,CAAC,CACJ,CAAC,cAGPhE,IAAA,CAACT,IAAI,EAAC2E,KAAK,CAAC,iCAAuB,CAAAF,QAAA,cACjC9D,KAAA,CAACR,KAAK,EAAC0E,SAAS,CAAC,UAAU,CAAAJ,QAAA,eACzBhE,IAAA,CAACK,IAAI,EAAA2D,QAAA,CAAC,mDAAiD,CAAM,CAAC,cAC9DhE,IAAA,CAACK,IAAI,EAAA2D,QAAA,CAAC,oCAAkC,CAAM,CAAC,cAC/ChE,IAAA,CAACK,IAAI,EAAA2D,QAAA,CAAC,sDAAoD,CAAM,CAAC,cACjEhE,IAAA,CAACK,IAAI,EAAA2D,QAAA,CAAC,kDAAgD,CAAM,CAAC,cAC7DhE,IAAA,CAACK,IAAI,EAAA2D,QAAA,CAAC,6DAA2D,CAAM,CAAC,EACnE,CAAC,CACJ,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}