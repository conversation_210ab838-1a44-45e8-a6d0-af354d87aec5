const mongoose = require('mongoose');
const User = require('./server/models/userModel');
const Subscription = require('./server/models/subscriptionModel');
const Plan = require('./server/models/planModel');
require('dotenv').config({ path: './server/.env' });

const activateLucySubscription = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL, {
      bufferCommands: false,
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
    });
    console.log('✅ Connected to MongoDB');

    // Find Lucy <PERSON>sha
    const lucyUser = await User.findOne({ name: '<PERSON>' });
    
    if (!lucyUser) {
      console.log('❌ Lucy Mosha not found');
      return;
    }

    console.log('\n👤 <PERSON> Mosha found:');
    console.log('- ID:', lucyUser._id);
    console.log('- Name:', lucyUser.name);
    console.log('- Phone:', lucyUser.phoneNumber);
    console.log('- Current Status:', lucyUser.subscriptionStatus);
    console.log('- Payment Required:', lucyUser.paymentRequired);

    // Find her pending subscription
    const pendingSubscription = await Subscription.findOne({
      user: lucyUser._id,
      paymentStatus: 'pending'
    }).populate('activePlan');

    if (!pendingSubscription) {
      console.log('\n❌ No pending subscription found for Lucy Mosha');
      
      // Check if she already has an active subscription
      const activeSubscription = await Subscription.findOne({
        user: lucyUser._id,
        paymentStatus: 'paid',
        status: 'active'
      }).populate('activePlan');
      
      if (activeSubscription) {
        console.log('✅ Lucy already has an active subscription!');
        console.log('- Plan:', activeSubscription.activePlan?.title);
        console.log('- End Date:', activeSubscription.endDate);
        
        // Just update user status to remove payment requirement
        lucyUser.paymentRequired = false;
        lucyUser.subscriptionStatus = 'active';
        await lucyUser.save();
        
        console.log('✅ Updated user status to remove payment requirement');
        return;
      }
      
      console.log('❌ No subscription records found. Creating a new one...');
      
      // Get a default plan (Basic or first available plan)
      const defaultPlan = await Plan.findOne({ status: true }).sort({ discountedPrice: 1 });
      
      if (!defaultPlan) {
        console.log('❌ No plans available');
        return;
      }
      
      // Create new subscription
      const newSubscription = new Subscription({
        user: lucyUser._id,
        activePlan: defaultPlan._id,
        paymentStatus: 'paid',
        status: 'active',
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + (defaultPlan.duration * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
        paymentHistory: [{
          orderId: `MANUAL_ACTIVATION_${Date.now()}`,
          plan: defaultPlan._id,
          amount: defaultPlan.discountedPrice,
          paymentStatus: 'paid',
          paymentDate: new Date().toISOString().split('T')[0],
          referenceId: `MANUAL_${Date.now()}`
        }]
      });
      
      await newSubscription.save();
      console.log('✅ Created new active subscription for Lucy');
      
    } else {
      console.log('\n💳 Found pending subscription:');
      console.log('- Plan:', pendingSubscription.activePlan?.title);
      console.log('- Amount:', pendingSubscription.activePlan?.discountedPrice);
      console.log('- Created:', pendingSubscription.createdAt);
      
      // Mark the payment as successful
      console.log('\n🔄 Activating subscription...');
      
      // Update payment history
      if (pendingSubscription.paymentHistory.length > 0) {
        const latestPayment = pendingSubscription.paymentHistory[pendingSubscription.paymentHistory.length - 1];
        latestPayment.paymentStatus = 'paid';
        latestPayment.referenceId = `MANUAL_ACTIVATION_${Date.now()}`;
      }
      
      // Update subscription status
      pendingSubscription.paymentStatus = 'paid';
      pendingSubscription.status = 'active';
      
      // Set subscription dates
      const startDate = new Date();
      const endDate = new Date();
      const planDuration = pendingSubscription.activePlan?.duration || 1;
      endDate.setMonth(endDate.getMonth() + planDuration);
      
      pendingSubscription.startDate = startDate.toISOString().split('T')[0];
      pendingSubscription.endDate = endDate.toISOString().split('T')[0];
      
      await pendingSubscription.save();
      console.log('✅ Subscription activated successfully!');
    }

    // Update user account
    console.log('\n🔄 Updating user account...');
    
    const activeSubscription = await Subscription.findOne({
      user: lucyUser._id,
      paymentStatus: 'paid',
      status: 'active'
    }).populate('activePlan');
    
    if (activeSubscription) {
      lucyUser.subscriptionStatus = 'active';
      lucyUser.subscriptionStartDate = new Date(activeSubscription.startDate);
      lucyUser.subscriptionEndDate = new Date(activeSubscription.endDate);
      lucyUser.subscriptionPlan = activeSubscription.activePlan?.title?.toLowerCase().includes('basic') ? 'basic' : 
                                  activeSubscription.activePlan?.title?.toLowerCase().includes('premium') ? 'premium' : 'basic';
      lucyUser.paymentRequired = false;
      
      // Add email if missing
      if (!lucyUser.email || lucyUser.email.trim() === '') {
        lucyUser.email = `lucy.mosha.${lucyUser._id}@brainwave.temp`;
      }
      
      await lucyUser.save();
      
      console.log('✅ User account updated successfully!');
      
      console.log('\n🎉 Final Status:');
      console.log('- Subscription Status:', lucyUser.subscriptionStatus);
      console.log('- Payment Required:', lucyUser.paymentRequired);
      console.log('- Plan:', lucyUser.subscriptionPlan);
      console.log('- Start Date:', activeSubscription.startDate);
      console.log('- End Date:', activeSubscription.endDate);
      console.log('- Email:', lucyUser.email);
      
      console.log('\n✅ Lucy Mosha can now access the platform without payment restrictions!');
      console.log('📱 She should refresh her browser or log out and log back in to see the changes.');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
};

activateLucySubscription();
