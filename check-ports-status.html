<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Port Status Check</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .service-card {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s;
        }
        .service-card.online {
            border-color: #10b981;
            background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
        }
        .service-card.offline {
            border-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2, #fef7f7);
        }
        .service-card.checking {
            border-color: #f59e0b;
            background: linear-gradient(135deg, #fef3c7, #fde68a);
        }
        .service-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-indicator.online {
            background: #10b981;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
        }
        .status-indicator.offline {
            background: #ef4444;
        }
        .status-indicator.checking {
            background: #f59e0b;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .service-details {
            margin: 15px 0;
        }
        .service-details div {
            margin: 5px 0;
            font-size: 0.9rem;
        }
        .test-btn {
            background: #4f46e5;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        .test-btn:hover {
            background: #4338ca;
        }
        .test-btn.success {
            background: #10b981;
        }
        .test-btn.danger {
            background: #ef4444;
        }
        .refresh-btn {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px 0;
            font-size: 1rem;
        }
        .refresh-btn:hover {
            background: #2563eb;
        }
        .terminal-info {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .summary {
            background: #e0f2fe;
            border: 1px solid #0284c7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Port Status Check</h1>
        <p>Checking the status of all services running on different terminals and ports</p>

        <button class="refresh-btn" onclick="checkAllServices()">🔄 Refresh All Services</button>

        <div class="service-grid">
            <!-- MongoDB Database -->
            <div class="service-card checking" id="mongodb-card">
                <div class="service-title">
                    <span class="status-indicator checking" id="mongodb-indicator"></span>
                    🗄️ MongoDB Database
                </div>
                <div class="service-details">
                    <div><strong>Port:</strong> 27017</div>
                    <div><strong>Terminal:</strong> Terminal 35</div>
                    <div><strong>Command:</strong> mongod --dbpath "C:\data\db"</div>
                    <div><strong>Status:</strong> <span id="mongodb-status">Checking...</span></div>
                </div>
                <div>
                    <button class="test-btn" onclick="testMongoDB()">🔍 Test Connection</button>
                </div>
            </div>

            <!-- Node.js Server -->
            <div class="service-card checking" id="server-card">
                <div class="service-title">
                    <span class="status-indicator checking" id="server-indicator"></span>
                    🚀 Node.js Server
                </div>
                <div class="service-details">
                    <div><strong>Port:</strong> 5000</div>
                    <div><strong>Terminal:</strong> Terminal 36</div>
                    <div><strong>Command:</strong> node server.js</div>
                    <div><strong>Status:</strong> <span id="server-status">Checking...</span></div>
                </div>
                <div>
                    <a href="http://localhost:5000" target="_blank" class="test-btn">🌐 Open Server</a>
                    <a href="http://localhost:5000/api/plans" target="_blank" class="test-btn">📋 Test API</a>
                </div>
            </div>

            <!-- React Client -->
            <div class="service-card checking" id="client-card">
                <div class="service-title">
                    <span class="status-indicator checking" id="client-indicator"></span>
                    ⚛️ React Client
                </div>
                <div class="service-details">
                    <div><strong>Port:</strong> 3000</div>
                    <div><strong>Terminal:</strong> Terminal 38</div>
                    <div><strong>Command:</strong> npx react-scripts start</div>
                    <div><strong>Status:</strong> <span id="client-status">Checking...</span></div>
                </div>
                <div>
                    <a href="http://localhost:3000" target="_blank" class="test-btn">🌐 Open App</a>
                    <a href="http://localhost:3000/subscription" target="_blank" class="test-btn">📋 Subscription</a>
                    <a href="http://localhost:3000/login" target="_blank" class="test-btn">🔐 Login</a>
                </div>
            </div>
        </div>

        <div class="summary" id="summary">
            <h3>📊 Service Summary</h3>
            <div id="summary-content">Checking all services...</div>
        </div>

        <div class="terminal-info">
            <h4>🖥️ Terminal Information</h4>
            <div><strong>Terminal 35:</strong> MongoDB Database (Port 27017)</div>
            <div><strong>Terminal 36:</strong> Node.js Server (Port 5000)</div>
            <div><strong>Terminal 38:</strong> React Client (Port 3000)</div>
        </div>

        <div style="margin-top: 30px;">
            <h3>🧪 Quick Tests</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <a href="http://localhost:3000" target="_blank" class="test-btn success">🏠 Home Page</a>
                <a href="http://localhost:3000/login" target="_blank" class="test-btn success">🔐 Login</a>
                <a href="http://localhost:3000/subscription" target="_blank" class="test-btn success">📋 Subscription</a>
                <a href="http://localhost:5000/api/plans" target="_blank" class="test-btn success">📋 Plans API</a>
                <a href="http://localhost:5000" target="_blank" class="test-btn success">🚀 Server Status</a>
            </div>
        </div>
    </div>

    <script>
        async function testService(url, name) {
            try {
                const response = await fetch(url, { 
                    method: 'GET',
                    timeout: 5000 
                });
                return {
                    name,
                    status: response.ok ? 'online' : 'error',
                    statusCode: response.status,
                    message: response.ok ? 'Service is running' : `HTTP ${response.status}`
                };
            } catch (error) {
                return {
                    name,
                    status: 'offline',
                    message: error.message.includes('fetch') ? 'Service not responding' : error.message
                };
            }
        }

        async function testMongoDB() {
            // MongoDB is harder to test directly from browser, so we'll test if server can connect to it
            const result = await testService('http://localhost:5000/api/plans', 'MongoDB (via Server)');
            updateServiceCard('mongodb', result.status, result.message);
        }

        async function checkAllServices() {
            // Reset all to checking state
            ['mongodb', 'server', 'client'].forEach(service => {
                updateServiceCard(service, 'checking', 'Checking...');
            });

            // Test Server
            const serverResult = await testService('http://localhost:5000', 'Node.js Server');
            updateServiceCard('server', serverResult.status, serverResult.message);

            // Test Client
            const clientResult = await testService('http://localhost:3000', 'React Client');
            updateServiceCard('client', clientResult.status, clientResult.message);

            // Test MongoDB (via server API)
            const mongoResult = await testService('http://localhost:5000/api/plans', 'MongoDB');
            updateServiceCard('mongodb', mongoResult.status, 
                mongoResult.status === 'online' ? 'Database connected (via server)' : 'Database connection issue');

            // Update summary
            updateSummary([serverResult, clientResult, mongoResult]);
        }

        function updateServiceCard(service, status, message) {
            const card = document.getElementById(`${service}-card`);
            const indicator = document.getElementById(`${service}-indicator`);
            const statusSpan = document.getElementById(`${service}-status`);

            // Update card class
            card.className = `service-card ${status}`;
            
            // Update indicator
            indicator.className = `status-indicator ${status}`;
            
            // Update status text
            statusSpan.textContent = message;
        }

        function updateSummary(results) {
            const onlineCount = results.filter(r => r.status === 'online').length;
            const totalCount = results.length;
            
            let summaryHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <span><strong>Services Online:</strong> ${onlineCount}/${totalCount}</span>
                    <span style="font-size: 1.5rem;">${onlineCount === totalCount ? '✅' : '⚠️'}</span>
                </div>
            `;
            
            results.forEach(result => {
                const icon = result.status === 'online' ? '✅' : '❌';
                summaryHTML += `<div>${icon} ${result.name}: ${result.message}</div>`;
            });

            if (onlineCount === totalCount) {
                summaryHTML += `
                    <div style="margin-top: 15px; padding: 10px; background: #d1fae5; border-radius: 5px; color: #065f46;">
                        🎉 All services are running successfully! The application is ready to use.
                    </div>
                `;
            }

            document.getElementById('summary-content').innerHTML = summaryHTML;
        }

        // Auto-check on page load
        window.addEventListener('load', () => {
            setTimeout(checkAllServices, 1000);
        });

        // Auto-refresh every 30 seconds
        setInterval(checkAllServices, 30000);
    </script>
</body>
</html>
