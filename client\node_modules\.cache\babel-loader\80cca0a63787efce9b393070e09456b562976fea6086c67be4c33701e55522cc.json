{"ast": null, "code": "import React,{useState}from'react';import{motion}from'framer-motion';import UserRankingList from'./UserRankingList';import UserRankingCard from'./UserRankingCard';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const RankingDemo=()=>{const[layout,setLayout]=useState('horizontal');const[size,setSize]=useState('medium');// Sample user data\nconst sampleUsers=[{userId:'1',name:'<PERSON>',profilePicture:'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'active',totalPoints:2850,passedExamsCount:15,quizzesTaken:23,score:2850,rank:1},{userId:'2',name:'<PERSON>',profilePicture:'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'free',totalPoints:2720,passedExamsCount:12,quizzesTaken:20,score:2720,rank:2},{userId:'3',name:'Carol Davis',profilePicture:'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'premium',totalPoints:2650,passedExamsCount:14,quizzesTaken:19,score:2650,rank:3},{userId:'4',name:'David Wilson',profilePicture:'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'expired',totalPoints:2400,passedExamsCount:10,quizzesTaken:18,score:2400,rank:4},{userId:'5',name:'Emma Brown',profilePicture:'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'active',totalPoints:2350,passedExamsCount:11,quizzesTaken:16,score:2350,rank:5},{userId:'6',name:'Frank Miller',profilePicture:'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'free',totalPoints:2200,passedExamsCount:9,quizzesTaken:15,score:2200,rank:6},{userId:'7',name:'Grace Lee',profilePicture:'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'premium',totalPoints:2100,passedExamsCount:8,quizzesTaken:14,score:2100,rank:7},{userId:'8',name:'Henry Taylor',profilePicture:'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',subscriptionStatus:'free',totalPoints:1950,passedExamsCount:7,quizzesTaken:13,score:1950,rank:8}];return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 py-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:\"text-center mb-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl font-bold text-gray-900 mb-4\",children:\"Modern User Ranking Component\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 max-w-3xl mx-auto\",children:\"A beautiful, responsive ranking component with Instagram-style profile circles, premium user highlighting, and multiple layout options.\"})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{delay:0.2},className:\"bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-200\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900 mb-4\",children:\"Customization Options\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Layout Style\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:['horizontal','vertical','grid'].map(layoutOption=>/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{id:\"layout-\".concat(layoutOption),type:\"radio\",name:\"layout\",value:layoutOption,checked:layout===layoutOption,onChange:e=>setLayout(e.target.value),className:\"mr-2 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"capitalize\",children:layoutOption})]},layoutOption))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Component Size\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:['small','medium','large'].map(sizeOption=>/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{id:\"size-\".concat(sizeOption),type:\"radio\",name:\"size\",value:sizeOption,checked:size===sizeOption,onChange:e=>setSize(e.target.value),className:\"mr-2 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{className:\"capitalize\",children:sizeOption})]},sizeOption))})]})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.3},className:\"mb-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 mb-6\",children:\"Individual Card Examples\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl p-6 border border-gray-200\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"Premium User (Gold Glow)\"}),/*#__PURE__*/_jsx(UserRankingCard,{user:sampleUsers[0],rank:1,isCurrentUser:false,layout:\"horizontal\",size:size,showStats:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl p-6 border border-gray-200\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 mb-4\",children:\"Current User (Highlighted)\"}),/*#__PURE__*/_jsx(UserRankingCard,{user:sampleUsers[1],rank:2,isCurrentUser:true,layout:\"horizontal\",size:size,showStats:true})]})]})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.4},children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 mb-6\",children:\"Complete Ranking List\"}),/*#__PURE__*/_jsx(UserRankingList,{users:sampleUsers,currentUserId:\"3\"// Carol Davis as current user\n,layout:layout,size:size,showSearch:true,showFilters:true,showStats:true})]}),/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.5},className:\"mt-12 bg-white rounded-xl p-8 border border-gray-200\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900 mb-6\",children:\"Key Features\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:\"\\uD83C\\uDFA8 Premium Highlighting\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Gold gradient glow for premium users with vibrant visual distinction\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:\"\\uD83D\\uDCF1 Responsive Design\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Adapts perfectly to mobile, tablet, and desktop screens\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:\"\\uD83D\\uDD0D Search & Filter\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Real-time search and filtering by subscription status\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:\"\\u2728 Smooth Animations\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Framer Motion powered animations for engaging interactions\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:\"\\uD83C\\uDFC6 Rank Indicators\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Special icons and colors for top performers\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold text-gray-900\",children:\"\\uD83C\\uDFAF Current User Focus\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Automatic highlighting and scroll-to functionality\"})]})]})]})]})});};export default RankingDemo;", "map": {"version": 3, "names": ["React", "useState", "motion", "UserRankingList", "UserRankingCard", "jsx", "_jsx", "jsxs", "_jsxs", "RankingDemo", "layout", "setLayout", "size", "setSize", "sampleUsers", "userId", "name", "profilePicture", "subscriptionStatus", "totalPoints", "passedExamsCount", "quizzesTaken", "score", "rank", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "delay", "map", "layoutOption", "id", "concat", "type", "value", "checked", "onChange", "e", "target", "sizeOption", "user", "isCurrentUser", "showStats", "users", "currentUserId", "showSearch", "showFilters"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/RankingDemo.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport UserRankingList from './UserRankingList';\nimport UserRankingCard from './UserRankingCard';\n\nconst RankingDemo = () => {\n    const [layout, setLayout] = useState('horizontal');\n    const [size, setSize] = useState('medium');\n\n    // Sample user data\n    const sampleUsers = [\n        {\n            userId: '1',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'active',\n            totalPoints: 2850,\n            passedExamsCount: 15,\n            quizzesTaken: 23,\n            score: 2850,\n            rank: 1\n        },\n        {\n            userId: '2',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 2720,\n            passedExamsCount: 12,\n            quizzesTaken: 20,\n            score: 2720,\n            rank: 2\n        },\n        {\n            userId: '3',\n            name: '<PERSON>',\n            profilePicture: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'premium',\n            totalPoints: 2650,\n            passedExamsCount: 14,\n            quizzesTaken: 19,\n            score: 2650,\n            rank: 3\n        },\n        {\n            userId: '4',\n            name: 'David Wilson',\n            profilePicture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'expired',\n            totalPoints: 2400,\n            passedExamsCount: 10,\n            quizzesTaken: 18,\n            score: 2400,\n            rank: 4\n        },\n        {\n            userId: '5',\n            name: 'Emma Brown',\n            profilePicture: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'active',\n            totalPoints: 2350,\n            passedExamsCount: 11,\n            quizzesTaken: 16,\n            score: 2350,\n            rank: 5\n        },\n        {\n            userId: '6',\n            name: 'Frank Miller',\n            profilePicture: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 2200,\n            passedExamsCount: 9,\n            quizzesTaken: 15,\n            score: 2200,\n            rank: 6\n        },\n        {\n            userId: '7',\n            name: 'Grace Lee',\n            profilePicture: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'premium',\n            totalPoints: 2100,\n            passedExamsCount: 8,\n            quizzesTaken: 14,\n            score: 2100,\n            rank: 7\n        },\n        {\n            userId: '8',\n            name: 'Henry Taylor',\n            profilePicture: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n            subscriptionStatus: 'free',\n            totalPoints: 1950,\n            passedExamsCount: 7,\n            quizzesTaken: 13,\n            score: 1950,\n            rank: 8\n        }\n    ];\n\n    return (\n        <div className=\"min-h-screen bg-gray-50 py-8\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n                {/* Header */}\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-center mb-8\"\n                >\n                    <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n                        Modern User Ranking Component\n                    </h1>\n                    <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n                        A beautiful, responsive ranking component with Instagram-style profile circles, \n                        premium user highlighting, and multiple layout options.\n                    </p>\n                </motion.div>\n\n                {/* Controls */}\n                <motion.div\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.2 }}\n                    className=\"bg-white rounded-xl p-6 mb-8 shadow-sm border border-gray-200\"\n                >\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Customization Options</h2>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        {/* Layout Options */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Layout Style\n                            </label>\n                            <div className=\"space-y-2\">\n                                {['horizontal', 'vertical', 'grid'].map((layoutOption) => (\n                                    <label key={layoutOption} className=\"flex items-center\">\n                                        <input\n                                            id={`layout-${layoutOption}`}\n                                            type=\"radio\"\n                                            name=\"layout\"\n                                            value={layoutOption}\n                                            checked={layout === layoutOption}\n                                            onChange={(e) => setLayout(e.target.value)}\n                                            className=\"mr-2 text-blue-600\"\n                                        />\n                                        <span className=\"capitalize\">{layoutOption}</span>\n                                    </label>\n                                ))}\n                            </div>\n                        </div>\n\n                        {/* Size Options */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                                Component Size\n                            </label>\n                            <div className=\"space-y-2\">\n                                {['small', 'medium', 'large'].map((sizeOption) => (\n                                    <label key={sizeOption} className=\"flex items-center\">\n                                        <input\n                                            id={`size-${sizeOption}`}\n                                            type=\"radio\"\n                                            name=\"size\"\n                                            value={sizeOption}\n                                            checked={size === sizeOption}\n                                            onChange={(e) => setSize(e.target.value)}\n                                            className=\"mr-2 text-blue-600\"\n                                        />\n                                        <span className=\"capitalize\">{sizeOption}</span>\n                                    </label>\n                                ))}\n                            </div>\n                        </div>\n                    </div>\n                </motion.div>\n\n                {/* Individual Card Examples */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.3 }}\n                    className=\"mb-8\"\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Individual Card Examples</h2>\n                    \n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                        {/* Premium User Card */}\n                        <div className=\"bg-white rounded-xl p-6 border border-gray-200\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Premium User (Gold Glow)</h3>\n                            <UserRankingCard\n                                user={sampleUsers[0]}\n                                rank={1}\n                                isCurrentUser={false}\n                                layout=\"horizontal\"\n                                size={size}\n                                showStats={true}\n                            />\n                        </div>\n\n                        {/* Current User Card */}\n                        <div className=\"bg-white rounded-xl p-6 border border-gray-200\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Current User (Highlighted)</h3>\n                            <UserRankingCard\n                                user={sampleUsers[1]}\n                                rank={2}\n                                isCurrentUser={true}\n                                layout=\"horizontal\"\n                                size={size}\n                                showStats={true}\n                            />\n                        </div>\n                    </div>\n                </motion.div>\n\n                {/* Full Ranking List */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.4 }}\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Complete Ranking List</h2>\n                    \n                    <UserRankingList\n                        users={sampleUsers}\n                        currentUserId=\"3\" // Carol Davis as current user\n                        layout={layout}\n                        size={size}\n                        showSearch={true}\n                        showFilters={true}\n                        showStats={true}\n                    />\n                </motion.div>\n\n                {/* Features List */}\n                <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.5 }}\n                    className=\"mt-12 bg-white rounded-xl p-8 border border-gray-200\"\n                >\n                    <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Key Features</h2>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🎨 Premium Highlighting</h3>\n                            <p className=\"text-gray-600 text-sm\">Gold gradient glow for premium users with vibrant visual distinction</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">📱 Responsive Design</h3>\n                            <p className=\"text-gray-600 text-sm\">Adapts perfectly to mobile, tablet, and desktop screens</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🔍 Search & Filter</h3>\n                            <p className=\"text-gray-600 text-sm\">Real-time search and filtering by subscription status</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">✨ Smooth Animations</h3>\n                            <p className=\"text-gray-600 text-sm\">Framer Motion powered animations for engaging interactions</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🏆 Rank Indicators</h3>\n                            <p className=\"text-gray-600 text-sm\">Special icons and colors for top performers</p>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                            <h3 className=\"font-semibold text-gray-900\">🎯 Current User Focus</h3>\n                            <p className=\"text-gray-600 text-sm\">Automatic highlighting and scroll-to functionality</p>\n                        </div>\n                    </div>\n                </motion.div>\n            </div>\n        </div>\n    );\n};\n\nexport default RankingDemo;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,KAAQ,eAAe,CACtC,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGV,QAAQ,CAAC,YAAY,CAAC,CAClD,KAAM,CAACW,IAAI,CAAEC,OAAO,CAAC,CAAGZ,QAAQ,CAAC,QAAQ,CAAC,CAE1C;AACA,KAAM,CAAAa,WAAW,CAAG,CAChB,CACIC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,eAAe,CACrBC,cAAc,CAAE,6FAA6F,CAC7GC,kBAAkB,CAAE,QAAQ,CAC5BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACD,CACIR,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,WAAW,CACjBC,cAAc,CAAE,6FAA6F,CAC7GC,kBAAkB,CAAE,MAAM,CAC1BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACD,CACIR,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,aAAa,CACnBC,cAAc,CAAE,6FAA6F,CAC7GC,kBAAkB,CAAE,SAAS,CAC7BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACD,CACIR,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,cAAc,CACpBC,cAAc,CAAE,6FAA6F,CAC7GC,kBAAkB,CAAE,SAAS,CAC7BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACD,CACIR,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,YAAY,CAClBC,cAAc,CAAE,0FAA0F,CAC1GC,kBAAkB,CAAE,QAAQ,CAC5BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,EAAE,CACpBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACD,CACIR,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,cAAc,CACpBC,cAAc,CAAE,6FAA6F,CAC7GC,kBAAkB,CAAE,MAAM,CAC1BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACD,CACIR,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,WAAW,CACjBC,cAAc,CAAE,6FAA6F,CAC7GC,kBAAkB,CAAE,SAAS,CAC7BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACD,CACIR,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,cAAc,CACpBC,cAAc,CAAE,6FAA6F,CAC7GC,kBAAkB,CAAE,MAAM,CAC1BC,WAAW,CAAE,IAAI,CACjBC,gBAAgB,CAAE,CAAC,CACnBC,YAAY,CAAE,EAAE,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,CACV,CAAC,CACJ,CAED,mBACIjB,IAAA,QAAKkB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cACzCjB,KAAA,QAAKgB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAEnDjB,KAAA,CAACN,MAAM,CAACwB,GAAG,EACPC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BL,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5BnB,IAAA,OAAIkB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,+BAEtD,CAAI,CAAC,cACLnB,IAAA,MAAGkB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,yIAGvD,CAAG,CAAC,EACI,CAAC,cAGbjB,KAAA,CAACN,MAAM,CAACwB,GAAG,EACPC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BR,SAAS,CAAC,+DAA+D,CAAAC,QAAA,eAEzEnB,IAAA,OAAIkB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAEnFjB,KAAA,QAAKgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAElDjB,KAAA,QAAAiB,QAAA,eACInB,IAAA,UAAOkB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,cAEhE,CAAO,CAAC,cACRnB,IAAA,QAAKkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACrB,CAAC,YAAY,CAAE,UAAU,CAAE,MAAM,CAAC,CAACQ,GAAG,CAAEC,YAAY,eACjD1B,KAAA,UAA0BgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnDnB,IAAA,UACI6B,EAAE,WAAAC,MAAA,CAAYF,YAAY,CAAG,CAC7BG,IAAI,CAAC,OAAO,CACZrB,IAAI,CAAC,QAAQ,CACbsB,KAAK,CAAEJ,YAAa,CACpBK,OAAO,CAAE7B,MAAM,GAAKwB,YAAa,CACjCM,QAAQ,CAAGC,CAAC,EAAK9B,SAAS,CAAC8B,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE,CAC3Cd,SAAS,CAAC,oBAAoB,CACjC,CAAC,cACFlB,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAES,YAAY,CAAO,CAAC,GAV1CA,YAWL,CACV,CAAC,CACD,CAAC,EACL,CAAC,cAGN1B,KAAA,QAAAiB,QAAA,eACInB,IAAA,UAAOkB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,gBAEhE,CAAO,CAAC,cACRnB,IAAA,QAAKkB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACrB,CAAC,OAAO,CAAE,QAAQ,CAAE,OAAO,CAAC,CAACQ,GAAG,CAAEU,UAAU,eACzCnC,KAAA,UAAwBgB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjDnB,IAAA,UACI6B,EAAE,SAAAC,MAAA,CAAUO,UAAU,CAAG,CACzBN,IAAI,CAAC,OAAO,CACZrB,IAAI,CAAC,MAAM,CACXsB,KAAK,CAAEK,UAAW,CAClBJ,OAAO,CAAE3B,IAAI,GAAK+B,UAAW,CAC7BH,QAAQ,CAAGC,CAAC,EAAK5B,OAAO,CAAC4B,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE,CACzCd,SAAS,CAAC,oBAAoB,CACjC,CAAC,cACFlB,IAAA,SAAMkB,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEkB,UAAU,CAAO,CAAC,GAVxCA,UAWL,CACV,CAAC,CACD,CAAC,EACL,CAAC,EACL,CAAC,EACE,CAAC,cAGbnC,KAAA,CAACN,MAAM,CAACwB,GAAG,EACPC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BR,SAAS,CAAC,MAAM,CAAAC,QAAA,eAEhBnB,IAAA,OAAIkB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAEnFjB,KAAA,QAAKgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAElDjB,KAAA,QAAKgB,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC3DnB,IAAA,OAAIkB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cACtFnB,IAAA,CAACF,eAAe,EACZwC,IAAI,CAAE9B,WAAW,CAAC,CAAC,CAAE,CACrBS,IAAI,CAAE,CAAE,CACRsB,aAAa,CAAE,KAAM,CACrBnC,MAAM,CAAC,YAAY,CACnBE,IAAI,CAAEA,IAAK,CACXkC,SAAS,CAAE,IAAK,CACnB,CAAC,EACD,CAAC,cAGNtC,KAAA,QAAKgB,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC3DnB,IAAA,OAAIkB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,4BAA0B,CAAI,CAAC,cACxFnB,IAAA,CAACF,eAAe,EACZwC,IAAI,CAAE9B,WAAW,CAAC,CAAC,CAAE,CACrBS,IAAI,CAAE,CAAE,CACRsB,aAAa,CAAE,IAAK,CACpBnC,MAAM,CAAC,YAAY,CACnBE,IAAI,CAAEA,IAAK,CACXkC,SAAS,CAAE,IAAK,CACnB,CAAC,EACD,CAAC,EACL,CAAC,EACE,CAAC,cAGbtC,KAAA,CAACN,MAAM,CAACwB,GAAG,EACPC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAAAP,QAAA,eAE3BnB,IAAA,OAAIkB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAEhFnB,IAAA,CAACH,eAAe,EACZ4C,KAAK,CAAEjC,WAAY,CACnBkC,aAAa,CAAC,GAAI;AAAA,CAClBtC,MAAM,CAAEA,MAAO,CACfE,IAAI,CAAEA,IAAK,CACXqC,UAAU,CAAE,IAAK,CACjBC,WAAW,CAAE,IAAK,CAClBJ,SAAS,CAAE,IAAK,CACnB,CAAC,EACM,CAAC,cAGbtC,KAAA,CAACN,MAAM,CAACwB,GAAG,EACPC,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BC,OAAO,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BE,UAAU,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAC3BR,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAEhEnB,IAAA,OAAIkB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAEvEjB,KAAA,QAAKgB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACjEjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnB,IAAA,OAAIkB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,mCAAuB,CAAI,CAAC,cACxEnB,IAAA,MAAGkB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,sEAAoE,CAAG,CAAC,EAC5G,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnB,IAAA,OAAIkB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,gCAAoB,CAAI,CAAC,cACrEnB,IAAA,MAAGkB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,yDAAuD,CAAG,CAAC,EAC/F,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnB,IAAA,OAAIkB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,8BAAkB,CAAI,CAAC,cACnEnB,IAAA,MAAGkB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uDAAqD,CAAG,CAAC,EAC7F,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnB,IAAA,OAAIkB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,0BAAmB,CAAI,CAAC,cACpEnB,IAAA,MAAGkB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,4DAA0D,CAAG,CAAC,EAClG,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnB,IAAA,OAAIkB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,8BAAkB,CAAI,CAAC,cACnEnB,IAAA,MAAGkB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6CAA2C,CAAG,CAAC,EACnF,CAAC,cAENjB,KAAA,QAAKgB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACtBnB,IAAA,OAAIkB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,iCAAqB,CAAI,CAAC,cACtEnB,IAAA,MAAGkB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,oDAAkD,CAAG,CAAC,EAC1F,CAAC,EACL,CAAC,EACE,CAAC,EACZ,CAAC,CACL,CAAC,CAEd,CAAC,CAED,cAAe,CAAAhB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}