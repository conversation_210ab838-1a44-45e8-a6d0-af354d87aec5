// Simple ZenoPay Test - API Key Only
const axios = require('axios');
require('dotenv').config({ path: './server/.env' });

console.log('🧪 Testing ZenoPay Configuration (API Key Only)...\n');

// Check environment variables
console.log('📋 ZenoPay Configuration Check:');
console.log('✓ ZENOPAY_ACCOUNT_ID:', process.env.ZENOPAY_ACCOUNT_ID || '❌ MISSING');
console.log('✓ ZENOPAY_API_KEY:', process.env.ZENOPAY_API_KEY ? 'SET' : '❌ MISSING');
console.log('✓ ZENOPAY_WEBHOOK_URL:', process.env.ZENOPAY_WEBHOOK_URL || '❌ MISSING');
console.log('');

// Test data for ZenoPay API (Updated format per documentation)
const testData = {
  order_id: `TEST_ORDER_${Date.now()}`,
  buyer_email: '<EMAIL>',
  buyer_name: 'Test User',
  buyer_phone: '**********', // Tanzanian format: 07XXXXXXXX
  amount: 1000 // 1000 TZS for testing
};

// Add webhook URL if configured
if (process.env.ZENOPAY_WEBHOOK_URL) {
  testData.webhook_url = process.env.ZENOPAY_WEBHOOK_URL;
}

async function testZenoPayAPI() {
  try {
    console.log('🔄 Testing ZenoPay API connection...');
    console.log('📤 Test payment data:');
    console.log(JSON.stringify(testData, null, 2));
    
    const response = await axios.post('https://zenoapi.com/api/payments/mobile_money_tanzania', testData, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 30000
    });

    console.log('\n✅ ZenoPay API Response:');
    console.log('Status:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));

    if (response.data.status === 'success') {
      console.log('\n🎉 SUCCESS! ZenoPay API is working correctly.');
      console.log('📱 SMS should be sent to:', testData.buyer_phone);
      console.log('💡 This means payment integration is ready!');
    } else {
      console.log('\n⚠️ ZenoPay returned an error:', response.data.message || 'Unknown error');
      console.log('💡 This might be normal for test data - check the error details above.');
    }

  } catch (error) {
    console.error('\n❌ ZenoPay API Test Failed:');
    console.error('Error Message:', error.message);
    
    if (error.response) {
      console.error('HTTP Status:', error.response.status);
      console.error('Response Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 403) {
        console.error('\n🚨 IP WHITELIST ISSUE:');
        console.error('Your server IP needs to be whitelisted in ZenoPay.');
        console.error('Contact ZenoPay support to whitelist your IP address.');
      } else if (error.response.status === 401) {
        console.error('\n🔑 AUTHENTICATION ISSUE:');
        console.error('Check if your ZENOPAY_API_KEY is correct.');
      } else if (error.response.status === 400) {
        console.error('\n📝 DATA VALIDATION ISSUE:');
        console.error('Check if the test data format is correct.');
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.error('\n🌐 CONNECTION ISSUE:');
      console.error('Cannot connect to ZenoPay API. Check your internet connection.');
    }
  }
}

// Test order status endpoint
async function testOrderStatusAPI() {
  try {
    console.log('\n🔍 Testing ZenoPay Order Status API...');
    
    const testOrderId = 'TEST_ORDER_123';
    const response = await axios.get(`https://zenoapi.com/api/payments/order-status?order_id=${testOrderId}`, {
      headers: {
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 15000
    });

    console.log('✅ Order Status API Response:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('\n📝 Order Status API Test:');
    if (error.response && error.response.status === 404) {
      console.log('✅ API is working (test order not found - this is expected)');
    } else {
      console.error('❌ Order Status API failed:', error.message);
      if (error.response) {
        console.error('Status:', error.response.status);
        console.error('Data:', error.response.data);
      }
    }
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting ZenoPay Tests...\n');
  
  // Check if API key is set
  if (!process.env.ZENOPAY_API_KEY) {
    console.error('❌ ZENOPAY_API_KEY is not set in environment variables');
    console.error('Please check your server/.env file');
    return;
  }
  
  if (!process.env.ZENOPAY_ACCOUNT_ID) {
    console.error('❌ ZENOPAY_ACCOUNT_ID is not set in environment variables');
    console.error('Please check your server/.env file');
    return;
  }
  
  // Run tests
  await testZenoPayAPI();
  await testOrderStatusAPI();
  
  console.log('\n✅ ZenoPay configuration tests completed!');
  console.log('\n💡 Next Steps:');
  console.log('1. Start the server: cd server && npm start');
  console.log('2. Start the client: cd client && npm start');
  console.log('3. Test payment flow in the browser');
}

runTests().catch(console.error);
