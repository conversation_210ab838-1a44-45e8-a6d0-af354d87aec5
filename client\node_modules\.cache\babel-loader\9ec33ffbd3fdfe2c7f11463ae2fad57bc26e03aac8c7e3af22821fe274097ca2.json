{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Plans\\\\Plans.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { message } from \"antd\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport { addPayment } from \"../../../apicalls/payment\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport WaitingModal from \"./components/WaitingModal\";\nimport ConfirmModal from \"./components/ConfirmModal\";\nimport \"./Plans.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Plans = () => {\n  _s();\n  var _subscriptionData$pla, _subscriptionData$pla2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [selectedPlanId, setSelectedPlanId] = useState(null);\n  const [showWaitingModal, setShowWaitingModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [transactionData, setTransactionData] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  useEffect(() => {\n    fetchPlans();\n  }, []);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error(\"Error fetching plans:\", error);\n      setError(\"Failed to load plans. Please try again.\");\n      setPlans([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const hasActiveSubscription = () => {\n    if (!subscriptionData) return false;\n    if (subscriptionData.paymentStatus === \"paid\") {\n      if (subscriptionData.status === \"active\") return true;\n      if (subscriptionData.endDate) {\n        const endDate = new Date(subscriptionData.endDate);\n        const now = new Date();\n        return endDate > now;\n      }\n    }\n    return false;\n  };\n  const isActive = hasActiveSubscription();\n  const handlePlanSelect = async plan => {\n    if (!plan || paymentLoading) return;\n    if (!(user !== null && user !== void 0 && user.phoneNumber)) {\n      message.error(\"Please update your phone number in profile to proceed with payment.\");\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      setSelectedPlanId(plan._id);\n      setShowWaitingModal(true); // Show beautiful waiting modal\n\n      const paymentData = {\n        plan: plan,\n        // Send the complete plan object\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        // Store transaction data for success modal\n        setTransactionData({\n          amount: plan.discountedPrice,\n          planTitle: plan.title,\n          orderId: response.order_id,\n          status: 'success'\n        });\n\n        // Hide waiting modal and show success modal\n        setShowWaitingModal(false);\n        setShowSuccessModal(true);\n        message.success(\"Payment initiated successfully! Please check your phone for SMS confirmation.\");\n      } else {\n        throw new Error(response.message || \"Payment failed\");\n      }\n    } catch (error) {\n      console.error(\"Payment error:\", error);\n      setShowWaitingModal(false); // Hide waiting modal on error\n      message.error(error.message || \"Payment failed. Please try again.\");\n    } finally {\n      setPaymentLoading(false);\n      setSelectedPlanId(null);\n    }\n  };\n  const formatDate = dateString => {\n    if (!dateString) return \"Not available\";\n    try {\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    } catch {\n      return \"Invalid date\";\n    }\n  };\n  const getDaysRemaining = endDate => {\n    if (!endDate) return 0;\n    try {\n      const end = new Date(endDate);\n      const now = new Date();\n      const diffTime = end - now;\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      return Math.max(0, diffDays);\n    } catch {\n      return 0;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Loading Plans...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please wait while we fetch your subscription options\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-page\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"retry-button\",\n          onClick: fetchPlans,\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"plans-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"page-title\",\n        children: \"Your Learning Journey\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"page-subtitle\",\n        children: \"Choose the perfect plan to unlock your potential\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 13\n    }, this), isActive && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"active-subscription\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-badge\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"badge-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 25\n        }, this), \"Active Subscription\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"subscription-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"subscription-title\",\n          children: (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$pla = subscriptionData.plan) === null || _subscriptionData$pla === void 0 ? void 0 : _subscriptionData$pla.title) || \"Premium Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83D\\uDCC5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Started\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.startDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\u23F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Expires\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Days Left\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value highlight\",\n                children: [getDaysRemaining(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate), \" days\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-icon\",\n              children: \"\\uD83D\\uDC8E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Plan Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$pla2 = subscriptionData.plan) === null || _subscriptionData$pla2 === void 0 ? void 0 : _subscriptionData$pla2.title) || \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"benefits-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Your Benefits\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefits-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83D\\uDCDA Unlimited Quiz Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83C\\uDFAF Progress Tracking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83C\\uDFC6 Achievement System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit\",\n              children: \"\\uD83D\\uDE80 AI Study Assistant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"primary-button\",\n            onClick: () => navigate('/user/hub'),\n            children: \"Continue Learning \\uD83C\\uDF93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"secondary-button\",\n            onClick: () => navigate('/user/profile'),\n            children: \"Manage Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 17\n    }, this), !isActive && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"plans-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"section-title\",\n        children: \"Choose Your Plan\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 21\n      }, this), plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-icon\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Plans Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please check back later for subscription options.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"plans-grid\",\n        children: plans.map(plan => {\n          var _plan$title, _plan$discountedPrice, _plan$features, _plan$title2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plan-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"plan-title\",\n                children: plan.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 41\n              }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-badge\",\n                children: \"\\uD83D\\uDE80 Quick Start\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-price\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-main\",\n                children: [((_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()) || '0', \" TZS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 41\n              }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-old\",\n                children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"price-period\",\n                children: plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-features\",\n              children: ((_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-check\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-text\",\n                  children: feature\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 49\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 45\n              }, this))) || /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-check\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"feature-text\",\n                  children: \"Premium access included\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `plan-button ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`,\n              onClick: () => handlePlanSelect(plan),\n              disabled: paymentLoading,\n              children: paymentLoading && selectedPlanId === plan._id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"button-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 49\n                }, this), \"Processing...\"]\n              }, void 0, true) : (_plan$title2 = plan.title) !== null && _plan$title2 !== void 0 && _plan$title2.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 37\n            }, this)]\n          }, plan._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 9\n  }, this);\n};\n_s(Plans, \"V/22+v9B+8/B49hn0Cl11qcf6Lw=\", false, function () {\n  return [useSelector, useSelector, useDispatch, useNavigate];\n});\n_c = Plans;\nexport default Plans;\nvar _c;\n$RefreshReg$(_c, \"Plans\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSelector", "useDispatch", "useNavigate", "message", "getPlans", "addPayment", "HideLoading", "ShowLoading", "WaitingModal", "ConfirmModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Plans", "_s", "_subscriptionData$pla", "_subscriptionData$pla2", "plans", "setPlans", "loading", "setLoading", "error", "setError", "paymentLoading", "setPaymentLoading", "selectedPlanId", "setSelectedPlanId", "showWaitingModal", "setShowWaitingModal", "showSuccessModal", "setShowSuccessModal", "transactionData", "setTransactionData", "user", "state", "subscriptionData", "subscription", "dispatch", "navigate", "fetchPlans", "response", "Array", "isArray", "console", "hasActiveSubscription", "paymentStatus", "status", "endDate", "Date", "now", "isActive", "handlePlanSelect", "plan", "phoneNumber", "_user$name", "_id", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "success", "amount", "discountedPrice", "planTitle", "title", "orderId", "order_id", "Error", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "end", "diffTime", "diffDays", "Math", "ceil", "max", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "startDate", "length", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$title2", "includes", "toLocaleString", "actualPrice", "duration", "features", "feature", "index", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Plans/Plans.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { message } from \"antd\";\nimport { getPlans } from \"../../../apicalls/plans\";\nimport { addPayment } from \"../../../apicalls/payment\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport WaitingModal from \"./components/WaitingModal\";\nimport ConfirmModal from \"./components/ConfirmModal\";\nimport \"./Plans.css\";\n\nconst Plans = () => {\n    const [plans, setPlans] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [paymentLoading, setPaymentLoading] = useState(false);\n    const [selectedPlanId, setSelectedPlanId] = useState(null);\n    const [showWaitingModal, setShowWaitingModal] = useState(false);\n    const [showSuccessModal, setShowSuccessModal] = useState(false);\n    const [transactionData, setTransactionData] = useState(null);\n    \n    const { user } = useSelector((state) => state.user);\n    const { subscriptionData } = useSelector((state) => state.subscription);\n    const dispatch = useDispatch();\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        fetchPlans();\n    }, []);\n\n    const fetchPlans = async () => {\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await getPlans();\n            setPlans(Array.isArray(response) ? response : []);\n        } catch (error) {\n            console.error(\"Error fetching plans:\", error);\n            setError(\"Failed to load plans. Please try again.\");\n            setPlans([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const hasActiveSubscription = () => {\n        if (!subscriptionData) return false;\n        \n        if (subscriptionData.paymentStatus === \"paid\") {\n            if (subscriptionData.status === \"active\") return true;\n            \n            if (subscriptionData.endDate) {\n                const endDate = new Date(subscriptionData.endDate);\n                const now = new Date();\n                return endDate > now;\n            }\n        }\n        return false;\n    };\n\n    const isActive = hasActiveSubscription();\n\n    const handlePlanSelect = async (plan) => {\n        if (!plan || paymentLoading) return;\n        \n        if (!user?.phoneNumber) {\n            message.error(\"Please update your phone number in profile to proceed with payment.\");\n            return;\n        }\n\n        try {\n            setPaymentLoading(true);\n            setSelectedPlanId(plan._id);\n            setShowWaitingModal(true); // Show beautiful waiting modal\n\n            const paymentData = {\n                plan: plan,  // Send the complete plan object\n                userId: user._id,\n                userPhone: user.phoneNumber,\n                userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n            };\n\n            const response = await addPayment(paymentData);\n\n            if (response.success) {\n                // Store transaction data for success modal\n                setTransactionData({\n                    amount: plan.discountedPrice,\n                    planTitle: plan.title,\n                    orderId: response.order_id,\n                    status: 'success'\n                });\n\n                // Hide waiting modal and show success modal\n                setShowWaitingModal(false);\n                setShowSuccessModal(true);\n\n                message.success(\"Payment initiated successfully! Please check your phone for SMS confirmation.\");\n            } else {\n                throw new Error(response.message || \"Payment failed\");\n            }\n\n        } catch (error) {\n            console.error(\"Payment error:\", error);\n            setShowWaitingModal(false); // Hide waiting modal on error\n            message.error(error.message || \"Payment failed. Please try again.\");\n        } finally {\n            setPaymentLoading(false);\n            setSelectedPlanId(null);\n        }\n    };\n\n    const formatDate = (dateString) => {\n        if (!dateString) return \"Not available\";\n        try {\n            return new Date(dateString).toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            });\n        } catch {\n            return \"Invalid date\";\n        }\n    };\n\n    const getDaysRemaining = (endDate) => {\n        if (!endDate) return 0;\n        try {\n            const end = new Date(endDate);\n            const now = new Date();\n            const diffTime = end - now;\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            return Math.max(0, diffDays);\n        } catch {\n            return 0;\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"plans-page\">\n                <div className=\"loading-container\">\n                    <div className=\"spinner\"></div>\n                    <h3>Loading Plans...</h3>\n                    <p>Please wait while we fetch your subscription options</p>\n                </div>\n            </div>\n        );\n    }\n\n    if (error) {\n        return (\n            <div className=\"plans-page\">\n                <div className=\"error-container\">\n                    <div className=\"error-icon\">⚠️</div>\n                    <h3>Something went wrong</h3>\n                    <p>{error}</p>\n                    <button className=\"retry-button\" onClick={fetchPlans}>\n                        Try Again\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"plans-page\">\n            <div className=\"page-header\">\n                <h1 className=\"page-title\">Your Learning Journey</h1>\n                <p className=\"page-subtitle\">Choose the perfect plan to unlock your potential</p>\n            </div>\n\n            {isActive && (\n                <div className=\"active-subscription\">\n                    <div className=\"subscription-badge\">\n                        <span className=\"badge-dot\"></span>\n                        Active Subscription\n                    </div>\n                    \n                    <div className=\"subscription-content\">\n                        <h2 className=\"subscription-title\">\n                            {subscriptionData?.plan?.title || \"Premium Plan\"}\n                        </h2>\n                        \n                        <div className=\"subscription-grid\">\n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">📅</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Started</span>\n                                    <span className=\"info-value\">{formatDate(subscriptionData?.startDate)}</span>\n                                </div>\n                            </div>\n                            \n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">⏰</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Expires</span>\n                                    <span className=\"info-value\">{formatDate(subscriptionData?.endDate)}</span>\n                                </div>\n                            </div>\n                            \n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">🎯</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Days Left</span>\n                                    <span className=\"info-value highlight\">\n                                        {getDaysRemaining(subscriptionData?.endDate)} days\n                                    </span>\n                                </div>\n                            </div>\n                            \n                            <div className=\"info-card\">\n                                <div className=\"info-icon\">💎</div>\n                                <div className=\"info-text\">\n                                    <span className=\"info-label\">Plan Type</span>\n                                    <span className=\"info-value\">{subscriptionData?.plan?.title || \"Premium\"}</span>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"benefits-section\">\n                            <h3>Your Benefits</h3>\n                            <div className=\"benefits-list\">\n                                <div className=\"benefit\">📚 Unlimited Quiz Access</div>\n                                <div className=\"benefit\">🎯 Progress Tracking</div>\n                                <div className=\"benefit\">🏆 Achievement System</div>\n                                <div className=\"benefit\">🚀 AI Study Assistant</div>\n                            </div>\n                        </div>\n\n                        <div className=\"action-buttons\">\n                            <button \n                                className=\"primary-button\"\n                                onClick={() => navigate('/user/hub')}\n                            >\n                                Continue Learning 🎓\n                            </button>\n                            <button \n                                className=\"secondary-button\"\n                                onClick={() => navigate('/user/profile')}\n                            >\n                                Manage Account\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {!isActive && (\n                <div className=\"plans-section\">\n                    <h2 className=\"section-title\">Choose Your Plan</h2>\n                    \n                    {plans.length === 0 ? (\n                        <div className=\"no-plans\">\n                            <div className=\"no-plans-icon\">📋</div>\n                            <h3>No Plans Available</h3>\n                            <p>Please check back later for subscription options.</p>\n                        </div>\n                    ) : (\n                        <div className=\"plans-grid\">\n                            {plans.map((plan) => (\n                                <div key={plan._id} className=\"plan-card\">\n                                    <div className=\"plan-header\">\n                                        <h3 className=\"plan-title\">{plan.title}</h3>\n                                        {plan.title?.toLowerCase().includes('glimp') && (\n                                            <div className=\"plan-badge\">🚀 Quick Start</div>\n                                        )}\n                                    </div>\n\n                                    <div className=\"plan-price\">\n                                        <div className=\"price-main\">\n                                            {plan.discountedPrice?.toLocaleString() || '0'} TZS\n                                        </div>\n                                        {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                                            <div className=\"price-old\">\n                                                {plan.actualPrice.toLocaleString()} TZS\n                                            </div>\n                                        )}\n                                        <div className=\"price-period\">\n                                            {plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'}\n                                        </div>\n                                    </div>\n\n                                    <div className=\"plan-features\">\n                                        {plan.features?.map((feature, index) => (\n                                            <div key={index} className=\"feature\">\n                                                <span className=\"feature-check\">✓</span>\n                                                <span className=\"feature-text\">{feature}</span>\n                                            </div>\n                                        )) || (\n                                            <div className=\"feature\">\n                                                <span className=\"feature-check\">✓</span>\n                                                <span className=\"feature-text\">Premium access included</span>\n                                            </div>\n                                        )}\n                                    </div>\n\n                                    <button\n                                        className={`plan-button ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`}\n                                        onClick={() => handlePlanSelect(plan)}\n                                        disabled={paymentLoading}\n                                    >\n                                        {paymentLoading && selectedPlanId === plan._id ? (\n                                            <>\n                                                <span className=\"button-spinner\"></span>\n                                                Processing...\n                                            </>\n                                        ) : (\n                                            plan.title?.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'\n                                        )}\n                                    </button>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Plans;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErB,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAChB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM;IAAEmC;EAAK,CAAC,GAAGlC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGpC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAMsC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAE9BJ,SAAS,CAAC,MAAM;IACZ0C,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACAnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMkB,QAAQ,GAAG,MAAMrC,QAAQ,CAAC,CAAC;MACjCe,QAAQ,CAACuB,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACrD,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,yCAAyC,CAAC;MACnDJ,QAAQ,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwB,qBAAqB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACT,gBAAgB,EAAE,OAAO,KAAK;IAEnC,IAAIA,gBAAgB,CAACU,aAAa,KAAK,MAAM,EAAE;MAC3C,IAAIV,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE,OAAO,IAAI;MAErD,IAAIX,gBAAgB,CAACY,OAAO,EAAE;QAC1B,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAACb,gBAAgB,CAACY,OAAO,CAAC;QAClD,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;QACtB,OAAOD,OAAO,GAAGE,GAAG;MACxB;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EAED,MAAMC,QAAQ,GAAGN,qBAAqB,CAAC,CAAC;EAExC,MAAMO,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI,CAACA,IAAI,IAAI7B,cAAc,EAAE;IAE7B,IAAI,EAACU,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoB,WAAW,GAAE;MACpBnD,OAAO,CAACmB,KAAK,CAAC,qEAAqE,CAAC;MACpF;IACJ;IAEA,IAAI;MAAA,IAAAiC,UAAA;MACA9B,iBAAiB,CAAC,IAAI,CAAC;MACvBE,iBAAiB,CAAC0B,IAAI,CAACG,GAAG,CAAC;MAC3B3B,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE3B,MAAM4B,WAAW,GAAG;QAChBJ,IAAI,EAAEA,IAAI;QAAG;QACbK,MAAM,EAAExB,IAAI,CAACsB,GAAG;QAChBG,SAAS,EAAEzB,IAAI,CAACoB,WAAW;QAC3BM,SAAS,EAAE1B,IAAI,CAAC2B,KAAK,IAAK,IAAAN,UAAA,GAAErB,IAAI,CAAC4B,IAAI,cAAAP,UAAA,uBAATA,UAAA,CAAWQ,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC7E,CAAC;MAED,MAAMvB,QAAQ,GAAG,MAAMpC,UAAU,CAACoD,WAAW,CAAC;MAE9C,IAAIhB,QAAQ,CAACwB,OAAO,EAAE;QAClB;QACAhC,kBAAkB,CAAC;UACfiC,MAAM,EAAEb,IAAI,CAACc,eAAe;UAC5BC,SAAS,EAAEf,IAAI,CAACgB,KAAK;UACrBC,OAAO,EAAE7B,QAAQ,CAAC8B,QAAQ;UAC1BxB,MAAM,EAAE;QACZ,CAAC,CAAC;;QAEF;QACAlB,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,mBAAmB,CAAC,IAAI,CAAC;QAEzB5B,OAAO,CAAC8D,OAAO,CAAC,+EAA+E,CAAC;MACpG,CAAC,MAAM;QACH,MAAM,IAAIO,KAAK,CAAC/B,QAAQ,CAACtC,OAAO,IAAI,gBAAgB,CAAC;MACzD;IAEJ,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACZsB,OAAO,CAACtB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCO,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC5B1B,OAAO,CAACmB,KAAK,CAACA,KAAK,CAACnB,OAAO,IAAI,mCAAmC,CAAC;IACvE,CAAC,SAAS;MACNsB,iBAAiB,CAAC,KAAK,CAAC;MACxBE,iBAAiB,CAAC,IAAI,CAAC;IAC3B;EACJ,CAAC;EAED,MAAM8C,UAAU,GAAIC,UAAU,IAAK;IAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,eAAe;IACvC,IAAI;MACA,OAAO,IAAIzB,IAAI,CAACyB,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACpDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACT,CAAC,CAAC;IACN,CAAC,CAAC,MAAM;MACJ,OAAO,cAAc;IACzB;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAI/B,OAAO,IAAK;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IACtB,IAAI;MACA,MAAMgC,GAAG,GAAG,IAAI/B,IAAI,CAACD,OAAO,CAAC;MAC7B,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAMgC,QAAQ,GAAGD,GAAG,GAAG9B,GAAG;MAC1B,MAAMgC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;IAChC,CAAC,CAAC,MAAM;MACJ,OAAO,CAAC;IACZ;EACJ,CAAC;EAED,IAAI9D,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK2E,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvB5E,OAAA;QAAK2E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9B5E,OAAA;UAAK2E,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/BhF,OAAA;UAAA4E,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBhF,OAAA;UAAA4E,QAAA,EAAG;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIrE,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK2E,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvB5E,OAAA;QAAK2E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5B5E,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpChF,OAAA;UAAA4E,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BhF,OAAA;UAAA4E,QAAA,EAAIjE;QAAK;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdhF,OAAA;UAAQ2E,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEpD,UAAW;UAAA+C,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIhF,OAAA;IAAK2E,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACvB5E,OAAA;MAAK2E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxB5E,OAAA;QAAI2E,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrDhF,OAAA;QAAG2E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChF,CAAC,EAELxC,QAAQ,iBACLxC,OAAA;MAAK2E,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChC5E,OAAA;QAAK2E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAC/B5E,OAAA;UAAM2E,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,uBAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAENhF,OAAA;QAAK2E,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACjC5E,OAAA;UAAI2E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC7B,CAAAnD,gBAAgB,aAAhBA,gBAAgB,wBAAApB,qBAAA,GAAhBoB,gBAAgB,CAAEiB,IAAI,cAAArC,qBAAA,uBAAtBA,qBAAA,CAAwBqD,KAAK,KAAI;QAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAELhF,OAAA;UAAK2E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9B5E,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5E,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3ChF,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEd,UAAU,CAACrC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyD,SAAS;cAAC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5E,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClChF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3ChF,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEd,UAAU,CAACrC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEY,OAAO;cAAC;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5E,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ChF,OAAA;gBAAM2E,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,GACjCR,gBAAgB,CAAC3C,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEY,OAAO,CAAC,EAAC,OACjD;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB5E,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnChF,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5E,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ChF,OAAA;gBAAM2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE,CAAAnD,gBAAgB,aAAhBA,gBAAgB,wBAAAnB,sBAAA,GAAhBmB,gBAAgB,CAAEiB,IAAI,cAAApC,sBAAA,uBAAtBA,sBAAA,CAAwBoD,KAAK,KAAI;cAAS;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7B5E,OAAA;YAAA4E,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBhF,OAAA;YAAK2E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B5E,OAAA;cAAK2E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDhF,OAAA;cAAK2E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnDhF,OAAA;cAAK2E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpDhF,OAAA;cAAK2E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhF,OAAA;UAAK2E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3B5E,OAAA;YACI2E,SAAS,EAAC,gBAAgB;YAC1BM,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,WAAW,CAAE;YAAAgD,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThF,OAAA;YACI2E,SAAS,EAAC,kBAAkB;YAC5BM,OAAO,EAAEA,CAAA,KAAMrD,QAAQ,CAAC,eAAe,CAAE;YAAAgD,QAAA,EAC5C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEA,CAACxC,QAAQ,iBACNxC,OAAA;MAAK2E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B5E,OAAA;QAAI2E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAElDzE,KAAK,CAAC4E,MAAM,KAAK,CAAC,gBACfnF,OAAA;QAAK2E,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACrB5E,OAAA;UAAK2E,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvChF,OAAA;UAAA4E,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BhF,OAAA;UAAA4E,QAAA,EAAG;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,gBAENhF,OAAA;QAAK2E,SAAS,EAAC,YAAY;QAAAC,QAAA,EACtBrE,KAAK,CAAC6E,GAAG,CAAE1C,IAAI;UAAA,IAAA2C,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,YAAA;UAAA,oBACZxF,OAAA;YAAoB2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACrC5E,OAAA;cAAK2E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxB5E,OAAA;gBAAI2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAElC,IAAI,CAACgB;cAAK;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAC3C,EAAAK,WAAA,GAAA3C,IAAI,CAACgB,KAAK,cAAA2B,WAAA,uBAAVA,WAAA,CAAYhC,WAAW,CAAC,CAAC,CAACoC,QAAQ,CAAC,OAAO,CAAC,kBACxCzF,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAClD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAENhF,OAAA;cAAK2E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvB5E,OAAA;gBAAK2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,GACtB,EAAAU,qBAAA,GAAA5C,IAAI,CAACc,eAAe,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBI,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,MACnD;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACLtC,IAAI,CAACiD,WAAW,IAAIjD,IAAI,CAACiD,WAAW,KAAKjD,IAAI,CAACc,eAAe,iBAC1DxD,OAAA;gBAAK2E,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACrBlC,IAAI,CAACiD,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MACvC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACR,eACDhF,OAAA;gBAAK2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,EACxBlC,IAAI,CAACkD,QAAQ,GAAI,GAAElD,IAAI,CAACkD,QAAS,SAAQlD,IAAI,CAACkD,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAG,EAAC,GAAG;cAAU;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENhF,OAAA;cAAK2E,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzB,EAAAW,cAAA,GAAA7C,IAAI,CAACmD,QAAQ,cAAAN,cAAA,uBAAbA,cAAA,CAAeH,GAAG,CAAC,CAACU,OAAO,EAAEC,KAAK,kBAC/B/F,OAAA;gBAAiB2E,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBAChC5E,OAAA;kBAAM2E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxChF,OAAA;kBAAM2E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEkB;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFzCe,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACR,CAAC,kBACEhF,OAAA;gBAAK2E,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpB5E,OAAA;kBAAM2E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxChF,OAAA;kBAAM2E,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAENhF,OAAA;cACI2E,SAAS,EAAG,eAAc9D,cAAc,IAAIE,cAAc,KAAK2B,IAAI,CAACG,GAAG,GAAG,SAAS,GAAG,EAAG,EAAE;cAC3FoC,OAAO,EAAEA,CAAA,KAAMxC,gBAAgB,CAACC,IAAI,CAAE;cACtCsD,QAAQ,EAAEnF,cAAe;cAAA+D,QAAA,EAExB/D,cAAc,IAAIE,cAAc,KAAK2B,IAAI,CAACG,GAAG,gBAC1C7C,OAAA,CAAAE,SAAA;gBAAA0E,QAAA,gBACI5E,OAAA;kBAAM2E,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAE5C;cAAA,eAAE,CAAC,GAEH,CAAAQ,YAAA,GAAA9C,IAAI,CAACgB,KAAK,cAAA8B,YAAA,eAAVA,YAAA,CAAYnC,WAAW,CAAC,CAAC,CAACoC,QAAQ,CAAC,OAAO,CAAC,GAAG,gBAAgB,GAAG;YACpE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAjDHtC,IAAI,CAACG,GAAG;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkDb,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC5E,EAAA,CApTID,KAAK;EAAA,QAUUd,WAAW,EACCA,WAAW,EACvBC,WAAW,EACXC,WAAW;AAAA;AAAA0G,EAAA,GAb1B9F,KAAK;AAsTX,eAAeA,KAAK;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}