{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Register\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input, Select } from \"antd\";\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\nimport Logo from \"../../../assets/logo.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction Register() {\n  _s();\n  const [verification, setVerification] = useState(false);\n  const [data, setData] = useState(\"\");\n  const [otp, setOTP] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\");\n  const navigate = useNavigate();\n  const onFinish = async values => {\n    try {\n      const response = await registerUser(values);\n      if (response.success) {\n        message.success(response.message);\n        navigate(\"/login\");\n      } else {\n        message.error(response.message);\n        setVerification(false);\n      }\n    } catch (error) {\n      message.error(error.message);\n      setVerification(false);\n    }\n  };\n  const verifyUser = async values => {\n    if (values.otp === otp) {\n      onFinish(data);\n    } else {\n      message.error(\"Invalid OTP\");\n    }\n  };\n  const generateOTP = async formData => {\n    if (!formData.name || !formData.email || !formData.password) {\n      message.error(\"Please fill all fields!\");\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await sendOTP(formData);\n      if (response.success) {\n        message.success(response.message);\n        setData(formData);\n        setOTP(response.data);\n        setVerification(true);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    setLoading(false);\n  };\n\n  // Handle Google OAuth success\n  const handleGoogleSuccess = async credentialResponse => {\n    setLoading(true);\n    try {\n      const response = await registerWithGoogle({\n        credential: credentialResponse.credential\n      });\n      if (response.success) {\n        message.success(\"Registration successful with Google!\");\n        navigate(\"/login\");\n      } else {\n        message.error(response.message || \"Google registration failed\");\n      }\n    } catch (error) {\n      console.error(\"Google registration error:\", error);\n      message.error(\"Failed to register with Google. Please try again.\");\n    }\n    setLoading(false);\n  };\n\n  // Handle Google OAuth error\n  const handleGoogleError = () => {\n    message.error(\"Google registration was cancelled or failed\");\n  };\n  return /*#__PURE__*/_jsxDEV(GoogleOAuthProvider, {\n    clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID || \"your-google-client-id\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"register-card\",\n        children: verification ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"register-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"verification-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-shield-check-line\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"register-title\",\n              children: \"Verify Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"register-subtitle\",\n              children: \"Enter the OTP sent to your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            layout: \"vertical\",\n            onFinish: verifyUser,\n            className: \"register-form\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"otp\",\n              label: \"OTP Code\",\n              rules: [{\n                required: true,\n                message: \"Please enter the OTP!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                className: \"form-input otp-input\",\n                placeholder: \"Enter 6-digit OTP\",\n                maxLength: 6\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"register-btn\",\n              children: \"Verify & Complete Registration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"register-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: Logo,\n              alt: \"BrainWave Logo\",\n              className: \"register-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"register-title\",\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"register-subtitle\",\n              children: \"Join thousands of students learning with BrainWave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), process.env.REACT_APP_GOOGLE_CLIENT_ID && process.env.REACT_APP_GOOGLE_CLIENT_ID !== 'your-google-client-id-here' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"google-auth-section\",\n            children: [/*#__PURE__*/_jsxDEV(GoogleLogin, {\n              onSuccess: handleGoogleSuccess,\n              onError: handleGoogleError,\n              useOneTap: false,\n              theme: \"outline\",\n              size: \"large\",\n              text: \"continue_with\",\n              shape: \"rectangular\",\n              logo_alignment: \"left\",\n              width: \"100%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              className: \"auth-divider\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"divider-text\",\n                children: \"or continue with email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            layout: \"vertical\",\n            onFinish: generateOTP,\n            className: \"register-form\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"Full Name\",\n              rules: [{\n                required: true,\n                message: \"Please enter your name!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                className: \"form-input\",\n                placeholder: \"Enter your full name\",\n                autoComplete: \"name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"school\",\n              label: \"School\",\n              rules: [{\n                required: true,\n                message: \"Please enter your school!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"text\",\n                className: \"form-input\",\n                placeholder: \"Enter your school name\",\n                autoComplete: \"organization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"Education Level\",\n              rules: [{\n                required: true,\n                message: \"Please select your level!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                onChange: value => setSchoolType(value),\n                className: \"form-input\",\n                placeholder: \"Select Education Level\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"Primary\",\n                  children: \"Primary Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"Secondary\",\n                  children: \"Secondary Education\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"Advance\",\n                  children: \"Advanced Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"class\",\n              label: \"Class\",\n              rules: [{\n                required: true,\n                message: \"Please select your class!\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                className: \"form-input\",\n                placeholder: \"Select Your Class\",\n                children: [schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                  value: i,\n                  children: `Class ${i}`\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)), schoolType === \"Secondary\" && [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                  value: `Form-${i}`,\n                  children: `Form ${i}`\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this)), schoolType === \"Advance\" && [5, 6].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                  value: `Form-${i}`,\n                  children: `Form ${i}`\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"email\",\n              label: \"Email Address\",\n              rules: [{\n                required: true,\n                message: \"Please enter your email!\"\n              }, {\n                type: \"email\",\n                message: \"Please enter a valid email address!\"\n              }, {\n                pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\n                message: \"Please enter a properly formatted email address!\"\n              }],\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                type: \"email\",\n                className: \"form-input\",\n                placeholder: \"Enter your email address (e.g., <EMAIL>)\",\n                autoComplete: \"email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"We'll send important updates to this email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phoneNumber\",\n              label: \"Phone Number\",\n              rules: [{\n                required: true,\n                message: \"Please enter your phone number!\"\n              }, {\n                pattern: /^\\d{10}$/,\n                message: \"Phone number must be exactly 10 digits!\"\n              }],\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                type: \"tel\",\n                maxLength: 10,\n                className: \"form-input\",\n                placeholder: \"Enter 10-digit phone number\",\n                autoComplete: \"tel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"Used for payment verification\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              label: \"Password\",\n              rules: [{\n                required: true,\n                message: \"Please enter your password!\"\n              }, {\n                min: 8,\n                message: \"Password must be at least 8 characters long!\"\n              }, {\n                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n                message: \"Password must contain uppercase, lowercase, number and special character!\"\n              }],\n              children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n                className: \"form-input\",\n                placeholder: \"Create a strong password (min 8 characters)\",\n                autoComplete: \"new-password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"Must include: uppercase, lowercase, number, and special character\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"confirmPassword\",\n              label: \"Retype Password\",\n              dependencies: ['password'],\n              rules: [{\n                required: true,\n                message: \"Please retype your password!\"\n              }, ({\n                getFieldValue\n              }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('password') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('The two passwords do not match!'));\n                }\n              })],\n              children: [/*#__PURE__*/_jsxDEV(Input.Password, {\n                className: \"form-input\",\n                placeholder: \"Retype your password to confirm\",\n                autoComplete: \"new-password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"form-help-text\",\n                children: \"Must match the password above\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"register-btn\",\n                disabled: loading,\n                children: loading ? \"Creating Account...\" : \"Create Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"register-footer\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Already have an account? \", \" \", /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"register-link\",\n                children: \"Sign In\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"ZCOX0z1U6pPkSU7THaoXima+72A=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["Form", "message", "Input", "Select", "React", "useState", "Link", "useNavigate", "registerUser", "sendOTP", "Logo", "jsxDEV", "_jsxDEV", "Option", "Register", "_s", "verification", "setVerification", "data", "setData", "otp", "setOTP", "loading", "setLoading", "schoolType", "setSchoolType", "navigate", "onFinish", "values", "response", "success", "error", "verifyUser", "generateOTP", "formData", "name", "email", "password", "handleGoogleSuccess", "credentialResponse", "registerWithGoogle", "credential", "console", "handleGoogleError", "GoogleOAuthProvider", "clientId", "process", "env", "REACT_APP_GOOGLE_CLIENT_ID", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "label", "rules", "required", "type", "placeholder", "max<PERSON><PERSON><PERSON>", "src", "alt", "GoogleLogin", "onSuccess", "onError", "useOneTap", "theme", "size", "text", "shape", "logo_alignment", "width", "Divider", "autoComplete", "onChange", "value", "map", "i", "pattern", "min", "Password", "dependencies", "getFieldValue", "validator", "_", "Promise", "resolve", "reject", "Error", "disabled", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Register/index.js"], "sourcesContent": ["import { Form, message, Input, Select } from \"antd\";\r\nimport React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\r\nimport Logo from \"../../../assets/logo.png\";\r\n\r\nconst { Option } = Select;\r\n\r\nfunction Register() {\r\n  const [verification, setVerification] = useState(false);\r\n  const [data, setData] = useState(\"\");\r\n  const [otp, setOTP] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n  const [schoolType, setSchoolType] = useState(\"\");\r\n  const navigate = useNavigate();\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      const response = await registerUser(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        navigate(\"/login\");\r\n      } else {\r\n        message.error(response.message);\r\n        setVerification(false);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      setVerification(false);\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === otp) {\r\n      onFinish(data);\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  const generateOTP = async (formData) => {\r\n    if (!formData.name || !formData.email || !formData.password) {\r\n      message.error(\"Please fill all fields!\");\r\n      return;\r\n    }\r\n    setLoading(true);\r\n    try {\r\n      const response = await sendOTP(formData);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setData(formData);\r\n        setOTP(response.data);\r\n        setVerification(true);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  // Handle Google OAuth success\r\n  const handleGoogleSuccess = async (credentialResponse) => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await registerWithGoogle({\r\n        credential: credentialResponse.credential\r\n      });\r\n\r\n      if (response.success) {\r\n        message.success(\"Registration successful with Google!\");\r\n        navigate(\"/login\");\r\n      } else {\r\n        message.error(response.message || \"Google registration failed\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Google registration error:\", error);\r\n      message.error(\"Failed to register with Google. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  // Handle Google OAuth error\r\n  const handleGoogleError = () => {\r\n    message.error(\"Google registration was cancelled or failed\");\r\n  };\r\n\r\n  return (\r\n    <GoogleOAuthProvider clientId={process.env.REACT_APP_GOOGLE_CLIENT_ID || \"your-google-client-id\"}>\r\n      <div className=\"register-container\">\r\n        <div className=\"register-card\">\r\n        {verification ? (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <div className=\"verification-icon\">\r\n                <i className=\"ri-shield-check-line\"></i>\r\n              </div>\r\n              <h1 className=\"register-title\">Verify Your Email</h1>\r\n              <p className=\"register-subtitle\">Enter the OTP sent to your email</p>\r\n            </div>\r\n\r\n            <Form layout=\"vertical\" onFinish={verifyUser} className=\"register-form\">\r\n              <Form.Item name=\"otp\" label=\"OTP Code\" rules={[{ required: true, message: \"Please enter the OTP!\" }]}>\r\n                <Input\r\n                  type=\"number\"\r\n                  className=\"form-input otp-input\"\r\n                  placeholder=\"Enter 6-digit OTP\"\r\n                  maxLength={6}\r\n                />\r\n              </Form.Item>\r\n\r\n              <button type=\"submit\" className=\"register-btn\">\r\n                Verify & Complete Registration\r\n              </button>\r\n            </Form>\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Create Account</h1>\r\n              <p className=\"register-subtitle\">Join thousands of students learning with BrainWave</p>\r\n            </div>\r\n\r\n            {/* Google OAuth Section - Temporarily disabled for testing */}\r\n            {process.env.REACT_APP_GOOGLE_CLIENT_ID && process.env.REACT_APP_GOOGLE_CLIENT_ID !== 'your-google-client-id-here' && (\r\n              <div className=\"google-auth-section\">\r\n                <GoogleLogin\r\n                  onSuccess={handleGoogleSuccess}\r\n                  onError={handleGoogleError}\r\n                  useOneTap={false}\r\n                  theme=\"outline\"\r\n                  size=\"large\"\r\n                  text=\"continue_with\"\r\n                  shape=\"rectangular\"\r\n                  logo_alignment=\"left\"\r\n                  width=\"100%\"\r\n                />\r\n\r\n                <Divider className=\"auth-divider\">\r\n                  <span className=\"divider-text\">or continue with email</span>\r\n                </Divider>\r\n              </div>\r\n            )}\r\n\r\n            <Form layout=\"vertical\" onFinish={generateOTP} className=\"register-form\">\r\n              <Form.Item name=\"name\" label=\"Full Name\" rules={[{ required: true, message: \"Please enter your name!\" }]}>\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your full name\"\r\n                  autoComplete=\"name\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"school\" label=\"School\" rules={[{ required: true, message: \"Please enter your school!\" }]}>\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your school name\"\r\n                  autoComplete=\"organization\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"level\" label=\"Education Level\" rules={[{ required: true, message: \"Please select your level!\" }]}>\r\n                <Select\r\n                  onChange={(value) => setSchoolType(value)}\r\n                  className=\"form-input\"\r\n                  placeholder=\"Select Education Level\"\r\n                >\r\n                  <Option value=\"Primary\">Primary Education</Option>\r\n                  <Option value=\"Secondary\">Secondary Education</Option>\r\n                  <Option value=\"Advance\">Advanced Level</Option>\r\n                </Select>\r\n              </Form.Item>\r\n\r\n              <Form.Item name=\"class\" label=\"Class\" rules={[{ required: true, message: \"Please select your class!\" }]}>\r\n                <Select className=\"form-input\" placeholder=\"Select Your Class\">\r\n                  {schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map((i) => (\r\n                    <Option key={i} value={i}>{`Class ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Secondary\" && [1, 2, 3, 4].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`Form ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Advance\" && [5, 6].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`Form ${i}`}</Option>\r\n                  ))}\r\n                </Select>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"Email Address\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your email!\" },\r\n                  { type: \"email\", message: \"Please enter a valid email address!\" },\r\n                  {\r\n                    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\r\n                    message: \"Please enter a properly formatted email address!\"\r\n                  }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"email\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your email address (e.g., <EMAIL>)\"\r\n                  autoComplete=\"email\"\r\n                />\r\n                <p className=\"form-help-text\">We'll send important updates to this email</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phoneNumber\"\r\n                label=\"Phone Number\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your phone number!\" },\r\n                  { pattern: /^\\d{10}$/, message: \"Phone number must be exactly 10 digits!\" },\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"tel\"\r\n                  maxLength={10}\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter 10-digit phone number\"\r\n                  autoComplete=\"tel\"\r\n                />\r\n                <p className=\"form-help-text\">Used for payment verification</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                label=\"Password\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your password!\" },\r\n                  { min: 8, message: \"Password must be at least 8 characters long!\" },\r\n                  {\r\n                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\r\n                    message: \"Password must contain uppercase, lowercase, number and special character!\"\r\n                  }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Create a strong password (min 8 characters)\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must include: uppercase, lowercase, number, and special character</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                label=\"Retype Password\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: \"Please retype your password!\" },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('The two passwords do not match!'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Retype your password to confirm\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n                <p className=\"form-help-text\">Must match the password above</p>\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <button type=\"submit\" className=\"register-btn\" disabled={loading}>\r\n                  {loading ? \"Creating Account...\" : \"Create Account\"}\r\n                </button>\r\n              </Form.Item>\r\n            </Form>\r\n\r\n            <div className=\"register-footer\">\r\n              <p>\r\n                Already have an account? {\" \"}\r\n                <Link to=\"/login\" className=\"register-link\">\r\n                  Sign In\r\n                </Link>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n        </div>\r\n      </div>\r\n    </GoogleOAuthProvider>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACnD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAC/D,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAM;EAAEC;AAAO,CAAC,GAAGV,MAAM;AAEzB,SAASW,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACa,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACe,GAAG,EAAEC,MAAM,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMqB,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAMoB,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,YAAY,CAACoB,MAAM,CAAC;MAC3C,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB7B,OAAO,CAAC6B,OAAO,CAACD,QAAQ,CAAC5B,OAAO,CAAC;QACjCyB,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLzB,OAAO,CAAC8B,KAAK,CAACF,QAAQ,CAAC5B,OAAO,CAAC;QAC/BgB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd9B,OAAO,CAAC8B,KAAK,CAACA,KAAK,CAAC9B,OAAO,CAAC;MAC5BgB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMe,UAAU,GAAG,MAAOJ,MAAM,IAAK;IACnC,IAAIA,MAAM,CAACR,GAAG,KAAKA,GAAG,EAAE;MACtBO,QAAQ,CAACT,IAAI,CAAC;IAChB,CAAC,MAAM;MACLjB,OAAO,CAAC8B,KAAK,CAAC,aAAa,CAAC;IAC9B;EACF,CAAC;EAED,MAAME,WAAW,GAAG,MAAOC,QAAQ,IAAK;IACtC,IAAI,CAACA,QAAQ,CAACC,IAAI,IAAI,CAACD,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MAC3DpC,OAAO,CAAC8B,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF;IACAR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMpB,OAAO,CAACyB,QAAQ,CAAC;MACxC,IAAIL,QAAQ,CAACC,OAAO,EAAE;QACpB7B,OAAO,CAAC6B,OAAO,CAACD,QAAQ,CAAC5B,OAAO,CAAC;QACjCkB,OAAO,CAACe,QAAQ,CAAC;QACjBb,MAAM,CAACQ,QAAQ,CAACX,IAAI,CAAC;QACrBD,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACLhB,OAAO,CAAC8B,KAAK,CAACF,QAAQ,CAAC5B,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACd9B,OAAO,CAAC8B,KAAK,CAACA,KAAK,CAAC9B,OAAO,CAAC;IAC9B;IACAsB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;;EAED;EACA,MAAMe,mBAAmB,GAAG,MAAOC,kBAAkB,IAAK;IACxDhB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMW,kBAAkB,CAAC;QACxCC,UAAU,EAAEF,kBAAkB,CAACE;MACjC,CAAC,CAAC;MAEF,IAAIZ,QAAQ,CAACC,OAAO,EAAE;QACpB7B,OAAO,CAAC6B,OAAO,CAAC,sCAAsC,CAAC;QACvDJ,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLzB,OAAO,CAAC8B,KAAK,CAACF,QAAQ,CAAC5B,OAAO,IAAI,4BAA4B,CAAC;MACjE;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD9B,OAAO,CAAC8B,KAAK,CAAC,mDAAmD,CAAC;IACpE;IACAR,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;;EAED;EACA,MAAMoB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1C,OAAO,CAAC8B,KAAK,CAAC,6CAA6C,CAAC;EAC9D,CAAC;EAED,oBACEnB,OAAA,CAACgC,mBAAmB;IAACC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAI,uBAAwB;IAAAC,QAAA,eAC/FrC,OAAA;MAAKsC,SAAS,EAAC,oBAAoB;MAAAD,QAAA,eACjCrC,OAAA;QAAKsC,SAAS,EAAC,eAAe;QAAAD,QAAA,EAC7BjC,YAAY,gBACXJ,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BrC,OAAA;cAAKsC,SAAS,EAAC,mBAAmB;cAAAD,QAAA,eAChCrC,OAAA;gBAAGsC,SAAS,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACN1C,OAAA;cAAIsC,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD1C,OAAA;cAAGsC,SAAS,EAAC,mBAAmB;cAAAD,QAAA,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAEN1C,OAAA,CAACZ,IAAI;YAACuD,MAAM,EAAC,UAAU;YAAC5B,QAAQ,EAAEK,UAAW;YAACkB,SAAS,EAAC,eAAe;YAAAD,QAAA,gBACrErC,OAAA,CAACZ,IAAI,CAACwD,IAAI;cAACrB,IAAI,EAAC,KAAK;cAACsB,KAAK,EAAC,UAAU;cAACC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAAgD,QAAA,eACnGrC,OAAA,CAACV,KAAK;gBACJ0D,IAAI,EAAC,QAAQ;gBACbV,SAAS,EAAC,sBAAsB;gBAChCW,WAAW,EAAC,mBAAmB;gBAC/BC,SAAS,EAAE;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1C,OAAA;cAAQgD,IAAI,EAAC,QAAQ;cAACV,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAC;YAE/C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAEN1C,OAAA;UAAAqC,QAAA,gBACErC,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BrC,OAAA;cAAKmD,GAAG,EAAErD,IAAK;cAACsD,GAAG,EAAC,gBAAgB;cAACd,SAAS,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjE1C,OAAA;cAAIsC,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClD1C,OAAA;cAAGsC,SAAS,EAAC,mBAAmB;cAAAD,QAAA,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,EAGLR,OAAO,CAACC,GAAG,CAACC,0BAA0B,IAAIF,OAAO,CAACC,GAAG,CAACC,0BAA0B,KAAK,4BAA4B,iBAChHpC,OAAA;YAAKsC,SAAS,EAAC,qBAAqB;YAAAD,QAAA,gBAClCrC,OAAA,CAACqD,WAAW;cACVC,SAAS,EAAE5B,mBAAoB;cAC/B6B,OAAO,EAAExB,iBAAkB;cAC3ByB,SAAS,EAAE,KAAM;cACjBC,KAAK,EAAC,SAAS;cACfC,IAAI,EAAC,OAAO;cACZC,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAC,aAAa;cACnBC,cAAc,EAAC,MAAM;cACrBC,KAAK,EAAC;YAAM;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAEF1C,OAAA,CAAC+D,OAAO;cAACzB,SAAS,EAAC,cAAc;cAAAD,QAAA,eAC/BrC,OAAA;gBAAMsC,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,eAED1C,OAAA,CAACZ,IAAI;YAACuD,MAAM,EAAC,UAAU;YAAC5B,QAAQ,EAAEM,WAAY;YAACiB,SAAS,EAAC,eAAe;YAAAD,QAAA,gBACtErC,OAAA,CAACZ,IAAI,CAACwD,IAAI;cAACrB,IAAI,EAAC,MAAM;cAACsB,KAAK,EAAC,WAAW;cAACC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAA0B,CAAC,CAAE;cAAAgD,QAAA,eACvGrC,OAAA,CAACV,KAAK;gBACJ0D,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,YAAY;gBACtBW,WAAW,EAAC,sBAAsB;gBAClCe,YAAY,EAAC;cAAM;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cAACrB,IAAI,EAAC,QAAQ;cAACsB,KAAK,EAAC,QAAQ;cAACC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAAgD,QAAA,eACxGrC,OAAA,CAACV,KAAK;gBACJ0D,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,YAAY;gBACtBW,WAAW,EAAC,wBAAwB;gBACpCe,YAAY,EAAC;cAAc;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cAACrB,IAAI,EAAC,OAAO;cAACsB,KAAK,EAAC,iBAAiB;cAACC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAAgD,QAAA,eAChHrC,OAAA,CAACT,MAAM;gBACL0E,QAAQ,EAAGC,KAAK,IAAKrD,aAAa,CAACqD,KAAK,CAAE;gBAC1C5B,SAAS,EAAC,YAAY;gBACtBW,WAAW,EAAC,wBAAwB;gBAAAZ,QAAA,gBAEpCrC,OAAA,CAACC,MAAM;kBAACiE,KAAK,EAAC,SAAS;kBAAA7B,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClD1C,OAAA,CAACC,MAAM;kBAACiE,KAAK,EAAC,WAAW;kBAAA7B,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtD1C,OAAA,CAACC,MAAM;kBAACiE,KAAK,EAAC,SAAS;kBAAA7B,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cAACrB,IAAI,EAAC,OAAO;cAACsB,KAAK,EAAC,OAAO;cAACC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAA4B,CAAC,CAAE;cAAAgD,QAAA,eACtGrC,OAAA,CAACT,MAAM;gBAAC+C,SAAS,EAAC,YAAY;gBAACW,WAAW,EAAC,mBAAmB;gBAAAZ,QAAA,GAC3DzB,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACuD,GAAG,CAAEC,CAAC,iBACvDpE,OAAA,CAACC,MAAM;kBAASiE,KAAK,EAAEE,CAAE;kBAAA/B,QAAA,EAAG,SAAQ+B,CAAE;gBAAC,GAA1BA,CAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkC,CACjD,CAAC,EACD9B,UAAU,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACuD,GAAG,CAAEC,CAAC,iBAChDpE,OAAA,CAACC,MAAM;kBAASiE,KAAK,EAAG,QAAOE,CAAE,EAAE;kBAAA/B,QAAA,EAAG,QAAO+B,CAAE;gBAAC,GAAnCA,CAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2C,CAC1D,CAAC,EACD9B,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuD,GAAG,CAAEC,CAAC,iBACxCpE,OAAA,CAACC,MAAM;kBAASiE,KAAK,EAAG,QAAOE,CAAE,EAAE;kBAAA/B,QAAA,EAAG,QAAO+B,CAAE;gBAAC,GAAnCA,CAAC;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2C,CAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cACRrB,IAAI,EAAC,OAAO;cACZsB,KAAK,EAAC,eAAe;cACrBC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAA2B,CAAC,EACvD;gBAAE2D,IAAI,EAAE,OAAO;gBAAE3D,OAAO,EAAE;cAAsC,CAAC,EACjE;gBACEgF,OAAO,EAAE,kDAAkD;gBAC3DhF,OAAO,EAAE;cACX,CAAC,CACD;cAAAgD,QAAA,gBAEFrC,OAAA,CAACV,KAAK;gBACJ0D,IAAI,EAAC,OAAO;gBACZV,SAAS,EAAC,YAAY;gBACtBW,WAAW,EAAC,sDAAsD;gBAClEe,YAAY,EAAC;cAAO;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACF1C,OAAA;gBAAGsC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAC;cAA0C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cACRrB,IAAI,EAAC,aAAa;cAClBsB,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAAkC,CAAC,EAC9D;gBAAEgF,OAAO,EAAE,UAAU;gBAAEhF,OAAO,EAAE;cAA0C,CAAC,CAC3E;cAAAgD,QAAA,gBAEFrC,OAAA,CAACV,KAAK;gBACJ0D,IAAI,EAAC,KAAK;gBACVE,SAAS,EAAE,EAAG;gBACdZ,SAAS,EAAC,YAAY;gBACtBW,WAAW,EAAC,6BAA6B;gBACzCe,YAAY,EAAC;cAAK;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACF1C,OAAA;gBAAGsC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cACRrB,IAAI,EAAC,UAAU;cACfsB,KAAK,EAAC,UAAU;cAChBC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAA8B,CAAC,EAC1D;gBAAEiF,GAAG,EAAE,CAAC;gBAAEjF,OAAO,EAAE;cAA+C,CAAC,EACnE;gBACEgF,OAAO,EAAE,iEAAiE;gBAC1EhF,OAAO,EAAE;cACX,CAAC,CACD;cAAAgD,QAAA,gBAEFrC,OAAA,CAACV,KAAK,CAACiF,QAAQ;gBACbjC,SAAS,EAAC,YAAY;gBACtBW,WAAW,EAAC,6CAA6C;gBACzDe,YAAY,EAAC;cAAc;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACF1C,OAAA;gBAAGsC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAC;cAAiE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cACRrB,IAAI,EAAC,iBAAiB;cACtBsB,KAAK,EAAC,iBAAiB;cACvB2B,YAAY,EAAE,CAAC,UAAU,CAAE;cAC3B1B,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1D,OAAO,EAAE;cAA+B,CAAC,EAC3D,CAAC;gBAAEoF;cAAc,CAAC,MAAM;gBACtBC,SAASA,CAACC,CAAC,EAAET,KAAK,EAAE;kBAClB,IAAI,CAACA,KAAK,IAAIO,aAAa,CAAC,UAAU,CAAC,KAAKP,KAAK,EAAE;oBACjD,OAAOU,OAAO,CAACC,OAAO,CAAC,CAAC;kBAC1B;kBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBACrE;cACF,CAAC,CAAC,CACF;cAAA1C,QAAA,gBAEFrC,OAAA,CAACV,KAAK,CAACiF,QAAQ;gBACbjC,SAAS,EAAC,YAAY;gBACtBW,WAAW,EAAC,iCAAiC;gBAC7Ce,YAAY,EAAC;cAAc;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACF1C,OAAA;gBAAGsC,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAC;cAA6B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAEZ1C,OAAA,CAACZ,IAAI,CAACwD,IAAI;cAAAP,QAAA,eACRrC,OAAA;gBAAQgD,IAAI,EAAC,QAAQ;gBAACV,SAAS,EAAC,cAAc;gBAAC0C,QAAQ,EAAEtE,OAAQ;gBAAA2B,QAAA,EAC9D3B,OAAO,GAAG,qBAAqB,GAAG;cAAgB;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEP1C,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAD,QAAA,eAC9BrC,OAAA;cAAAqC,QAAA,GAAG,2BACwB,EAAC,GAAG,eAC7BrC,OAAA,CAACN,IAAI;gBAACuF,EAAE,EAAC,QAAQ;gBAAC3C,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAC;cAE5C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE1B;AAACvC,EAAA,CA/RQD,QAAQ;EAAA,QAMEP,WAAW;AAAA;AAAAuF,EAAA,GANrBhF,QAAQ;AAiSjB,eAAeA,QAAQ;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}