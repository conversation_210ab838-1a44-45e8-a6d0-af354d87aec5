{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Register\\\\index.js\",\n  _s = $RefreshSig$();\nimport { Form, message, Input, Select } from \"antd\";\nimport React, { useState } from \"react\";\nimport \"./index.css\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\nimport Logo from \"../../../assets/logo.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction Register() {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [schoolType, setSchoolType] = useState(\"\");\n  const [suggestedUsername, setSuggestedUsername] = useState(\"\");\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n\n  // Generate username from names\n  const generateUsername = (firstName, middleName, lastName) => {\n    if (!firstName) return \"\";\n    const cleanName = name => (name === null || name === void 0 ? void 0 : name.toLowerCase().replace(/[^a-z]/g, '')) || '';\n    const first = cleanName(firstName);\n    const middle = cleanName(middleName);\n    const last = cleanName(lastName);\n\n    // Generate different username options\n    const options = [`${first}${middle}${last}`, `${first}${last}`, `${first}_${last}`, `${first}${last}${Math.floor(Math.random() * 100)}`, `${first}_${middle}_${last}`].filter(option => option.length >= 3);\n    return options[0] || `user${Math.floor(Math.random() * 10000)}`;\n  };\n\n  // Handle name changes to auto-generate username\n  const handleNameChange = () => {\n    const firstName = form.getFieldValue('firstName');\n    const middleName = form.getFieldValue('middleName');\n    const lastName = form.getFieldValue('lastName');\n    if (firstName) {\n      const username = generateUsername(firstName, middleName, lastName);\n      setSuggestedUsername(username);\n      form.setFieldsValue({\n        username\n      });\n    }\n  };\n  const onFinish = async values => {\n    console.log(\"🚀 Registration data:\", values);\n    try {\n      setLoading(true);\n\n      // Prepare registration data\n      const registrationData = {\n        firstName: values.firstName,\n        middleName: values.middleName,\n        lastName: values.lastName,\n        username: values.username,\n        school: values.school,\n        level: values.level,\n        class: values.class,\n        phoneNumber: values.phoneNumber,\n        password: values.password\n      };\n      const response = await registerUser(registrationData);\n      if (response.success) {\n        message.success({\n          content: \"🎉 Registration successful! Welcome to BrainWave!\",\n          duration: 5,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        // Add a small delay to let user see the success message\n        setTimeout(() => {\n          navigate(\"/login\");\n        }, 1500);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Registration failed\");\n      }\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const verifyUser = async values => {\n    var _values$otp;\n    if (!((_values$otp = values.otp) !== null && _values$otp !== void 0 && _values$otp.trim())) {\n      message.error(\"🔢 Please enter the verification code\");\n      return;\n    }\n    if (values.otp.length !== 6) {\n      message.error(\"🔢 Verification code must be 6 digits\");\n      return;\n    }\n    if (values.otp === otp) {\n      message.loading(\"✅ Verifying your code...\", 1);\n      setTimeout(() => {\n        onFinish(data);\n      }, 1000);\n    } else {\n      message.error({\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\n        duration: 5,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n\n  // Enhanced error handling function\n  const showUserFriendlyError = (error, defaultMessage) => {\n    var _error$response, _error$response$data, _error$response2, _error$response2$data;\n    const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || defaultMessage;\n    const errorType = (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.errorType;\n\n    // Show field-specific errors for duplicate email/phone\n    if (errorType === \"EMAIL_EXISTS\") {\n      form.setFields([{\n        name: 'email',\n        errors: [errorMessage]\n      }]);\n      message.warning({\n        content: \"Please use a different email address\",\n        duration: 4,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else if (errorType === \"PHONE_EXISTS\") {\n      form.setFields([{\n        name: 'phoneNumber',\n        errors: [errorMessage]\n      }]);\n      message.warning({\n        content: \"Please use a different phone number\",\n        duration: 4,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    } else if (errorType === \"INVALID_EMAIL\") {\n      form.setFields([{\n        name: 'email',\n        errors: [errorMessage]\n      }]);\n    } else if (errorType === \"INVALID_PHONE\") {\n      form.setFields([{\n        name: 'phoneNumber',\n        errors: [errorMessage]\n      }]);\n    } else {\n      message.error({\n        content: errorMessage,\n        duration: 4,\n        style: {\n          marginTop: '20px'\n        }\n      });\n    }\n  };\n  const generateOTP = async formData => {\n    console.log(\"🚀 Form submitted with data:\", formData);\n\n    // Simple validation - let Ant Design handle the form validation\n    console.log(\"✅ Form validation passed, proceeding with OTP generation...\");\n    setLoading(true);\n    try {\n      const response = await sendOTP(formData);\n      if (response.success) {\n        message.success({\n          content: response.message,\n          duration: 5,\n          style: {\n            marginTop: '20px'\n          }\n        });\n        setData(formData);\n        setOTP(response.data);\n        setVerification(true);\n      } else {\n        showUserFriendlyError({\n          response\n        }, \"Failed to send verification code\");\n      }\n    } catch (error) {\n      console.error(\"OTP generation error:\", error);\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"register-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"register-card\",\n      children: verification ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Verify Your Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"We've sent a verification code to your email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"otp-instructions\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"otp-info-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"otp-info-title\",\n              children: \"\\uD83D\\uDCE7 Check Your Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"otp-info-text\",\n              children: [\"We've sent a \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"6-digit verification code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 32\n              }, this), \" to your email address.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-steps\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Open your email app or website\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Look for an email from BrainWave\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Copy the 6-digit code from the email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"otp-step\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-number\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"step-text\",\n                  children: \"Enter the code in the box below\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"otp-help\",\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"help-text\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"\\uD83D\\uDCA1 Can't find the email?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this), \" Check your spam/junk folder or wait a few minutes for delivery.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          layout: \"vertical\",\n          onFinish: verifyUser,\n          className: \"register-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"otp\",\n            label: \"Verification Code\",\n            rules: [{\n              required: true,\n              message: \"Please enter the OTP!\"\n            }],\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              type: \"number\",\n              className: \"form-input otp-input\",\n              placeholder: \"Enter 6-digit code (e.g., 123456)\",\n              maxLength: 6\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"form-help-text\",\n              children: \"Enter the verification code from your email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"register-btn\",\n            disabled: loading,\n            children: loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resend-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"resend-text\",\n              children: \"Didn't receive the code?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"resend-btn\",\n              onClick: () => generateOTP(data),\n              disabled: loading,\n              children: \"\\uD83D\\uDCE7 Resend Verification Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: Logo,\n            alt: \"BrainWave Logo\",\n            className: \"register-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"register-title\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"register-subtitle\",\n            children: \"Join thousands of students learning with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          onFinish: generateOTP,\n          className: \"register-form\",\n          onFinishFailed: errorInfo => {\n            console.log(\"❌ Form validation failed:\", errorInfo);\n            message.error(\"Please fill all required fields correctly\");\n          },\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"Full Name\",\n            rules: [{\n              required: true,\n              message: \"Please enter your full name\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your full name (e.g., John Doe)\",\n              autoComplete: \"name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"school\",\n            label: \"School Name\",\n            rules: [{\n              required: true,\n              message: \"Please enter your school name\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              className: \"form-input\",\n              placeholder: \"Enter your school name (e.g., Dar es Salaam Secondary School)\",\n              autoComplete: \"organization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Education Level\",\n            rules: [{\n              required: true,\n              message: \"Please select your education level\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              onChange: value => setSchoolType(value),\n              className: \"form-input\",\n              placeholder: \"Choose your current education level\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"Primary\",\n                children: \"\\uD83C\\uDF92 Primary Education (Classes 1-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Secondary\",\n                children: \"\\uD83D\\uDCDA Secondary Education (Forms 1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Advance\",\n                children: \"\\uD83C\\uDF93 Advanced Level (Forms 5-6)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class\",\n            label: \"Class/Form\",\n            rules: [{\n              required: true,\n              message: \"Please select your class or form\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              className: \"form-input\",\n              placeholder: schoolType ? \"Select your class/form\" : \"Please select education level first\",\n              disabled: !schoolType,\n              children: [schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: i,\n                children: `📚 Class ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 21\n              }, this)), schoolType === \"Secondary\" && [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `📖 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 21\n              }, this)), schoolType === \"Advance\" && [5, 6].map(i => /*#__PURE__*/_jsxDEV(Option, {\n                value: `Form-${i}`,\n                children: `🎓 Form ${i}`\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"Email Address\",\n            rules: [{\n              required: true,\n              message: \"Please enter your email\"\n            }, {\n              type: \"email\",\n              message: \"Email is wrong written\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              className: \"form-input\",\n              placeholder: \"Enter your email address (e.g., <EMAIL>)\",\n              autoComplete: \"email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"phoneNumber\",\n            label: \"Phone Number\",\n            rules: [{\n              required: true,\n              message: \"Please enter your phone number\"\n            }, {\n              pattern: /^0[67]\\d{8}$/,\n              message: \"Phone number is wrong written\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              type: \"tel\",\n              className: \"form-input\",\n              placeholder: \"Enter mobile number (e.g., ********** or 0612345678)\",\n              autoComplete: \"tel\",\n              maxLength: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"Password\",\n            rules: [{\n              required: true,\n              message: \"Please enter your password\"\n            }, {\n              min: 8,\n              message: \"Password must be at least 8 characters long\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Create a strong password (min 8 characters)\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"confirmPassword\",\n            label: \"Retype Password\",\n            dependencies: ['password'],\n            rules: [{\n              required: true,\n              message: \"Please retype your password\"\n            }, ({\n              getFieldValue\n            }) => ({\n              validator(_, value) {\n                if (!value || getFieldValue('password') === value) {\n                  return Promise.resolve();\n                }\n                return Promise.reject(new Error('The two passwords do not match'));\n              }\n            })],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              className: \"form-input\",\n              placeholder: \"Retype your password to confirm\",\n              autoComplete: \"new-password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"register-btn\",\n              disabled: loading,\n              onClick: () => console.log(\"🔘 Create Account button clicked\"),\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this), \"Creating Account...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: \"\\uD83D\\uDE80 Create Account\"\n              }, void 0, false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"register-btn\",\n              style: {\n                marginTop: '10px',\n                background: '#28a745'\n              },\n              onClick: () => {\n                // Get form instance and values\n                const form = document.querySelector('.register-form');\n                const formData = new FormData(form);\n                const values = {};\n                for (let [key, value] of formData.entries()) {\n                  values[key] = value;\n                }\n                console.log(\"📋 Form values:\", values);\n\n                // Call generateOTP directly with test data\n                const testData = {\n                  name: \"Test User\",\n                  school: \"Test School\",\n                  level: \"Primary\",\n                  class: 1,\n                  email: \"<EMAIL>\",\n                  phoneNumber: \"**********\",\n                  password: \"password123\",\n                  confirmPassword: \"password123\"\n                };\n                generateOTP(testData);\n              },\n              children: \"\\uD83E\\uDDEA Test Submit (Bypass Validation)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Already have an account? \", \" \", /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"register-link\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"cYtGySinwHu363NFtWUU229ohlM=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["Form", "message", "Input", "Select", "React", "useState", "Link", "useNavigate", "registerUser", "sendOTP", "Logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "Register", "_s", "loading", "setLoading", "schoolType", "setSchoolType", "suggestedUsername", "setSuggestedUsername", "form", "useForm", "navigate", "generateUsername", "firstName", "middleName", "lastName", "cleanName", "name", "toLowerCase", "replace", "first", "middle", "last", "options", "Math", "floor", "random", "filter", "option", "length", "handleNameChange", "getFieldValue", "username", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onFinish", "values", "console", "log", "registrationData", "school", "level", "class", "phoneNumber", "password", "response", "success", "content", "duration", "style", "marginTop", "setTimeout", "showUserFriendlyError", "error", "verifyUser", "_values$otp", "otp", "trim", "data", "defaultMessage", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errorMessage", "errorType", "setFields", "errors", "warning", "generateOTP", "formData", "setData", "setOTP", "setVerification", "className", "children", "verification", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "<PERSON><PERSON>", "label", "rules", "required", "type", "placeholder", "max<PERSON><PERSON><PERSON>", "disabled", "onClick", "onFinishFailed", "errorInfo", "autoComplete", "onChange", "value", "map", "i", "pattern", "min", "Password", "dependencies", "validator", "_", "Promise", "resolve", "reject", "Error", "background", "document", "querySelector", "FormData", "key", "entries", "testData", "email", "confirmPassword", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Register/index.js"], "sourcesContent": ["import { Form, message, Input, Select } from \"antd\";\r\nimport React, { useState } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useNavigate } from \"react-router-dom\";\r\nimport { registerUser, sendOTP } from \"../../../apicalls/users\";\r\nimport Logo from \"../../../assets/logo.png\";\r\n\r\nconst { Option } = Select;\r\n\r\nfunction Register() {\r\n  const [loading, setLoading] = useState(false);\r\n  const [schoolType, setSchoolType] = useState(\"\");\r\n  const [suggestedUsername, setSuggestedUsername] = useState(\"\");\r\n  const [form] = Form.useForm();\r\n  const navigate = useNavigate();\r\n\r\n  // Generate username from names\r\n  const generateUsername = (firstName, middleName, lastName) => {\r\n    if (!firstName) return \"\";\r\n\r\n    const cleanName = (name) => name?.toLowerCase().replace(/[^a-z]/g, '') || '';\r\n    const first = cleanName(firstName);\r\n    const middle = cleanName(middleName);\r\n    const last = cleanName(lastName);\r\n\r\n    // Generate different username options\r\n    const options = [\r\n      `${first}${middle}${last}`,\r\n      `${first}${last}`,\r\n      `${first}_${last}`,\r\n      `${first}${last}${Math.floor(Math.random() * 100)}`,\r\n      `${first}_${middle}_${last}`,\r\n    ].filter(option => option.length >= 3);\r\n\r\n    return options[0] || `user${Math.floor(Math.random() * 10000)}`;\r\n  };\r\n\r\n  // Handle name changes to auto-generate username\r\n  const handleNameChange = () => {\r\n    const firstName = form.getFieldValue('firstName');\r\n    const middleName = form.getFieldValue('middleName');\r\n    const lastName = form.getFieldValue('lastName');\r\n\r\n    if (firstName) {\r\n      const username = generateUsername(firstName, middleName, lastName);\r\n      setSuggestedUsername(username);\r\n      form.setFieldsValue({ username });\r\n    }\r\n  };\r\n\r\n  const onFinish = async (values) => {\r\n    console.log(\"🚀 Registration data:\", values);\r\n\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Prepare registration data\r\n      const registrationData = {\r\n        firstName: values.firstName,\r\n        middleName: values.middleName,\r\n        lastName: values.lastName,\r\n        username: values.username,\r\n        school: values.school,\r\n        level: values.level,\r\n        class: values.class,\r\n        phoneNumber: values.phoneNumber,\r\n        password: values.password\r\n      };\r\n\r\n      const response = await registerUser(registrationData);\r\n      if (response.success) {\r\n        message.success({\r\n          content: \"🎉 Registration successful! Welcome to BrainWave!\",\r\n          duration: 5,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        // Add a small delay to let user see the success message\r\n        setTimeout(() => {\r\n          navigate(\"/login\");\r\n        }, 1500);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Registration failed\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Registration error:\", error);\r\n      showUserFriendlyError(error, \"Registration failed. Please try again.\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (!values.otp?.trim()) {\r\n      message.error(\"🔢 Please enter the verification code\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp.length !== 6) {\r\n      message.error(\"🔢 Verification code must be 6 digits\");\r\n      return;\r\n    }\r\n\r\n    if (values.otp === otp) {\r\n      message.loading(\"✅ Verifying your code...\", 1);\r\n      setTimeout(() => {\r\n        onFinish(data);\r\n      }, 1000);\r\n    } else {\r\n      message.error({\r\n        content: \"❌ The verification code is incorrect. Please check your email and try again.\",\r\n        duration: 5,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  // Enhanced error handling function\r\n  const showUserFriendlyError = (error, defaultMessage) => {\r\n    const errorMessage = error.response?.data?.message || error.message || defaultMessage;\r\n    const errorType = error.response?.data?.errorType;\r\n\r\n    // Show field-specific errors for duplicate email/phone\r\n    if (errorType === \"EMAIL_EXISTS\") {\r\n      form.setFields([{\r\n        name: 'email',\r\n        errors: [errorMessage]\r\n      }]);\r\n      message.warning({\r\n        content: \"Please use a different email address\",\r\n        duration: 4,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else if (errorType === \"PHONE_EXISTS\") {\r\n      form.setFields([{\r\n        name: 'phoneNumber',\r\n        errors: [errorMessage]\r\n      }]);\r\n      message.warning({\r\n        content: \"Please use a different phone number\",\r\n        duration: 4,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    } else if (errorType === \"INVALID_EMAIL\") {\r\n      form.setFields([{\r\n        name: 'email',\r\n        errors: [errorMessage]\r\n      }]);\r\n    } else if (errorType === \"INVALID_PHONE\") {\r\n      form.setFields([{\r\n        name: 'phoneNumber',\r\n        errors: [errorMessage]\r\n      }]);\r\n    } else {\r\n      message.error({\r\n        content: errorMessage,\r\n        duration: 4,\r\n        style: { marginTop: '20px' }\r\n      });\r\n    }\r\n  };\r\n\r\n  const generateOTP = async (formData) => {\r\n    console.log(\"🚀 Form submitted with data:\", formData);\r\n\r\n    // Simple validation - let Ant Design handle the form validation\r\n    console.log(\"✅ Form validation passed, proceeding with OTP generation...\");\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await sendOTP(formData);\r\n      if (response.success) {\r\n        message.success({\r\n          content: response.message,\r\n          duration: 5,\r\n          style: { marginTop: '20px' }\r\n        });\r\n        setData(formData);\r\n        setOTP(response.data);\r\n        setVerification(true);\r\n      } else {\r\n        showUserFriendlyError({ response }, \"Failed to send verification code\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"OTP generation error:\", error);\r\n      showUserFriendlyError(error, \"Something went wrong. Please check your information and try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"register-container\">\r\n      <div className=\"register-card\">\r\n        {verification ? (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Verify Your Email</h1>\r\n              <p className=\"register-subtitle\">We've sent a verification code to your email</p>\r\n            </div>\r\n\r\n            {/* OTP Instructions */}\r\n            <div className=\"otp-instructions\">\r\n              <div className=\"otp-info-card\">\r\n                <h3 className=\"otp-info-title\">📧 Check Your Email</h3>\r\n                <p className=\"otp-info-text\">\r\n                  We've sent a <strong>6-digit verification code</strong> to your email address.\r\n                </p>\r\n                <div className=\"otp-steps\">\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">1</span>\r\n                    <span className=\"step-text\">Open your email app or website</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">2</span>\r\n                    <span className=\"step-text\">Look for an email from BrainWave</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">3</span>\r\n                    <span className=\"step-text\">Copy the 6-digit code from the email</span>\r\n                  </div>\r\n                  <div className=\"otp-step\">\r\n                    <span className=\"step-number\">4</span>\r\n                    <span className=\"step-text\">Enter the code in the box below</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"otp-help\">\r\n                  <p className=\"help-text\">\r\n                    <strong>💡 Can't find the email?</strong> Check your spam/junk folder or wait a few minutes for delivery.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <Form layout=\"vertical\" onFinish={verifyUser} className=\"register-form\">\r\n              <Form.Item name=\"otp\" label=\"Verification Code\" rules={[{ required: true, message: \"Please enter the OTP!\" }]}>\r\n                <Input\r\n                  type=\"number\"\r\n                  className=\"form-input otp-input\"\r\n                  placeholder=\"Enter 6-digit code (e.g., 123456)\"\r\n                  maxLength={6}\r\n                />\r\n                <p className=\"form-help-text\">Enter the verification code from your email</p>\r\n              </Form.Item>\r\n\r\n              <button type=\"submit\" className=\"register-btn\" disabled={loading}>\r\n                {loading ? \"⏳ Verifying...\" : \"✅ Verify & Complete Registration\"}\r\n              </button>\r\n\r\n              <div className=\"resend-section\">\r\n                <p className=\"resend-text\">Didn't receive the code?</p>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"resend-btn\"\r\n                  onClick={() => generateOTP(data)}\r\n                  disabled={loading}\r\n                >\r\n                  📧 Resend Verification Code\r\n                </button>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <div className=\"register-header\">\r\n              <img src={Logo} alt=\"BrainWave Logo\" className=\"register-logo\" />\r\n              <h1 className=\"register-title\">Create Account</h1>\r\n              <p className=\"register-subtitle\">Join thousands of students learning with BrainWave</p>\r\n            </div>\r\n\r\n\r\n\r\n            <Form\r\n              form={form}\r\n              layout=\"vertical\"\r\n              onFinish={generateOTP}\r\n              className=\"register-form\"\r\n              onFinishFailed={(errorInfo) => {\r\n                console.log(\"❌ Form validation failed:\", errorInfo);\r\n                message.error(\"Please fill all required fields correctly\");\r\n              }}\r\n            >\r\n\r\n\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"Full Name\"\r\n                rules={[{ required: true, message: \"Please enter your full name\" }]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your full name (e.g., John Doe)\"\r\n                  autoComplete=\"name\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"school\"\r\n                label=\"School Name\"\r\n                rules={[{ required: true, message: \"Please enter your school name\" }]}\r\n              >\r\n                <Input\r\n                  type=\"text\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your school name (e.g., Dar es Salaam Secondary School)\"\r\n                  autoComplete=\"organization\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"level\"\r\n                label=\"Education Level\"\r\n                rules={[{ required: true, message: \"Please select your education level\" }]}\r\n              >\r\n                <Select\r\n                  onChange={(value) => setSchoolType(value)}\r\n                  className=\"form-input\"\r\n                  placeholder=\"Choose your current education level\"\r\n                >\r\n                  <Option value=\"Primary\">🎒 Primary Education (Classes 1-7)</Option>\r\n                  <Option value=\"Secondary\">📚 Secondary Education (Forms 1-4)</Option>\r\n                  <Option value=\"Advance\">🎓 Advanced Level (Forms 5-6)</Option>\r\n                </Select>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"class\"\r\n                label=\"Class/Form\"\r\n                rules={[{ required: true, message: \"Please select your class or form\" }]}\r\n              >\r\n                <Select\r\n                  className=\"form-input\"\r\n                  placeholder={schoolType ? \"Select your class/form\" : \"Please select education level first\"}\r\n                  disabled={!schoolType}\r\n                >\r\n                  {schoolType === \"Primary\" && [1, 2, 3, 4, 5, 6, 7].map((i) => (\r\n                    <Option key={i} value={i}>{`📚 Class ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Secondary\" && [1, 2, 3, 4].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`📖 Form ${i}`}</Option>\r\n                  ))}\r\n                  {schoolType === \"Advance\" && [5, 6].map((i) => (\r\n                    <Option key={i} value={`Form-${i}`}>{`🎓 Form ${i}`}</Option>\r\n                  ))}\r\n                </Select>\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"email\"\r\n                label=\"Email Address\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your email\" },\r\n                  { type: \"email\", message: \"Email is wrong written\" }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"email\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter your email address (e.g., <EMAIL>)\"\r\n                  autoComplete=\"email\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"phoneNumber\"\r\n                label=\"Phone Number\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your phone number\" },\r\n                  { pattern: /^0[67]\\d{8}$/, message: \"Phone number is wrong written\" }\r\n                ]}\r\n              >\r\n                <Input\r\n                  type=\"tel\"\r\n                  className=\"form-input\"\r\n                  placeholder=\"Enter mobile number (e.g., ********** or 0612345678)\"\r\n                  autoComplete=\"tel\"\r\n                  maxLength={10}\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"password\"\r\n                label=\"Password\"\r\n                rules={[\r\n                  { required: true, message: \"Please enter your password\" },\r\n                  { min: 8, message: \"Password must be at least 8 characters long\" }\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Create a strong password (min 8 characters)\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item\r\n                name=\"confirmPassword\"\r\n                label=\"Retype Password\"\r\n                dependencies={['password']}\r\n                rules={[\r\n                  { required: true, message: \"Please retype your password\" },\r\n                  ({ getFieldValue }) => ({\r\n                    validator(_, value) {\r\n                      if (!value || getFieldValue('password') === value) {\r\n                        return Promise.resolve();\r\n                      }\r\n                      return Promise.reject(new Error('The two passwords do not match'));\r\n                    },\r\n                  }),\r\n                ]}\r\n              >\r\n                <Input.Password\r\n                  className=\"form-input\"\r\n                  placeholder=\"Retype your password to confirm\"\r\n                  autoComplete=\"new-password\"\r\n                />\r\n              </Form.Item>\r\n\r\n              <Form.Item>\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"register-btn\"\r\n                  disabled={loading}\r\n                  onClick={() => console.log(\"🔘 Create Account button clicked\")}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <span className=\"loading-spinner\"></span>\r\n                      Creating Account...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      🚀 Create Account\r\n                    </>\r\n                  )}\r\n                </button>\r\n\r\n                {/* Test button to bypass validation */}\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"register-btn\"\r\n                  style={{marginTop: '10px', background: '#28a745'}}\r\n                  onClick={() => {\r\n                    // Get form instance and values\r\n                    const form = document.querySelector('.register-form');\r\n                    const formData = new FormData(form);\r\n                    const values = {};\r\n                    for (let [key, value] of formData.entries()) {\r\n                      values[key] = value;\r\n                    }\r\n                    console.log(\"📋 Form values:\", values);\r\n\r\n                    // Call generateOTP directly with test data\r\n                    const testData = {\r\n                      name: \"Test User\",\r\n                      school: \"Test School\",\r\n                      level: \"Primary\",\r\n                      class: 1,\r\n                      email: \"<EMAIL>\",\r\n                      phoneNumber: \"**********\",\r\n                      password: \"password123\",\r\n                      confirmPassword: \"password123\"\r\n                    };\r\n                    generateOTP(testData);\r\n                  }}\r\n                >\r\n                  🧪 Test Submit (Bypass Validation)\r\n                </button>\r\n              </Form.Item>\r\n            </Form>\r\n\r\n            <div className=\"register-footer\">\r\n              <p>\r\n                Already have an account? {\" \"}\r\n                <Link to=\"/login\" className=\"register-link\">\r\n                  Sign In\r\n                </Link>\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Register;\r\n"], "mappings": ";;AAAA,SAASA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AACnD,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAC/D,OAAOC,IAAI,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAM;EAAEC;AAAO,CAAC,GAAGZ,MAAM;AAEzB,SAASa,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmB,IAAI,CAAC,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoB,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IAC5D,IAAI,CAACF,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMG,SAAS,GAAIC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,KAAI,EAAE;IAC5E,MAAMC,KAAK,GAAGJ,SAAS,CAACH,SAAS,CAAC;IAClC,MAAMQ,MAAM,GAAGL,SAAS,CAACF,UAAU,CAAC;IACpC,MAAMQ,IAAI,GAAGN,SAAS,CAACD,QAAQ,CAAC;;IAEhC;IACA,MAAMQ,OAAO,GAAG,CACb,GAAEH,KAAM,GAAEC,MAAO,GAAEC,IAAK,EAAC,EACzB,GAAEF,KAAM,GAAEE,IAAK,EAAC,EAChB,GAAEF,KAAM,IAAGE,IAAK,EAAC,EACjB,GAAEF,KAAM,GAAEE,IAAK,GAAEE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAE,EAAC,EAClD,GAAEN,KAAM,IAAGC,MAAO,IAAGC,IAAK,EAAC,CAC7B,CAACK,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,MAAM,IAAI,CAAC,CAAC;IAEtC,OAAON,OAAO,CAAC,CAAC,CAAC,IAAK,OAAMC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,CAAE,EAAC;EACjE,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMjB,SAAS,GAAGJ,IAAI,CAACsB,aAAa,CAAC,WAAW,CAAC;IACjD,MAAMjB,UAAU,GAAGL,IAAI,CAACsB,aAAa,CAAC,YAAY,CAAC;IACnD,MAAMhB,QAAQ,GAAGN,IAAI,CAACsB,aAAa,CAAC,UAAU,CAAC;IAE/C,IAAIlB,SAAS,EAAE;MACb,MAAMmB,QAAQ,GAAGpB,gBAAgB,CAACC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,CAAC;MAClEP,oBAAoB,CAACwB,QAAQ,CAAC;MAC9BvB,IAAI,CAACwB,cAAc,CAAC;QAAED;MAAS,CAAC,CAAC;IACnC;EACF,CAAC;EAED,MAAME,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,MAAM,CAAC;IAE5C,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMkC,gBAAgB,GAAG;QACvBzB,SAAS,EAAEsB,MAAM,CAACtB,SAAS;QAC3BC,UAAU,EAAEqB,MAAM,CAACrB,UAAU;QAC7BC,QAAQ,EAAEoB,MAAM,CAACpB,QAAQ;QACzBiB,QAAQ,EAAEG,MAAM,CAACH,QAAQ;QACzBO,MAAM,EAAEJ,MAAM,CAACI,MAAM;QACrBC,KAAK,EAAEL,MAAM,CAACK,KAAK;QACnBC,KAAK,EAAEN,MAAM,CAACM,KAAK;QACnBC,WAAW,EAAEP,MAAM,CAACO,WAAW;QAC/BC,QAAQ,EAAER,MAAM,CAACQ;MACnB,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMnD,YAAY,CAAC6C,gBAAgB,CAAC;MACrD,IAAIM,QAAQ,CAACC,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAAC;UACdC,OAAO,EAAE,mDAAmD;UAC5DC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACF;QACAC,UAAU,CAAC,MAAM;UACfvC,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLwC,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,qBAAqB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CD,qBAAqB,CAACC,KAAK,EAAE,wCAAwC,CAAC;IACxE,CAAC,SAAS;MACRhD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiD,UAAU,GAAG,MAAOlB,MAAM,IAAK;IAAA,IAAAmB,WAAA;IACnC,IAAI,GAAAA,WAAA,GAACnB,MAAM,CAACoB,GAAG,cAAAD,WAAA,eAAVA,WAAA,CAAYE,IAAI,CAAC,CAAC,GAAE;MACvBtE,OAAO,CAACkE,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIjB,MAAM,CAACoB,GAAG,CAAC1B,MAAM,KAAK,CAAC,EAAE;MAC3B3C,OAAO,CAACkE,KAAK,CAAC,uCAAuC,CAAC;MACtD;IACF;IAEA,IAAIjB,MAAM,CAACoB,GAAG,KAAKA,GAAG,EAAE;MACtBrE,OAAO,CAACiB,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;MAC9C+C,UAAU,CAAC,MAAM;QACfhB,QAAQ,CAACuB,IAAI,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACLvE,OAAO,CAACkE,KAAK,CAAC;QACZN,OAAO,EAAE,8EAA8E;QACvFC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAACC,KAAK,EAAEM,cAAc,KAAK;IAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACvD,MAAMC,YAAY,GAAG,EAAAJ,eAAA,GAAAP,KAAK,CAACR,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsB1E,OAAO,KAAIkE,KAAK,CAAClE,OAAO,IAAIwE,cAAc;IACrF,MAAMM,SAAS,IAAAH,gBAAA,GAAGT,KAAK,CAACR,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBE,SAAS;;IAEjD;IACA,IAAIA,SAAS,KAAK,cAAc,EAAE;MAChCvD,IAAI,CAACwD,SAAS,CAAC,CAAC;QACdhD,IAAI,EAAE,OAAO;QACbiD,MAAM,EAAE,CAACH,YAAY;MACvB,CAAC,CAAC,CAAC;MACH7E,OAAO,CAACiF,OAAO,CAAC;QACdrB,OAAO,EAAE,sCAAsC;QAC/CC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIe,SAAS,KAAK,cAAc,EAAE;MACvCvD,IAAI,CAACwD,SAAS,CAAC,CAAC;QACdhD,IAAI,EAAE,aAAa;QACnBiD,MAAM,EAAE,CAACH,YAAY;MACvB,CAAC,CAAC,CAAC;MACH7E,OAAO,CAACiF,OAAO,CAAC;QACdrB,OAAO,EAAE,qCAAqC;QAC9CC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIe,SAAS,KAAK,eAAe,EAAE;MACxCvD,IAAI,CAACwD,SAAS,CAAC,CAAC;QACdhD,IAAI,EAAE,OAAO;QACbiD,MAAM,EAAE,CAACH,YAAY;MACvB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAIC,SAAS,KAAK,eAAe,EAAE;MACxCvD,IAAI,CAACwD,SAAS,CAAC,CAAC;QACdhD,IAAI,EAAE,aAAa;QACnBiD,MAAM,EAAE,CAACH,YAAY;MACvB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL7E,OAAO,CAACkE,KAAK,CAAC;QACZN,OAAO,EAAEiB,YAAY;QACrBhB,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAO;MAC7B,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMmB,WAAW,GAAG,MAAOC,QAAQ,IAAK;IACtCjC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgC,QAAQ,CAAC;;IAErD;IACAjC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1EjC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMwC,QAAQ,GAAG,MAAMlD,OAAO,CAAC2E,QAAQ,CAAC;MACxC,IAAIzB,QAAQ,CAACC,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAAC;UACdC,OAAO,EAAEF,QAAQ,CAAC1D,OAAO;UACzB6D,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO;QAC7B,CAAC,CAAC;QACFqB,OAAO,CAACD,QAAQ,CAAC;QACjBE,MAAM,CAAC3B,QAAQ,CAACa,IAAI,CAAC;QACrBe,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,MAAM;QACLrB,qBAAqB,CAAC;UAAEP;QAAS,CAAC,EAAE,kCAAkC,CAAC;MACzE;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,qBAAqB,CAACC,KAAK,EAAE,oEAAoE,CAAC;IACpG;IACAhD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAID,oBACEP,OAAA;IAAK4E,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjC7E,OAAA;MAAK4E,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BC,YAAY,gBACX9E,OAAA;QAAA6E,QAAA,gBACE7E,OAAA;UAAK4E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7E,OAAA;YAAK+E,GAAG,EAAEjF,IAAK;YAACkF,GAAG,EAAC,gBAAgB;YAACJ,SAAS,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjEpF,OAAA;YAAI4E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDpF,OAAA;YAAG4E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAA4C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAGNpF,OAAA;UAAK4E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B7E,OAAA;YAAK4E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7E,OAAA;cAAI4E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDpF,OAAA;cAAG4E,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,eACd,eAAA7E,OAAA;gBAAA6E,QAAA,EAAQ;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,2BACzD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpF,OAAA;cAAK4E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB7E,OAAA;gBAAK4E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7E,OAAA;kBAAM4E,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCpF,OAAA;kBAAM4E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNpF,OAAA;gBAAK4E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7E,OAAA;kBAAM4E,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCpF,OAAA;kBAAM4E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAgC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACNpF,OAAA;gBAAK4E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7E,OAAA;kBAAM4E,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCpF,OAAA;kBAAM4E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAoC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNpF,OAAA;gBAAK4E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB7E,OAAA;kBAAM4E,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCpF,OAAA;kBAAM4E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpF,OAAA;cAAK4E,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB7E,OAAA;gBAAG4E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtB7E,OAAA;kBAAA6E,QAAA,EAAQ;gBAAwB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,oEAC3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA,CAACZ,IAAI;UAACiG,MAAM,EAAC,UAAU;UAAChD,QAAQ,EAAEmB,UAAW;UAACoB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACrE7E,OAAA,CAACZ,IAAI,CAACkG,IAAI;YAAClE,IAAI,EAAC,KAAK;YAACmE,KAAK,EAAC,mBAAmB;YAACC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAAwF,QAAA,gBAC5G7E,OAAA,CAACV,KAAK;cACJoG,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,sBAAsB;cAChCe,WAAW,EAAC,mCAAmC;cAC/CC,SAAS,EAAE;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACFpF,OAAA;cAAG4E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA2C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEZpF,OAAA;YAAQ0F,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,cAAc;YAACiB,QAAQ,EAAEvF,OAAQ;YAAAuE,QAAA,EAC9DvE,OAAO,GAAG,gBAAgB,GAAG;UAAkC;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAETpF,OAAA;YAAK4E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7E,OAAA;cAAG4E,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAwB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvDpF,OAAA;cACE0F,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,YAAY;cACtBkB,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACX,IAAI,CAAE;cACjCiC,QAAQ,EAAEvF,OAAQ;cAAAuE,QAAA,EACnB;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,gBAENpF,OAAA;QAAA6E,QAAA,gBACE7E,OAAA;UAAK4E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7E,OAAA;YAAK+E,GAAG,EAAEjF,IAAK;YAACkF,GAAG,EAAC,gBAAgB;YAACJ,SAAS,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjEpF,OAAA;YAAI4E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDpF,OAAA;YAAG4E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAkD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAINpF,OAAA,CAACZ,IAAI;UACHwB,IAAI,EAAEA,IAAK;UACXyE,MAAM,EAAC,UAAU;UACjBhD,QAAQ,EAAEkC,WAAY;UACtBK,SAAS,EAAC,eAAe;UACzBmB,cAAc,EAAGC,SAAS,IAAK;YAC7BzD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEwD,SAAS,CAAC;YACnD3G,OAAO,CAACkE,KAAK,CAAC,2CAA2C,CAAC;UAC5D,CAAE;UAAAsB,QAAA,gBAIF7E,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,MAAM;YACXmE,KAAK,EAAC,WAAW;YACjBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAA8B,CAAC,CAAE;YAAAwF,QAAA,eAEpE7E,OAAA,CAACV,KAAK;cACJoG,IAAI,EAAC,MAAM;cACXd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,uCAAuC;cACnDM,YAAY,EAAC;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,QAAQ;YACbmE,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAAgC,CAAC,CAAE;YAAAwF,QAAA,eAEtE7E,OAAA,CAACV,KAAK;cACJoG,IAAI,EAAC,MAAM;cACXd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,+DAA+D;cAC3EM,YAAY,EAAC;YAAc;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,OAAO;YACZmE,KAAK,EAAC,iBAAiB;YACvBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAAqC,CAAC,CAAE;YAAAwF,QAAA,eAE3E7E,OAAA,CAACT,MAAM;cACL2G,QAAQ,EAAGC,KAAK,IAAK1F,aAAa,CAAC0F,KAAK,CAAE;cAC1CvB,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,qCAAqC;cAAAd,QAAA,gBAEjD7E,OAAA,CAACG,MAAM;gBAACgG,KAAK,EAAC,SAAS;gBAAAtB,QAAA,EAAC;cAAkC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnEpF,OAAA,CAACG,MAAM;gBAACgG,KAAK,EAAC,WAAW;gBAAAtB,QAAA,EAAC;cAAkC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrEpF,OAAA,CAACG,MAAM;gBAACgG,KAAK,EAAC,SAAS;gBAAAtB,QAAA,EAAC;cAA6B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,OAAO;YACZmE,KAAK,EAAC,YAAY;YAClBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAAmC,CAAC,CAAE;YAAAwF,QAAA,eAEzE7E,OAAA,CAACT,MAAM;cACLqF,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAEnF,UAAU,GAAG,wBAAwB,GAAG,qCAAsC;cAC3FqF,QAAQ,EAAE,CAACrF,UAAW;cAAAqE,QAAA,GAErBrE,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC4F,GAAG,CAAEC,CAAC,iBACvDrG,OAAA,CAACG,MAAM;gBAASgG,KAAK,EAAEE,CAAE;gBAAAxB,QAAA,EAAG,YAAWwB,CAAE;cAAC,GAA7BA,CAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CACpD,CAAC,EACD5E,UAAU,KAAK,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC4F,GAAG,CAAEC,CAAC,iBAChDrG,OAAA,CAACG,MAAM;gBAASgG,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAAxB,QAAA,EAAG,WAAUwB,CAAE;cAAC,GAAtCA,CAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC,EACD5E,UAAU,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC4F,GAAG,CAAEC,CAAC,iBACxCrG,OAAA,CAACG,MAAM;gBAASgG,KAAK,EAAG,QAAOE,CAAE,EAAE;gBAAAxB,QAAA,EAAG,WAAUwB,CAAE;cAAC,GAAtCA,CAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8C,CAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,OAAO;YACZmE,KAAK,EAAC,eAAe;YACrBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAA0B,CAAC,EACtD;cAAEqG,IAAI,EAAE,OAAO;cAAErG,OAAO,EAAE;YAAyB,CAAC,CACpD;YAAAwF,QAAA,eAEF7E,OAAA,CAACV,KAAK;cACJoG,IAAI,EAAC,OAAO;cACZd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,sDAAsD;cAClEM,YAAY,EAAC;YAAO;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,aAAa;YAClBmE,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAAiC,CAAC,EAC7D;cAAEiH,OAAO,EAAE,cAAc;cAAEjH,OAAO,EAAE;YAAgC,CAAC,CACrE;YAAAwF,QAAA,eAEF7E,OAAA,CAACV,KAAK;cACJoG,IAAI,EAAC,KAAK;cACVd,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,sDAAsD;cAClEM,YAAY,EAAC,KAAK;cAClBL,SAAS,EAAE;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,UAAU;YACfmE,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAA6B,CAAC,EACzD;cAAEkH,GAAG,EAAE,CAAC;cAAElH,OAAO,EAAE;YAA8C,CAAC,CAClE;YAAAwF,QAAA,eAEF7E,OAAA,CAACV,KAAK,CAACkH,QAAQ;cACb5B,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,6CAA6C;cACzDM,YAAY,EAAC;YAAc;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YACRlE,IAAI,EAAC,iBAAiB;YACtBmE,KAAK,EAAC,iBAAiB;YACvBkB,YAAY,EAAE,CAAC,UAAU,CAAE;YAC3BjB,KAAK,EAAE,CACL;cAAEC,QAAQ,EAAE,IAAI;cAAEpG,OAAO,EAAE;YAA8B,CAAC,EAC1D,CAAC;cAAE6C;YAAc,CAAC,MAAM;cACtBwE,SAASA,CAACC,CAAC,EAAER,KAAK,EAAE;gBAClB,IAAI,CAACA,KAAK,IAAIjE,aAAa,CAAC,UAAU,CAAC,KAAKiE,KAAK,EAAE;kBACjD,OAAOS,OAAO,CAACC,OAAO,CAAC,CAAC;gBAC1B;gBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,gCAAgC,CAAC,CAAC;cACpE;YACF,CAAC,CAAC,CACF;YAAAlC,QAAA,eAEF7E,OAAA,CAACV,KAAK,CAACkH,QAAQ;cACb5B,SAAS,EAAC,YAAY;cACtBe,WAAW,EAAC,iCAAiC;cAC7CM,YAAY,EAAC;YAAc;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZpF,OAAA,CAACZ,IAAI,CAACkG,IAAI;YAAAT,QAAA,gBACR7E,OAAA;cACE0F,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,cAAc;cACxBiB,QAAQ,EAAEvF,OAAQ;cAClBwF,OAAO,EAAEA,CAAA,KAAMvD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAE;cAAAqC,QAAA,EAE9DvE,OAAO,gBACNN,OAAA,CAAAE,SAAA;gBAAA2E,QAAA,gBACE7E,OAAA;kBAAM4E,SAAS,EAAC;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,uBAE3C;cAAA,eAAE,CAAC,gBAEHpF,OAAA,CAAAE,SAAA;gBAAA2E,QAAA,EAAE;cAEF,gBAAE;YACH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAGTpF,OAAA;cACE0F,IAAI,EAAC,QAAQ;cACbd,SAAS,EAAC,cAAc;cACxBzB,KAAK,EAAE;gBAACC,SAAS,EAAE,MAAM;gBAAE4D,UAAU,EAAE;cAAS,CAAE;cAClDlB,OAAO,EAAEA,CAAA,KAAM;gBACb;gBACA,MAAMlF,IAAI,GAAGqG,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;gBACrD,MAAM1C,QAAQ,GAAG,IAAI2C,QAAQ,CAACvG,IAAI,CAAC;gBACnC,MAAM0B,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,IAAI,CAAC8E,GAAG,EAAEjB,KAAK,CAAC,IAAI3B,QAAQ,CAAC6C,OAAO,CAAC,CAAC,EAAE;kBAC3C/E,MAAM,CAAC8E,GAAG,CAAC,GAAGjB,KAAK;gBACrB;gBACA5D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,MAAM,CAAC;;gBAEtC;gBACA,MAAMgF,QAAQ,GAAG;kBACflG,IAAI,EAAE,WAAW;kBACjBsB,MAAM,EAAE,aAAa;kBACrBC,KAAK,EAAE,SAAS;kBAChBC,KAAK,EAAE,CAAC;kBACR2E,KAAK,EAAE,kBAAkB;kBACzB1E,WAAW,EAAE,YAAY;kBACzBC,QAAQ,EAAE,aAAa;kBACvB0E,eAAe,EAAE;gBACnB,CAAC;gBACDjD,WAAW,CAAC+C,QAAQ,CAAC;cACvB,CAAE;cAAAzC,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEPpF,OAAA;UAAK4E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B7E,OAAA;YAAA6E,QAAA,GAAG,2BACwB,EAAC,GAAG,eAC7B7E,OAAA,CAACN,IAAI;cAAC+H,EAAE,EAAC,QAAQ;cAAC7C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE5C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/E,EAAA,CA7dQD,QAAQ;EAAA,QAIAhB,IAAI,CAACyB,OAAO,EACVlB,WAAW;AAAA;AAAA+H,EAAA,GALrBtH,QAAQ;AA+djB,eAAeA,QAAQ;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}