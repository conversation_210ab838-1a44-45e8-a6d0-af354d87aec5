{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\SubscriptionModal\\\\SubscriptionModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SubscriptionModal = ({\n  isOpen,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  var _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = phone => {\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile\n  const updateUserPhoneNumber = async newPhone => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || '',\n        level: user.level || '',\n        phoneNumber: newPhone\n      };\n      const response = await updateUserInfo(updatePayload);\n      if (response.success) {\n        // Update Redux store with new user data\n        dispatch(SetUser(response.data));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(response.data));\n        console.log('✅ User phone number updated successfully');\n        return true;\n      } else {\n        console.error('❌ Failed to update user phone number:', response.message);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error updating user phone number:', error);\n      return false;\n    }\n  };\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    }\n  }, [isOpen, user === null || user === void 0 ? void 0 : user.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.phoneNumber && !isEditingPhone) {\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user === null || user === void 0 ? void 0 : user.phoneNumber, isEditingPhone]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePlanSelect = plan => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n    try {\n      var _user$name;\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone,\n        // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n\n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const checkPaymentConfirmation = async orderId => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n          return true;\n        }\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"modal-title\",\n          children: [step === 'plans' && '🚀 Choose Your Learning Plan', step === 'payment' && '💳 Complete Your Payment', step === 'success' && '⏳ Processing Payment...']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-button\",\n          onClick: handleClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        children: [step === 'plans' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-state\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading plans...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this) : plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$features, _plan$features2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-card\",\n              onClick: () => handlePlanSelect(plan),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('glimp')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-price\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-amount\",\n                  children: [(_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this), plan.actualPrice && plan.actualPrice !== plan.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-original\",\n                  children: [plan.actualPrice.toLocaleString(), \" TZS\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"price-period\",\n                  children: [plan.duration, \" month\", plan.duration > 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: [(_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 25\n                }, this)), ((_plan$features2 = plan.features) === null || _plan$features2 === void 0 ? void 0 : _plan$features2.length) > 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-icon\",\n                    children: \"+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"feature-text\",\n                    children: [plan.features.length - 4, \" more features\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                children: [\"Choose \", plan.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), step === 'payment' && selectedPlan && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-plan-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [\"Selected Plan: \", selectedPlan.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"plan-price-summary\",\n              children: [(_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS for \", selectedPlan.duration, \" month\", selectedPlan.duration > 1 ? 's' : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"phone-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"info-label\",\n                  children: \"Phone Number for Payment:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), !isEditingPhone ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `info-value ${phoneUpdated ? 'updated' : ''}`,\n                    children: [paymentPhone || 'Not provided', phoneUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"updated-indicator\",\n                      children: \"\\u2705 Updated\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 44\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"edit-phone-btn\",\n                    onClick: () => setIsEditingPhone(true),\n                    type: \"button\",\n                    children: \"\\u270F\\uFE0F Change\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"phone-edit\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    value: paymentPhone,\n                    onChange: e => setPaymentPhone(e.target.value),\n                    placeholder: \"Enter phone number (e.g., 0744963858)\",\n                    className: `phone-input ${paymentPhone ? isValidPhone(paymentPhone) ? 'valid' : 'invalid' : ''}`,\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this), paymentPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`,\n                    children: isValidPhone(paymentPhone) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message valid\",\n                      children: \"\\u2705 Valid phone number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"validation-message invalid\",\n                      children: \"\\u274C Must start with 06 or 07 and be 10 digits\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 327,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"phone-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"save-phone-btn\",\n                      onClick: async () => {\n                        if (isValidPhone(paymentPhone)) {\n                          try {\n                            // Show loading state\n                            const btn = document.querySelector('.save-phone-btn');\n                            const originalText = btn.textContent;\n                            btn.textContent = '⏳ Saving...';\n                            btn.disabled = true;\n\n                            // Check if phone number is different from current user's phone\n                            const isPhoneChanged = paymentPhone !== (user === null || user === void 0 ? void 0 : user.phoneNumber);\n                            if (isPhoneChanged) {\n                              // Update user's profile with new phone number\n                              const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n                              if (updateSuccess) {\n                                setIsEditingPhone(false);\n                                setPhoneUpdated(true);\n\n                                // Show success messages\n                                message.success({\n                                  content: '📱 Phone number updated successfully!',\n                                  duration: 4,\n                                  style: {\n                                    marginTop: '20vh',\n                                    fontSize: '15px',\n                                    fontWeight: '600'\n                                  }\n                                });\n\n                                // Additional success message for payment\n                                setTimeout(() => {\n                                  message.info({\n                                    content: '💡 Your profile has been updated. This number will receive payment SMS.',\n                                    duration: 5,\n                                    style: {\n                                      marginTop: '20vh',\n                                      fontSize: '14px'\n                                    }\n                                  });\n                                }, 1000);\n\n                                // Reset the updated indicator after 5 seconds\n                                setTimeout(() => {\n                                  setPhoneUpdated(false);\n                                }, 5000);\n                              } else {\n                                message.error('Failed to update phone number in profile. Please try again.');\n                              }\n                            } else {\n                              // Phone number is the same, just close editing\n                              setIsEditingPhone(false);\n                              message.success({\n                                content: '📱 Phone number confirmed for payment',\n                                duration: 3,\n                                style: {\n                                  marginTop: '20vh',\n                                  fontSize: '15px',\n                                  fontWeight: '600'\n                                }\n                              });\n                            }\n\n                            // Restore button state\n                            btn.textContent = originalText;\n                            btn.disabled = !isValidPhone(paymentPhone);\n                          } catch (error) {\n                            console.error('Error saving phone number:', error);\n                            message.error('Failed to save phone number. Please try again.');\n                          }\n                        } else {\n                          message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                        }\n                      },\n                      disabled: !isValidPhone(paymentPhone),\n                      type: \"button\",\n                      children: \"\\u2705 Save\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"cancel-phone-btn\",\n                      onClick: () => {\n                        setPaymentPhone((user === null || user === void 0 ? void 0 : user.phoneNumber) || '');\n                        setIsEditingPhone(false);\n                      },\n                      type: \"button\",\n                      children: \"\\u274C Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-note\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: \"\\uD83D\\uDCA1 This number will receive the payment SMS. You can use a different number than your profile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"info-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-label\",\n                children: \"Payment Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"info-value\",\n                children: \"Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"back-btn\",\n              onClick: () => setStep('plans'),\n              children: \"\\u2190 Back to Plans\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"pay-btn\",\n              onClick: handlePayment,\n              disabled: paymentLoading || !paymentPhone || isEditingPhone,\n              children: paymentLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"btn-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 23\n                }, this), \"Processing...\"]\n              }, void 0, true) : isEditingPhone ? 'Save phone number first' : !paymentPhone ? 'Enter phone number' : `Pay ${(_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString()} TZS`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), step === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-animation\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pulse-circle\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-icon\",\n                children: \"\\uD83D\\uDCF1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Payment Request Sent!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Please check your phone for SMS confirmation and complete the payment.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-steps\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Check your phone for SMS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Follow the payment instructions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-number\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-text\",\n                children: \"Your subscription will activate automatically\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"check-status-btn\",\n              onClick: async () => {\n                console.log('🔍 Manual payment check triggered');\n                try {\n                  const response = await checkPaymentStatus();\n                  console.log('📥 Manual check response:', response);\n                  if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                    console.log('✅ Payment confirmed manually!');\n                    dispatch(SetSubscription(response));\n                    message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                    onSuccess && onSuccess();\n                    setTimeout(() => onClose(), 1000);\n                  } else {\n                    message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                  }\n                } catch (error) {\n                  console.error('❌ Manual check error:', error);\n                  message.error('Error checking payment status');\n                }\n              },\n              children: \"\\u2705 Check Payment Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"done-btn\",\n              onClick: handleClose,\n              children: \"I'll complete the payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 5\n  }, this);\n};\n_s(SubscriptionModal, \"aZxc1xREfgbzjCdSZ68Mez4hUuk=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = SubscriptionModal;\nexport default SubscriptionModal;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "message", "getPlans", "addPayment", "checkPaymentStatus", "updateUserInfo", "SetSubscription", "SetUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SubscriptionModal", "isOpen", "onClose", "onSuccess", "_s", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "step", "setStep", "paymentPhone", "setPaymentPhone", "isEditingPhone", "setIsEditingPhone", "phoneUpdated", "setPhoneUpdated", "isValidPhone", "phone", "test", "updateUserPhoneNumber", "newPhone", "console", "log", "updatePayload", "userId", "user", "_id", "name", "email", "school", "class_", "class", "level", "phoneNumber", "response", "success", "dispatch", "data", "localStorage", "setItem", "JSON", "stringify", "error", "state", "fetchPlans", "Array", "isArray", "handlePlanSelect", "plan", "handlePayment", "length", "_user$name", "paymentData", "userPhone", "userEmail", "replace", "toLowerCase", "checkPaymentConfirmation", "order_id", "Error", "orderId", "attempts", "maxAttempts", "checkStatus", "paymentStatus", "status", "content", "duration", "style", "marginTop", "fontSize", "fontWeight", "setTimeout", "warning", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "_plan$title", "_plan$discountedPrice", "_plan$features", "_plan$features2", "title", "includes", "discountedPrice", "toLocaleString", "actualPrice", "features", "slice", "feature", "index", "type", "value", "onChange", "e", "target", "placeholder", "max<PERSON><PERSON><PERSON>", "btn", "document", "querySelector", "originalText", "textContent", "disabled", "isPhoneChanged", "updateSuccess", "info", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/SubscriptionModal/SubscriptionModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport { getPlans } from '../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../apicalls/payment';\nimport { updateUserInfo } from '../../apicalls/users';\nimport { SetSubscription } from '../../redux/subscriptionSlice';\nimport { SetUser } from '../../redux/usersSlice';\nimport { HideLoading, ShowLoading } from '../../redux/loaderSlice';\nimport './SubscriptionModal.css';\n\nconst SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {\n  const [plans, setPlans] = useState([]);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(false);\n  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'\n  const [paymentPhone, setPaymentPhone] = useState('');\n  const [isEditingPhone, setIsEditingPhone] = useState(false);\n  const [phoneUpdated, setPhoneUpdated] = useState(false);\n\n  // Validate phone number format\n  const isValidPhone = (phone) => {\n    return phone && /^(06|07)\\d{8}$/.test(phone);\n  };\n\n  // Update user's phone number in profile\n  const updateUserPhoneNumber = async (newPhone) => {\n    try {\n      console.log('📱 Updating user phone number in profile:', newPhone);\n\n      const updatePayload = {\n        userId: user._id,\n        name: user.name,\n        email: user.email || '',\n        school: user.school || '',\n        class_: user.class || '',\n        level: user.level || '',\n        phoneNumber: newPhone\n      };\n\n      const response = await updateUserInfo(updatePayload);\n\n      if (response.success) {\n        // Update Redux store with new user data\n        dispatch(SetUser(response.data));\n\n        // Update localStorage\n        localStorage.setItem('user', JSON.stringify(response.data));\n\n        console.log('✅ User phone number updated successfully');\n        return true;\n      } else {\n        console.error('❌ Failed to update user phone number:', response.message);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ Error updating user phone number:', error);\n      return false;\n    }\n  };\n  \n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchPlans();\n      // Initialize payment phone with user's current phone\n      setPaymentPhone(user?.phoneNumber || '');\n    }\n  }, [isOpen, user?.phoneNumber]);\n\n  // Update payment phone when user data changes (after profile update)\n  useEffect(() => {\n    if (user?.phoneNumber && !isEditingPhone) {\n      setPaymentPhone(user.phoneNumber);\n    }\n  }, [user?.phoneNumber, isEditingPhone]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      const response = await getPlans();\n      setPlans(Array.isArray(response) ? response : []);\n    } catch (error) {\n      console.error('Error fetching plans:', error);\n      message.error('Failed to load subscription plans');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePlanSelect = (plan) => {\n    setSelectedPlan(plan);\n    setStep('payment');\n  };\n\n  const handlePayment = async () => {\n    if (!selectedPlan) {\n      message.error('Please select a plan first');\n      return;\n    }\n\n    if (!paymentPhone || paymentPhone.length < 10) {\n      message.error('Please enter a valid phone number (e.g., 0744963858)');\n      return;\n    }\n\n    // Validate Tanzanian phone number format\n    if (!/^(06|07)\\d{8}$/.test(paymentPhone)) {\n      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n      return;\n    }\n\n    try {\n      setPaymentLoading(true);\n      dispatch(ShowLoading());\n\n      const paymentData = {\n        plan: selectedPlan,\n        userId: user._id,\n        userPhone: paymentPhone, // Use the payment phone number (may be different from profile)\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        message.success('Payment initiated! Please check your phone for SMS confirmation.');\n        setStep('success');\n        \n        // Start checking payment status\n        checkPaymentConfirmation(response.order_id);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('Payment error:', error);\n      message.error(error.message || 'Payment failed. Please try again.');\n    } finally {\n      setPaymentLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    let attempts = 0;\n    const maxAttempts = 120; // 10 minutes (increased for better user experience)\n\n    const checkStatus = async () => {\n      try {\n        attempts++;\n        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);\n\n        const response = await checkPaymentStatus();\n        console.log('📥 Payment status response:', response);\n\n        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n          console.log('✅ Payment confirmed! Showing success...');\n\n          // Update Redux store\n          dispatch(SetSubscription(response));\n\n          // Show success message with celebration\n          message.success({\n            content: '🎉 Payment Confirmed! Welcome to Premium!',\n            duration: 5,\n            style: {\n              marginTop: '20vh',\n              fontSize: '16px',\n              fontWeight: '600'\n            }\n          });\n\n          // Trigger success callback\n          onSuccess && onSuccess();\n\n          // Close modal after short delay to show success\n          setTimeout(() => {\n            onClose();\n          }, 2000);\n\n          return true;\n        }\n\n        if (attempts >= maxAttempts) {\n          console.log('⏰ Payment check timeout reached');\n          message.warning({\n            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',\n            duration: 8\n          });\n          return false;\n        }\n\n        // Continue checking\n        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response\n      } catch (error) {\n        console.error('❌ Error checking payment status:', error);\n        if (attempts >= maxAttempts) {\n          message.error('Unable to verify payment. Please contact support if payment was completed.');\n        } else {\n          setTimeout(checkStatus, 3000);\n        }\n      }\n    };\n\n    // Start checking immediately\n    checkStatus();\n  };\n\n  const handleClose = () => {\n    setStep('plans');\n    setSelectedPlan(null);\n    setPaymentLoading(false);\n    setIsEditingPhone(false);\n    setPaymentPhone(user?.phoneNumber || '');\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"subscription-modal-overlay\">\n      <div className=\"subscription-modal\">\n        <div className=\"modal-header\">\n          <h2 className=\"modal-title\">\n            {step === 'plans' && '🚀 Choose Your Learning Plan'}\n            {step === 'payment' && '💳 Complete Your Payment'}\n            {step === 'success' && '⏳ Processing Payment...'}\n          </h2>\n          <button className=\"close-button\" onClick={handleClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {step === 'plans' && (\n            <div className=\"plans-grid\">\n              {loading ? (\n                <div className=\"loading-state\">\n                  <div className=\"spinner\"></div>\n                  <p>Loading plans...</p>\n                </div>\n              ) : (\n                plans.map((plan) => (\n                  <div key={plan._id} className=\"plan-card\" onClick={() => handlePlanSelect(plan)}>\n                    <div className=\"plan-header\">\n                      <h3 className=\"plan-title\">{plan.title}</h3>\n                      {plan.title?.toLowerCase().includes('glimp') && (\n                        <span className=\"plan-badge\">🔥 Popular</span>\n                      )}\n                    </div>\n                    \n                    <div className=\"plan-price\">\n                      <span className=\"price-amount\">{plan.discountedPrice?.toLocaleString()} TZS</span>\n                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (\n                        <span className=\"price-original\">{plan.actualPrice.toLocaleString()} TZS</span>\n                      )}\n                      <span className=\"price-period\">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>\n                    </div>\n\n                    <div className=\"plan-features\">\n                      {plan.features?.slice(0, 4).map((feature, index) => (\n                        <div key={index} className=\"feature\">\n                          <span className=\"feature-icon\">✓</span>\n                          <span className=\"feature-text\">{feature}</span>\n                        </div>\n                      ))}\n                      {plan.features?.length > 4 && (\n                        <div className=\"feature\">\n                          <span className=\"feature-icon\">+</span>\n                          <span className=\"feature-text\">{plan.features.length - 4} more features</span>\n                        </div>\n                      )}\n                    </div>\n\n                    <button className=\"select-plan-btn\">\n                      Choose {plan.title}\n                    </button>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n\n          {step === 'payment' && selectedPlan && (\n            <div className=\"payment-step\">\n              <div className=\"selected-plan-summary\">\n                <h3>Selected Plan: {selectedPlan.title}</h3>\n                <p className=\"plan-price-summary\">\n                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}\n                </p>\n              </div>\n\n              <div className=\"payment-info\">\n                <div className=\"phone-section\">\n                  <div className=\"info-item\">\n                    <span className=\"info-label\">Phone Number for Payment:</span>\n                    {!isEditingPhone ? (\n                      <div className=\"phone-display\">\n                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>\n                          {paymentPhone || 'Not provided'}\n                          {phoneUpdated && <span className=\"updated-indicator\">✅ Updated</span>}\n                        </span>\n                        <button\n                          className=\"edit-phone-btn\"\n                          onClick={() => setIsEditingPhone(true)}\n                          type=\"button\"\n                        >\n                          ✏️ Change\n                        </button>\n                      </div>\n                    ) : (\n                      <div className=\"phone-edit\">\n                        <input\n                          type=\"tel\"\n                          value={paymentPhone}\n                          onChange={(e) => setPaymentPhone(e.target.value)}\n                          placeholder=\"Enter phone number (e.g., 0744963858)\"\n                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}\n                          maxLength=\"10\"\n                        />\n                        {paymentPhone && (\n                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>\n                            {isValidPhone(paymentPhone) ? (\n                              <span className=\"validation-message valid\">✅ Valid phone number</span>\n                            ) : (\n                              <span className=\"validation-message invalid\">❌ Must start with 06 or 07 and be 10 digits</span>\n                            )}\n                          </div>\n                        )}\n                        <div className=\"phone-actions\">\n                          <button\n                            className=\"save-phone-btn\"\n                            onClick={async () => {\n                              if (isValidPhone(paymentPhone)) {\n                                try {\n                                  // Show loading state\n                                  const btn = document.querySelector('.save-phone-btn');\n                                  const originalText = btn.textContent;\n                                  btn.textContent = '⏳ Saving...';\n                                  btn.disabled = true;\n\n                                  // Check if phone number is different from current user's phone\n                                  const isPhoneChanged = paymentPhone !== user?.phoneNumber;\n\n                                  if (isPhoneChanged) {\n                                    // Update user's profile with new phone number\n                                    const updateSuccess = await updateUserPhoneNumber(paymentPhone);\n\n                                    if (updateSuccess) {\n                                      setIsEditingPhone(false);\n                                      setPhoneUpdated(true);\n\n                                      // Show success messages\n                                      message.success({\n                                        content: '📱 Phone number updated successfully!',\n                                        duration: 4,\n                                        style: {\n                                          marginTop: '20vh',\n                                          fontSize: '15px',\n                                          fontWeight: '600'\n                                        }\n                                      });\n\n                                      // Additional success message for payment\n                                      setTimeout(() => {\n                                        message.info({\n                                          content: '💡 Your profile has been updated. This number will receive payment SMS.',\n                                          duration: 5,\n                                          style: {\n                                            marginTop: '20vh',\n                                            fontSize: '14px'\n                                          }\n                                        });\n                                      }, 1000);\n\n                                      // Reset the updated indicator after 5 seconds\n                                      setTimeout(() => {\n                                        setPhoneUpdated(false);\n                                      }, 5000);\n                                    } else {\n                                      message.error('Failed to update phone number in profile. Please try again.');\n                                    }\n                                  } else {\n                                    // Phone number is the same, just close editing\n                                    setIsEditingPhone(false);\n                                    message.success({\n                                      content: '📱 Phone number confirmed for payment',\n                                      duration: 3,\n                                      style: {\n                                        marginTop: '20vh',\n                                        fontSize: '15px',\n                                        fontWeight: '600'\n                                      }\n                                    });\n                                  }\n\n                                  // Restore button state\n                                  btn.textContent = originalText;\n                                  btn.disabled = !isValidPhone(paymentPhone);\n                                } catch (error) {\n                                  console.error('Error saving phone number:', error);\n                                  message.error('Failed to save phone number. Please try again.');\n                                }\n                              } else {\n                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');\n                              }\n                            }}\n                            disabled={!isValidPhone(paymentPhone)}\n                            type=\"button\"\n                          >\n                            ✅ Save\n                          </button>\n                          <button\n                            className=\"cancel-phone-btn\"\n                            onClick={() => {\n                              setPaymentPhone(user?.phoneNumber || '');\n                              setIsEditingPhone(false);\n                            }}\n                            type=\"button\"\n                          >\n                            ❌ Cancel\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"phone-note\">\n                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>\n                  </div>\n                </div>\n\n                <div className=\"info-item\">\n                  <span className=\"info-label\">Payment Method:</span>\n                  <span className=\"info-value\">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>\n                </div>\n              </div>\n\n              <div className=\"payment-actions\">\n                <button className=\"back-btn\" onClick={() => setStep('plans')}>\n                  ← Back to Plans\n                </button>\n                <button\n                  className=\"pay-btn\"\n                  onClick={handlePayment}\n                  disabled={paymentLoading || !paymentPhone || isEditingPhone}\n                >\n                  {paymentLoading ? (\n                    <>\n                      <span className=\"btn-spinner\"></span>\n                      Processing...\n                    </>\n                  ) : isEditingPhone ? (\n                    'Save phone number first'\n                  ) : !paymentPhone ? (\n                    'Enter phone number'\n                  ) : (\n                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`\n                  )}\n                </button>\n              </div>\n            </div>\n          )}\n\n          {step === 'success' && (\n            <div className=\"success-step\">\n              <div className=\"success-animation\">\n                <div className=\"pulse-circle\">\n                  <div className=\"phone-icon\">📱</div>\n                </div>\n              </div>\n              \n              <h3>Payment Request Sent!</h3>\n              <p>Please check your phone for SMS confirmation and complete the payment.</p>\n              \n              <div className=\"payment-steps\">\n                <div className=\"step\">\n                  <span className=\"step-number\">1</span>\n                  <span className=\"step-text\">Check your phone for SMS</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">2</span>\n                  <span className=\"step-text\">Follow the payment instructions</span>\n                </div>\n                <div className=\"step\">\n                  <span className=\"step-number\">3</span>\n                  <span className=\"step-text\">Your subscription will activate automatically</span>\n                </div>\n              </div>\n\n              <div className=\"success-actions\">\n                <button\n                  className=\"check-status-btn\"\n                  onClick={async () => {\n                    console.log('🔍 Manual payment check triggered');\n                    try {\n                      const response = await checkPaymentStatus();\n                      console.log('📥 Manual check response:', response);\n\n                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {\n                        console.log('✅ Payment confirmed manually!');\n                        dispatch(SetSubscription(response));\n                        message.success('🎉 Payment Confirmed! Welcome to Premium!');\n                        onSuccess && onSuccess();\n                        setTimeout(() => onClose(), 1000);\n                      } else {\n                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');\n                      }\n                    } catch (error) {\n                      console.error('❌ Manual check error:', error);\n                      message.error('Error checking payment status');\n                    }\n                  }}\n                >\n                  ✅ Check Payment Status\n                </button>\n\n                <button className=\"done-btn\" onClick={handleClose}>\n                  I'll complete the payment\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SubscriptionModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,wBAAwB;AACvE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,yBAAyB;AAClE,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgC,IAAI,EAAEC,OAAO,CAAC,GAAGjC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMwC,YAAY,GAAIC,KAAK,IAAK;IAC9B,OAAOA,KAAK,IAAI,gBAAgB,CAACC,IAAI,CAACD,KAAK,CAAC;EAC9C,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEF,QAAQ,CAAC;MAElE,MAAMG,aAAa,GAAG;QACpBC,MAAM,EAAEC,IAAI,CAACC,GAAG;QAChBC,IAAI,EAAEF,IAAI,CAACE,IAAI;QACfC,KAAK,EAAEH,IAAI,CAACG,KAAK,IAAI,EAAE;QACvBC,MAAM,EAAEJ,IAAI,CAACI,MAAM,IAAI,EAAE;QACzBC,MAAM,EAAEL,IAAI,CAACM,KAAK,IAAI,EAAE;QACxBC,KAAK,EAAEP,IAAI,CAACO,KAAK,IAAI,EAAE;QACvBC,WAAW,EAAEb;MACf,CAAC;MAED,MAAMc,QAAQ,GAAG,MAAMlD,cAAc,CAACuC,aAAa,CAAC;MAEpD,IAAIW,QAAQ,CAACC,OAAO,EAAE;QACpB;QACAC,QAAQ,CAAClD,OAAO,CAACgD,QAAQ,CAACG,IAAI,CAAC,CAAC;;QAEhC;QACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACG,IAAI,CAAC,CAAC;QAE3DhB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAO,IAAI;MACb,CAAC,MAAM;QACLD,OAAO,CAACqB,KAAK,CAAC,uCAAuC,EAAER,QAAQ,CAACtD,OAAO,CAAC;QACxE,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAM;IAAEjB;EAAK,CAAC,GAAG/C,WAAW,CAAEiE,KAAK,IAAKA,KAAK,CAAClB,IAAI,CAAC;EACnD,MAAMW,QAAQ,GAAGzD,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIiB,MAAM,EAAE;MACVkD,UAAU,CAAC,CAAC;MACZ;MACAjC,eAAe,CAAC,CAAAc,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,WAAW,KAAI,EAAE,CAAC;IAC1C;EACF,CAAC,EAAE,CAACvC,MAAM,EAAE+B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,WAAW,CAAC,CAAC;;EAE/B;EACAxD,SAAS,CAAC,MAAM;IACd,IAAIgD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEQ,WAAW,IAAI,CAACrB,cAAc,EAAE;MACxCD,eAAe,CAACc,IAAI,CAACQ,WAAW,CAAC;IACnC;EACF,CAAC,EAAE,CAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,WAAW,EAAErB,cAAc,CAAC,CAAC;EAEvC,MAAMgC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFvC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,QAAQ,GAAG,MAAMrD,QAAQ,CAAC,CAAC;MACjCoB,QAAQ,CAAC4C,KAAK,CAACC,OAAO,CAACZ,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC;IACnD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C9D,OAAO,CAAC8D,KAAK,CAAC,mCAAmC,CAAC;IACpD,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,gBAAgB,GAAIC,IAAI,IAAK;IACjC7C,eAAe,CAAC6C,IAAI,CAAC;IACrBvC,OAAO,CAAC,SAAS,CAAC;EACpB,CAAC;EAED,MAAMwC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC/C,YAAY,EAAE;MACjBtB,OAAO,CAAC8D,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF;IAEA,IAAI,CAAChC,YAAY,IAAIA,YAAY,CAACwC,MAAM,GAAG,EAAE,EAAE;MAC7CtE,OAAO,CAAC8D,KAAK,CAAC,sDAAsD,CAAC;MACrE;IACF;;IAEA;IACA,IAAI,CAAC,gBAAgB,CAACxB,IAAI,CAACR,YAAY,CAAC,EAAE;MACxC9B,OAAO,CAAC8D,KAAK,CAAC,wEAAwE,CAAC;MACvF;IACF;IAEA,IAAI;MAAA,IAAAS,UAAA;MACF5C,iBAAiB,CAAC,IAAI,CAAC;MACvB6B,QAAQ,CAAChD,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMgE,WAAW,GAAG;QAClBJ,IAAI,EAAE9C,YAAY;QAClBsB,MAAM,EAAEC,IAAI,CAACC,GAAG;QAChB2B,SAAS,EAAE3C,YAAY;QAAE;QACzB4C,SAAS,EAAE7B,IAAI,CAACG,KAAK,IAAK,IAAAuB,UAAA,GAAE1B,IAAI,CAACE,IAAI,cAAAwB,UAAA,uBAATA,UAAA,CAAWI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED,MAAMtB,QAAQ,GAAG,MAAMpD,UAAU,CAACsE,WAAW,CAAC;MAE9C,IAAIlB,QAAQ,CAACC,OAAO,EAAE;QACpBvD,OAAO,CAACuD,OAAO,CAAC,kEAAkE,CAAC;QACnF1B,OAAO,CAAC,SAAS,CAAC;;QAElB;QACAgD,wBAAwB,CAACvB,QAAQ,CAACwB,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACzB,QAAQ,CAACtD,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC9D,OAAO,CAAC8D,KAAK,CAACA,KAAK,CAAC9D,OAAO,IAAI,mCAAmC,CAAC;IACrE,CAAC,SAAS;MACR2B,iBAAiB,CAAC,KAAK,CAAC;MACxB6B,QAAQ,CAACjD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMsE,wBAAwB,GAAG,MAAOG,OAAO,IAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFF,QAAQ,EAAE;QACVxC,OAAO,CAACC,GAAG,CAAE,yCAAwCuC,QAAS,IAAGC,WAAY,EAAC,CAAC;QAE/E,MAAM5B,QAAQ,GAAG,MAAMnD,kBAAkB,CAAC,CAAC;QAC3CsC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEY,QAAQ,CAAC;QAEpD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACQ,KAAK,IAAIR,QAAQ,CAAC8B,aAAa,KAAK,MAAM,IAAI9B,QAAQ,CAAC+B,MAAM,KAAK,QAAQ,EAAE;UACpG5C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;UAEtD;UACAc,QAAQ,CAACnD,eAAe,CAACiD,QAAQ,CAAC,CAAC;;UAEnC;UACAtD,OAAO,CAACuD,OAAO,CAAC;YACd+B,OAAO,EAAE,2CAA2C;YACpDC,QAAQ,EAAE,CAAC;YACXC,KAAK,EAAE;cACLC,SAAS,EAAE,MAAM;cACjBC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd;UACF,CAAC,CAAC;;UAEF;UACA3E,SAAS,IAAIA,SAAS,CAAC,CAAC;;UAExB;UACA4E,UAAU,CAAC,MAAM;YACf7E,OAAO,CAAC,CAAC;UACX,CAAC,EAAE,IAAI,CAAC;UAER,OAAO,IAAI;QACb;QAEA,IAAIkE,QAAQ,IAAIC,WAAW,EAAE;UAC3BzC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C1C,OAAO,CAAC6F,OAAO,CAAC;YACdP,OAAO,EAAE,sGAAsG;YAC/GC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACF,OAAO,KAAK;QACd;;QAEA;QACAK,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAImB,QAAQ,IAAIC,WAAW,EAAE;UAC3BlF,OAAO,CAAC8D,KAAK,CAAC,4EAA4E,CAAC;QAC7F,CAAC,MAAM;UACL8B,UAAU,CAACT,WAAW,EAAE,IAAI,CAAC;QAC/B;MACF;IACF,CAAC;;IAED;IACAA,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBjE,OAAO,CAAC,OAAO,CAAC;IAChBN,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,KAAK,CAAC;IACxBM,iBAAiB,CAAC,KAAK,CAAC;IACxBF,eAAe,CAAC,CAAAc,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,WAAW,KAAI,EAAE,CAAC;IACxCtC,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IAAKqF,SAAS,EAAC,4BAA4B;IAAAC,QAAA,eACzCtF,OAAA;MAAKqF,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCtF,OAAA;QAAKqF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BtF,OAAA;UAAIqF,SAAS,EAAC,aAAa;UAAAC,QAAA,GACxBpE,IAAI,KAAK,OAAO,IAAI,8BAA8B,EAClDA,IAAI,KAAK,SAAS,IAAI,0BAA0B,EAChDA,IAAI,KAAK,SAAS,IAAI,yBAAyB;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACL1F,OAAA;UAAQqF,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEP,WAAY;UAAAE,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eAEN1F,OAAA;QAAKqF,SAAS,EAAC,eAAe;QAAAC,QAAA,GAC3BpE,IAAI,KAAK,OAAO,iBACflB,OAAA;UAAKqF,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBxE,OAAO,gBACNd,OAAA;YAAKqF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtF,OAAA;cAAKqF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/B1F,OAAA;cAAAsF,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,GAENhF,KAAK,CAACkF,GAAG,CAAElC,IAAI;YAAA,IAAAmC,WAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA;YAAA,oBACbhG,OAAA;cAAoBqF,SAAS,EAAC,WAAW;cAACM,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAACC,IAAI,CAAE;cAAA4B,QAAA,gBAC9EtF,OAAA;gBAAKqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BtF,OAAA;kBAAIqF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE5B,IAAI,CAACuC;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAG,WAAA,GAAAnC,IAAI,CAACuC,KAAK,cAAAJ,WAAA,uBAAVA,WAAA,CAAY3B,WAAW,CAAC,CAAC,CAACgC,QAAQ,CAAC,OAAO,CAAC,kBAC1ClG,OAAA;kBAAMqF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1F,OAAA;gBAAKqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBtF,OAAA;kBAAMqF,SAAS,EAAC,cAAc;kBAAAC,QAAA,IAAAQ,qBAAA,GAAEpC,IAAI,CAACyC,eAAe,cAAAL,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACjFhC,IAAI,CAAC2C,WAAW,IAAI3C,IAAI,CAAC2C,WAAW,KAAK3C,IAAI,CAACyC,eAAe,iBAC5DnG,OAAA;kBAAMqF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAE5B,IAAI,CAAC2C,WAAW,CAACD,cAAc,CAAC,CAAC,EAAC,MAAI;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/E,eACD1F,OAAA;kBAAMqF,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAE5B,IAAI,CAACmB,QAAQ,EAAC,QAAM,EAACnB,IAAI,CAACmB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eAEN1F,OAAA;gBAAKqF,SAAS,EAAC,eAAe;gBAAAC,QAAA,IAAAS,cAAA,GAC3BrC,IAAI,CAAC4C,QAAQ,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,OAAO,EAAEC,KAAK,kBAC7CzG,OAAA;kBAAiBqF,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBAClCtF,OAAA;oBAAMqF,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC1F,OAAA;oBAAMqF,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAEkB;kBAAO;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFvCe,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC,EACD,EAAAM,eAAA,GAAAtC,IAAI,CAAC4C,QAAQ,cAAAN,eAAA,uBAAbA,eAAA,CAAepC,MAAM,IAAG,CAAC,iBACxB5D,OAAA;kBAAKqF,SAAS,EAAC,SAAS;kBAAAC,QAAA,gBACtBtF,OAAA;oBAAMqF,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvC1F,OAAA;oBAAMqF,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAE5B,IAAI,CAAC4C,QAAQ,CAAC1C,MAAM,GAAG,CAAC,EAAC,gBAAc;kBAAA;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1F,OAAA;gBAAQqF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,SAC3B,EAAC5B,IAAI,CAACuC,KAAK;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA,GAjCDhC,IAAI,CAACtB,GAAG;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCb,CAAC;UAAA,CACP;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEAxE,IAAI,KAAK,SAAS,IAAIN,YAAY,iBACjCZ,OAAA;UAAKqF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtF,OAAA;YAAKqF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCtF,OAAA;cAAAsF,QAAA,GAAI,iBAAe,EAAC1E,YAAY,CAACqF,KAAK;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5C1F,OAAA;cAAGqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,IAAA9E,qBAAA,GAC9BI,YAAY,CAACuF,eAAe,cAAA3F,qBAAA,uBAA5BA,qBAAA,CAA8B4F,cAAc,CAAC,CAAC,EAAC,WAAS,EAACxF,YAAY,CAACiE,QAAQ,EAAC,QAAM,EAACjE,YAAY,CAACiE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN1F,OAAA;YAAKqF,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BtF,OAAA;cAAKqF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BtF,OAAA;gBAAKqF,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBtF,OAAA;kBAAMqF,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAC5D,CAACpE,cAAc,gBACdtB,OAAA;kBAAKqF,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BtF,OAAA;oBAAMqF,SAAS,EAAG,cAAa7D,YAAY,GAAG,SAAS,GAAG,EAAG,EAAE;oBAAA8D,QAAA,GAC5DlE,YAAY,IAAI,cAAc,EAC9BI,YAAY,iBAAIxB,OAAA;sBAAMqF,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACP1F,OAAA;oBACEqF,SAAS,EAAC,gBAAgB;oBAC1BM,OAAO,EAAEA,CAAA,KAAMpE,iBAAiB,CAAC,IAAI,CAAE;oBACvCmF,IAAI,EAAC,QAAQ;oBAAApB,QAAA,EACd;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,gBAEN1F,OAAA;kBAAKqF,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBtF,OAAA;oBACE0G,IAAI,EAAC,KAAK;oBACVC,KAAK,EAAEvF,YAAa;oBACpBwF,QAAQ,EAAGC,CAAC,IAAKxF,eAAe,CAACwF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBACjDI,WAAW,EAAC,uCAAuC;oBACnD1B,SAAS,EAAG,eAAcjE,YAAY,GAAIM,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAS,GAAI,EAAG,EAAE;oBACnG4F,SAAS,EAAC;kBAAI;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACDtE,YAAY,iBACXpB,OAAA;oBAAKqF,SAAS,EAAG,oBAAmB3D,YAAY,CAACN,YAAY,CAAC,GAAG,OAAO,GAAG,SAAU,EAAE;oBAAAkE,QAAA,EACpF5D,YAAY,CAACN,YAAY,CAAC,gBACzBpB,OAAA;sBAAMqF,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEtE1F,OAAA;sBAAMqF,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAA2C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAC/F;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eACD1F,OAAA;oBAAKqF,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BtF,OAAA;sBACEqF,SAAS,EAAC,gBAAgB;sBAC1BM,OAAO,EAAE,MAAAA,CAAA,KAAY;wBACnB,IAAIjE,YAAY,CAACN,YAAY,CAAC,EAAE;0BAC9B,IAAI;4BACF;4BACA,MAAM6F,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAC;4BACrD,MAAMC,YAAY,GAAGH,GAAG,CAACI,WAAW;4BACpCJ,GAAG,CAACI,WAAW,GAAG,aAAa;4BAC/BJ,GAAG,CAACK,QAAQ,GAAG,IAAI;;4BAEnB;4BACA,MAAMC,cAAc,GAAGnG,YAAY,MAAKe,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,WAAW;4BAEzD,IAAI4E,cAAc,EAAE;8BAClB;8BACA,MAAMC,aAAa,GAAG,MAAM3F,qBAAqB,CAACT,YAAY,CAAC;8BAE/D,IAAIoG,aAAa,EAAE;gCACjBjG,iBAAiB,CAAC,KAAK,CAAC;gCACxBE,eAAe,CAAC,IAAI,CAAC;;gCAErB;gCACAnC,OAAO,CAACuD,OAAO,CAAC;kCACd+B,OAAO,EAAE,uCAAuC;kCAChDC,QAAQ,EAAE,CAAC;kCACXC,KAAK,EAAE;oCACLC,SAAS,EAAE,MAAM;oCACjBC,QAAQ,EAAE,MAAM;oCAChBC,UAAU,EAAE;kCACd;gCACF,CAAC,CAAC;;gCAEF;gCACAC,UAAU,CAAC,MAAM;kCACf5F,OAAO,CAACmI,IAAI,CAAC;oCACX7C,OAAO,EAAE,yEAAyE;oCAClFC,QAAQ,EAAE,CAAC;oCACXC,KAAK,EAAE;sCACLC,SAAS,EAAE,MAAM;sCACjBC,QAAQ,EAAE;oCACZ;kCACF,CAAC,CAAC;gCACJ,CAAC,EAAE,IAAI,CAAC;;gCAER;gCACAE,UAAU,CAAC,MAAM;kCACfzD,eAAe,CAAC,KAAK,CAAC;gCACxB,CAAC,EAAE,IAAI,CAAC;8BACV,CAAC,MAAM;gCACLnC,OAAO,CAAC8D,KAAK,CAAC,6DAA6D,CAAC;8BAC9E;4BACF,CAAC,MAAM;8BACL;8BACA7B,iBAAiB,CAAC,KAAK,CAAC;8BACxBjC,OAAO,CAACuD,OAAO,CAAC;gCACd+B,OAAO,EAAE,uCAAuC;gCAChDC,QAAQ,EAAE,CAAC;gCACXC,KAAK,EAAE;kCACLC,SAAS,EAAE,MAAM;kCACjBC,QAAQ,EAAE,MAAM;kCAChBC,UAAU,EAAE;gCACd;8BACF,CAAC,CAAC;4BACJ;;4BAEA;4BACAgC,GAAG,CAACI,WAAW,GAAGD,YAAY;4BAC9BH,GAAG,CAACK,QAAQ,GAAG,CAAC5F,YAAY,CAACN,YAAY,CAAC;0BAC5C,CAAC,CAAC,OAAOgC,KAAK,EAAE;4BACdrB,OAAO,CAACqB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;4BAClD9D,OAAO,CAAC8D,KAAK,CAAC,gDAAgD,CAAC;0BACjE;wBACF,CAAC,MAAM;0BACL9D,OAAO,CAAC8D,KAAK,CAAC,wEAAwE,CAAC;wBACzF;sBACF,CAAE;sBACFkE,QAAQ,EAAE,CAAC5F,YAAY,CAACN,YAAY,CAAE;sBACtCsF,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT1F,OAAA;sBACEqF,SAAS,EAAC,kBAAkB;sBAC5BM,OAAO,EAAEA,CAAA,KAAM;wBACbtE,eAAe,CAAC,CAAAc,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,WAAW,KAAI,EAAE,CAAC;wBACxCpB,iBAAiB,CAAC,KAAK,CAAC;sBAC1B,CAAE;sBACFmF,IAAI,EAAC,QAAQ;sBAAApB,QAAA,EACd;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN1F,OAAA;gBAAKqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBtF,OAAA;kBAAAsF,QAAA,EAAO;gBAA8F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1F,OAAA;cAAKqF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBtF,OAAA;gBAAMqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnD1F,OAAA;gBAAMqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKqF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BtF,OAAA;cAAQqF,SAAS,EAAC,UAAU;cAACM,OAAO,EAAEA,CAAA,KAAMxE,OAAO,CAAC,OAAO,CAAE;cAAAmE,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1F,OAAA;cACEqF,SAAS,EAAC,SAAS;cACnBM,OAAO,EAAEhC,aAAc;cACvB2D,QAAQ,EAAEtG,cAAc,IAAI,CAACI,YAAY,IAAIE,cAAe;cAAAgE,QAAA,EAE3DtE,cAAc,gBACbhB,OAAA,CAAAE,SAAA;gBAAAoF,QAAA,gBACEtF,OAAA;kBAAMqF,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,iBAEvC;cAAA,eAAE,CAAC,GACDpE,cAAc,GAChB,yBAAyB,GACvB,CAACF,YAAY,GACf,oBAAoB,GAEnB,OAAI,CAAAX,sBAAA,GAAEG,YAAY,CAACuF,eAAe,cAAA1F,sBAAA,uBAA5BA,sBAAA,CAA8B2F,cAAc,CAAC,CAAE;YACvD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAxE,IAAI,KAAK,SAAS,iBACjBlB,OAAA;UAAKqF,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BtF,OAAA;YAAKqF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChCtF,OAAA;cAAKqF,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BtF,OAAA;gBAAKqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAAsF,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9B1F,OAAA;YAAAsF,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE7E1F,OAAA;YAAKqF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BtF,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtF,OAAA;gBAAMqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC1F,OAAA;gBAAMqF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN1F,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtF,OAAA;gBAAMqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC1F,OAAA;gBAAMqF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN1F,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtF,OAAA;gBAAMqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtC1F,OAAA;gBAAMqF,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAKqF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BtF,OAAA;cACEqF,SAAS,EAAC,kBAAkB;cAC5BM,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACnB5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;gBAChD,IAAI;kBACF,MAAMY,QAAQ,GAAG,MAAMnD,kBAAkB,CAAC,CAAC;kBAC3CsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEY,QAAQ,CAAC;kBAElD,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACQ,KAAK,IAAIR,QAAQ,CAAC8B,aAAa,KAAK,MAAM,IAAI9B,QAAQ,CAAC+B,MAAM,KAAK,QAAQ,EAAE;oBACpG5C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;oBAC5Cc,QAAQ,CAACnD,eAAe,CAACiD,QAAQ,CAAC,CAAC;oBACnCtD,OAAO,CAACuD,OAAO,CAAC,2CAA2C,CAAC;oBAC5DvC,SAAS,IAAIA,SAAS,CAAC,CAAC;oBACxB4E,UAAU,CAAC,MAAM7E,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;kBACnC,CAAC,MAAM;oBACLf,OAAO,CAACmI,IAAI,CAAC,0EAA0E,CAAC;kBAC1F;gBACF,CAAC,CAAC,OAAOrE,KAAK,EAAE;kBACdrB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;kBAC7C9D,OAAO,CAAC8D,KAAK,CAAC,+BAA+B,CAAC;gBAChD;cACF,CAAE;cAAAkC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1F,OAAA;cAAQqF,SAAS,EAAC,UAAU;cAACM,OAAO,EAAEP,WAAY;cAAAE,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnF,EAAA,CApgBIJ,iBAAiB;EAAA,QAmDJf,WAAW,EACXC,WAAW;AAAA;AAAAqI,EAAA,GApDxBvH,iBAAiB;AAsgBvB,eAAeA,iBAAiB;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}